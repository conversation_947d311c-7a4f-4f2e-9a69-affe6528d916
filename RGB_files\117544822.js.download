var cPubgJNt = '{"campaigns":{"217079924":{"name":"All EN Pages (excl. HP) - Nav Primary CTA","metrics":[{"id":"197975399","name":"AutoGoal: On-page engagement","eventIds":["157031759"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417230771":{"name":"All EN Pages (excl. HP) - Nav Primary CTA","type":"cc","pageIds":["137711190"],"state":"live","ignore":0,"variations":{"617109946":{"name":"No Change (Request a demo)","state":"live","preconditions":[],"changes":[]},"617109947":{"name":"Talk to sales","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".self-center.z-\\\\[100\\\\].btn-primary","attributes":{"html":"Talk to sales<!-- --> "}}]},"617109950":{"name":"Contact sales","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".self-center.z-\\\\[100\\\\].btn-primary","attributes":{"html":"Contact sales<!-- --> "}}]},"617109951":{"name":"Book a demo","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".self-center.z-\\\\[100\\\\].btn-primary","attributes":{"html":"Book a demo<!-- --> "}}]}},"preconditions":[],"boundingSelectors":[]}}},"217084233":{"name":"/contact-sales - EN - Personalized","metrics":[{"id":"197983445","name":"AutoGoal: On-page engagement","eventIds":["157031754"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417235080":{"name":"/contact-sales - EN - Personalized","type":"rbp","pageIds":["137709823"],"state":"live","ignore":0,"variations":{"617119688":{"name":"Default","state":"live","preconditions":[]},"617120725":{"name":"UTM gbi_eastern_conference","state":"live","condition":{"type":"audience","audienceId":"187602326"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":"h1","attributes":{"html":"Explore Dialpad"}},{"type":"ATTRIBUTE","selector":"div.lg\\\\:mb-16 .mb-3","attributes":{"html":"<strong>Exclusive offer for GBI Eastern Conference attendees<br>\\n<br>\\n</strong>See how Dialpad helps CIOs and business leaders modernize communications with a demo tailored to your business.<br>\\n<br>\\n\\n✅ &nbsp;Upgrade from legacy systems for a secure, cloud-native platform built to scale.<br>\\n<br>\\n\\n✅ &nbsp;Bring every conversation—voice, video, chat—onto one platform to serve customers faster.<br>\\n<br>\\n\\n✅ &nbsp;Get Ai-powered call guidance, summaries, and insights—right when they matter most."}},{"type":"ATTRIBUTE","selector":"div.text-left","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":"main > div:nth-of-type(2)","attributes":{"css":{"display":"none"}}}]},"617122159":{"name":"UTM gds_cio_summit","state":"live","condition":{"type":"audience","audienceId":"187602421"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":"h1","attributes":{"css":{},"html":"Explore Dialpad"}},{"type":"ATTRIBUTE","selector":"div.lg\\\\:mb-16 .mb-3","attributes":{"css":{},"html":"<strong>Exclusive offer for GDS CIO Insights attendees<br>\\n <br>\\n </strong>See how Dialpad helps CIOs and business leaders modernize communications with a demo tailored to your business.<br>\\n <br>\\n ✅ &nbsp;Upgrade from legacy systems for a secure, cloud-native platform built to scale.<br>\\n <br>\\n ✅ &nbsp;Bring every conversation—voice, video, chat—onto one platform to serve customers faster.<br>\\n <br>\\n ✅ &nbsp;Get Ai-powered call guidance, summaries, and insights—right when they matter most."}},{"type":"ATTRIBUTE","selector":"div.text-left","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":"main > div:nth-of-type(2)","attributes":{"css":{"display":"none"}}}]},"617122160":{"name":"UTM gartner_cio_community_nyc","state":"live","condition":{"type":"audience","audienceId":"187602422"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":"h1","attributes":{"css":{},"html":"Explore Dialpad"}},{"type":"ATTRIBUTE","selector":"div.lg\\\\:mb-16 .mb-3","attributes":{"css":{},"html":"<strong>Exclusive offer for Gartner CIO Community attendees<br>\\n <br>\\n </strong>See how Dialpad helps CIOs and business leaders modernize communications with a demo tailored to your business.<br>\\n <br>\\n ✅ &nbsp;Upgrade from legacy systems for a secure, cloud-native platform built to scale.<br>\\n <br>\\n ✅ &nbsp;Bring every conversation—voice, video, chat—onto one platform to serve customers faster.<br>\\n <br>\\n ✅ &nbsp;Get Ai-powered call guidance, summaries, and insights—right when they matter most."}},{"type":"ATTRIBUTE","selector":"div.text-left","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":"main > div:nth-of-type(2)","attributes":{"css":{"display":"none"}}}]},"617129393":{"name":"US only","state":"live","condition":{"type":"audience","audienceId":"187601745"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(2)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(3)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(4)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(5)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(6)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(7)","attributes":{"css":{"display":"none"}}}]},"617129394":{"name":"UK only","state":"live","condition":{"type":"audience","audienceId":"187602534"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(1)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(3)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(4)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(5)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(6)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(7)","attributes":{"css":{"display":"none"}}}]},"617129395":{"name":"CA only","state":"live","condition":{"type":"audience","audienceId":"187602535"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(1)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(2)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(3)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(4)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(7)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(5)","attributes":{"css":{"display":"none"}}}]},"617129396":{"name":"ANZ only","state":"live","condition":{"type":"audience","audienceId":"187602537"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(1)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(2)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(3)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(5)","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".md\\\\:flex-row.md\\\\:flex-wrap.gap-4.flex-col.md\\\\:justify-start > a:nth-of-type(6)","attributes":{"css":{"display":"none"}}}]}},"variationPriority":["617120725","617122159","617122160","617129393","617129394","617129395","617129396","617119688"],"preconditions":[],"boundingSelectors":[]}}},"217084239":{"name":"HP - EN - Hero Secondary CTA","metrics":[{"id":"197983456","name":"AutoGoal: On-page engagement","eventIds":["157031323"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417235086":{"name":"HP - EN - Hero Secondary CTA","type":"cc","audienceIds":["!187602428"],"pageIds":["137709557"],"state":"live","ignore":0,"variations":{"617119701":{"name":"No Change","state":"live","preconditions":[]},"617122632":{"name":"Gartner Peer Insights","state":"live","preconditions":[],"changes":[{"type":"INSERT_ELEMENT","elementType":"html-block","targetPosition":{"selector":".btn-secondary-dark","insertPosition":"beforebegin"},"idAttribute":"insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56"},{"type":"ATTRIBUTE","selector":"#insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56","attributes":{"className":"btn-secondary-dark btn-primary-secondary-styles","css":{},"html":"<a href=\\"/resources/gartner-peer-insights/\\">Gartner Peer Insights™ report</a>"}},{"type":"ATTRIBUTE","selector":"a.btn-secondary-dark","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":"#insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56 > a","attributes":{"html":"Gartner® Peer Insights™ report"}}]}},"preconditions":[],"boundingSelectors":[]}}},"217084517":{"name":"HP - EN - Hero & Nav Primary CTA","metrics":[{"id":"197984061","name":"AutoGoal: On-page engagement","eventIds":["157031323"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417235364":{"name":"HP - EN - Hero & Nav Primary CTA","type":"cc","pageIds":["137709557"],"state":"live","ignore":0,"variations":{"617120346":{"name":"No Change (Request a demo)","state":"live","preconditions":[],"changes":[]},"617120347":{"name":"Talk to sales","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".lg\\\\:mt-8 > .btn-primary-dark","attributes":{"css":{},"html":"Talk to sales<!-- --> "}},{"type":"ATTRIBUTE","selector":".self-center.z-\\\\[100\\\\].btn-primary","attributes":{"html":"Talk to sales<!-- --> "}}]},"617120348":{"name":"Book a demo","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".lg\\\\:mt-8 > .btn-primary-dark","attributes":{"css":{},"html":"Book a demo<!-- --> "}},{"type":"ATTRIBUTE","selector":".self-center.z-\\\\[100\\\\].btn-primary","attributes":{"html":"Book a demo<!-- --> "}}]},"617120349":{"name":"Contact sales","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".lg\\\\:mt-8 > .btn-primary-dark","attributes":{"css":{},"html":"Contact sales<!-- --> "}},{"type":"ATTRIBUTE","selector":".self-center.z-\\\\[100\\\\].btn-primary","attributes":{"html":"Contact sales<!-- --> "}}]}},"preconditions":[],"boundingSelectors":[]}}},"217084702":{"name":"HP - EN - Content","metrics":[{"id":"197984394","name":"AutoGoal: On-page engagement","eventIds":["157031323"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417235549":{"name":"HP - EN - Content","type":"cc","pageIds":["137709557"],"state":"live","ignore":0,"variations":{"617120723":{"name":"No Change","state":"live","preconditions":[]},"617120724":{"name":"No images ATF","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".w-max","attributes":{"css":{"display":"none"}}},{"type":"ATTRIBUTE","selector":".pb-\\\\[64px\\\\].lg\\\\:pb-\\\\[96px\\\\].overflow-hidden","attributes":{"css":{"display":"none"}}}]}},"preconditions":[],"boundingSelectors":[]}}},"217085260":{"name":"/business-communications EN - Hero Secondary CTA","metrics":[{"id":"197985483","name":"AutoGoal: On-page engagement","eventIds":["157039206"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417236107":{"name":"/business-communications EN - Hero Secondary CTA","type":"cc","pageIds":["137711201"],"state":"live","ignore":0,"variations":{"617121939":{"name":"No Change (Talk to sales)","state":"live","preconditions":[],"changes":[]},"617121946":{"name":"Contact sales","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".btn-secondary.btn-primary","attributes":{"html":"Contact sales<!-- --> "}}]},"617121947":{"name":"Request a demo","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".btn-secondary.btn-primary","attributes":{"html":"Request a demo<!-- --> "}}]},"617121959":{"name":"See pricing","state":"live","preconditions":[],"changes":[{"type":"INSERT_ELEMENT","elementType":"html-block","targetPosition":{"selector":".btn-secondary.btn-primary","insertPosition":"beforebegin"},"idAttribute":"insert-46ae7fd0-69f9-4225-bbcb-103d4257b4da"},{"type":"ATTRIBUTE","selector":"#insert-46ae7fd0-69f9-4225-bbcb-103d4257b4da","attributes":{"className":"btn-secondary btn-primary-secondary-styles-lg","html":"<a href=\\"/pricing/#business\\">See pricing</a>"}},{"type":"ATTRIBUTE","selector":".btn-secondary.btn-primary","attributes":{"css":{"display":"none"}}}]},"617122381":{"name":"See it in action","state":"live","preconditions":[],"changes":[{"type":"INSERT_ELEMENT","elementType":"html-block","targetPosition":{"selector":".btn-secondary.btn-primary","insertPosition":"beforebegin"},"idAttribute":"insert-46ae7fd0-69f9-4225-bbcb-103d4257b4da"},{"type":"ATTRIBUTE","selector":"#insert-46ae7fd0-69f9-4225-bbcb-103d4257b4da","attributes":{"className":"btn-secondary btn-primary-secondary-styles-lg","css":{},"html":"<a href=\\"/demo/\\">See it in action</a>"}},{"type":"ATTRIBUTE","selector":".btn-secondary.btn-primary","attributes":{"css":{"display":"none"}}}]}},"preconditions":[],"boundingSelectors":[]}}},"217085261":{"name":"/ai-sales EN - Hero Secondary CTA","metrics":[{"id":"197985484","name":"AutoGoal: On-page engagement","eventIds":["157039207"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417236108":{"name":"/ai-sales EN - Hero Secondary CTA","type":"cc","pageIds":["137711203"],"state":"live","ignore":0,"variations":{"617121942":{"name":"No Change (Watch demo)","state":"live","preconditions":[],"changes":[]},"617121943":{"name":"See it in action","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".btn-secondary.btn-primary","attributes":{"html":"See it in action<!-- --> "}}]}},"preconditions":[],"boundingSelectors":[]}}},"217085262":{"name":"/ai-contact-center - EN - Hero Secondary CTA","metrics":[{"id":"197985485","name":"AutoGoal: On-page engagement","eventIds":["157039208"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417236109":{"name":"/ai-contact-center - EN - Hero Secondary CTA","type":"cc","pageIds":["137711204"],"state":"live","ignore":0,"variations":{"617121944":{"name":"No Change (Watch demo)","state":"live","preconditions":[],"changes":[]},"617121945":{"name":"See it in action","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".btn-secondary.btn-primary","attributes":{"html":"See it in action<!-- --> "}}]}},"preconditions":[],"boundingSelectors":[]}}},"217085263":{"name":"/ai-contact-center - EN - Hero Primary CTA","metrics":[{"id":"197985486","name":"AutoGoal: On-page engagement","eventIds":["157039208"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417236110":{"name":"/ai-contact-center - EN - Hero Primary CTA","type":"cc","pageIds":["137711204"],"state":"live","ignore":0,"variations":{"617121948":{"name":"No Change (Talk to sales)","state":"live","preconditions":[],"changes":[]},"617121949":{"name":"Contact sales","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".justify-start.mt-8.gap-6.flex-row > a:nth-of-type(1)","attributes":{"html":"Contact sales<!-- --> "}}]},"617121950":{"name":"Book a demo","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".justify-start.mt-8.gap-6.flex-row > a:nth-of-type(1)","attributes":{"html":"Book a demo<!-- --> "}}]},"617121951":{"name":"Request a demo","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".justify-start.mt-8.gap-6.flex-row > a:nth-of-type(1)","attributes":{"html":"Request a demo<!-- --> "}}]}},"preconditions":[],"boundingSelectors":[]}}},"217085264":{"name":"/ai-sales - EN - Hero Primary CTA","metrics":[{"id":"197985489","name":"AutoGoal: On-page engagement","eventIds":["157039207"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417236111":{"name":"/ai-sales - EN - Hero Primary CTA","type":"cc","pageIds":["137711203"],"state":"live","ignore":0,"variations":{"617121952":{"name":"No Change (Talk to sales)","state":"live","preconditions":[],"changes":[]},"617121953":{"name":"Contact sales","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".justify-start.mt-8.gap-6.flex-row > a:nth-of-type(1)","attributes":{"html":"Contact sales<!-- --> "}}]},"617121954":{"name":"Book a demo","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".justify-start.mt-8.gap-6.flex-row > a:nth-of-type(1)","attributes":{"html":"Book a demo<!-- --> "}}]},"617121955":{"name":"Request a demo","state":"live","preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".justify-start.mt-8.gap-6.flex-row > a:nth-of-type(1)","attributes":{"html":"Request a demo<!-- --> "}}]}},"preconditions":[],"boundingSelectors":[]}}},"217085618":{"name":"HP - EN - SMB Hero Secondary CTA","metrics":[{"id":"197986047","name":"AutoGoal: On-page engagement","eventIds":["157031323"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417236465":{"name":"HP - EN - SMB Hero Secondary CTA","type":"cc","audienceIds":["187602428"],"pageIds":["137709557"],"state":"live","ignore":0,"variations":{"617122762":{"name":"No Change","state":"live","preconditions":[]},"617122763":{"name":"See it in action","state":"live","preconditions":[],"changes":[{"type":"INSERT_ELEMENT","elementType":"html-block","targetPosition":{"selector":".btn-secondary-dark","insertPosition":"beforebegin"},"idAttribute":"insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56"},{"type":"ATTRIBUTE","selector":"#insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56","attributes":{"className":"btn-secondary-dark btn-primary-secondary-styles","html":"<a href=\\"/demo/\\">See it in action</a>"}},{"type":"ATTRIBUTE","selector":"a.btn-secondary-dark","attributes":{"css":{"display":"none"}}}]},"617122764":{"name":"See pricing","state":"live","preconditions":[],"changes":[{"type":"INSERT_ELEMENT","elementType":"html-block","targetPosition":{"selector":".btn-secondary-dark","insertPosition":"beforebegin"},"idAttribute":"insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56"},{"type":"ATTRIBUTE","selector":"#insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56","attributes":{"className":"btn-secondary-dark btn-primary-secondary-styles","css":{},"html":"<a href=\\"/pricing/\\">See pricing</a>"}},{"type":"ATTRIBUTE","selector":"a.btn-secondary-dark","attributes":{"css":{"display":"none"}}}]},"617122765":{"name":"Explore plans","state":"live","preconditions":[],"changes":[{"type":"INSERT_ELEMENT","elementType":"html-block","targetPosition":{"selector":".btn-secondary-dark","insertPosition":"beforebegin"},"idAttribute":"insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56"},{"type":"ATTRIBUTE","selector":"#insert-c33ef254-b1c7-4ef8-bdb8-eac465908d56","attributes":{"className":"btn-secondary-dark btn-primary-secondary-styles","css":{},"html":"<a href=\\"/pricing/\\">Explore plans</a>"}},{"type":"ATTRIBUTE","selector":"a.btn-secondary-dark","attributes":{"css":{"display":"none"}}}]}},"preconditions":[],"boundingSelectors":[]}}},"217085732":{"name":"/ceds-test-page/","metrics":[{"id":"197986252","name":"AutoGoal: On-page engagement","eventIds":["157039796"],"scope":"session","type":"conversion","countingMethod":"unique","isGoal":true}],"experiences":{"417236579":{"name":"/ceds-test-page/","type":"rbp","pageIds":["137711297"],"state":"live","ignore":0,"variations":{"617123012":{"name":"Default","state":"live","preconditions":[]},"617123013":{"name":"UTM content = dptest_internal","state":"live","condition":{"type":"audience","audienceId":"187602484"},"preconditions":[],"changes":[{"type":"ATTRIBUTE","selector":".h-\\\\[660px\\\\]","attributes":{"css":{"height":"450px"}}},{"type":"ATTRIBUTE","selector":"section:nth-of-type(2) > .flex-col.items-center","attributes":{"css":{"paddingTop":"60px"}}},{"type":"ATTRIBUTE","selector":"h2.text-\\\\[32px\\\\].lg\\\\:text-\\\\[48px\\\\].mb-6.text-center.font-semibold.text-deep-blue","attributes":{"css":{"color":"rgb(255, 255, 255)"}}},{"type":"ATTRIBUTE","selector":"section:nth-of-type(4) > .flex-col.items-center","attributes":{"css":{"paddingTop":"50px"}}}]}},"variationPriority":["617123013","617123012"],"preconditions":[],"boundingSelectors":[]}}}},"events":{"157031323":{"name":"On-page engagement (Homepage)","type":"click","pageIds":["137709557"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157031753":{"name":"On-page engagement (All Pages)","type":"click","pageIds":["137709821"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157031754":{"name":"On-page engagement (/contact-sales Page)","type":"click","pageIds":["137709823"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157031759":{"name":"On-page engagement (US homepage)","type":"click","pageIds":["137711190"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157032169":{"name":"POC-Form-submit","type":"custom","apiName":"POC-Form-submit"},"157033705":{"name":"POC-Form-Submit","type":"custom","apiName":"POC-Form-Submit"},"157038197":{"name":"On-page engagement (/pricing EN Page)","type":"click","pageIds":["137711030"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157038199":{"name":"On-page engagement (/ai-voice-contact-us/ EN Page)","type":"click","pageIds":["137711034"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157038207":{"name":"On-page engagement (/ai-contact-center-contact-us/ EN Page)","type":"click","pageIds":["137711035"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157038208":{"name":"On-page engagement (/ai-sales-center-contact-us/ EN Page)","type":"click","pageIds":["137711037"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157038651":{"name":"Contact Us (Support) - FormID: 2233 Form ID: 2233","type":"marketo","state":"live","formId":"2233"},"157038652":{"name":"Clearbit Contact Us (Sell) - FormID: 2241 Form ID: 2241","type":"marketo","state":"live","formId":"2241"},"157038653":{"name":"Global Contact Us - Clearbit Form (ID: 3252) Form ID: 3252","type":"marketo","state":"live","formId":"3252"},"157038654":{"name":"Demo Request - CCass - Dialpad Sell - 6689 Form ID: 6689","type":"marketo","state":"live","formId":"6689"},"157038655":{"name":"Contact Us - CCaaS - Dialpad Sell - 6690 Form ID: 6690","type":"marketo","state":"live","formId":"6690"},"157038656":{"name":"Contact Us - Dialpad Connect - 6691 Form ID: 6691","type":"marketo","state":"live","formId":"6691"},"157038657":{"name":"Demo Request - Dialpad Connect - 6693 Form ID: 6693","type":"marketo","state":"live","formId":"6693"},"157038658":{"name":"Surfboard Contact Us: 8303 Form ID: 8303","type":"marketo","state":"live","formId":"8303"},"157038659":{"name":"07. Universal Form [Demo Request] - 5735 Form ID: 5735","type":"marketo","state":"live","formId":"5735"},"157038660":{"name":"Demo Request - CCass - Dialpad Support - 6685 Form ID: 6685","type":"marketo","state":"live","formId":"6685"},"157038661":{"name":"Contact Us - CCaaS - Dialpad Support - 6687 Form ID: 6687","type":"marketo","state":"live","formId":"6687"},"157038662":{"name":"Japan Sales Form - Contact Us : 3696 Form ID: 3696","type":"marketo","state":"live","formId":"3696"},"157038663":{"name":"01. Universal Form [Webinar] - 4601 Form ID: 4601","type":"marketo","state":"live","formId":"4601"},"157038664":{"name":"02. Universal Form [Field Event] - 4957 Form ID: 4957","type":"marketo","state":"live","formId":"4957"},"157038665":{"name":"03. Universal Form [On-Demand Webinar] - 5283 Form ID: 5283","type":"marketo","state":"live","formId":"5283"},"157038666":{"name":"05. Universal Form [eBook / Guide / Whitepaper] - 5462 Form ID: 5462","type":"marketo","state":"live","formId":"5462"},"157038667":{"name":"Generic Demo Center [6052] Form ID: 6052","type":"marketo","state":"live","formId":"6052"},"157038668":{"name":"Demo Center - T Mobile [6522] Form ID: 6522","type":"marketo","state":"live","formId":"6522"},"157038669":{"name":"06. Universal Form [Report] - 5723 Form ID: 5723","type":"marketo","state":"live","formId":"5723"},"157038670":{"name":"04. Universal Form [Checklist / Datasheet] - 5724 Form ID: 5724","type":"marketo","state":"live","formId":"5724"},"157039193":{"name":"On-page engagement (Blog test Page)","type":"click","pageIds":["137711198"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157039206":{"name":"On-page engagement (/business-communications - EN Page)","type":"click","pageIds":["137711201"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157039207":{"name":"On-page engagement (/ai-sales EN Page)","type":"click","pageIds":["137711203"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157039208":{"name":"On-page engagement (/ai-contact-center EN Page)","type":"click","pageIds":["137711204"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157039370":{"name":"On-page engagement (/dialpad-ai EN Page)","type":"click","pageIds":["137711232"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157039569":{"name":"Web form submission (Marketo)","type":"custom","apiName":"marketo_form_submission"},"157039715":{"name":"Qualified Meeting Booked","type":"custom","apiName":"qualified_meeting_booked"},"157039716":{"name":"Qualified Email Caputred","type":"custom","apiName":"qualified_email_captured"},"157039796":{"name":"On-page engagement (Test page)","type":"click","pageIds":["137711297"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157039819":{"name":"On-page engagement (HP - dialpadbeta.com Page)","type":"click","pageIds":["137711308"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157046071":{"name":"On-page engagement (Signup test page (dialpadbeta))","type":"click","pageIds":["137711364"],"selector":"a:not([href^=\\"(http:|https:)?//\\"],[href^=\\"#\\"])"},"157046411":{"name":"web-form-submission-marketo","type":"custom","apiName":"web-form-submission-marketo"},"157047230":{"name":"Universal Form - Content - Analyst Reports - 5723","type":"custom","apiName":"5723"},"157047231":{"name":"Generic Demo Center - Content - Demo video - 6052","type":"custom","apiName":"6052"},"157047232":{"name":"AiVoice Mobile App Traffic Form - Contact Us - Growth - 7749","type":"custom","apiName":"7749"},"157047233":{"name":"CPC - Demo Request - General (CTA: Request a Demo) - Handraiser - Demo request - 7007","type":"custom","apiName":"7007"},"157047234":{"name":"Universal Form [Checklist / Datasheet] - Content - Whitepaper eBook - 5724","type":"custom","apiName":"5724"},"157047235":{"name":"Email Subscription Form - Content - Email subscription - 3676","type":"custom","apiName":"3676"},"157047236":{"name":"Universal Form [eBook / Guide / Whitepaper] - Content - Whitepaper eBook - 5462","type":"custom","apiName":"5462"},"157047256":{"name":"Universal Form [Field Event] - Events - In person event - 4957","type":"custom","apiName":"4957"},"157047257":{"name":"Universal Form [Webinar] - Events - Webinar - 4601","type":"custom","apiName":"4601"},"157047258":{"name":"Report: TEI Study CB Form - Content - Analyst Reports - 3077","type":"custom","apiName":"3077"},"157047259":{"name":"CPC - Request a demo CTA - Ai CC - Handraiser - Demo request - 6696","type":"custom","apiName":"6696"},"157047260":{"name":"Universal Form [On-Demand Webinar] - Events - Webinar - 5283","type":"custom","apiName":"5283"},"157047261":{"name":"Global Contact Us - Handraiser - Contact us - 3252","type":"custom","apiName":"3252"},"157047262":{"name":"Global Contact Us with Multi Product - Handraiser - Contact us - 7802","type":"custom","apiName":"7802"},"157047263":{"name":"Contact us Ucaas - Handraiser - Contact us - 6691","type":"custom","apiName":"6691"},"157047264":{"name":"UCaaS Demo - Start my tour - Handraiser - Product Tour - 6048","type":"custom","apiName":"6048"},"157047265":{"name":"CPC - UCaas - Request a Demo CTA - Handraiser - Demo request - 6710","type":"custom","apiName":"6710"},"157047266":{"name":"CPC - Ai CC - Get a Quote CTA - Handraiser - Contact us - 6698","type":"custom","apiName":"6698"},"157047267":{"name":"Japan Sales Form - Contact Us - Handraiser - Contact us - 3696","type":"custom","apiName":"3696"},"157047268":{"name":"Contact us Ai Sales - Handraiser - Contact us - 6690","type":"custom","apiName":"6690"},"157047269":{"name":"Contact us Ai CC - Handraiser - Contact us - 6687","type":"custom","apiName":"6687"},"157047270":{"name":"CPC - UCaas - Get a Quote CTA - Handraiser - Contact us - 6709","type":"custom","apiName":"6709"},"157047271":{"name":"Universal Form [On-Demand Webinar] for Wistia - Events - Webinar - 8461 ","type":"custom","apiName":"8461"},"157047272":{"name":"Partner Signup: Form - Become a Partner - Sub Agent - Partner - Partner registration - 3821","type":"custom","apiName":"3821"},"157047273":{"name":"CCaaS Demo - Start my tour - Handraiser - Product Tour - 6050","type":"custom","apiName":"6050"},"157047274":{"name":"Customer Referral Program - Handraiser - Contact us - 8306","type":"custom","apiName":"8306"},"157047275":{"name":"Surfboard Form - Handraiser - Contact us - 8303","type":"custom","apiName":"8303"},"157047276":{"name":"CPC - Generic - Get Started CTA - Handraiser - Contact us - 6712","type":"custom","apiName":"6712"},"157047277":{"name":"Partner Signup: Partner Allbound Request - Partner - Partner registration - 4138","type":"custom","apiName":"4138"},"157047278":{"name":"Contact Us (Support) - Handraiser - Contact us - 2233","type":"custom","apiName":"2233"},"157047279":{"name":"Ent/MM/SMB - UCaaS - Gift Card - Demo - Handraiser - Demo request - 8059","type":"custom","apiName":"8059"},"157047280":{"name":"eBook - AI + Compliance Guide - Content - Whitepaper eBook - 4448","type":"custom","apiName":"4448"},"157047281":{"name":"CPC - Request a demo CTA - Ai Sales - Handraiser - Demo request - 6703","type":"custom","apiName":"6703"},"157047282":{"name":"eBook: The Contact Center Playbook - Content - Whitepaper eBook - 4503","type":"custom","apiName":"4503"},"157047283":{"name":"Clearbit Contact Us (Sell) - Handraiser - Contact us - 2241","type":"custom","apiName":"2241"},"157047284":{"name":"eBook: On-Premises Vs Cloud Contact Centers: A Complete Guide - Content - Whitepaper eBook - 4863","type":"custom","apiName":"4863"},"157047285":{"name":"Demo Request UCaas - Handraiser - Demo request - 6693","type":"custom","apiName":"6693"},"157047286":{"name":"eBook: The CIO Playbook Form - Content - Whitepaper eBook - 4813","type":"custom","apiName":"4813"},"157047289":{"name":"eBook: Switching to Cloud-based Communications for Healthcare - Content - Whitepaper eBook - 4476","type":"custom","apiName":"4476"},"157047290":{"name":"eBook: Hybrid Work Playbook Form - Content - Whitepaper eBook - 4414","type":"custom","apiName":"4414"},"157047291":{"name":"eBook: Contact Center RFP Form - Content - Whitepaper eBook - 4811","type":"custom","apiName":"4811"},"157047292":{"name":"Dialpad Talk Pricing - Handraiser - Demo request - 2244","type":"custom","apiName":"2244"},"157047293":{"name":"Healthcare eBook - Content - Whitepaper eBook - 4252","type":"custom","apiName":"4252"},"157047294":{"name":"CPC - UCaas - Talk to Sales CTA - Handraiser - Contact us - 6707","type":"custom","apiName":"6707"},"157047295":{"name":"GoogleNext Form - In person event (Partner) - 7125","type":"custom","apiName":"7125"},"157047296":{"name":"Ent/MM/SMB - Ai CC Evergreen - Gift Card - Demo - Handraiser - Demo request - 8179","type":"custom","apiName":"8179"},"157047297":{"name":"Demo Request Ai CC - Handraiser - Demo request - 6685","type":"custom","apiName":"6685"},"157047298":{"name":"Ent/MM/SMB - Ai UCaaS Evergreen - Gift Card - Demo - Handraiser - Demo request - 8048","type":"custom","apiName":"8048"},"157047299":{"name":"Ent/MM - UCaaS EMEA - Gift Card - Demo - Handraiser - Demo request - 8058","type":"custom","apiName":"8058"},"157047300":{"name":"Demo Request Ai Sales - Handraiser - Demo request - 6689","type":"custom","apiName":"6689"},"157047301":{"name":"Universal Form [Demo Request] - Handraiser - Demo request - 5735","type":"custom","apiName":"5735"},"157047302":{"name":"Demo Center - T Mobile [6522] - Handraiser - Demo request - 6522","type":"custom","apiName":"6522"},"157047303":{"name":"Dialpad Champion Request Form - Handraiser - Contact us - 3495","type":"custom","apiName":"3495"},"157047304":{"name":"eBook - State of Video Conferencing Report - Content - Whitepaper eBook - 4678","type":"custom","apiName":"4678"},"157047305":{"name":"Ent/MM/SMB - Ai Sales Evergreen - Gift Card - Demo - Handraiser - Demo request - 8180","type":"custom","apiName":"8180"},"157047306":{"name":"eBook - Optimizing a Contact Center for Transportation - Content - Whitepaper eBook - 4501","type":"custom","apiName":"4501"},"157047307":{"name":"CPC - Ai CC - Talk to Sales CTA - Handraiser - Contact us - 6695","type":"custom","apiName":"6695"},"157047308":{"name":"eBook: Switching from One Cloud Contact Center to Another: A Guide - Content - Whitepaper eBook - 4864","type":"custom","apiName":"4864"},"157047309":{"name":"eBook_AI in Customer Service Report - Content - Whitepaper eBook - 5451","type":"custom","apiName":"5451"},"157047310":{"name":"SMB - UCaaS Reactivation - Demo - Handraiser - Demo request - 7972","type":"custom","apiName":"7972"},"157047311":{"name":"Whiskey Tasting - Channel Marketing - 6647 - In person event (Partner) - 6647","type":"custom","apiName":"6647"},"157047312":{"name":"EAP - Ai Playbooks Waitlist - Growth - 7220","type":"custom","apiName":"7220"},"157047313":{"name":"MM - Ai UCaaS Mid Quarter - Gift Card - Demo - Handraiser - Demo request - 8158","type":"custom","apiName":"8158"},"157047315":{"name":"eBook - Staffing a Contact Center for Real Estate - Content - Whitepaper eBook - 4500","type":"custom","apiName":"4500"},"157047316":{"name":"eBook: How can AI help you tackle your contact center’s challenges? - Content - Whitepaper eBook - 5222","type":"custom","apiName":"5222"},"157047317":{"name":"CPC - Ai Sales - Get a Quote CTA - Handraiser - Contact us - 6704","type":"custom","apiName":"6704"},"157047318":{"name":"Ent/MM/SMB - Ai CC Reactivation - Gift Card - Demo - Handraiser - Demo request - 7997","type":"custom","apiName":"7997"},"157047319":{"name":"MM/SMB - ANZ - Demo - Paid Social - Ai Sales Gift for Meeting - Handraiser - Demo request - 8289","type":"custom","apiName":"8289"},"157047320":{"name":"Ent/MM/SMB - ANZ - Demo - Dialpad Support Evergreen - Gift Card - [ID - 8216] - Handraiser - Demo request - 8216","type":"custom","apiName":"8216"},"157047321":{"name":"Report: G2 VoIP Momentum Form - Content - Analyst Reports - 3647","type":"custom","apiName":"3647"},"157047322":{"name":"Report: ENT IDC MarketScape - Content - Analyst Reports - 3991","type":"custom","apiName":"3991"},"157047323":{"name":"Whitepaper: Frost & Sullivan form - Content - Whitepaper eBook - 4451","type":"custom","apiName":"4451"},"157047324":{"name":"Report - Frost Sullivan Buyers Guide NA 21 Form - Content - Analyst Reports - 4664","type":"custom","apiName":"4664"},"157047325":{"name":"eBook_CC for Colleges RFP Guide - Content - Whitepaper eBook - 4771","type":"custom","apiName":"4771"},"157047326":{"name":"Gift for a Meeting Evergreen - Handraiser - Demo request - 7639","type":"custom","apiName":"7639"},"157047327":{"name":"TMobile - Nike by You - In person event (Partner) - 7979","type":"custom","apiName":"7979"},"157047328":{"name":"Growth Team - Survey Form 2024 - Content - Survey - 8017","type":"custom","apiName":"8017"},"157047329":{"name":"Ent/MM/SMB - Demo - Genesys Takeout - Gift Card - Handraiser - Contact us - 8084","type":"custom","apiName":"8084"},"157047330":{"name":"Ent/MM/SMB - EMEA Demo - Ai Sales - Handraiser - Demo request - 8095","type":"custom","apiName":"8095"},"157047331":{"name":"Partner Form - Dietary Restrictions - In person event (Partner) - 8124","type":"custom","apiName":"8124"},"157047332":{"name":"SMB - Coworking Space - UCaaS - Handraiser - Contact us - 8204","type":"custom","apiName":"8204"},"157047333":{"name":"SMB - Coworking Space - CCaaS - Handraiser - Contact us - 8205","type":"custom","apiName":"8205"},"157047334":{"name":"UCaaS Weekly Demo Form [Webinar] - 8208 - Events - Webinar - 8208","type":"custom","apiName":"8208"},"157047335":{"name":"SMB - Coworking Space - Master Promo Form - Handraiser - Contact us - 8218","type":"custom","apiName":"8218"},"157047336":{"name":"CCaaS Weekly Demo Form [Webinar] - 8223 - Events - Webinar - 8223","type":"custom","apiName":"8223"},"157047337":{"name":"Virtual Cooking Class Form - Events - Virtual event - 8279","type":"custom","apiName":"8279"},"157047338":{"name":"04b. Universal Form [Checklist / Datasheet] - VARIATION (ID: 8301) - Content - Whitepaper eBook - 8301","type":"custom","apiName":"8301"},"157047339":{"name":"CPC - Demo Request - General (CTA: Speak to Sales) - Handraiser - Demo request - 8071","type":"custom","apiName":"8071"},"157047340":{"name":"Ent/MM/SMB - Demo - RingCentral Takeout - Gift Card - Handraiser - Demo request - 8085","type":"custom","apiName":"8085"},"157047341":{"name":"Ent/MM/SMB - EMEA - Demo - RingCentral Takeout - Gift Card - Handraiser - Demo request - 8086","type":"custom","apiName":"8086"},"157047342":{"name":"Ent/MM/SMB - EMEA Demo - UCaaS - Handraiser - Demo request - 8097","type":"custom","apiName":"8097"},"157047343":{"name":"Ent/MM/SMB - AMER - Demo - Paid - RingCentral Takeout - Gift Card - Handraiser - Demo request - 8199","type":"custom","apiName":"8199"},"157047344":{"name":"Ent - Demo - CIO Direct Mail - Gift Card - Handraiser - Demo request - 8227","type":"custom","apiName":"8227"}},"pages":{"137709557":{"name":"Homepage EN","type":"matching","urls":[{"match":"regex","value":"^https://www\\\\.dialpad\\\\.com(?:/(?:uk|ca|au)?)?/?$"}]},"137709821":{"name":"All Pages","type":"matching","urls":[{"match":"regex","value":"^(https?:\\\\/\\\\/)?(www\\\\.)?dialpad\\\\.com(\\\\/(?!(uk|fr|au|jp)($|\\\\/)).*)?$"}]},"137709823":{"name":"/contact-sales EN","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/contact-sales/"},{"match":"simple","value":"https://www.dialpad.com/uk/contact-sales/"},{"match":"simple","value":"https://www.dialpad.com/au/contact-sales/"},{"match":"simple","value":"https://www.dialpad.com/ca/contact-sales/"}]},"137710939":{"name":"All Pages EN","type":"matching","urls":[{"match":"regex","value":"^https://www\\\\.dialpad\\\\.com(/((?!jp/|fr/).*)?)?$"}]},"137711030":{"name":"/pricing EN","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/pricing/"},{"match":"simple","value":"https://www.dialpad.com/ca/pricing/"},{"match":"simple","value":"https://www.dialpad.com/uk/pricing/"},{"match":"simple","value":"https://www.dialpad.com/au/pricing/"}]},"137711034":{"name":"/ai-voice-contact-us/ EN","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/ai-voice-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/uk/ai-voice-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/ca/ai-voice-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/au/ai-voice-contact-us/"}]},"137711035":{"name":"/ai-contact-center-contact-us/ EN","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/ai-contact-center-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/au/ai-contact-center-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/ca/ai-contact-center-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/uk/ai-contact-center-contact-us/"}]},"137711037":{"name":"/ai-sales-center-contact-us/ EN","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/ai-sales-center-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/au/ai-sales-center-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/ca/ai-sales-center-contact-us/"},{"match":"simple","value":"https://www.dialpad.com/uk/ai-sales-center-contact-us/"}]},"137711190":{"name":"All Pages EN - excl. HPs","type":"matching","urls":[{"match":"regex","value":"^https://www\\\\.dialpad\\\\.com/(?!fr/|jp/)(?!(?:ca/|uk/|au/)?$).*$"}]},"137711198":{"name":"Blog test","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/blog/ai-customer-experience/"}]},"137711201":{"name":"/business-communications - EN","type":"matching","urls":[{"match":"regex","value":"^https://www\\\\.dialpad\\\\.com/(?:(?:ca|uk|au)/)?(business-communications)/?$"}]},"137711203":{"name":"/ai-sales EN","type":"matching","urls":[{"match":"regex","value":"^https://www\\\\.dialpad\\\\.com/(?:(?:ca|uk|au)/)?(ai-sales)/?$"}]},"137711204":{"name":"/ai-contact-center EN","type":"matching","urls":[{"match":"regex","value":"^https://www\\\\.dialpad\\\\.com/(?:(?:ca|uk|au)/)?(ai-contact-center)/?$"}]},"137711232":{"name":"/dialpad-ai EN","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/dialpad-ai/"},{"match":"simple","value":"https://www.dialpad.com/ca/dialpad-ai/"},{"match":"simple","value":"https://www.dialpad.com/uk/dialpad-ai/"},{"match":"simple","value":"https://www.dialpad.com/au/dialpad-ai/"}]},"137711297":{"name":"Contentful test page","type":"matching","urls":[{"match":"simple","value":"https://www.dialpad.com/ceds-test-page/"}]},"137711308":{"name":"HP - dialpadbeta.com","type":"matching","urls":[{"match":"simple","value":"https://www.dialpadbeta.com/"}]},"137711364":{"name":"Signup test page (dialpadbeta)","type":"matching","urls":[{"match":"simple","value":"https://dialpadbeta.com/signup"}]}},"audiences":{"187601251":{"name":"Signup Started","condition":{"type":"comparison-string","operator":"eq","value":"true","attrDefId":{"name":"signup_started","namespace":"custom"}}},"187601262":{"name":"Signup Completed","condition":{"type":"comparison-string","operator":"eq","value":"true","attrDefId":{"name":"signup_completed","namespace":"custom"}}},"187601312":{"name":"Nav CTA - Contact sales","condition":{"type":"logic-or","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230769"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109939"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230769"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109940"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109947"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109950"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}]}},"187601313":{"name":"Nav CTA - Request quote","condition":{"type":"logic-or","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230769"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109941"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109954"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}]}},"187601314":{"name":"Nav CTA - No change (demo)","condition":{"type":"logic-or","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230769"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109938"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109946"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}]}},"187601373":{"name":"lc-qa=1","condition":{"type":"code","code":"window.location.href.indexOf(\\"lc-qa=1\\")>-1||document.cookie.indexOf(\\"lc-qa=1\\")>-1;"}},"187601374":{"name":"Desktop only","condition":{"type":"comparison-enum","operator":"eq","value":"D","attrDefId":{"name":"device_type","namespace":"standard"}}},"187601375":{"name":"POC-contact-sales","condition":{"type":"logic-or","conditions":[{"type":"logic-and","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417232461"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"comparison-enum","operator":"eq","value":"D","attrDefId":{"name":"device_type","namespace":"standard"}}]},{"type":"logic-and","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P30D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417232462"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"comparison-enum","operator":"eq","value":"D","attrDefId":{"name":"device_type","namespace":"standard"}}]}]}},"187601635":{"name":"lc-qa=2","condition":{"type":"code","code":"window.location.href.indexOf(\\"lc-qa=2\\")>-1||document.cookie.indexOf(\\"lc-qa=2\\")>-1;"}},"187601744":{"name":"Not US","condition":{"type":"comparison-enum","operator":"ne","value":"US","attrDefId":{"name":"country","namespace":"standard"}}},"187601745":{"name":"US only","condition":{"type":"comparison-enum","operator":"eq","value":"US","attrDefId":{"name":"country","namespace":"standard"}}},"187602252":{"name":"CTA - Contact sales","condition":{"type":"logic-or","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109950"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417235364"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617120349"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236110"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121949"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236111"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121953"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236107"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121946"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}]}},"187602279":{"name":"CTA - Request demo","condition":{"type":"logic-or","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109946"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417235364"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617120346"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236110"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121951"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236111"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121955"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236107"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121947"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}]}},"187602280":{"name":"CTA - Request quote","condition":{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417235375"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617120380"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}},"187602326":{"name":"UTM gbi_eastern_conference","condition":{"type":"comparison-string","operator":"eq","value":"gbi_eastern_conference","attrDefId":{"name":"utm_campaign","namespace":"standard"}}},"187602414":{"name":"CTA - Talk to sales","condition":{"type":"logic-or","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417235364"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617120347"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109947"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236110"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121948"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236111"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121952"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236107"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121939"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}]}},"187602415":{"name":"CTA - Book a demo","condition":{"type":"logic-or","conditions":[{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417235364"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617120348"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417230771"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617109951"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236110"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121950"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}},{"type":"activity","filter":{"type":"logic-and","filters":[{"type":"comparison-unix-time","fieldName":"time","operation":{"operator":"withinPastDuration","argument":"P3D"}},{"type":"comparison-enum","fieldName":"eventType","operation":{"operator":"eq","argument":"v"}},{"type":"comparison-enum","fieldName":"experienceId","operation":{"operator":"eq","argument":"417236111"}},{"type":"comparison-enum","fieldName":"variationId","operation":{"operator":"eq","argument":"617121954"}}]},"aggregation":{"type":"count"},"operation":{"operator":">=","argument":1}}]}},"187602421":{"name":"UTM gds_cio_summit","condition":{"type":"comparison-string","operator":"eq","value":"gds_cio_summit","attrDefId":{"name":"utm_campaign","namespace":"standard"}}},"187602422":{"name":"UTM gartner_cio_community_nyc","condition":{"type":"logic-or","conditions":[{"type":"comparison-string","operator":"eq","value":"garnter_cio_community_nyc","attrDefId":{"name":"utm_campaign","namespace":"standard"}},{"type":"comparison-string","operator":"eq","value":"gartner_cio_community_nyc","attrDefId":{"name":"utm_campaign","namespace":"standard"}}]}},"187602427":{"name":"Enterprise (EE ≥ 1k)","condition":{"type":"comparison-number","operator":">=","value":1000,"attrDefId":{"name":"employeeCount","namespace":"6sense"}}},"187602428":{"name":"SMB (EE ≤ 250)","condition":{"type":"comparison-number","operator":"<=","value":250,"attrDefId":{"name":"employeeCount","namespace":"6sense"}}},"187602480":{"name":"MM (EE 251-999)","condition":{"type":"logic-and","conditions":[{"type":"comparison-number","operator":">","value":250,"attrDefId":{"name":"employeeCount","namespace":"6sense"}},{"type":"comparison-number","operator":"<","value":1000,"attrDefId":{"name":"employeeCount","namespace":"6sense"}}]}},"187602484":{"name":"UTM content = dptest_internal","condition":{"type":"comparison-string","operator":"eq","value":"dptest_internal","attrDefId":{"name":"utm_content","namespace":"standard"}}},"187602534":{"name":"UK only","condition":{"type":"comparison-enum","operator":"eq","value":"GB","attrDefId":{"name":"country","namespace":"standard"}}},"187602535":{"name":"CA only","condition":{"type":"comparison-enum","operator":"eq","value":"CA","attrDefId":{"name":"country","namespace":"standard"}}},"187602536":{"name":"JA only","condition":{"type":"comparison-enum","operator":"eq","value":"JP","attrDefId":{"name":"country","namespace":"standard"}}},"187602537":{"name":"ANZ only","condition":{"type":"logic-or","conditions":[{"type":"comparison-enum","operator":"eq","value":"AU","attrDefId":{"name":"country","namespace":"standard"}},{"type":"comparison-enum","operator":"eq","value":"NZ","attrDefId":{"name":"country","namespace":"standard"}}]}}},"model":{},"id":"117544822","name":"Dialpad","encrypt":false,"iint":{"marketo":{"enabled":true,"fieldsToIngest":[],"listsToIngest":[]},"sixsense":{"enabled":true,"oemEnabled":true},"googleAds":{"enabled":true},"firmographic":{"enabled":true}},"code":"(()=>{var yc=Object.create;var Ln=Object.defineProperty;var gc=Object.getOwnPropertyDescriptor;var hc=Object.getOwnPropertyNames;var Sc=Object.getPrototypeOf,Ec=Object.prototype.hasOwnProperty;var a=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports);var Oc=(r,e,t,n)=>{if(e&&typeof e==\\"object\\"||typeof e==\\"function\\")for(let i of hc(e))!Ec.call(r,i)&&i!==t&&Ln(r,i,{get:()=>e[i],enumerable:!(n=gc(e,i))||n.enumerable});return r};var bc=(r,e,t)=>(t=r!=null?yc(Sc(r)):{},Oc(e||!r||!r.__esModule?Ln(t,\\"default\\",{value:r,enumerable:!0}):t,r));var y=a((jS,Un)=>{var Br=function(r){return r&&r.Math==Math&&r};Un.exports=Br(typeof globalThis==\\"object\\"&&globalThis)||Br(typeof window==\\"object\\"&&window)||Br(typeof self==\\"object\\"&&self)||Br(typeof global==\\"object\\"&&global)||function(){return this}()||Function(\\"return this\\")()});var O=a((DS,Mn)=>{Mn.exports=function(r){try{return!!r()}catch{return!0}}});var R=a((LS,Fn)=>{var Tc=O();Fn.exports=!Tc(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var yr=a((US,Bn)=>{var Ic=O();Bn.exports=!Ic(function(){var r=function(){}.bind();return typeof r!=\\"function\\"||r.hasOwnProperty(\\"prototype\\")})});var m=a((MS,$n)=>{var mc=yr(),$r=Function.prototype.call;$n.exports=mc?$r.bind($r):function(){return $r.apply($r,arguments)}});var Yn=a(Kn=>{\\"use strict\\";var Gn={}.propertyIsEnumerable,kn=Object.getOwnPropertyDescriptor,Pc=kn&&!Gn.call({1:2},1);Kn.f=Pc?function(e){var t=kn(this,e);return!!t&&t.enumerable}:Gn});var gr=a((BS,Wn)=>{Wn.exports=function(r,e){return{enumerable:!(r&1),configurable:!(r&2),writable:!(r&4),value:e}}});var E=a(($S,zn)=>{var Vn=yr(),Hn=Function.prototype,xc=Hn.bind,Ae=Hn.call,Rc=Vn&&xc.bind(Ae,Ae);zn.exports=Vn?function(r){return r&&Rc(r)}:function(r){return r&&function(){return Ae.apply(r,arguments)}}});var Q=a((GS,Xn)=>{var Jn=E(),Cc=Jn({}.toString),_c=Jn(\\"\\".slice);Xn.exports=function(r){return _c(Cc(r),8,-1)}});var Zn=a((kS,Qn)=>{var wc=E(),Nc=O(),Ac=Q(),je=Object,jc=wc(\\"\\".split);Qn.exports=Nc(function(){return!je(\\"z\\").propertyIsEnumerable(0)})?function(r){return Ac(r)==\\"String\\"?jc(r,\\"\\"):je(r)}:je});var Z=a((KS,ra)=>{ra.exports=function(r){return r==null}});var hr=a((YS,ea)=>{var Dc=Z(),Lc=TypeError;ea.exports=function(r){if(Dc(r))throw Lc(\\"Can\'t call method on \\"+r);return r}});var rr=a((WS,ta)=>{var Uc=Zn(),Mc=hr();ta.exports=function(r){return Uc(Mc(r))}});var g=a((VS,na)=>{na.exports=function(r){return typeof r==\\"function\\"}});var N=a((HS,ia)=>{var aa=g(),De=typeof document==\\"object\\"&&document.all,Fc=typeof De>\\"u\\"&&De!==void 0;ia.exports=Fc?function(r){return typeof r==\\"object\\"?r!==null:aa(r)||r===De}:function(r){return typeof r==\\"object\\"?r!==null:aa(r)}});var j=a((zS,oa)=>{var Le=y(),Bc=g(),$c=function(r){return Bc(r)?r:void 0};oa.exports=function(r,e){return arguments.length<2?$c(Le[r]):Le[r]&&Le[r][e]}});var er=a((JS,ua)=>{var Gc=E();ua.exports=Gc({}.isPrototypeOf)});var Sr=a((XS,va)=>{var kc=j();va.exports=kc(\\"navigator\\",\\"userAgent\\")||\\"\\"});var Me=a((QS,qa)=>{var pa=y(),Ue=Sr(),sa=pa.process,ca=pa.Deno,la=sa&&sa.versions||ca&&ca.version,fa=la&&la.v8,A,Gr;fa&&(A=fa.split(\\".\\"),Gr=A[0]>0&&A[0]<4?1:+(A[0]+A[1]));!Gr&&Ue&&(A=Ue.match(/Edge\\\\/(\\\\d+)/),(!A||A[1]>=74)&&(A=Ue.match(/Chrome\\\\/(\\\\d+)/),A&&(Gr=+A[1])));qa.exports=Gr});var Fe=a((ZS,ya)=>{var da=Me(),Kc=O();ya.exports=!!Object.getOwnPropertySymbols&&!Kc(function(){var r=Symbol();return!String(r)||!(Object(r)instanceof Symbol)||!Symbol.sham&&da&&da<41})});var Be=a((rE,ga)=>{var Yc=Fe();ga.exports=Yc&&!Symbol.sham&&typeof Symbol.iterator==\\"symbol\\"});var $e=a((eE,ha)=>{var Wc=j(),Vc=g(),Hc=er(),zc=Be(),Jc=Object;ha.exports=zc?function(r){return typeof r==\\"symbol\\"}:function(r){var e=Wc(\\"Symbol\\");return Vc(e)&&Hc(e.prototype,Jc(r))}});var Er=a((tE,Sa)=>{var Xc=String;Sa.exports=function(r){try{return Xc(r)}catch{return\\"Object\\"}}});var F=a((nE,Ea)=>{var Qc=g(),Zc=Er(),rl=TypeError;Ea.exports=function(r){if(Qc(r))return r;throw rl(Zc(r)+\\" is not a function\\")}});var Or=a((aE,Oa)=>{var el=F(),tl=Z();Oa.exports=function(r,e){var t=r[e];return tl(t)?void 0:el(t)}});var Ta=a((iE,ba)=>{var Ge=m(),ke=g(),Ke=N(),nl=TypeError;ba.exports=function(r,e){var t,n;if(e===\\"string\\"&&ke(t=r.toString)&&!Ke(n=Ge(t,r))||ke(t=r.valueOf)&&!Ke(n=Ge(t,r))||e!==\\"string\\"&&ke(t=r.toString)&&!Ke(n=Ge(t,r)))return n;throw nl(\\"Can\'t convert object to primitive value\\")}});var D=a((oE,Ia)=>{Ia.exports=!1});var kr=a((uE,Pa)=>{var ma=y(),al=Object.defineProperty;Pa.exports=function(r,e){try{al(ma,r,{value:e,configurable:!0,writable:!0})}catch{ma[r]=e}return e}});var Kr=a((vE,Ra)=>{var il=y(),ol=kr(),xa=\\"__core-js_shared__\\",ul=il[xa]||ol(xa,{});Ra.exports=ul});var Yr=a((sE,_a)=>{var vl=D(),Ca=Kr();(_a.exports=function(r,e){return Ca[r]||(Ca[r]=e!==void 0?e:{})})(\\"versions\\",[]).push({version:\\"3.25.0\\",mode:vl?\\"pure\\":\\"global\\",copyright:\\"\\\\xA9 2014-2022 Denis Pushkarev (zloirock.ru)\\",license:\\"https://github.com/zloirock/core-js/blob/v3.25.0/LICENSE\\",source:\\"https://github.com/zloirock/core-js\\"})});var Wr=a((cE,wa)=>{var sl=hr(),cl=Object;wa.exports=function(r){return cl(sl(r))}});var P=a((lE,Na)=>{var ll=E(),fl=Wr(),pl=ll({}.hasOwnProperty);Na.exports=Object.hasOwn||function(e,t){return pl(fl(e),t)}});var Ye=a((fE,Aa)=>{var ql=E(),dl=0,yl=Math.random(),gl=ql(1 .toString);Aa.exports=function(r){return\\"Symbol(\\"+(r===void 0?\\"\\":r)+\\")_\\"+gl(++dl+yl,36)}});var T=a((pE,Ma)=>{var hl=y(),Sl=Yr(),ja=P(),El=Ye(),Da=Fe(),Ua=Be(),tr=Sl(\\"wks\\"),k=hl.Symbol,La=k&&k.for,Ol=Ua?k:k&&k.withoutSetter||El;Ma.exports=function(r){if(!ja(tr,r)||!(Da||typeof tr[r]==\\"string\\")){var e=\\"Symbol.\\"+r;Da&&ja(k,r)?tr[r]=k[r]:Ua&&La?tr[r]=La(e):tr[r]=Ol(e)}return tr[r]}});var Ga=a((qE,$a)=>{var bl=m(),Fa=N(),Ba=$e(),Tl=Or(),Il=Ta(),ml=T(),Pl=TypeError,xl=ml(\\"toPrimitive\\");$a.exports=function(r,e){if(!Fa(r)||Ba(r))return r;var t=Tl(r,xl),n;if(t){if(e===void 0&&(e=\\"default\\"),n=bl(t,r,e),!Fa(n)||Ba(n))return n;throw Pl(\\"Can\'t convert object to primitive value\\")}return e===void 0&&(e=\\"number\\"),Il(r,e)}});var We=a((dE,ka)=>{var Rl=Ga(),Cl=$e();ka.exports=function(r){var e=Rl(r,\\"string\\");return Cl(e)?e:e+\\"\\"}});var br=a((yE,Ya)=>{var _l=y(),Ka=N(),Ve=_l.document,wl=Ka(Ve)&&Ka(Ve.createElement);Ya.exports=function(r){return wl?Ve.createElement(r):{}}});var He=a((gE,Wa)=>{var Nl=R(),Al=O(),jl=br();Wa.exports=!Nl&&!Al(function(){return Object.defineProperty(jl(\\"div\\"),\\"a\\",{get:function(){return 7}}).a!=7})});var Vr=a(Ha=>{var Dl=R(),Ll=m(),Ul=Yn(),Ml=gr(),Fl=rr(),Bl=We(),$l=P(),Gl=He(),Va=Object.getOwnPropertyDescriptor;Ha.f=Dl?Va:function(e,t){if(e=Fl(e),t=Bl(t),Gl)try{return Va(e,t)}catch{}if($l(e,t))return Ml(!Ll(Ul.f,e,t),e[t])}});var ze=a((SE,za)=>{var kl=R(),Kl=O();za.exports=kl&&Kl(function(){return Object.defineProperty(function(){},\\"prototype\\",{value:42,writable:!1}).prototype!=42})});var x=a((EE,Ja)=>{var Yl=N(),Wl=String,Vl=TypeError;Ja.exports=function(r){if(Yl(r))return r;throw Vl(Wl(r)+\\" is not an object\\")}});var w=a(Qa=>{var Hl=R(),zl=He(),Jl=ze(),Hr=x(),Xa=We(),Xl=TypeError,Je=Object.defineProperty,Ql=Object.getOwnPropertyDescriptor,Xe=\\"enumerable\\",Qe=\\"configurable\\",Ze=\\"writable\\";Qa.f=Hl?Jl?function(e,t,n){if(Hr(e),t=Xa(t),Hr(n),typeof e==\\"function\\"&&t===\\"prototype\\"&&\\"value\\"in n&&Ze in n&&!n[Ze]){var i=Ql(e,t);i&&i[Ze]&&(e[t]=n.value,n={configurable:Qe in n?n[Qe]:i[Qe],enumerable:Xe in n?n[Xe]:i[Xe],writable:!1})}return Je(e,t,n)}:Je:function(e,t,n){if(Hr(e),t=Xa(t),Hr(n),zl)try{return Je(e,t,n)}catch{}if(\\"get\\"in n||\\"set\\"in n)throw Xl(\\"Accessors not supported\\");return\\"value\\"in n&&(e[t]=n.value),e}});var B=a((bE,Za)=>{var Zl=R(),rf=w(),ef=gr();Za.exports=Zl?function(r,e,t){return rf.f(r,e,ef(1,t))}:function(r,e,t){return r[e]=t,r}});var tt=a((TE,ei)=>{var rt=R(),tf=P(),ri=Function.prototype,nf=rt&&Object.getOwnPropertyDescriptor,et=tf(ri,\\"name\\"),af=et&&function(){}.name===\\"something\\",of=et&&(!rt||rt&&nf(ri,\\"name\\").configurable);ei.exports={EXISTS:et,PROPER:af,CONFIGURABLE:of}});var zr=a((IE,ti)=>{var uf=E(),vf=g(),nt=Kr(),sf=uf(Function.toString);vf(nt.inspectSource)||(nt.inspectSource=function(r){return sf(r)});ti.exports=nt.inspectSource});var ii=a((mE,ai)=>{var cf=y(),lf=g(),ni=cf.WeakMap;ai.exports=lf(ni)&&/native code/.test(String(ni))});var Jr=a((PE,ui)=>{var ff=Yr(),pf=Ye(),oi=ff(\\"keys\\");ui.exports=function(r){return oi[r]||(oi[r]=pf(r))}});var Xr=a((xE,vi)=>{vi.exports={}});var Ir=a((RE,pi)=>{var qf=ii(),fi=y(),at=E(),df=N(),yf=B(),it=P(),ot=Kr(),gf=Jr(),hf=Xr(),si=\\"Object already initialized\\",vt=fi.TypeError,Sf=fi.WeakMap,Qr,Tr,Zr,Ef=function(r){return Zr(r)?Tr(r):Qr(r,{})},Of=function(r){return function(e){var t;if(!df(e)||(t=Tr(e)).type!==r)throw vt(\\"Incompatible receiver, \\"+r+\\" required\\");return t}};qf||ot.state?($=ot.state||(ot.state=new Sf),ci=at($.get),ut=at($.has),li=at($.set),Qr=function(r,e){if(ut($,r))throw vt(si);return e.facade=r,li($,r,e),e},Tr=function(r){return ci($,r)||{}},Zr=function(r){return ut($,r)}):(K=gf(\\"state\\"),hf[K]=!0,Qr=function(r,e){if(it(r,K))throw vt(si);return e.facade=r,yf(r,K,e),e},Tr=function(r){return it(r,K)?r[K]:{}},Zr=function(r){return it(r,K)});var $,ci,ut,li,K;pi.exports={set:Qr,get:Tr,has:Zr,enforce:Ef,getterFor:Of}});var yi=a((CE,di)=>{var bf=O(),Tf=g(),re=P(),st=R(),If=tt().CONFIGURABLE,mf=zr(),qi=Ir(),Pf=qi.enforce,xf=qi.get,ee=Object.defineProperty,Rf=st&&!bf(function(){return ee(function(){},\\"length\\",{value:8}).length!==8}),Cf=String(String).split(\\"String\\"),_f=di.exports=function(r,e,t){String(e).slice(0,7)===\\"Symbol(\\"&&(e=\\"[\\"+String(e).replace(/^Symbol\\\\(([^)]*)\\\\)/,\\"$1\\")+\\"]\\"),t&&t.getter&&(e=\\"get \\"+e),t&&t.setter&&(e=\\"set \\"+e),(!re(r,\\"name\\")||If&&r.name!==e)&&(st?ee(r,\\"name\\",{value:e,configurable:!0}):r.name=e),Rf&&t&&re(t,\\"arity\\")&&r.length!==t.arity&&ee(r,\\"length\\",{value:t.arity});try{t&&re(t,\\"constructor\\")&&t.constructor?st&&ee(r,\\"prototype\\",{writable:!1}):r.prototype&&(r.prototype=void 0)}catch{}var n=Pf(r);return re(n,\\"source\\")||(n.source=Cf.join(typeof e==\\"string\\"?e:\\"\\")),r};Function.prototype.toString=_f(function(){return Tf(this)&&xf(this).source||mf(this)},\\"toString\\")});var Y=a((_E,gi)=>{var wf=g(),Nf=w(),Af=yi(),jf=kr();gi.exports=function(r,e,t,n){n||(n={});var i=n.enumerable,o=n.name!==void 0?n.name:e;if(wf(t)&&Af(t,o,n),n.global)i?r[e]=t:jf(e,t);else{try{n.unsafe?r[e]&&(i=!0):delete r[e]}catch{}i?r[e]=t:Nf.f(r,e,{value:t,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return r}});var Si=a((wE,hi)=>{var Df=Math.ceil,Lf=Math.floor;hi.exports=Math.trunc||function(e){var t=+e;return(t>0?Lf:Df)(t)}});var mr=a((NE,Ei)=>{var Uf=Si();Ei.exports=function(r){var e=+r;return e!==e||e===0?0:Uf(e)}});var bi=a((AE,Oi)=>{var Mf=mr(),Ff=Math.max,Bf=Math.min;Oi.exports=function(r,e){var t=Mf(r);return t<0?Ff(t+e,0):Bf(t,e)}});var ct=a((jE,Ti)=>{var $f=mr(),Gf=Math.min;Ti.exports=function(r){return r>0?Gf($f(r),9007199254740991):0}});var lt=a((DE,Ii)=>{var kf=ct();Ii.exports=function(r){return kf(r.length)}});var ft=a((LE,Pi)=>{var Kf=rr(),Yf=bi(),Wf=lt(),mi=function(r){return function(e,t,n){var i=Kf(e),o=Wf(i),v=Yf(n,o),u;if(r&&t!=t){for(;o>v;)if(u=i[v++],u!=u)return!0}else for(;o>v;v++)if((r||v in i)&&i[v]===t)return r||v||0;return!r&&-1}};Pi.exports={includes:mi(!0),indexOf:mi(!1)}});var qt=a((UE,Ri)=>{var Vf=E(),pt=P(),Hf=rr(),zf=ft().indexOf,Jf=Xr(),xi=Vf([].push);Ri.exports=function(r,e){var t=Hf(r),n=0,i=[],o;for(o in t)!pt(Jf,o)&&pt(t,o)&&xi(i,o);for(;e.length>n;)pt(t,o=e[n++])&&(~zf(i,o)||xi(i,o));return i}});var te=a((ME,Ci)=>{Ci.exports=[\\"constructor\\",\\"hasOwnProperty\\",\\"isPrototypeOf\\",\\"propertyIsEnumerable\\",\\"toLocaleString\\",\\"toString\\",\\"valueOf\\"]});var wi=a(_i=>{var Xf=qt(),Qf=te(),Zf=Qf.concat(\\"length\\",\\"prototype\\");_i.f=Object.getOwnPropertyNames||function(e){return Xf(e,Zf)}});var Ai=a(Ni=>{Ni.f=Object.getOwnPropertySymbols});var Di=a(($E,ji)=>{var rp=j(),ep=E(),tp=wi(),np=Ai(),ap=x(),ip=ep([].concat);ji.exports=rp(\\"Reflect\\",\\"ownKeys\\")||function(e){var t=tp.f(ap(e)),n=np.f;return n?ip(t,n(e)):t}});var ne=a((GE,Ui)=>{var Li=P(),op=Di(),up=Vr(),vp=w();Ui.exports=function(r,e,t){for(var n=op(e),i=vp.f,o=up.f,v=0;v<n.length;v++){var u=n[v];!Li(r,u)&&!(t&&Li(t,u))&&i(r,u,o(e,u))}}});var dt=a((kE,Mi)=>{var sp=O(),cp=g(),lp=/#|\\\\.prototype\\\\./,Pr=function(r,e){var t=pp[fp(r)];return t==dp?!0:t==qp?!1:cp(e)?sp(e):!!e},fp=Pr.normalize=function(r){return String(r).replace(lp,\\".\\").toLowerCase()},pp=Pr.data={},qp=Pr.NATIVE=\\"N\\",dp=Pr.POLYFILL=\\"P\\";Mi.exports=Pr});var C=a((KE,Fi)=>{var yt=y(),yp=Vr().f,gp=B(),hp=Y(),Sp=kr(),Ep=ne(),Op=dt();Fi.exports=function(r,e){var t=r.target,n=r.global,i=r.stat,o,v,u,l,s,c;if(n?v=yt:i?v=yt[t]||Sp(t,{}):v=(yt[t]||{}).prototype,v)for(u in e){if(s=e[u],r.dontCallGetSet?(c=yp(v,u),l=c&&c.value):l=v[u],o=Op(n?u:t+(i?\\".\\":\\"#\\")+u,r.forced),!o&&l!==void 0){if(typeof s==typeof l)continue;Ep(s,l)}(r.sham||l&&l.sham)&&gp(s,\\"sham\\",!0),hp(v,u,s,r)}}});var Gi=a((YE,$i)=>{var bp=T(),Tp=bp(\\"toStringTag\\"),Bi={};Bi[Tp]=\\"z\\";$i.exports=String(Bi)===\\"[object z]\\"});var ie=a((WE,ki)=>{var Ip=Gi(),mp=g(),ae=Q(),Pp=T(),xp=Pp(\\"toStringTag\\"),Rp=Object,Cp=ae(function(){return arguments}())==\\"Arguments\\",_p=function(r,e){try{return r[e]}catch{}};ki.exports=Ip?ae:function(r){var e,t,n;return r===void 0?\\"Undefined\\":r===null?\\"Null\\":typeof(t=_p(e=Rp(r),xp))==\\"string\\"?t:Cp?ae(e):(n=ae(e))==\\"Object\\"&&mp(e.callee)?\\"Arguments\\":n}});var nr=a((VE,Ki)=>{var wp=ie(),Np=String;Ki.exports=function(r){if(wp(r)===\\"Symbol\\")throw TypeError(\\"Cannot convert a Symbol value to a string\\");return Np(r)}});var Qi=a((HE,Xi)=>{Xi.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}});var eo=a((zE,ro)=>{var Gp=br(),ht=Gp(\\"span\\").classList,Zi=ht&&ht.constructor&&ht.constructor.prototype;ro.exports=Zi===Object.prototype?void 0:Zi});var no=a((JE,to)=>{var kp=qt(),Kp=te();to.exports=Object.keys||function(e){return kp(e,Kp)}});var io=a(ao=>{var Yp=R(),Wp=ze(),Vp=w(),Hp=x(),zp=rr(),Jp=no();ao.f=Yp&&!Wp?Object.defineProperties:function(e,t){Hp(e);for(var n=zp(t),i=Jp(t),o=i.length,v=0,u;o>v;)Vp.f(e,u=i[v++],n[u]);return e}});var St=a((QE,oo)=>{var Xp=j();oo.exports=Xp(\\"document\\",\\"documentElement\\")});var Rr=a((ZE,po)=>{var Qp=x(),Zp=io(),uo=te(),rq=Xr(),eq=St(),tq=br(),nq=Jr(),vo=\\">\\",so=\\"<\\",Ot=\\"prototype\\",bt=\\"script\\",lo=nq(\\"IE_PROTO\\"),Et=function(){},fo=function(r){return so+bt+vo+r+so+\\"/\\"+bt+vo},co=function(r){r.write(fo(\\"\\")),r.close();var e=r.parentWindow.Object;return r=null,e},aq=function(){var r=tq(\\"iframe\\"),e=\\"java\\"+bt+\\":\\",t;return r.style.display=\\"none\\",eq.appendChild(r),r.src=String(e),t=r.contentWindow.document,t.open(),t.write(fo(\\"document.F=Object\\")),t.close(),t.F},ue,ve=function(){try{ue=new ActiveXObject(\\"htmlfile\\")}catch{}ve=typeof document<\\"u\\"?document.domain&&ue?co(ue):aq():co(ue);for(var r=uo.length;r--;)delete ve[Ot][uo[r]];return ve()};rq[lo]=!0;po.exports=Object.create||function(e,t){var n;return e!==null?(Et[Ot]=Qp(e),n=new Et,Et[Ot]=null,n[lo]=e):n=ve(),t===void 0?n:Zp.f(n,t)}});var mt=a((rO,qo)=>{var iq=T(),oq=Rr(),uq=w().f,Tt=iq(\\"unscopables\\"),It=Array.prototype;It[Tt]==null&&uq(It,Tt,{configurable:!0,value:oq(null)});qo.exports=function(r){It[Tt][r]=!0}});var ar=a((eO,yo)=>{yo.exports={}});var ho=a((tO,go)=>{var vq=O();go.exports=!vq(function(){function r(){}return r.prototype.constructor=null,Object.getPrototypeOf(new r)!==r.prototype})});var xt=a((nO,Eo)=>{var sq=P(),cq=g(),lq=Wr(),fq=Jr(),pq=ho(),So=fq(\\"IE_PROTO\\"),Pt=Object,qq=Pt.prototype;Eo.exports=pq?Pt.getPrototypeOf:function(r){var e=lq(r);if(sq(e,So))return e[So];var t=e.constructor;return cq(t)&&e instanceof t?t.prototype:e instanceof Pt?qq:null}});var wt=a((aO,To)=>{\\"use strict\\";var dq=O(),yq=g(),gq=N(),hq=Rr(),Oo=xt(),Sq=Y(),Eq=T(),Oq=D(),_t=Eq(\\"iterator\\"),bo=!1,M,Rt,Ct;[].keys&&(Ct=[].keys(),\\"next\\"in Ct?(Rt=Oo(Oo(Ct)),Rt!==Object.prototype&&(M=Rt)):bo=!0);var bq=!gq(M)||dq(function(){var r={};return M[_t].call(r)!==r});bq?M={}:Oq&&(M=hq(M));yq(M[_t])||Sq(M,_t,function(){return this});To.exports={IteratorPrototype:M,BUGGY_SAFARI_ITERATORS:bo}});var se=a((iO,mo)=>{var Tq=w().f,Iq=P(),mq=T(),Io=mq(\\"toStringTag\\");mo.exports=function(r,e,t){r&&!t&&(r=r.prototype),r&&!Iq(r,Io)&&Tq(r,Io,{configurable:!0,value:e})}});var xo=a((oO,Po)=>{\\"use strict\\";var Pq=wt().IteratorPrototype,xq=Rr(),Rq=gr(),Cq=se(),_q=ar(),wq=function(){return this};Po.exports=function(r,e,t,n){var i=e+\\" Iterator\\";return r.prototype=xq(Pq,{next:Rq(+!n,t)}),Cq(r,i,!1,!0),_q[i]=wq,r}});var Co=a((uO,Ro)=>{var Nq=g(),Aq=String,jq=TypeError;Ro.exports=function(r){if(typeof r==\\"object\\"||Nq(r))return r;throw jq(\\"Can\'t set \\"+Aq(r)+\\" as a prototype\\")}});var Cr=a((vO,_o)=>{var Dq=E(),Lq=x(),Uq=Co();_o.exports=Object.setPrototypeOf||(\\"__proto__\\"in{}?function(){var r=!1,e={},t;try{t=Dq(Object.getOwnPropertyDescriptor(Object.prototype,\\"__proto__\\").set),t(e,[]),r=e instanceof Array}catch{}return function(i,o){return Lq(i),Uq(o),r?t(i,o):i.__proto__=o,i}}():void 0)});var $o=a((sO,Bo)=>{\\"use strict\\";var Mq=C(),Fq=m(),ce=D(),Mo=tt(),Bq=g(),$q=xo(),wo=xt(),No=Cr(),Gq=se(),kq=B(),Nt=Y(),Kq=T(),Ao=ar(),Fo=wt(),Yq=Mo.PROPER,Wq=Mo.CONFIGURABLE,jo=Fo.IteratorPrototype,le=Fo.BUGGY_SAFARI_ITERATORS,_r=Kq(\\"iterator\\"),Do=\\"keys\\",wr=\\"values\\",Lo=\\"entries\\",Uo=function(){return this};Bo.exports=function(r,e,t,n,i,o,v){$q(t,e,n);var u=function(b){if(b===i&&q)return q;if(!le&&b in c)return c[b];switch(b){case Do:return function(){return new t(this,b)};case wr:return function(){return new t(this,b)};case Lo:return function(){return new t(this,b)}}return function(){return new t(this)}},l=e+\\" Iterator\\",s=!1,c=r.prototype,f=c[_r]||c[\\"@@iterator\\"]||i&&c[i],q=!le&&f||u(i),p=e==\\"Array\\"&&c.entries||f,h,d,I;if(p&&(h=wo(p.call(new r)),h!==Object.prototype&&h.next&&(!ce&&wo(h)!==jo&&(No?No(h,jo):Bq(h[_r])||Nt(h,_r,Uo)),Gq(h,l,!0,!0),ce&&(Ao[l]=Uo))),Yq&&i==wr&&f&&f.name!==wr&&(!ce&&Wq?kq(c,\\"name\\",wr):(s=!0,q=function(){return Fq(f,this)})),i)if(d={values:u(wr),keys:o?q:u(Do),entries:u(Lo)},v)for(I in d)(le||s||!(I in c))&&Nt(c,I,d[I]);else Mq({target:e,proto:!0,forced:le||s},d);return(!ce||v)&&c[_r]!==q&&Nt(c,_r,q,{name:i}),Ao[e]=q,d}});var Vo=a((cO,Wo)=>{\\"use strict\\";var Vq=rr(),At=mt(),Go=ar(),Ko=Ir(),Hq=w().f,zq=$o(),Jq=D(),Xq=R(),Yo=\\"Array Iterator\\",Qq=Ko.set,Zq=Ko.getterFor(Yo);Wo.exports=zq(Array,\\"Array\\",function(r,e){Qq(this,{type:Yo,target:Vq(r),index:0,kind:e})},function(){var r=Zq(this),e=r.target,t=r.kind,n=r.index++;return!e||n>=e.length?(r.target=void 0,{value:void 0,done:!0}):t==\\"keys\\"?{value:n,done:!1}:t==\\"values\\"?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}},\\"values\\");var ko=Go.Arguments=Go.Array;At(\\"keys\\");At(\\"values\\");At(\\"entries\\");if(!Jq&&Xq&&ko.name!==\\"values\\")try{Hq(ko,\\"name\\",{value:\\"values\\"})}catch{}});var ru=a((lO,Zo)=>{var ed=y();Zo.exports=ed});var tu=a(eu=>{var td=T();eu.f=td});var iu=a((pO,au)=>{var nu=ru(),nd=P(),ad=tu(),id=w().f;au.exports=function(r){var e=nu.Symbol||(nu.Symbol={});nd(e,r)||id(e,r,{value:ad.f(r)})}});var pe=a((qO,su)=>{var ud=yr(),vu=Function.prototype,ou=vu.apply,uu=vu.call;su.exports=typeof Reflect==\\"object\\"&&Reflect.apply||(ud?uu.bind(ou):function(){return uu.apply(ou,arguments)})});var lu=a((dO,cu)=>{var vd=w().f;cu.exports=function(r,e,t){t in r||vd(r,t,{configurable:!0,get:function(){return e[t]},set:function(n){e[t]=n}})}});var qu=a((yO,pu)=>{var sd=g(),cd=N(),fu=Cr();pu.exports=function(r,e,t){var n,i;return fu&&sd(n=e.constructor)&&n!==t&&cd(i=n.prototype)&&i!==t.prototype&&fu(r,i),r}});var yu=a((gO,du)=>{var ld=nr();du.exports=function(r,e){return r===void 0?arguments.length<2?\\"\\":e:ld(r)}});var hu=a((hO,gu)=>{var fd=N(),pd=B();gu.exports=function(r,e){fd(e)&&\\"cause\\"in e&&pd(r,\\"cause\\",e.cause)}});var bu=a((SO,Ou)=>{var qd=E(),Su=Error,dd=qd(\\"\\".replace),yd=function(r){return String(Su(r).stack)}(\\"zxcasd\\"),Eu=/\\\\n\\\\s*at [^:]*:[^\\\\n]*/,gd=Eu.test(yd);Ou.exports=function(r,e){if(gd&&typeof r==\\"string\\"&&!Su.prepareStackTrace)for(;e--;)r=dd(r,Eu,\\"\\");return r}});var Iu=a((EO,Tu)=>{var hd=O(),Sd=gr();Tu.exports=!hd(function(){var r=Error(\\"a\\");return\\"stack\\"in r?(Object.defineProperty(r,\\"stack\\",Sd(1,7)),r.stack!==7):!0})});var wu=a((OO,_u)=>{\\"use strict\\";var mu=j(),Ed=P(),Ut=B(),Od=er(),Pu=Cr(),xu=ne(),Ru=lu(),bd=qu(),Td=yu(),Id=hu(),md=bu(),Pd=Iu(),xd=R(),Cu=D();_u.exports=function(r,e,t,n){var i=\\"stackTraceLimit\\",o=n?2:1,v=r.split(\\".\\"),u=v[v.length-1],l=mu.apply(null,v);if(!!l){var s=l.prototype;if(!Cu&&Ed(s,\\"cause\\")&&delete s.cause,!t)return l;var c=mu(\\"Error\\"),f=e(function(q,p){var h=Td(n?p:q,void 0),d=n?new l(q):new l;return h!==void 0&&Ut(d,\\"message\\",h),Pd&&Ut(d,\\"stack\\",md(d.stack,2)),this&&Od(s,this)&&bd(d,this,f),arguments.length>o&&Id(d,arguments[o]),d});if(f.prototype=s,u!==\\"Error\\"?Pu?Pu(f,c):xu(f,c,{name:!0}):xd&&i in l&&(Ru(f,l,i),Ru(f,l,\\"prepareStackTrace\\")),xu(f,l),!Cu)try{s.name!==u&&Ut(s,\\"name\\",u),s.constructor=f}catch{}return f}}});var Ar=a((bO,Du)=>{var Cd=Q(),_d=y();Du.exports=Cd(_d.process)==\\"process\\"});var Mu=a((TO,Uu)=>{\\"use strict\\";var wd=j(),Nd=w(),Ad=T(),jd=R(),Lu=Ad(\\"species\\");Uu.exports=function(r){var e=wd(r),t=Nd.f;jd&&e&&!e[Lu]&&t(e,Lu,{configurable:!0,get:function(){return this}})}});var Bu=a((IO,Fu)=>{var Dd=er(),Ld=TypeError;Fu.exports=function(r,e){if(Dd(e,r))return r;throw Ld(\\"Incorrect invocation\\")}});var Wu=a((mO,Yu)=>{var Ud=E(),Md=O(),$u=g(),Fd=ie(),Bd=j(),$d=zr(),Gu=function(){},Gd=[],ku=Bd(\\"Reflect\\",\\"construct\\"),Bt=/^\\\\s*(?:class|function)\\\\b/,kd=Ud(Bt.exec),Kd=!Bt.exec(Gu),jr=function(e){if(!$u(e))return!1;try{return ku(Gu,Gd,e),!0}catch{return!1}},Ku=function(e){if(!$u(e))return!1;switch(Fd(e)){case\\"AsyncFunction\\":case\\"GeneratorFunction\\":case\\"AsyncGeneratorFunction\\":return!1}try{return Kd||!!kd(Bt,$d(e))}catch{return!0}};Ku.sham=!0;Yu.exports=!ku||Md(function(){var r;return jr(jr.call)||!jr(Object)||!jr(function(){r=!0})||r})?Ku:jr});var Hu=a((PO,Vu)=>{var Yd=Wu(),Wd=Er(),Vd=TypeError;Vu.exports=function(r){if(Yd(r))return r;throw Vd(Wd(r)+\\" is not a constructor\\")}});var Xu=a((xO,Ju)=>{var zu=x(),Hd=Hu(),zd=Z(),Jd=T(),Xd=Jd(\\"species\\");Ju.exports=function(r,e){var t=zu(r).constructor,n;return t===void 0||zd(n=zu(t)[Xd])?e:Hd(n)}});var de=a((RO,Zu)=>{var Qu=E(),Qd=F(),Zd=yr(),ry=Qu(Qu.bind);Zu.exports=function(r,e){return Qd(r),e===void 0?r:Zd?ry(r,e):function(){return r.apply(e,arguments)}}});var ev=a((CO,rv)=>{var ey=E();rv.exports=ey([].slice)});var nv=a((_O,tv)=>{var ty=TypeError;tv.exports=function(r,e){if(r<e)throw ty(\\"Not enough arguments\\");return r}});var $t=a((wO,av)=>{var ny=Sr();av.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(ny)});var Jt=a((NO,fv)=>{var _=y(),ay=pe(),iy=de(),iv=g(),oy=P(),uy=O(),ov=St(),vy=ev(),uv=br(),sy=nv(),cy=$t(),ly=Ar(),Vt=_.setImmediate,Ht=_.clearImmediate,fy=_.process,Gt=_.Dispatch,py=_.Function,vv=_.MessageChannel,qy=_.String,kt=0,Dr={},sv=\\"onreadystatechange\\",Lr,H,Kt,Yt;try{Lr=_.location}catch{}var zt=function(r){if(oy(Dr,r)){var e=Dr[r];delete Dr[r],e()}},Wt=function(r){return function(){zt(r)}},cv=function(r){zt(r.data)},lv=function(r){_.postMessage(qy(r),Lr.protocol+\\"//\\"+Lr.host)};(!Vt||!Ht)&&(Vt=function(e){sy(arguments.length,1);var t=iv(e)?e:py(e),n=vy(arguments,1);return Dr[++kt]=function(){ay(t,void 0,n)},H(kt),kt},Ht=function(e){delete Dr[e]},ly?H=function(r){fy.nextTick(Wt(r))}:Gt&&Gt.now?H=function(r){Gt.now(Wt(r))}:vv&&!cy?(Kt=new vv,Yt=Kt.port2,Kt.port1.onmessage=cv,H=iy(Yt.postMessage,Yt)):_.addEventListener&&iv(_.postMessage)&&!_.importScripts&&Lr&&Lr.protocol!==\\"file:\\"&&!uy(lv)?(H=lv,_.addEventListener(\\"message\\",cv,!1)):sv in uv(\\"script\\")?H=function(r){ov.appendChild(uv(\\"script\\"))[sv]=function(){ov.removeChild(this),zt(r)}}:H=function(r){setTimeout(Wt(r),0)});fv.exports={set:Vt,clear:Ht}});var qv=a((AO,pv)=>{var dy=Sr(),yy=y();pv.exports=/ipad|iphone|ipod/i.test(dy)&&yy.Pebble!==void 0});var yv=a((jO,dv)=>{var gy=Sr();dv.exports=/web0s(?!.*chrome)/i.test(gy)});var mv=a((DO,Iv)=>{var J=y(),gv=de(),hy=Vr().f,Xt=Jt().set,Sy=$t(),Ey=qv(),Oy=yv(),Qt=Ar(),hv=J.MutationObserver||J.WebKitMutationObserver,Sv=J.document,Ev=J.process,ye=J.Promise,Ov=hy(J,\\"queueMicrotask\\"),Tv=Ov&&Ov.value,Ur,z,Mr,ir,Zt,rn,ge,bv;Tv||(Ur=function(){var r,e;for(Qt&&(r=Ev.domain)&&r.exit();z;){e=z.fn,z=z.next;try{e()}catch(t){throw z?ir():Mr=void 0,t}}Mr=void 0,r&&r.enter()},!Sy&&!Qt&&!Oy&&hv&&Sv?(Zt=!0,rn=Sv.createTextNode(\\"\\"),new hv(Ur).observe(rn,{characterData:!0}),ir=function(){rn.data=Zt=!Zt}):!Ey&&ye&&ye.resolve?(ge=ye.resolve(void 0),ge.constructor=ye,bv=gv(ge.then,ge),ir=function(){bv(Ur)}):Qt?ir=function(){Ev.nextTick(Ur)}:(Xt=gv(Xt,J),ir=function(){Xt(Ur)}));Iv.exports=Tv||function(r){var e={fn:r,next:void 0};Mr&&(Mr.next=e),z||(z=e,ir()),Mr=e}});var xv=a((LO,Pv)=>{var by=y();Pv.exports=function(r,e){var t=by.console;t&&t.error&&(arguments.length==1?t.error(r):t.error(r,e))}});var he=a((UO,Rv)=>{Rv.exports=function(r){try{return{error:!1,value:r()}}catch(e){return{error:!0,value:e}}}});var wv=a((MO,_v)=>{var Cv=function(){this.head=null,this.tail=null};Cv.prototype={add:function(r){var e={item:r,next:null};this.head?this.tail.next=e:this.head=e,this.tail=e},get:function(){var r=this.head;if(r)return this.head=r.next,this.tail===r&&(this.tail=null),r.item}};_v.exports=Cv});var or=a((FO,Nv)=>{var Ty=y();Nv.exports=Ty.Promise});var en=a((BO,Av)=>{Av.exports=typeof Deno==\\"object\\"&&Deno&&typeof Deno.version==\\"object\\"});var Dv=a(($O,jv)=>{var Iy=en(),my=Ar();jv.exports=!Iy&&!my&&typeof window==\\"object\\"&&typeof document==\\"object\\"});var ur=a((GO,Mv)=>{var Py=y(),Fr=or(),xy=g(),Ry=dt(),Cy=zr(),_y=T(),wy=Dv(),Ny=en(),Ay=D(),tn=Me(),Lv=Fr&&Fr.prototype,jy=_y(\\"species\\"),nn=!1,Uv=xy(Py.PromiseRejectionEvent),Dy=Ry(\\"Promise\\",function(){var r=Cy(Fr),e=r!==String(Fr);if(!e&&tn===66||Ay&&!(Lv.catch&&Lv.finally))return!0;if(!tn||tn<51||!/native code/.test(r)){var t=new Fr(function(o){o(1)}),n=function(o){o(function(){},function(){})},i=t.constructor={};if(i[jy]=n,nn=t.then(function(){})instanceof n,!nn)return!0}return!e&&(wy||Ny)&&!Uv});Mv.exports={CONSTRUCTOR:Dy,REJECTION_EVENT:Uv,SUBCLASSING:nn}});var vr=a((kO,Bv)=>{\\"use strict\\";var Fv=F(),Ly=TypeError,Uy=function(r){var e,t;this.promise=new r(function(n,i){if(e!==void 0||t!==void 0)throw Ly(\\"Bad Promise constructor\\");e=n,t=i}),this.resolve=Fv(e),this.reject=Fv(t)};Bv.exports.f=function(r){return new Uy(r)}});var is=a(()=>{\\"use strict\\";var My=C(),Fy=D(),be=Ar(),G=y(),fr=m(),$v=Y(),Gv=Cr(),By=se(),$y=Mu(),Gy=F(),Oe=g(),ky=N(),Ky=Bu(),Yy=Xu(),Vv=Jt().set,sn=mv(),Wy=xv(),Vy=he(),Hy=wv(),Hv=Ir(),Te=or(),cn=ur(),zv=vr(),Ie=\\"Promise\\",Jv=cn.CONSTRUCTOR,zy=cn.REJECTION_EVENT,Jy=cn.SUBCLASSING,an=Hv.getterFor(Ie),Xy=Hv.set,sr=Te&&Te.prototype,X=Te,Se=sr,Xv=G.TypeError,on=G.document,ln=G.process,un=zv.f,Qy=un,Zy=!!(on&&on.createEvent&&G.dispatchEvent),Qv=\\"unhandledrejection\\",rg=\\"rejectionhandled\\",kv=0,Zv=1,eg=2,fn=1,rs=2,Ee,Kv,tg,Yv,es=function(r){var e;return ky(r)&&Oe(e=r.then)?e:!1},ts=function(r,e){var t=e.value,n=e.state==Zv,i=n?r.ok:r.fail,o=r.resolve,v=r.reject,u=r.domain,l,s,c;try{i?(n||(e.rejection===rs&&ag(e),e.rejection=fn),i===!0?l=t:(u&&u.enter(),l=i(t),u&&(u.exit(),c=!0)),l===r.promise?v(Xv(\\"Promise-chain cycle\\")):(s=es(l))?fr(s,l,o,v):o(l)):v(t)}catch(f){u&&!c&&u.exit(),v(f)}},ns=function(r,e){r.notified||(r.notified=!0,sn(function(){for(var t=r.reactions,n;n=t.get();)ts(n,r);r.notified=!1,e&&!r.rejection&&ng(r)}))},as=function(r,e,t){var n,i;Zy?(n=on.createEvent(\\"Event\\"),n.promise=e,n.reason=t,n.initEvent(r,!1,!0),G.dispatchEvent(n)):n={promise:e,reason:t},!zy&&(i=G[\\"on\\"+r])?i(n):r===Qv&&Wy(\\"Unhandled promise rejection\\",t)},ng=function(r){fr(Vv,G,function(){var e=r.facade,t=r.value,n=Wv(r),i;if(n&&(i=Vy(function(){be?ln.emit(\\"unhandledRejection\\",t,e):as(Qv,e,t)}),r.rejection=be||Wv(r)?rs:fn,i.error))throw i.value})},Wv=function(r){return r.rejection!==fn&&!r.parent},ag=function(r){fr(Vv,G,function(){var e=r.facade;be?ln.emit(\\"rejectionHandled\\",e):as(rg,e,r.value)})},cr=function(r,e,t){return function(n){r(e,n,t)}},lr=function(r,e,t){r.done||(r.done=!0,t&&(r=t),r.value=e,r.state=eg,ns(r,!0))},vn=function(r,e,t){if(!r.done){r.done=!0,t&&(r=t);try{if(r.facade===e)throw Xv(\\"Promise can\'t be resolved itself\\");var n=es(e);n?sn(function(){var i={done:!1};try{fr(n,e,cr(vn,i,r),cr(lr,i,r))}catch(o){lr(i,o,r)}}):(r.value=e,r.state=Zv,ns(r,!1))}catch(i){lr({done:!1},i,r)}}};if(Jv&&(X=function(e){Ky(this,Se),Gy(e),fr(Ee,this);var t=an(this);try{e(cr(vn,t),cr(lr,t))}catch(n){lr(t,n)}},Se=X.prototype,Ee=function(e){Xy(this,{type:Ie,done:!1,notified:!1,parent:!1,reactions:new Hy,rejection:!1,state:kv,value:void 0})},Ee.prototype=$v(Se,\\"then\\",function(e,t){var n=an(this),i=un(Yy(this,X));return n.parent=!0,i.ok=Oe(e)?e:!0,i.fail=Oe(t)&&t,i.domain=be?ln.domain:void 0,n.state==kv?n.reactions.add(i):sn(function(){ts(i,n)}),i.promise}),Kv=function(){var r=new Ee,e=an(r);this.promise=r,this.resolve=cr(vn,e),this.reject=cr(lr,e)},zv.f=un=function(r){return r===X||r===tg?new Kv(r):Qy(r)},!Fy&&Oe(Te)&&sr!==Object.prototype)){Yv=sr.then,Jy||$v(sr,\\"then\\",function(e,t){var n=this;return new X(function(i,o){fr(Yv,n,i,o)}).then(e,t)},{unsafe:!0});try{delete sr.constructor}catch{}Gv&&Gv(sr,Se)}My({global:!0,constructor:!0,wrap:!0,forced:Jv},{Promise:X});By(X,Ie,!1,!0);$y(Ie)});var us=a((WO,os)=>{var ig=T(),og=ar(),ug=ig(\\"iterator\\"),vg=Array.prototype;os.exports=function(r){return r!==void 0&&(og.Array===r||vg[ug]===r)}});var pn=a((VO,ss)=>{var sg=ie(),vs=Or(),cg=Z(),lg=ar(),fg=T(),pg=fg(\\"iterator\\");ss.exports=function(r){if(!cg(r))return vs(r,pg)||vs(r,\\"@@iterator\\")||lg[sg(r)]}});var ls=a((HO,cs)=>{var qg=m(),dg=F(),yg=x(),gg=Er(),hg=pn(),Sg=TypeError;cs.exports=function(r,e){var t=arguments.length<2?hg(r):e;if(dg(t))return yg(qg(t,r));throw Sg(gg(r)+\\" is not iterable\\")}});var qs=a((zO,ps)=>{var Eg=m(),fs=x(),Og=Or();ps.exports=function(r,e,t){var n,i;fs(r);try{if(n=Og(r,\\"return\\"),!n){if(e===\\"throw\\")throw t;return t}n=Eg(n,r)}catch(o){i=!0,n=o}if(e===\\"throw\\")throw t;if(i)throw n;return fs(n),t}});var qn=a((JO,hs)=>{var bg=de(),Tg=m(),Ig=x(),mg=Er(),Pg=us(),xg=lt(),ds=er(),Rg=ls(),Cg=pn(),ys=qs(),_g=TypeError,me=function(r,e){this.stopped=r,this.result=e},gs=me.prototype;hs.exports=function(r,e,t){var n=t&&t.that,i=!!(t&&t.AS_ENTRIES),o=!!(t&&t.IS_RECORD),v=!!(t&&t.IS_ITERATOR),u=!!(t&&t.INTERRUPTED),l=bg(e,n),s,c,f,q,p,h,d,I=function(S){return s&&ys(s,\\"normal\\",S),new me(!0,S)},b=function(S){return i?(Ig(S),u?l(S[0],S[1],I):l(S[0],S[1])):u?l(S,I):l(S)};if(o)s=r.iterator;else if(v)s=r;else{if(c=Cg(r),!c)throw _g(mg(r)+\\" is not iterable\\");if(Pg(c)){for(f=0,q=xg(r);q>f;f++)if(p=b(r[f]),p&&ds(gs,p))return p;return new me(!1)}s=Rg(r,c)}for(h=o?r.next:s.next;!(d=Tg(h,s)).done;){try{p=b(d.value)}catch(S){ys(s,\\"throw\\",S)}if(typeof p==\\"object\\"&&p&&ds(gs,p))return p}return new me(!1)}});var Ts=a((XO,bs)=>{var wg=T(),Es=wg(\\"iterator\\"),Os=!1;try{Ss=0,dn={next:function(){return{done:!!Ss++}},return:function(){Os=!0}},dn[Es]=function(){return this},Array.from(dn,function(){throw 2})}catch{}var Ss,dn;bs.exports=function(r,e){if(!e&&!Os)return!1;var t=!1;try{var n={};n[Es]=function(){return{next:function(){return{done:t=!0}}}},r(n)}catch{}return t}});var yn=a((QO,Is)=>{var Ng=or(),Ag=Ts(),jg=ur().CONSTRUCTOR;Is.exports=jg||!Ag(function(r){Ng.all(r).then(void 0,function(){})})});var ms=a(()=>{\\"use strict\\";var Dg=C(),Lg=m(),Ug=F(),Mg=vr(),Fg=he(),Bg=qn(),$g=yn();Dg({target:\\"Promise\\",stat:!0,forced:$g},{all:function(e){var t=this,n=Mg.f(t),i=n.resolve,o=n.reject,v=Fg(function(){var u=Ug(t.resolve),l=[],s=0,c=1;Bg(e,function(f){var q=s++,p=!1;c++,Lg(u,t,f).then(function(h){p||(p=!0,l[q]=h,--c||i(l))},o)}),--c||i(l)});return v.error&&o(v.value),n.promise}})});var xs=a(()=>{\\"use strict\\";var Gg=C(),kg=D(),Kg=ur().CONSTRUCTOR,hn=or(),Yg=j(),Wg=g(),Vg=Y(),Ps=hn&&hn.prototype;Gg({target:\\"Promise\\",proto:!0,forced:Kg,real:!0},{catch:function(r){return this.then(void 0,r)}});!kg&&Wg(hn)&&(gn=Yg(\\"Promise\\").prototype.catch,Ps.catch!==gn&&Vg(Ps,\\"catch\\",gn,{unsafe:!0}));var gn});var Rs=a(()=>{\\"use strict\\";var Hg=C(),zg=m(),Jg=F(),Xg=vr(),Qg=he(),Zg=qn(),rh=yn();Hg({target:\\"Promise\\",stat:!0,forced:rh},{race:function(e){var t=this,n=Xg.f(t),i=n.reject,o=Qg(function(){var v=Jg(t.resolve);Zg(e,function(u){zg(v,t,u).then(n.resolve,i)})});return o.error&&i(o.value),n.promise}})});var Cs=a(()=>{\\"use strict\\";var eh=C(),th=m(),nh=vr(),ah=ur().CONSTRUCTOR;eh({target:\\"Promise\\",stat:!0,forced:ah},{reject:function(e){var t=nh.f(this);return th(t.reject,void 0,e),t.promise}})});var ws=a((ub,_s)=>{var ih=x(),oh=N(),uh=vr();_s.exports=function(r,e){if(ih(r),oh(e)&&e.constructor===r)return e;var t=uh.f(r),n=t.resolve;return n(e),t.promise}});var js=a(()=>{\\"use strict\\";var vh=C(),sh=j(),Ns=D(),ch=or(),As=ur().CONSTRUCTOR,lh=ws(),fh=sh(\\"Promise\\"),ph=Ns&&!As;vh({target:\\"Promise\\",stat:!0,forced:Ns||As},{resolve:function(e){return lh(ph&&this===fh?ch:this,e)}})});var Ls=a((cb,Ds)=>{var qh=Q();Ds.exports=Array.isArray||function(e){return qh(e)==\\"Array\\"}});var Fs=a((lb,Ms)=>{\\"use strict\\";var Sh=x();Ms.exports=function(){var r=Sh(this),e=\\"\\";return r.hasIndices&&(e+=\\"d\\"),r.global&&(e+=\\"g\\"),r.ignoreCase&&(e+=\\"i\\"),r.multiline&&(e+=\\"m\\"),r.dotAll&&(e+=\\"s\\"),r.unicode&&(e+=\\"u\\"),r.unicodeSets&&(e+=\\"v\\"),r.sticky&&(e+=\\"y\\"),e}});var $s=a((fb,Bs)=>{var Sn=O(),Eh=y(),En=Eh.RegExp,On=Sn(function(){var r=En(\\"a\\",\\"y\\");return r.lastIndex=2,r.exec(\\"abcd\\")!=null}),Oh=On||Sn(function(){return!En(\\"a\\",\\"y\\").sticky}),bh=On||Sn(function(){var r=En(\\"^r\\",\\"gy\\");return r.lastIndex=2,r.exec(\\"str\\")!=null});Bs.exports={BROKEN_CARET:bh,MISSED_STICKY:Oh,UNSUPPORTED_Y:On}});var ks=a((pb,Gs)=>{var Th=O(),Ih=y(),mh=Ih.RegExp;Gs.exports=Th(function(){var r=mh(\\".\\",\\"s\\");return!(r.dotAll&&r.exec(`\\n`)&&r.flags===\\"s\\")})});var Ys=a((qb,Ks)=>{var Ph=O(),xh=y(),Rh=xh.RegExp;Ks.exports=Ph(function(){var r=Rh(\\"(?<a>b)\\",\\"g\\");return r.exec(\\"b\\").groups.a!==\\"b\\"||\\"b\\".replace(r,\\"$<a>c\\")!==\\"bc\\"})});var Re=a((db,Vs)=>{\\"use strict\\";var pr=m(),xe=E(),Ch=nr(),_h=Fs(),wh=$s(),Nh=Yr(),Ah=Rr(),jh=Ir().get,Dh=ks(),Lh=Ys(),Uh=Nh(\\"native-string-replace\\",String.prototype.replace),Pe=RegExp.prototype.exec,Tn=Pe,Mh=xe(\\"\\".charAt),Fh=xe(\\"\\".indexOf),Bh=xe(\\"\\".replace),bn=xe(\\"\\".slice),In=function(){var r=/a/,e=/b*/g;return pr(Pe,r,\\"a\\"),pr(Pe,e,\\"a\\"),r.lastIndex!==0||e.lastIndex!==0}(),Ws=wh.BROKEN_CARET,mn=/()??/.exec(\\"\\")[1]!==void 0,$h=In||mn||Ws||Dh||Lh;$h&&(Tn=function(e){var t=this,n=jh(t),i=Ch(e),o=n.raw,v,u,l,s,c,f,q;if(o)return o.lastIndex=t.lastIndex,v=pr(Tn,o,i),t.lastIndex=o.lastIndex,v;var p=n.groups,h=Ws&&t.sticky,d=pr(_h,t),I=t.source,b=0,S=i;if(h&&(d=Bh(d,\\"y\\",\\"\\"),Fh(d,\\"g\\")===-1&&(d+=\\"g\\"),S=bn(i,t.lastIndex),t.lastIndex>0&&(!t.multiline||t.multiline&&Mh(i,t.lastIndex-1)!==`\\n`)&&(I=\\"(?: \\"+I+\\")\\",S=\\" \\"+S,b++),u=new RegExp(\\"^(?:\\"+I+\\")\\",d)),mn&&(u=new RegExp(\\"^\\"+I+\\"$(?!\\\\\\\\s)\\",d)),In&&(l=t.lastIndex),s=pr(Pe,h?u:t,S),h?s?(s.input=bn(s.input,b),s[0]=bn(s[0],b),s.index=t.lastIndex,t.lastIndex+=s[0].length):t.lastIndex=0:In&&s&&(t.lastIndex=t.global?s.index+s[0].length:l),mn&&s&&s.length>1&&pr(Uh,s[0],u,function(){for(c=1;c<arguments.length-2;c++)arguments[c]===void 0&&(s[c]=void 0)}),s&&p)for(s.groups=f=Ah(null),c=0;c<p.length;c++)q=p[c],f[q[0]]=s[q[1]];return s});Vs.exports=Tn});var Pn=a(()=>{\\"use strict\\";var Gh=C(),Hs=Re();Gh({target:\\"RegExp\\",proto:!0,forced:/./.exec!==Hs},{exec:Hs})});var rc=a((hb,Zs)=>{\\"use strict\\";Pn();var zs=E(),Js=Y(),kh=Re(),Xs=O(),Qs=T(),Kh=B(),Yh=Qs(\\"species\\"),xn=RegExp.prototype;Zs.exports=function(r,e,t,n){var i=Qs(r),o=!Xs(function(){var s={};return s[i]=function(){return 7},\\"\\"[r](s)!=7}),v=o&&!Xs(function(){var s=!1,c=/a/;return r===\\"split\\"&&(c={},c.constructor={},c.constructor[Yh]=function(){return c},c.flags=\\"\\",c[i]=/./[i]),c.exec=function(){return s=!0,null},c[i](\\"\\"),!s});if(!o||!v||t){var u=zs(/./[i]),l=e(i,\\"\\"[r],function(s,c,f,q,p){var h=zs(s),d=c.exec;return d===kh||d===xn.exec?o&&!p?{done:!0,value:u(c,f,q)}:{done:!0,value:h(f,c,q)}:{done:!1}});Js(String.prototype,r,l[0]),Js(xn,i,l[1])}n&&Kh(xn[i],\\"sham\\",!0)}});var ac=a((Sb,nc)=>{var Rn=E(),Wh=mr(),Vh=nr(),Hh=hr(),zh=Rn(\\"\\".charAt),ec=Rn(\\"\\".charCodeAt),Jh=Rn(\\"\\".slice),tc=function(r){return function(e,t){var n=Vh(Hh(e)),i=Wh(t),o=n.length,v,u;return i<0||i>=o?r?\\"\\":void 0:(v=ec(n,i),v<55296||v>56319||i+1===o||(u=ec(n,i+1))<56320||u>57343?r?zh(n,i):v:r?Jh(n,i,i+2):(v-55296<<10)+(u-56320)+65536)}};nc.exports={codeAt:tc(!1),charAt:tc(!0)}});var oc=a((Eb,ic)=>{\\"use strict\\";var Xh=ac().charAt;ic.exports=function(r,e,t){return e+(t?Xh(r,e).length:1)}});var vc=a((Ob,uc)=>{var wn=E(),Qh=Wr(),Zh=Math.floor,Cn=wn(\\"\\".charAt),rS=wn(\\"\\".replace),_n=wn(\\"\\".slice),eS=/\\\\$([$&\'`]|\\\\d{1,2}|<[^>]*>)/g,tS=/\\\\$([$&\'`]|\\\\d{1,2})/g;uc.exports=function(r,e,t,n,i,o){var v=t+r.length,u=n.length,l=tS;return i!==void 0&&(i=Qh(i),l=eS),rS(o,l,function(s,c){var f;switch(Cn(c,0)){case\\"$\\":return\\"$\\";case\\"&\\":return r;case\\"`\\":return _n(e,0,t);case\\"\'\\":return _n(e,v);case\\"<\\":f=i[_n(c,1,-1)];break;default:var q=+c;if(q===0)return s;if(q>u){var p=Zh(q/10);return p===0?s:p<=u?n[p-1]===void 0?Cn(c,1):n[p-1]+Cn(c,1):s}f=n[q-1]}return f===void 0?\\"\\":f})}});var lc=a((bb,cc)=>{var sc=m(),nS=x(),aS=g(),iS=Q(),oS=Re(),uS=TypeError;cc.exports=function(r,e){var t=r.exec;if(aS(t)){var n=sc(t,r,e);return n!==null&&nS(n),n}if(iS(r)===\\"RegExp\\")return sc(oS,r,e);throw uS(\\"RegExp#exec called on incompatible receiver\\")}});var Ap=C(),jp=R(),Dp=y(),oe=E(),Lp=P(),Up=g(),Mp=er(),Fp=nr(),Bp=w().f,$p=ne(),U=Dp.Symbol,W=U&&U.prototype;jp&&Up(U)&&(!(\\"description\\"in W)||U().description!==void 0)&&(gt={},xr=function(){var e=arguments.length<1||arguments[0]===void 0?void 0:Fp(arguments[0]),t=Mp(W,this)?new U(e):e===void 0?U():U(e);return e===\\"\\"&&(gt[t]=!0),t},$p(xr,U),xr.prototype=W,W.constructor=xr,Yi=String(U(\\"test\\"))==\\"Symbol(test)\\",Wi=oe(W.valueOf),Vi=oe(W.toString),Hi=/^Symbol\\\\((.*)\\\\)[^)]+$/,zi=oe(\\"\\".replace),Ji=oe(\\"\\".slice),Bp(W,\\"description\\",{configurable:!0,get:function(){var e=Wi(this);if(Lp(gt,e))return\\"\\";var t=Vi(e),n=Yi?Ji(t,7,-1):zi(t,Hi,\\"$1\\");return n===\\"\\"?void 0:n}}),Ap({global:!0,constructor:!0,forced:!0},{Symbol:xr}));var gt,xr,Yi,Wi,Vi,Hi,zi,Ji;var Ho=y(),Jo=Qi(),rd=eo(),Nr=Vo(),jt=B(),Xo=T(),Dt=Xo(\\"iterator\\"),zo=Xo(\\"toStringTag\\"),Lt=Nr.values,Qo=function(r,e){if(r){if(r[Dt]!==Lt)try{jt(r,Dt,Lt)}catch{r[Dt]=Lt}if(r[zo]||jt(r,zo,e),Jo[e]){for(var t in Nr)if(r[t]!==Nr[t])try{jt(r,t,Nr[t])}catch{r[t]=Nr[t]}}}};for(fe in Jo)Qo(Ho[fe]&&Ho[fe].prototype,fe);var fe;Qo(rd,\\"DOMTokenList\\");var od=iu();od(\\"asyncIterator\\");var Au=C(),Rd=y(),L=pe(),ju=wu(),Mt=\\"WebAssembly\\",Nu=Rd[Mt],qe=Error(\\"e\\",{cause:7}).cause!==7,V=function(r,e){var t={};t[r]=ju(r,e,qe),Au({global:!0,constructor:!0,arity:1,forced:qe},t)},Ft=function(r,e){if(Nu&&Nu[r]){var t={};t[r]=ju(Mt+\\".\\"+r,e,qe),Au({target:Mt,stat:!0,constructor:!0,arity:1,forced:qe},t)}};V(\\"Error\\",function(r){return function(t){return L(r,this,arguments)}});V(\\"EvalError\\",function(r){return function(t){return L(r,this,arguments)}});V(\\"RangeError\\",function(r){return function(t){return L(r,this,arguments)}});V(\\"ReferenceError\\",function(r){return function(t){return L(r,this,arguments)}});V(\\"SyntaxError\\",function(r){return function(t){return L(r,this,arguments)}});V(\\"TypeError\\",function(r){return function(t){return L(r,this,arguments)}});V(\\"URIError\\",function(r){return function(t){return L(r,this,arguments)}});Ft(\\"CompileError\\",function(r){return function(t){return L(r,this,arguments)}});Ft(\\"LinkError\\",function(r){return function(t){return L(r,this,arguments)}});Ft(\\"RuntimeError\\",function(r){return function(t){return L(r,this,arguments)}});is();ms();xs();Rs();Cs();js();var dh=C(),yh=E(),gh=Ls(),hh=yh([].reverse),Us=[1,2];dh({target:\\"Array\\",proto:!0,forced:String(Us)===String(Us.reverse())},{reverse:function(){return gh(this)&&(this.length=this.length),hh(this)}});var Cb=bc(Pn());var vS=pe(),fc=m(),Ce=E(),sS=rc(),cS=O(),lS=x(),fS=g(),pS=Z(),qS=mr(),dS=ct(),qr=nr(),yS=hr(),gS=oc(),hS=Or(),SS=vc(),ES=lc(),OS=T(),An=OS(\\"replace\\"),bS=Math.max,TS=Math.min,IS=Ce([].concat),Nn=Ce([].push),pc=Ce(\\"\\".indexOf),qc=Ce(\\"\\".slice),mS=function(r){return r===void 0?r:String(r)},PS=function(){return\\"a\\".replace(/./,\\"$0\\")===\\"$0\\"}(),dc=function(){return/./[An]?/./[An](\\"a\\",\\"$0\\")===\\"\\":!1}(),xS=!cS(function(){var r=/./;return r.exec=function(){var e=[];return e.groups={a:\\"7\\"},e},\\"\\".replace(r,\\"$<a>\\")!==\\"7\\"});sS(\\"replace\\",function(r,e,t){var n=dc?\\"$\\":\\"$0\\";return[function(o,v){var u=yS(this),l=pS(o)?void 0:hS(o,An);return l?fc(l,o,u,v):fc(e,qr(u),o,v)},function(i,o){var v=lS(this),u=qr(i);if(typeof o==\\"string\\"&&pc(o,n)===-1&&pc(o,\\"$<\\")===-1){var l=t(e,v,u,o);if(l.done)return l.value}var s=fS(o);s||(o=qr(o));var c=v.global;if(c){var f=v.unicode;v.lastIndex=0}for(var q=[];;){var p=ES(v,u);if(p===null||(Nn(q,p),!c))break;var h=qr(p[0]);h===\\"\\"&&(v.lastIndex=gS(u,dS(v.lastIndex),f))}for(var d=\\"\\",I=0,b=0;b<q.length;b++){p=q[b];for(var S=qr(p[0]),dr=bS(TS(qS(p.index),u.length),0),_e=[],we=1;we<p.length;we++)Nn(_e,mS(p[we]));var Ne=p.groups;if(s){var jn=IS([S],_e,dr,u);Ne!==void 0&&Nn(jn,Ne);var Dn=qr(vS(o,void 0,jn))}else Dn=SS(S,u,dr,_e,Ne,o);dr>=I&&(d+=qc(u,I,dr)+Dn,I=dr+S.length)}return d+qc(u,I)}]},!xS||!PS||dc);var RS=C(),CS=ft().includes,_S=O(),wS=mt(),NS=_S(function(){return!Array(1).includes()});RS({target:\\"Array\\",proto:!0,forced:NS},{includes:function(e){return CS(this,e,arguments.length>1?arguments[1]:void 0)}});wS(\\"includes\\");})();\\n;\\n(()=>{})();;","ictp":0.2,"ipcc":false,"plugins":[],"schemaVersion":"2","origins":[{"domain":"dialpad.com","includeSubdomains":true},{"domain":"vercel.app","includeSubdomains":true},{"domain":"dialpadbeta.com","includeSubdomains":true}],"classicAccess":false}';
var iOverride = {};
(function(){"use strict";try{if(typeof document!="undefined"){var e=document.createElement("style");e.appendChild(document.createTextNode("")),document.head.appendChild(e)}}catch(t){console.error("vite-plugin-css-injected-by-js",t)}})();
var __defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__pow=Math.pow,__defNormalProp=(e,t,i)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,__spreadValues=(e,t)=>{for(var i in t||(t={}))__hasOwnProp.call(t,i)&&__defNormalProp(e,i,t[i]);if(__getOwnPropSymbols)for(var i of __getOwnPropSymbols(t))__propIsEnum.call(t,i)&&__defNormalProp(e,i,t[i]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__objRest=(e,t)=>{var i={};for(var s in e)__hasOwnProp.call(e,s)&&t.indexOf(s)<0&&(i[s]=e[s]);if(null!=e&&__getOwnPropSymbols)for(var s of __getOwnPropSymbols(e))t.indexOf(s)<0&&__propIsEnum.call(e,s)&&(i[s]=e[s]);return i},__publicField=(e,t,i)=>(__defNormalProp(e,"symbol"!=typeof t?t+"":t,i),i),__async=(e,t,i)=>new Promise(((s,n)=>{var r=e=>{try{a(i.next(e))}catch(t){n(t)}},o=e=>{try{a(i.throw(e))}catch(t){n(t)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,o);a((i=i.apply(e,t)).next())}));!function(){"use strict";var e,t,i={},s={},n={};Object.defineProperty(n,"__esModule",{value:!0}),n.LogLevel=void 0,(t=e||(n.LogLevel=e={}))[t.OFF=5]="OFF",t[t.ERROR=4]="ERROR",t[t.WARN=3]="WARN",t[t.INFO=2]="INFO",t[t.DEBUG=1]="DEBUG",t[t.TRACE=0]="TRACE",Object.defineProperty(s,"__esModule",{value:!0}),s.BasicConsoleLogger=void 0;var r=n,o=function(){function e(e,t){this.prefix="",this.theConsole=e,this.minimumLogLevel=t}return e.prototype.getLevel=function(){return this.minimumLogLevel},e.prototype.error=function(e,t){this.minimumLogLevel<=r.LogLevel.ERROR&&this.theConsole.error("[".concat(t,"] ").concat(this.format(e)))},e.prototype.warn=function(e,t){this.minimumLogLevel<=r.LogLevel.WARN&&this.theConsole.warn("[".concat(t,"] ").concat(this.format(e)))},e.prototype.info=function(e,t){if(this.minimumLogLevel<=r.LogLevel.INFO){var i=void 0!==t?"[".concat(t,"] "):"";this.theConsole.info("".concat(i).concat(this.format(e)))}},e.prototype.debug=function(e,t){if(this.minimumLogLevel<=r.LogLevel.DEBUG){var i=void 0!==t?"[".concat(t,"] "):"";(this.theConsole.debug?this.theConsole.debug:this.theConsole.log)("".concat(i).concat(this.format(e)))}},e.prototype.group=function(e){this.minimumLogLevel<=r.LogLevel.DEBUG&&(this.prefix+="⎢ ",this.theConsole.log("[1m"+this.format(e)+"[22m"))},e.prototype.groupEnd=function(){this.minimumLogLevel<=r.LogLevel.DEBUG&&(this.prefix=this.prefix.slice(0,-2))},e.prototype.time=function(e){this.minimumLogLevel<=r.LogLevel.DEBUG&&this.theConsole.time(this.format(e))},e.prototype.timeEnd=function(e){this.minimumLogLevel<=r.LogLevel.DEBUG&&this.theConsole.timeEnd(this.format(e))},e.prototype.format=function(e){return e.toString().replace(/^/gm,this.prefix)},e}();s.BasicConsoleLogger=o;var a={};Object.defineProperty(a,"__esModule",{value:!0}),a.NO_OP_LOGGER=void 0;var l=n;a.NO_OP_LOGGER=function(){return{getLevel:function(){return l.LogLevel.OFF},error:function(){},warn:function(){},info:function(){},debug:function(){},group:function(){},groupEnd:function(){},time:function(){},timeEnd:function(){}}}();var c={};Object.defineProperty(c,"__esModule",{value:!0}),c.GroupConsoleLogger=void 0;var d=n,u=function(){function e(e,t){this.theConsole=e,this.minimumLogLevel=t}return e.prototype.getLevel=function(){return this.minimumLogLevel},e.prototype.error=function(e,t){this.minimumLogLevel<=d.LogLevel.ERROR&&this.theConsole.error("[".concat(t,"] ").concat(e))},e.prototype.warn=function(e,t){this.minimumLogLevel<=d.LogLevel.WARN&&this.theConsole.warn("[".concat(t,"] ").concat(e))},e.prototype.info=function(e,t){if(this.minimumLogLevel<=d.LogLevel.INFO){var i=void 0!==t?"[".concat(t,"] "):"";this.theConsole.info("".concat(i).concat(e))}},e.prototype.debug=function(e,t){if(this.minimumLogLevel<=d.LogLevel.DEBUG){var i=void 0!==t?"[".concat(t,"] "):"";(this.theConsole.debug?this.theConsole.debug:this.theConsole.log)("".concat(i).concat(e))}},e.prototype.group=function(e){this.minimumLogLevel<=d.LogLevel.DEBUG&&this.theConsole.group(e)},e.prototype.groupEnd=function(){this.minimumLogLevel<=d.LogLevel.DEBUG&&this.theConsole.groupEnd()},e.prototype.time=function(e){this.minimumLogLevel<=d.LogLevel.DEBUG&&this.theConsole.time(e)},e.prototype.timeEnd=function(e){this.minimumLogLevel<=d.LogLevel.DEBUG&&this.theConsole.timeEnd(e)},e}();c.GroupConsoleLogger=u,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.GroupConsoleLogger=e.NO_OP_LOGGER=e.LogLevel=e.BasicConsoleLogger=void 0;var t=s;Object.defineProperty(e,"BasicConsoleLogger",{enumerable:!0,get:function(){return t.BasicConsoleLogger}});var i=n;Object.defineProperty(e,"LogLevel",{enumerable:!0,get:function(){return i.LogLevel}});var r=a;Object.defineProperty(e,"NO_OP_LOGGER",{enumerable:!0,get:function(){return r.NO_OP_LOGGER}});var o=c;Object.defineProperty(e,"GroupConsoleLogger",{enumerable:!0,get:function(){return o.GroupConsoleLogger}})}(i);const h=e=>"[object Array]"===Object.prototype.toString.call(e),g=e=>"boolean"==typeof e,p=e=>"number"==typeof e,m=e=>"[object Object]"===Object.prototype.toString.call(e),v=e=>"[object String]"===Object.prototype.toString.call(e),b=e=>v(e)&&e.length>0,f=e=>void 0===e,E=e=>v(e)&&/^[\da-f]{8}-[\da-f]{4}-4[\da-f]{3}-[89ab][\da-f]{3}-[\da-f]{12}$/.test(e),_=e=>{try{return b(e)&&/^[\u0020-\u007E]+$/.test(e)}catch(t){return!1}},y=(e,t)=>!!m(e)&&Object.keys(e).every((e=>t.includes(e))),S="imize-extension",w="imize-field-name";var I=(e=>(e.backgroundColor="backgroundColor",e.backgroundImage="backgroundImage",e.backgroundPosition="backgroundPosition",e.backgroundRepeat="backgroundRepeat",e.backgroundSize="backgroundSize",e.borderColor="borderColor",e.borderStyle="borderStyle",e.borderWidth="borderWidth",e.borderRadius="borderRadius",e.bottom="bottom",e.boxShadow="boxShadow",e.clear="clear",e.color="color",e.cursor="cursor",e.direction="direction",e.float="float",e.fontFamily="fontFamily",e.fontSize="fontSize",e.fontWeight="fontWeight",e.left="left",e.letterSpacing="letterSpacing",e.lineHeight="lineHeight",e.listStyleType="listStyleType",e.listStylePosition="listStylePosition",e.height="height",e.minHeight="minHeight",e.maxHeight="maxHeight",e.width="width",e.minWidth="minWidth",e.maxWidth="maxWidth",e.visibility="visibility",e.display="display",e.position="position",e.margin="margin",e.marginLeft="marginLeft",e.marginRight="marginRight",e.marginTop="marginTop",e.marginBottom="marginBottom",e.opacity="opacity",e.overflow="overflow",e.padding="padding",e.paddingLeft="paddingLeft",e.paddingRight="paddingRight",e.paddingTop="paddingTop",e.paddingBottom="paddingBottom",e.right="right",e.textAlign="textAlign",e.textShadow="textShadow",e.textTransform="textTransform",e.top="top",e.wordSpacing="wordSpacing",e.zIndex="zIndex",e))(I||{});const C=new Set(["beforebegin","afterbegin","beforeend","afterend"]),T=e=>v(e)&&C.has(e),x=e=>m(e)&&T(e.insertPosition)&&A(e.selector)&&y(e,["insertPosition","selector"]),A=_;var F=(e=>(e.UNAPPLIED="UNAPPLIED",e.APPLIED="APPLIED",e.ERROR="ERROR",e))(F||{});const k=e=>!!m(e)&&Object.keys(e).every((t=>t in I&&"string"==typeof e[t])),P=new Set(["rich-text","html-block","video","image","heading"]),M=e=>v(e)&&P.has(e);let D=class extends Error{constructor(e){super(e),this.name="UnexpectedNullException"}};function N(e,t){if(null==e||"string"==typeof e&&0===e.length)throw new D(t);return e}let O=class extends Error{constructor(e){super(e),this.name="IllegalArgumentException"}};function L(e,t){if(N(e),!e)throw new O(t)}let R=class extends Error{constructor(e){super(e),this.name="IllegalStateException"}};function U(e,t){if(N(e),!e)throw new R(t)}let $=class extends Error{constructor(e){super(e),this.name="NoSuchElementException"}};var V=Object.defineProperty,W=(e,t,i)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,z=(e,t,i)=>(W(e,"symbol"!=typeof t?t+"":t,i),i);let j=class{static empty(){return H.INSTANCE}static of(e){return new B(N(e))}static ofNullable(e){return null==e||"string"==typeof e&&0===e.length?H.INSTANCE:new B(e)}};class B extends j{constructor(e){super(),z(this,"value"),this.value=e}isPresent(){return!0}ifPresent(e){return e(this.value),this}ifAbsent(e){return this}get(){return this.value}orElse(e){return this.value}orElseRun(e){return this.value}toArray(){return[this.value]}map(e){return j.ofNullable(e(this.value))}flatMap(e){return e(this.value)}toString(){return`Optional[${this.value.toString()}]`}}const G=class extends j{isPresent(){return!1}ifPresent(e){return this}ifAbsent(e){return e(),this}get(){throw new $("No value present")}orElse(e){return e}orElseRun(e){return e()}toArray(){return[]}map(e){return j.empty()}flatMap(e){return j.empty()}toString(){return"Optional.empty"}};let H=G;z(H,"INSTANCE",new G);const Q=j.ofNullable("v5.d671d7cd").orElse("NA"),K=1e3,q=50,NO_PROGRESS_TOLERANCE_TIME_MS=48e4,Y=1800,J=86400,X=3600,Z=500,ee=1e3,te=1,ie="api.intellimize.co",se="log.intellimize.co",ne="intellimize_api_",re="intellimize_attributes_",oe="intellimize_integrations_",ae="intellimize_",le="intellimize_policy_",ce="intellimize_server_context_",de="intellimize_activity_",ue="intellimize_user_",he="intellimize_user_tracking_choice_",ge="intellimize_tracking_policy",pe="intellimize_data_tracking_type",me="intellimize_opt_out_",ve="always",be="optOut",fe="disabled",Ee="allow",_e="deny",ye="none",Se="intellimize_shopify_",we="intellimize_status_module_",Ie="_mkto_trk",Ce="hubspotutk",Te="wf",xe=`data-${Te}-`,Ae=`${xe}experience-`,Fe=`${xe}style-experience-`,ke=`${xe}variation-`,Pe=i.LogLevel.WARN,Me=i.LogLevel.OFF,De=["utt","utm","uts","utcn","utcm"],Ne="mkt_tok",Oe="_hsenc";function Le(e,t,i){try{return Re(e,t,i)}catch(s){return"CAN_NOT_SERIALIZE"}}function Re(e,t,i){let s;if(void 0!==Array.prototype.toJSON&&"[3]"!==JSON.stringify([3])){const n=Array.prototype.toJSON;delete Array.prototype.toJSON,s=JSON.stringify(e,t,i),Array.prototype.toJSON=n}else s=JSON.stringify(e,t,i);return s}function Ue(e){return JSON.parse(e)}function $e(e){const t={type:e.type,target:We(e.target)};return e.addedNodes&&e.addedNodes.length>0&&(t.addedNodes=Array.prototype.map.call(e.addedNodes,We)),e.removedNodes&&e.removedNodes.length>0&&(t.removedNodes=Array.prototype.map.call(e.removedNodes,We)),e.attributeName&&(t.attributeName=e.attributeName),e.oldValue&&(t.oldValue=e.oldValue),Re(t)}function Ve(e){return Re(We(e))}function We(e){const t={nodeName:e.nodeName};return null!==e.textContent&&e.textContent.length<500&&(t.textContent=e.textContent),e instanceof Element&&(e.id&&(t.id=e.id),e.classList&&e.classList.length>0&&(t.classList=e.classList),e.attributes&&e.attributes.length>0&&(t.attributes=ze(e.attributes))),t}function ze(e){const t=e.length,i=[];for(let s=0;s<t;s+=1){const t=e.item(s);null!==t&&i.push({name:t.name,value:t.value})}return i}function je(e,t){const i=e.getWindow().atob(t),s=Ge(e,i);return decodeURIComponent(s)}function Be(e,t){return Ue(je(e,t))}function Ge(e,t){const i=[...e.getWindow().atob("YmdKTnQ=")],s=[];for(let n=0;n<t.length;n++){const e=t.codePointAt(n)^i[n%i.length].codePointAt(0);s.push(String.fromCodePoint(e))}return s.join("")}function He(e){return e.getWindow().atob("Y1B1YmdKTnQ=")}function Qe(e){let t;if(void 0!==e)t=e;else{if("undefined"==typeof window)return i.NO_OP_LOGGER;t=window}const s=Je(t);return s===i.LogLevel.OFF?i.NO_OP_LOGGER:"group"in t.console?new Ke(t,s):new qe(t,s)}class Ke extends i.GroupConsoleLogger{constructor(e,t){super(e.console,t),__publicField(this,"idebFilters"),this.idebFilters=tt(e)}debug(e,t,i,s){Ye(this.idebFilters,s)&&super.debug(e,t,i)}info(e,t,i,s){Ye(this.idebFilters,s)&&super.info(e,t,i)}warn(e,t,i,s){Ye(this.idebFilters,s)&&super.warn(e,t,i)}error(e,t,i,s){Ye(this.idebFilters,s)&&super.error(e,t,i)}group(e,t){Ye(this.idebFilters,t)&&super.group(e)}groupEnd(e){Ye(this.idebFilters,e)&&super.groupEnd()}time(e,t){Ye(this.idebFilters,t)&&super.time(e)}timeEnd(e,t){Ye(this.idebFilters,t)&&super.timeEnd(e)}}class qe extends i.BasicConsoleLogger{constructor(e,t){super(e.console,t),__publicField(this,"idebFilters"),this.idebFilters=tt(e)}debug(e,t,i,s){Ye(this.idebFilters,s)&&super.debug(e,t,i)}info(e,t,i,s){Ye(this.idebFilters,s)&&super.info(e,t,i)}warn(e,t,i,s){Ye(this.idebFilters,s)&&super.warn(e,t,i)}error(e,t,i,s){Ye(this.idebFilters,s)&&super.error(e,t,i)}group(e,t){Ye(this.idebFilters,t)&&super.group(e)}groupEnd(e){Ye(this.idebFilters,e)&&super.groupEnd()}time(e,t){Ye(this.idebFilters,t)&&super.time(e)}timeEnd(e,t){Ye(this.idebFilters,t)&&super.timeEnd(e)}}function Ye(e,t){const i=[],s=[];for(const n of e)n.startsWith("!")?s.push(n.slice(1)):i.push(n);return void 0===t&&0===i.length||(!0===(null==t?void 0:t.some((e=>i.includes(e))))||!0!==(null==t?void 0:t.some((e=>s.includes(e))))&&(!(i.length>0)||void 0!==t&&!t.every((e=>!i.includes(e)))))}function Je(e){let t=Xe(e);return t.isPresent()?t.get():(t=Ze(e),t.isPresent()?t.get():(t=et(e),t.isPresent()?t.get():Me))}function Xe(e){var t,i;return j.ofNullable(null==(i=null==(t=null==e?void 0:e.location)?void 0:t.search)?void 0:i.slice(1)).flatMap((e=>j.ofNullable(/(?:^|\?|&)ideb=([0-5])(?:$|&|#)/.exec(e)).map((e=>Number.parseInt(e[1],10)))))}function Ze(e){var t;return j.ofNullable(null==(t=null==e?void 0:e.localStorage)?void 0:t.getItem("ideb")).flatMap((e=>j.ofNullable(/^([0-5])$/.exec(e)).map((e=>Number.parseInt(e[1],10)))))}function et(e){var t;return j.ofNullable(null==(t=null==e?void 0:e.iOverride)?void 0:t.logLevel)}function tt(e){var t;return j.ofNullable(null==(t=null==e?void 0:e.localStorage)?void 0:t.getItem("ideb_filters")).map((e=>e.split(","))).orElse([])}class it{constructor({name:e,namespace:t}){__publicField(this,"name"),__publicField(this,"namespace"),this.name=e,this.namespace=t}getId(){return{name:this.name,namespace:this.namespace}}getName(){return this.name}getNamespace(){return this.namespace}getValue(e){return e.getAttributeValue(this)}}class st{constructor(e){__publicField(this,"audience"),this.audience=e}getId(){return this.audience.getId()}getName(){return this.audience.getName()}getState(){return this.audience.getState()}getCondition(){return this.audience.getCondition()}addExperience(e){this.audience.addExperience(e)}addVariation(e){this.audience.addVariation(e)}getCode(){return this.audience.getCode()}getExperiences(){return this.audience.getExperiences()}getVariations(){return this.audience.getVariations()}isInclude(){return!1}}class nt{constructor(e,t,i,s,n){__publicField(this,"logger"),__publicField(this,"json"),__publicField(this,"filter"),__publicField(this,"newAggregator"),__publicField(this,"operation"),this.json=e,this.filter=t,this.newAggregator=i,this.logger=n,this.operation=s}evaluate(e){this.logger.group("ActivityCondition.evaluate()"),this.logger.debug(`activity condition config: ${JSON.stringify(this.json)}`);const t=e.getActivities();this.logger.debug(`activities: ${JSON.stringify(t)}`),this.logger.info(`activity count: ${t.length}`);const i=t.filter((e=>this.filter.evaluate(e)));this.logger.debug(`filtered activities: ${JSON.stringify(i)}`),this.logger.info(`filtered activities count: ${i.length}`);const s=this.newAggregator();i.forEach((e=>{s.accept(e)}));const n=s.result();this.logger.info(`aggregator result: ${n}`);const r=this.operation(j.of(n));return this.logger.info(`condition result: ${r}`),this.logger.groupEnd(),r}toJSON(){return JSON.parse(JSON.stringify(this.json))}}class rt{constructor(e,t){__publicField(this,"conditions"),this.conditions=e.conditions.map((e=>t.buildCondition(e)))}getConditions(){return this.conditions}evaluate(e){return this.conditions.every((t=>t.evaluate(e)))}toJSON(){return{conditions:this.conditions.map((e=>e.toJSON())),type:"logic-and"}}}class ot{constructor(e,t,i){__publicField(this,"audience");const s=t.getAudience(e.audienceId);this.audience=!0===i?new st(s):s}getAudience(){return this.audience}evaluate(e){return this.audience.getCondition().evaluate(e)}toJSON(){return{audienceId:this.audience.getId(),type:"audience"}}}class at{constructor(e){__publicField(this,"code"),this.code=e.code}getCode(){return this.code}evaluate(e){return e.evalBoolean(this.code)}toJSON(){return{code:this.code,type:"code"}}}class lt{constructor(e,t,i){__publicField(this,"json"),__publicField(this,"attributeDefinition"),__publicField(this,"operation"),this.json=e,this.attributeDefinition=t,this.operation=i}getAttributeDefinition(){return this.attributeDefinition}getComparisonOperation(){return this.operation}evaluate(e){return this.operation(this.attributeDefinition.getValue(e))}toJSON(){return JSON.parse(JSON.stringify(this.json))}}class ct{constructor(e,t){__publicField(this,"condition");const i="audience"===e.condition.type;this.condition=t.buildCondition(e.condition,i)}getCondition(){return this.condition}evaluate(e){return!this.condition.evaluate(e)}toJSON(){return{condition:this.condition.toJSON(),type:"logic-not"}}}class dt{constructor(e,t){__publicField(this,"conditions"),this.conditions=e.conditions.map((e=>t.buildCondition(e)))}getConditions(){return this.conditions}evaluate(e){return this.conditions.some((t=>t.evaluate(e)))}toJSON(){return{conditions:this.conditions.map((e=>e.toJSON())),type:"logic-or"}}}class ut{constructor(e,t,i){var s;if(__publicField(this,"id"),__publicField(this,"name"),__publicField(this,"state"),__publicField(this,"condition"),__publicField(this,"experiences",[]),__publicField(this,"variations",[]),this.id=e,this.name=t.name,this.state=null!=(s=t.state)?s:"live",void 0!==t.code&&void 0!==t.condition)this.condition=i.buildCondition({conditions:[{code:t.code,type:"code"},t.condition],type:"logic-and"});else if(void 0!==t.code)this.condition=i.buildCondition({code:t.code,type:"code"});else{if(void 0===t.condition)throw new D('Unable to build condition for audience. No "code" or "condition" property.');this.condition=i.buildCondition(t.condition)}}getId(){return this.id}getName(){return this.name}getState(){return this.state}getCondition(){return this.condition}addExperience(e){this.experiences.push(e)}addVariation(e){this.variations.push(e)}getCode(){return this.condition instanceof at?j.of(this.condition.getCode()):j.empty()}getExperiences(){return[...this.experiences]}getVariations(){return[...this.variations]}isInclude(){return!0}}const ht=["standard","custom","url_param","marketo","salesforce","firmographic","6sense","googleAds","demandbase","hubspot"];var gt=(e=>(e.ATTRIBUTE="ATTRIBUTE",e.CUSTOM_CODE="CUSTOM_CODE",e.INSERT_ELEMENT="INSERT_ELEMENT",e.EXTENSION="EXTENSION",e.EXTENSION_ATTRIBUTE="EXTENSION_ATTRIBUTE",e.MOVE_ELEMENT="MOVE_ELEMENT",e.WEBFLOW="WEBFLOW",e))(gt||{});const pt=["checkout_completed","checkout_started","collection_viewed","product_added_to_cart","product_viewed","search_submitted"],mt="118",vt="exists",bt="notExists";var ft=function e(t){return Object.freeze(t),Object.getOwnPropertyNames(t).forEach((function(i){!t.hasOwnProperty(i)||null===t[i]||"object"!=typeof t[i]&&"function"!=typeof t[i]||Object.isFrozen(t[i])||e(t[i])})),t};function Et(e,t){const i=e.getReferrerUrl();if(!i.isPresent())return j.of(!1);const s=i.get(),n=e.getHostingPageUrl();return s.getOrigin()===n.getOrigin()?j.of(!0):_t(t,s.getOrigin())}function _t(e,t){const i=e.getCrossOrigin().isEnabled(),s=e.getOrigins().length>0;if(!i&&!s)return j.empty();const n=e.getCrossOrigin().isOriginAllowed(t),r=e.getOrigins().some((e=>e.isOriginAllowed(t)));return i&&s&&n!==r&&Mo.error(`Customer crossOrigin ${n} does not match origins ${r} for origin ${t}`,251),j.of(n||r)}function yt(e,t){return new URL(`https://${e}${t}`)}class St{constructor(e){__publicField(this,"optionalUrl"),__publicField(this,"optionalOrigin"),__publicField(this,"urlParamsUnsafe"),this.optionalUrl=St.sanitizeUrl(e);const t=!0===(null==e?void 0:e.includes("?"))?e.slice(e.indexOf("?")):void 0;if(this.urlParamsUnsafe=ft(St.parseQueryStringUnsafe(t)),this.optionalOrigin=j.empty(),this.optionalUrl.isPresent()){const e=this.optionalUrl.get();try{const{origin:t}=new URL(e);this.optionalOrigin=j.ofNullable(t)}catch(i){const e=new Ei("Could not construct URL to determine origin",i);Mo.warn(e,135)}}else Mo.debug("cannot determine url origin")}static parseQueryStringUnsafe(e){const t={};return void 0!==e&&e.length>0&&e.slice(1).split("&").forEach((e=>{try{if(void 0!==e&&e.length>0){const i=e.split("=",2),s=decodeURIComponent(i[0]);if(!Object.prototype.hasOwnProperty.call(t,s)){let e="";void 0!==i[1]&&(e=decodeURIComponent(i[1].replace(/\+/g," "))),t[s]=e}}}catch(i){const e=fi(i);Mo.warn(e,29)}})),t}static sanitizeUrl(e){try{if(void 0!==e)return j.ofNullable(decodeURI(e))}catch(t){Mo.error(`Url could not be decoded: '${e}'`,44)}return j.ofNullable(e)}getAllUrlParamsUnsafe(){return this.urlParamsUnsafe}getUrl(){return this.optionalUrl}getOrigin(){return this.optionalOrigin}}class wt extends St{constructor(e){super(e),__publicField(this,"urlParamsEncoded"),__publicField(this,"utmMedium"),__publicField(this,"utmSource"),this.urlParamsEncoded=ft(wt.standardUrlParameterParser(e)),this.utmSource=this.urlParamsUnsafe.utm_source,this.utmMedium=this.urlParamsUnsafe.utm_medium}static standardUrlParameterParser(e){const t={};if(void 0===e)return t;const i=e.indexOf("#");let s=e.length-1;-1!==i&&(s=i-1);const n=e.indexOf("?"),r=n+1;return-1===n||r>=s||e.slice(r,s+1).split("&").filter((e=>void 0!==e&&e.length>0)).forEach((e=>{let i=e,s="";const n=e.indexOf("=");-1!==n&&(i=e.slice(0,Math.max(0,n)),s=e.slice(n+1)),void 0===t[i]?t[i]=[s]:t[i].push(s)})),t}get emailTagRegExp(){return/email|^send|hs_em|[\W_^]mail[\W_$]|sms|newsletter|^em[\W_$]|^attentive$/}get searchTagRegExp(){return/(search|bing|google|googlepla|sem|adwords)/i}get socialTagRegExp(){return/facebook|[\W_^]fb[\W_$]|youtube|[\W_^]ig[\W_$]|twitter|igstories|^tw$|twtr|profile|pinterest|instagram|socialmedia|quora|newsfeed|reddit|social|influencer|yelp|snapchat|linkedin|googleplus|g\+|gplus|google\+/i}get paidSocialRegExp(){return/influencer|ctc|ocpm|smm|socialp|cbo|bid|paidsocial/i}get organicSocialRegExp(){return/organic|socialo|stories|story|profile/i}get paidRegExp1(){return/(placement|attentive|taboola|gdn|criteo|googlepla|mrkt_adwords|outbrain|shareasale|klaviyo|inboxdollars|liveintent|adwords|ads|listrak|gsp|bluecore)/i}get paidRegExp2(){return/(cpc|affiliate|paid|smm|sem|ocpm|adroll|reengagement|remarketing|retargeting|cpa|paids|native|ppc|leads|display|[\W_^]ad[\W_$]|cpm|advertising|channel_partner|sponsored)/i}get searchIdRegExp(){return/([?&])(gclid|msclkid)=/i}get socialIdRegEx(){return/([?&])fbclid=/i}getAllUrlParamsEncoded(){return this.urlParamsEncoded}getSourceFromAllParams(e){let t=!1;for(const i of Object.keys(this.urlParamsUnsafe))if(Object.prototype.hasOwnProperty.call(this.urlParamsUnsafe,i)){const s=this.urlParamsUnsafe[i];if(t=t||this.isParamPaid(s),this.isParamSocialTagged(s))return e||t||this.isParamSocialPaid(s)?"CP":"CO";if(this.isParamSearchTagged(s))return e||t||this.isSearchPaid()?"SP":"SO";if(this.getUrl().isPresent()&&this.isParamEmailTagged(s))return"EM"}return t?"OP":"OT"}isSocialTagged(){return!(!this.optionalUrl.isPresent()||!this.socialIdRegEx.test(this.optionalUrl.get()))||(this.isParamSocialTagged(this.utmMedium)||this.isParamSocialTagged(this.utmSource))}isSocialPaid(){return this.isParamSocialPaid(this.utmMedium)||this.isParamSocialPaid(this.utmSource)}isSearchTagged(e){return!(!this.optionalUrl.isPresent()||!this.searchIdRegExp.test(this.optionalUrl.get()))||(!(!this.isParamSearchTagged(this.utmSource)&&!this.isParamSearchTagged(this.utmMedium))||e.getUrl().isPresent()&&e.isFromSearchEngine()&&(Boolean(this.utmMedium)||Boolean(this.utmSource)))}isSearchPaid(){return!(!this.optionalUrl.isPresent()||!this.searchIdRegExp.test(this.optionalUrl.get()))||(this.isParamSearchPaid(this.utmMedium)||this.isParamSearchPaid(this.utmSource))}isPaidTraffic(){return this.isParamPaid(this.utmSource)||this.isParamPaid(this.utmMedium)}isEmailTagged(){return this.isParamEmailTagged(this.utmSource)||this.isParamEmailTagged(this.utmMedium)}isParamPaid(e){return void 0!==e&&(this.paidRegExp1.test(e)||this.paidRegExp2.test(e))}isParamEmailTagged(e){return void 0!==e&&this.emailTagRegExp.test(e)}isParamSearchTagged(e){return void 0!==e&&this.searchTagRegExp.test(e)}isParamSearchPaid(e){return void 0!==e&&/googlep|adwords/.test(e)}isParamSocialPaid(e){return(void 0===e||!this.organicSocialRegExp.test(e))&&(void 0!==e&&this.paidSocialRegExp.test(e))}isParamSocialTagged(e){return void 0!==e&&this.socialTagRegExp.test(e)}}class It extends St{get emailDomainRegExp(){return/([/.])(webmail|live|mail|inbox|zimbra|email|em)\d{0,2}\.|google.android.gm/i}get socialDomainRegExp(){return/([/.])(t\.co|(plus\.google|facebook|twitter|linkedin|youtube|instagram|pinterest|quora|reddit|github|medium|producthunt|yelp|ycombinator|messenger)\.)/i}get paidDomainRegExp(){return/([/.])(taboola|aimtell|attentive|googlesyndication|shareasale|outbrain|shareasale-analytics|doubleclick)\./i}get invalidSearchRegExp(){return/([/.])((plus|mail|myactivity|chrome|classroom|accounts|news|docs|keep|sites)\.google|googlesyndication|google\.android\.(?!googlequicksearchbox))/i}get paidSearchRegExp(){return/([/.])(google|googleadservices)\..*?\/aclk\?/i}get validSearchRegExp(){return/([/.])(google|bing|baidu|duckduckgo|ask|search\.(yahoo|xfinity|aol|naver))\./i}isFromSearchEngine(){return this.optionalUrl.isPresent()&&!this.invalidSearchRegExp.test(this.optionalUrl.get())&&this.validSearchRegExp.test(this.optionalUrl.get())}isFromEmail(){return this.optionalUrl.isPresent()&&this.emailDomainRegExp.test(this.optionalUrl.get())}isFromSocialNetwork(){return this.optionalUrl.isPresent()&&this.socialDomainRegExp.test(this.optionalUrl.get())}isPaidTraffic(){return this.optionalUrl.isPresent()&&this.paidDomainRegExp.test(this.optionalUrl.get())}isSearchPaid(){return this.optionalUrl.isPresent()&&!this.invalidSearchRegExp.test(this.optionalUrl.get())&&this.paidSearchRegExp.test(this.optionalUrl.get())}}function Ct(e,t){const i=e.getReferrerUrl(),s=e.getHostingPageUrl(),n=new It(i.map((e=>e.getRawUrl())).orElse(void 0)),r=new wt(s.getRawUrl()),o=n.isPaidTraffic()||r.isPaidTraffic();let a=Tt(n,r,o);return"OT"===a&&(a=xt(n,r,o)),"OT"===a&&(a=r.getSourceFromAllParams(o)),"OT"===a&&(a=o?"OP":n.getUrl().isPresent()?Et(e,t).orElse(!1)?"RC":"OT":"DN"),a}function Tt(e,t,i){return e.isFromEmail()?"EM":e.isFromSocialNetwork()?i||t.isSocialPaid()?"CP":"CO":e.isFromSearchEngine()?i||t.isSearchTagged(e)||e.isSearchPaid()||t.isSearchPaid()?"SP":"SO":"OT"}function xt(e,t,i){return t.isSocialTagged()?i||t.isSocialPaid()?"CP":"CO":t.isSearchTagged(e)?i||t.isSearchPaid()?"SP":"SO":t.getUrl().isPresent()&&t.isEmailTagged()?"EM":"OT"}function At(){return[ae,re,le,ne,oe,ue,ce,de,we]}function Ft(e){return At().map((t=>`${t}${e.getId()}`))}function kt(e){return At().some((t=>e.startsWith(t)))}function Pt(e){return e instanceof DOMException&&("QuotaExceededError"===e.name||"QUOTA_EXCEEDED_ERROR"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)}function Mt(e,t){let i=!1;try{const s=e.getWindow();if(t in s){const e="_test_",n="_test_val_";s[t].setItem(e,n);const r=s[t].getItem(e);s[t].removeItem(e),null!==r&&r===n&&(i=!0)}}catch(s){}return i}class Dt{static evalBoolean(e){const t=Dt.eval(e);if("boolean"==typeof t)return t;throw new TypeError("Expected boolean response, but got "+typeof t+": "+t)}static evalString(e){const t=Dt.eval(e);if("string"==typeof t)return t;throw new TypeError("Expected string response, but got "+typeof t+": "+t)}static eval(e){return Mo.info(`eval code: ${e}`),(0,eval)('"use strict";\n'+Dt.reformatJqueryCode(e))}static injectCss(e,t,i){Mo.info(`inject css: ${e}`);const s=`intellimize-${t}`,n=i.querySelectorAll(`head #${s}`),[r]=n;if(void 0===r){const t=i.createElement("style");t.id=s,t.className="iStyleBlock",t.append(e),i.head.append(t)}else r.innerHTML=e}static removeCss(e,t){var i;Mo.info(`Remove css: ${e}`);const s=`intellimize-${e}`;null==(i=t.querySelector(`head #${s}`))||i.remove()}static executeFunction(e,t){try{e.call(void 0)}catch(i){const e=new Ei("Caught exception in function callback",i);Mo.error(e,t)}}static reformatJqueryCode(e){return`var $ = (window.intellimize && window.intellimize.plugins && window.intellimize.plugins.jquery) ? window.intellimize.plugins.jquery : window.$;\n${e}`}}var Nt=(e=>(e[e.NOT_STARTED=0]="NOT_STARTED",e[e.TRY_LATER=1]="TRY_LATER",e[e.WAITING=2]="WAITING",e[e.SUCCEEDED=3]="SUCCEEDED",e[e.FAILED=4]="FAILED",e[e.CANCELED=5]="CANCELED",e))(Nt||{});const Ot=new Set([0,1,2]),Lt=class{constructor(){__publicField(this,"manager"),__publicField(this,"id"),__publicField(this,"doneFunctions",[]),__publicField(this,"currentStatus",0),this.id=++Lt.lastId}static isDone(e){return!Ot.has(e)}getId(){return this.id}setManager(e){this.manager=e}cancel(){this.currentStatus=5,this.cleanup()}cleanup(){}runWrapper(){Mo.group(this.toString()+".run()");const e=this.currentStatus;try{this.currentStatus=this.run()}catch(i){const e=new Ei("Task exception",i);Mo.error(e,179,this.getEntityIds()),this.currentStatus=4}const t=this.currentStatus;return!Lt.isDone(e)&&Lt.isDone(t)&&this.executeDoneFunctions(),Mo.groupEnd(),this.currentStatus}onDone(e){this.isDone()?Dt.executeFunction(e,153):this.doneFunctions.push(e)}getStatus(){return this.currentStatus}isDone(){return!Ot.has(this.currentStatus)}getNotDoneDependencies(){return[]}isBlocked(){if(2===this.currentStatus)return!0;return this.getNotDoneDependencies().filter((e=>e.isBlocked())).length>0}toString(){return`Task(${this.id})`}getEntityIds(){return{}}setStatusTryLater(){this.currentStatus=1}executeDoneFunctions(){for(let e=this.doneFunctions.shift();void 0!==e;e=this.doneFunctions.shift())Dt.executeFunction(e,154)}};let Rt=Lt;__publicField(Rt,"lastId",0);const Ut=["xo"],$t={debug(e,t,i){Mo.debug(e,t,i,Ut)}};class Vt extends Rt{constructor(e,t,i){super(),__publicField(this,"localStorage"),__publicField(this,"storageKey"),__publicField(this,"storageDatum"),this.localStorage=e,this.storageKey=t,this.storageDatum=i}toString(){return`StorageCleanupTask(${this.storageKey})`}inTransaction(){return!1}isUnloading(){return!1}getStorageKey(){return this.storageKey}setStorageDatum(e){this.storageDatum=void 0===e?void 0:__spreadValues({},e)}getNotDoneDependencies(){return this.manager.findTasks((e=>!(e instanceof Vt||e.isUnloading()||e.isDone())))}run(){return $t.debug(`Running storage cleanup task for "${this.storageKey}".`),this.getNotDoneDependencies().length>0?($t.debug("Deferring storage cleanup; other tasks still running"),Nt.TRY_LATER):(void 0===this.storageDatum?($t.debug("Deleting data locally because the common origin is empty for this storageKey"),this.localStorage.delete(this.storageKey)):($t.debug(`Writing latest data from the common origin to the customer origin. ${Le(this.storageDatum,void 0,2)}`),this.localStorage.write(this.storageKey,Re(this.storageDatum))),Nt.SUCCEEDED)}}class Wt{constructor(e,t){__publicField(this,"localStorage"),__publicField(this,"taskManager"),this.localStorage=e,this.taskManager=t}run(){return __async(this,null,(function*(){return new Promise((e=>{this.taskManager.isDone()?($t.debug("[CleanupTaskManager]: no tasks to run"),e()):($t.debug("[CleanupTaskManager]: waiting for all tasks to complete"),this.taskManager.onDone((()=>{e()})))}))}))}queueCleanupTask(e,t){this.findNotDoneCleanupTask(e).ifPresent((i=>{$t.debug(`Cleanup task already exists for ${e}; updating storageDatum`),i.setStorageDatum(t)})).ifAbsent((()=>{$t.debug(`Creating cleanup task for ${e} with ${Le(t,void 0,2)}`),this.taskManager.addTask(new Vt(this.localStorage,e,t))}))}findNotDoneCleanupTask(e){const[t]=this.taskManager.findTasks((t=>t instanceof Vt&&t.getStorageKey()===e&&!t.isDone()));return j.ofNullable(t)}}function zt(){return Gt("xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx")}function jt(){return Gt("xxxx-yxxx-xxxxxxxxxxxx")}function Bt(){return Gt("xxxxxxxxxx")}function Gt(e){let t=Date.now();return e.replace(/[xy]/g,(e=>{const i=Math.trunc((t+16*Math.random())%16);return t=Math.floor(t/16),("x"===e?i:3&i|8).toString(16)}))}class Ht{constructor(e,t,i){__publicField(this,"localStorage"),__publicField(this,"requestId"),__publicField(this,"storageOrigin"),__publicField(this,"message"),__publicField(this,"iframe"),__publicField(this,"resolver"),__publicField(this,"isDone",!1),__publicField(this,"timeStart"),this.localStorage=e,this.requestId=zt(),this.storageOrigin=t,void 0!==i&&this.setMessage(i)}static get MESSAGE_EVENT_TIMEOUT_MS(){return 1e3}getRequestId(){return this.requestId}getStorageKey(){var e;return null==(e=this.message)?void 0:e.storageKey}setMessage(e){let t;this.getExpectedVersion(e.storageKey).ifPresent((e=>{t=e})),this.message=__spreadProps(__spreadValues({},e),{requestId:this.requestId,expectedVersion:t})}setIframe(e){this.iframe=e}send(){return __async(this,null,(function*(){if(void 0===this.iframe)throw new Error("Cannot call send() without setting the iframe");if(void 0===this.message)throw new Error("Cannot call send() without setting the message");const{iframe:e,message:t}=this;yield new Promise(((i,s)=>{var n;try{const r={resolve:i,reject:s};this.resolver=r,null===e.contentWindow?r.resolve():($t.debug(`send(): Sending message to storage domain (${this.storageOrigin}) ${Le(t,void 0,2)}`),this.timeStart=new Date,null==(n=e.contentWindow)||n.postMessage(t,this.storageOrigin),setTimeout((()=>{try{this.isDone||s(new Error(`Did not receive response within ${Ht.MESSAGE_EVENT_TIMEOUT_MS}ms`))}catch(e){const t=fi(e);Mo.error(t,174)}}),Ht.MESSAGE_EVENT_TIMEOUT_MS))}catch(r){s(r)}}))}))}reply(e){if(void 0===this.resolver)throw new Error("Cannot reply to message request if the message has no resolver");if(void 0!==this.timeStart){const e=(new Date).getTime()-this.timeStart.getTime();this.timeStart=void 0,$t.debug(`XO message round-trip time - ${e}ms`)}e.success?this.resolver.resolve():void 0===e.error?this.resolver.reject(new Error("Message request failed, but there is no error data (unexpected)")):this.resolver.reject(e),this.isDone=!0}getExpectedVersion(e){return this.localStorage.read(e).flatMap((e=>qt.validateStorageDatumString(e))).flatMap((e=>j.ofNullable(e.version)))}}var Qt=(e=>(e.UNEXPECTED="UNEXPECTED",e.LOCALSTORAGE_ACCESS="LOCALSTORAGE_ACCESS",e.INVALID_OP="INVALID_OP",e.INVALID_MESSAGE_FORMAT="INVALID_MESSAGE_FORMAT",e.INVALID_MESSAGE_META_FORMAT="INVALID_MESSAGE_META_FORMAT",e.INVALID_DATUM_FORMAT="INVALID_DATUM_FORMAT",e.UNEXPECTED_VERSION="UNEXPECTED_VERSION",e))(Qt||{});function Kt(e){return e}class qt{constructor(e,t,i,s){__publicField(this,"taskManager"),__publicField(this,"localStorage"),__publicField(this,"environment"),__publicField(this,"customer"),__publicField(this,"messageRequests"),__publicField(this,"storageKeys"),__publicField(this,"storageOrigin"),__publicField(this,"cleanupTaskManager"),__publicField(this,"isFeatureMissing"),__publicField(this,"iframeLoadPromise"),__publicField(this,"iframe"),this.localStorage=e,this.taskManager=i,this.environment=t,this.messageRequests={},this.customer=s,this.isFeatureMissing=!1;const n="intellimizeio.com";this.storageOrigin=`https://${s.getId()}.${n}`,this.storageKeys=Ft(s),this.cleanupTaskManager=new Wt(this.localStorage,this.taskManager),this.loadIframe().catch((()=>{this.isFeatureMissing=!0}))}static validateStorageDatumString(e){let t;try{if(t=Ue(e),"object"==typeof t&&null!==t&&("string"==typeof t.version||void 0===t.version)&&"string"==typeof t.data)return j.of(t)}catch(i){$t.debug(`validateStorageDatumString(): Not a StorageDatum (got ${Le(t)})`)}return j.empty()}static getNormalizedStorageValue(e){return qt.validateStorageDatumString(e).map((e=>e.data)).orElse(e)}static get READY_MESSAGE_TIMEOUT_MS(){return 3e3}static get WAIT_FOR_DATA_MS(){return 500}static isMessageReply(e){return"object"==typeof e&&null!==e&&"reply"===e.type}get isCrossOriginEnabled(){return!this.isFeatureMissing&&(this.customer.getCrossOrigin().isEnabled()||this.customer.getOrigins().length>0)}get shouldWaitForData(){if(this.environment.getReferrerUrl().isPresent()){const e=this.environment.getReferrerUrl().get(),t=this.environment.getHostingPageUrl();if(_t(this.customer,e.getOrigin()).orElse(!1)){if(e.getOrigin()!==t.getOrigin())return $t.debug("shouldWaitForData(): Detected a switch to a different origin."),!0;let i=!1;for(const e of this.storageKeys)if(this.localStorage.read(e).isPresent()){i=!0;break}if(!i)return Mo.warn("shouldWaitForData(): Detected an origin with no data.",63),!0;$t.debug("shouldWaitForData(): not waiting (user didn't cross origins, and there is existing data in LocalStorage)")}else $t.debug(`shouldWaitForData(): not waiting (referrer origin not allowed - ${e.getOrigin()})`)}else $t.debug("shouldWaitForData(): not waiting (no referrer - can't determine if user has crossed origins");return!1}initialize(){return __async(this,null,(function*(){if(this.isCrossOriginEnabled){$t.debug("Cross-Origin storage enabled by customer configuration. Persisting LocalStorage operations to the common origin.");const e=this.syncWithCommonOrigin().catch((e=>{const t=new Ei("Error syncing with common origin",e);Mo.error(t,130),this.isFeatureMissing=!0}));this.shouldWaitForData?($t.debug("initialize(): Blocking client execution until xd storage is ready (shouldWaitForData is true)"),yield this.waitForData(e)):$t.debug("initialize(): Not waiting until xd storage is ready (shouldWaitForData is false)")}else $t.debug("Cross-Origin storage disabled by customer configuration. Using LocalStorage only.");$t.debug("crossOriginStorage.initialize() has finished")}))}read(e){const t=this.readFromLocalStorage(e);return this.readAsync(e).catch((()=>{this.isFeatureMissing=!0})),t.flatMap((e=>j.ofNullable(e.data)))}write(e,t){this.writeAsync(e,t).catch((()=>{this.isFeatureMissing=!0}))}delete(e){this.deleteAsync(e).catch((()=>{this.isFeatureMissing=!0}))}loadIframe(){return __async(this,null,(function*(){if(void 0===this.iframeLoadPromise){const{document:e}=this.environment.getWindow();let t;this.iframe=e.createElement("iframe"),this.iframeLoadPromise=new Promise(((s,n)=>{let r=!1,o=!1;this.iframe.setAttribute("hidden",""),this.iframe.setAttribute("tabIndex","-1"),this.iframe.setAttribute("width","0"),this.iframe.setAttribute("height","0"),this.iframe.setAttribute("style","display:none");let a=`${this.storageOrigin}/storage.html`;Mo.getLevel()<i.LogLevel.OFF&&(a+=`?ideb=${Mo.getLevel()}`),this.iframe.setAttribute("src",a),this.iframe.setAttribute("referrerpolicy","strict-origin-when-cross-origin"),this.iframe.onerror=e=>{$t.debug("XO iframe onerror callback triggered"),r||o||setTimeout((()=>{try{o=!0;const t=new Ei("Iframe load error",e);Mo.error(t,66),n()}catch(t){const e=fi(t);Mo.error(e,170)}}),0)};const l=()=>{$t.debug("XO iframe ready resolver triggered"),o||setTimeout((()=>{var e;try{if($t.debug("loadIframe(): iframe is ready"),r=!0,void 0===(null==(e=this.iframe.contentWindow)?void 0:e.postMessage))Mo.error("postMessage() API not supported",65),n(),this.iframe.remove();else{if(void 0!==t){const e=(new Date).getTime()-t.getTime();$t.debug(`iframe load + ready message time - ${e}ms`)}s(void 0)}}catch(i){const e=fi(i);Mo.error(e,171)}}),0)};this.environment.addListener("message",(e=>{try{this.handleResponse(e,l)}catch(t){const e=new Ei("Exception in XO message response handler",t);Mo.error(e,237)}})),t=new Date,e.querySelectorAll("head")[0].insertAdjacentElement("afterbegin",this.iframe),setTimeout((()=>{try{r||(o=!0,Mo.error(`iframe did not load & receive "ready" message within ${qt.READY_MESSAGE_TIMEOUT_MS} ms`,67),n())}catch(e){const t=fi(e);Mo.error(t,172)}}),qt.READY_MESSAGE_TIMEOUT_MS)}))}return this.iframeLoadPromise}))}readFromLocalStorage(e){return this.localStorage.read(e).flatMap((e=>{try{return j.of(Ue(e))}catch(t){return Mo.error(`readFromLocalStorage(): Unable to parse as JSON (${e})`,68),j.empty()}}))}writeToLocalStorage(e,t){this.localStorage.write(e,Re(t)),$t.debug(`writeToLocalStorage(): Wrote "${e}" to LocalStorage (${Le(t,void 0,2)})`)}deleteFromLocalStorage(e){this.localStorage.delete(e),$t.debug(`deleteFromLocalStorage(): Deleted "${e}" from LocalStorage`)}readAsync(e){return __async(this,null,(function*(){if($t.debug(`readAsync(): Called with storageKey: "${e}"`),this.isCrossOriginEnabled){const t=new Ht(this.localStorage,this.storageOrigin,{requestId:"",op:"read",storageKey:e});yield this.sendMessageRequest(t)}}))}writeAsync(e,t){return __async(this,null,(function*(){let i;if($t.debug(`writeAsync(): Called with storageKey: "${e}", data ${t}`),this.isCrossOriginEnabled){const s=new Ht(this.localStorage,this.storageOrigin);i={version:s.getRequestId(),data:t},s.setMessage({requestId:"",op:"write",storageKey:e,storageDatum:i}),this.writeToLocalStorage(e,i),yield this.sendMessageRequest(s)}else this.writeToLocalStorage(e,{data:t})}))}deleteAsync(e){return __async(this,null,(function*(){if($t.debug(`delete(): Called with storageKey: "${e}"`),this.isCrossOriginEnabled){const t=new Ht(this.localStorage,this.storageOrigin,{requestId:"",op:"delete",storageKey:e});this.deleteFromLocalStorage(e),yield this.sendMessageRequest(t)}else this.deleteFromLocalStorage(e)}))}syncWithCommonOrigin(){return __async(this,null,(function*(){const e=[];this.storageKeys.forEach((t=>{this.readFromLocalStorage(t).ifPresent((({data:i,version:s})=>{s?($t.debug(`syncWithCommonOrigin(): Existing storageDatum found with a version (storageKey: ${t}). Sending "read" message to the common origin.`),e.push(this.readAsync(t))):($t.debug(`syncWithCommonOrigin(): Existing storageDatum found without a version (storageKey: ${t}). Sending "write" message to the common origin.`),e.push(this.writeAsync(t,i)))})).ifAbsent((()=>{$t.debug(`syncWithCommonOrigin(): No existing storageDatum found (storageKey: ${t}). Sending "read" message to the common origin.`),e.push(this.readAsync(t))}))})),yield Promise.all(e)}))}sendMessageRequest(e){return __async(this,null,(function*(){try{yield this.loadIframe()}catch(i){return void(this.isFeatureMissing=!0)}const t=e.getRequestId();e.setIframe(this.iframe),this.messageRequests[t]=e;try{yield e.send(),delete this.messageRequests[t]}catch(s){if(delete this.messageRequests[t],qt.isMessageReply(s))this.handleMessageReplyErrors(e,s);else{const e=new Ei("Could not send message request",s);Mo.error(e,129),this.isFeatureMissing=!0}}}))}handleResponse(e,t){const{data:i,origin:s}=e;if(s!==this.storageOrigin)return;if($t.debug(`handleResponse(): Received message event from storage domain (${this.storageOrigin})`),void 0!==i&&$t.debug(Le(i)),"object"!=typeof i||null===i)return void Mo.debug("handleResponse(): Refusing to handle message with invalid response format",69);if("ready"===i.type)return $t.debug('handleResponse(): Received "ready" message'),void t();if("sync"===i.type)return void this.handleSyncResponse(Kt(i));if("reply"!==i.type)return void Mo.debug(`handleResponse(): Refusing to handle message with unknown type (${i.type})`,70);const{correlationId:n}=i,r=this.messageRequests[n];void 0!==r?r.reply(i):Mo.error("handleResponse(): Could not find matching message in memory",71)}handleSyncResponse({storageKey:e,storageDatum:t}){this.cleanupTaskManager.queueCleanupTask(e,t)}handleMessageReplyErrors(e,t){const{code:i,msg:s}=t.error;if($t.debug(s),i===Qt.UNEXPECTED_VERSION)this.handleUnexpectedVersion(e,t);else{if(i!==Qt.LOCALSTORAGE_ACCESS)throw new Error(s);$t.debug("LocalStorage is not accessible on the common origin (i.e. browser security setting)")}}handleUnexpectedVersion(e,t){const i=e.getStorageKey(),{storageDatum:s}=t;if(void 0===s){const e=`handleUnexpectedVersion(): Common origin has no data for storageKey: "${i}"`;$t.debug(e)}else{const e=`handleUnexpectedVersion(): Common origin for storageKey: "${i}"`;$t.debug(`${e} ${Le(s,void 0,2)}`)}this.cleanupTaskManager.queueCleanupTask(i,s)}waitForData(e){return __async(this,null,(function*(){return new Promise(((t,i)=>__async(this,null,(function*(){let s=!1;const n=this.environment.getWindow().setTimeout((()=>__async(this,null,(function*(){s=!0;try{t()}catch(e){const t=fi(e);Mo.error(t,173)}}))),qt.WAIT_FOR_DATA_MS),r=Date.now();try{const i=yield e,o=Date.now();$t.debug(`waitForData(): iframe load + data sync time - ${o-r}ms`);const a=yield this.cleanupTaskManager.run(),l=Date.now();$t.debug(`waitForData(): Cleanup task time - ${l-o}ms`),s?Mo.warn(`waitForData(): Synchronization did not complete within ${qt.WAIT_FOR_DATA_MS}ms. (Took ${l-r}ms)`,72):($t.debug(`waitForData(): Sync completed; finished in under ${qt.WAIT_FOR_DATA_MS}ms (Took ${l-r}ms)`),this.environment.getWindow().clearTimeout(n),t([i,a]))}catch(o){s||(this.environment.getWindow().clearTimeout(n),i(o))}}))))}))}}class Yt{constructor(){__publicField(this,"storage"),this.storage={}}write(e,t){kt(e)||Mo.error(`Writing an unknown key to MemoryStorage: ${e}`,180),this.storage[e]=t}read(e){return j.ofNullable(this.storage[e])}delete(e){delete this.storage[e]}}class Jt{constructor(e){if(__publicField(this,"environment"),!Mt(e,"sessionStorage"))throw new Error("sessionStorage is not available");this.environment=e}write(e,t){kt(e)||Mo.error(`Writing an unknown key to SessionStorage: ${e}`,180),this.environment.writeSessionStorage(e,t)}read(e){return this.environment.readSessionStorage(e)}delete(e){this.environment.deleteSessionStorage(e)}}function Xt(e){return void 0!==e.error}function Zt(e,t,i){if(!Et(e,i).orElse(!1)){const s={utcm:"utm_campaign",utcn:"utm_content",utm:"utm_medium",uts:"utm_source",utt:"utm_term"},n=Ct(e,i);t.setInternalAttributes("user",{ts:n}),t.deleteInternalAttributes("user",De);const r={};De.forEach((t=>{const i=s[t],n=e.getHostingPageUrl().getQueryParam(i,(()=>!0));n.isPresent()&&(r[t]=n.get())})),Object.keys(r).length>0&&t.setInternalAttributes("user",r)}}function ei(e,t,i){e.getCss().ifPresent((s=>{try{Mo.info(`Injecting customer ${e.getId()} css`),Dt.injectCss(s,`customer-${e.getId()}`,i),t.setCustomer("css",!0)}catch(n){const i=new Ei(`Customer (${e.getId()}) css injection failed`,n);Mo.error(i,26),t.setCustomer("css",!1)}})),e.getCode().ifPresent((i=>{try{Mo.info(`Running customer ${e.getId()} code`),Dt.eval(i),t.setCustomer("code",!0)}catch(s){const i=new Ei(`Customer (${e.getId()}) code execution failed`,s);Mo.error(i,13),t.setCustomer("code",!1)}}))}function ti(e){return!e.startsWith('{"')}function ii(e){const t=e.getWindow()[He(e)];if(void 0===t)return Mo.error("Missing customer JSON. Aborting.",145),j.empty();let i;if(Mo.time("load json"),ti(t))try{i=Be(e,t)}catch(n){const e=new Ei("Could not decrypt CustomerJson",n);return Mo.error(e,213),j.empty()}else try{i=Ue(t)}catch(n){const e=new Ei("Could not parse CustomerJson",n);return Mo.error(e,210),j.empty()}const s=ft(i);return Mo.timeEnd("load json"),j.of(s)}function si(e){var t;const{name:i,audienceIds:s,condition:n,state:r,preconditions:o}=e,a=__spreadValues({name:i,condition:n,state:r,preconditions:o},void 0!==s&&{audienceIds:s});if(void 0!==e.changes&&e.changes.length>0)return __spreadProps(__spreadValues({},a),{type:"cl",changes:null!=(t=e.changes)?t:[]});if(void 0!==e.redirect)return __spreadProps(__spreadValues({},a),{type:"urlrd",redirect:e.redirect});if(void 0!==e.code||void 0!==e.css){const{code:t,css:i}=e;return __spreadProps(__spreadValues({},a),{type:"cl",changes:[__spreadValues(__spreadValues({type:gt.CUSTOM_CODE},void 0!==t&&{code:t}),void 0!==i&&{css:i})]})}return o.length>0?__spreadProps(__spreadValues({},a),{type:"cl",changes:[]}):__spreadProps(__spreadValues({},a),{type:"nochange",preconditions:[]})}function ni(e){return void 0===e.type?si(e):e}function ri(e){Mo.time("Build customer object time");const t=new Co(Mo).buildCustomer(e);return Mo.timeEnd("Build customer object time"),j.ofNullable(t)}function oi(e,t,i,s,n){return __async(this,null,(function*(){const r=n.useStorageType().orElse("CrossOriginStorage");switch(r){case"CrossOriginStorage":{const n=new qt(e,t,i,s);return yield n.initialize(),n}case"MemoryStorage":return new Yt;case"SessionStorage":return new Jt(t);case"LocalStorage":return e;default:throw new Error(`Unknown storage type: ${r}`)}}))}class ai{constructor(){__publicField(this,"count",0)}accept(e){this.count++}result(){return this.count}}class li{constructor(e){__publicField(this,"activityFieldFunction"),__publicField(this,"set",new Set),this.activityFieldFunction=e}accept(e){const t=this.activityFieldFunction(e);t.isPresent()&&this.set.add(t.get())}result(){return this.set.size}}class ci{constructor(e){__publicField(this,"activityFieldFunction"),__publicField(this,"sum",0),__publicField(this,"count",0),this.activityFieldFunction=e}accept(e){const t=this.activityFieldFunction(e);t.isPresent()&&(this.count++,this.sum+=t.get())}result(){return this.count>0?this.sum/this.count:0}}class di{constructor(e){__publicField(this,"activityFieldFunction"),__publicField(this,"sum",0),this.activityFieldFunction=e}accept(e){const t=this.activityFieldFunction(e);this.sum+=t.orElse(0)}result(){return this.sum}}class ui{constructor(e,t){__publicField(this,"filters"),this.filters=e.filters.map((e=>t.buildActivityFilter(e)))}evaluate(e){return this.filters.every((t=>t.evaluate(e)))}}class hi{constructor(e,t){__publicField(this,"activityFieldFunction"),__publicField(this,"operation"),this.activityFieldFunction=e,this.operation=t}evaluate(e){return this.operation(this.activityFieldFunction(e))}}class gi{constructor(e,t){__publicField(this,"filter"),this.filter=t.buildActivityFilter(e.filter)}evaluate(e){return!this.filter.evaluate(e)}}class pi{constructor(e,t){__publicField(this,"filters"),this.filters=e.filters.map((e=>t.buildActivityFilter(e)))}evaluate(e){return this.filters.some((t=>t.evaluate(e)))}}class mi{constructor(e,t,i,s){__publicField(this,"id"),__publicField(this,"name"),__publicField(this,"controlTrafficPercentage"),__publicField(this,"code"),__publicField(this,"css"),__publicField(this,"metrics"),__publicField(this,"experiences"),__publicField(this,"customer"),__publicField(this,"intervals"),__publicField(this,"intervalModifications"),__publicField(this,"important"),__publicField(this,"primaryMetrics"),this.id=e,this.customer=s,this.name=t.name,this.intervals=[],this.intervalModifications=[],void 0!==t.ictp&&(this.controlTrafficPercentage=t.ictp),void 0!==t.code&&(this.code=t.code),void 0!==t.css&&(this.css=t.css);const n=[];for(const[r,o]of Object.entries(t.experiences)){const e=i.buildExperience(r,o,this);n.push(e)}this.experiences=n,this.important=Boolean(t.important),void 0!==t.modifiedIntervals&&(this.intervals=t.modifiedIntervals),void 0!==t.intervalModifications&&(this.intervalModifications=t.intervalModifications),this.metrics=t.metrics.map((e=>i.buildMetric(e,this))),this.primaryMetrics=t.primaryMetrics}getId(){return this.id}getName(){return this.name}getControlTrafficPercentage(){return j.ofNullable(this.controlTrafficPercentage)}getCode(){return j.ofNullable(this.code)}getCss(){return j.ofNullable(this.css)}getExperiences(){return this.experiences}getMetrics(){return this.metrics}getMetric(e){const t=this.metrics.filter((t=>t.getId()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getCustomer(){return this.customer}getExperience(e){const t=this.experiences.filter((t=>t.getId()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getIntervals(){return this.intervals}getIntervalModifications(){return this.intervalModifications}isImportant(){return this.important}getPrimaryMetrics(){const{primaryMetrics:e}=this,t="customer"===e.level?this.customer.getMetrics():this.metrics;return 0===e.metricIds.length?t:t.filter((t=>e.metricIds.includes(t.getId())))}getPrimaryMetricLevel(){return this.primaryMetrics.level}}class vi{constructor(e){__publicField(this,"username"),__publicField(this,"role"),this.username=e.username,this.role=e.role}getUsername(){return this.username}getRole(){return this.role}}class bi{constructor(e){var t,i;__publicField(this,"enabled"),__publicField(this,"originWhitelist"),__publicField(this,"originBlacklist"),void 0===e?(this.enabled=!1,this.originWhitelist=[],this.originBlacklist=[]):(this.enabled=e.enabled||!1,this.originWhitelist=null!=(t=e.originWhitelist)?t:[],this.originBlacklist=null!=(i=e.originBlacklist)?i:[])}static isMatchingOrigin(e,t){return new RegExp(`^${e}$`).test(t)}isEnabled(){return this.enabled}getOriginWhitelist(){return this.originWhitelist}getOriginBlacklist(){return this.originBlacklist}isOriginAllowed(e){let t=!1;for(const i of this.originWhitelist)if(bi.isMatchingOrigin(i,e)){t=!0;break}if(t)for(const i of this.originBlacklist)if(bi.isMatchingOrigin(i,e)){t=!1;break}return t}}function fi(e){return e instanceof Error?e:new Error(e)}class Ei extends Error{constructor(e,t){super(e),__publicField(this,"cause"),this.name="ProxyError",this.cause=fi(t),this.message=this.getAggregateMessage(),void 0!==this.cause.stack&&(this.stack=this.cause.stack)}getCause(){return this.cause instanceof Ei?this.cause.getCause():this.cause}getAggregateMessage(){let e=`${this.message}. `;return e+=this.cause instanceof Ei?this.cause.getAggregateMessage():`${this.cause.name}: ${this.cause.message}`,e}}class _i{constructor(e,t,i){var s;__publicField(this,"logger"),__publicField(this,"id"),__publicField(this,"name"),__publicField(this,"type"),__publicField(this,"state"),__publicField(this,"metrics",[]),this.logger=i.getLogger(),this.id=e,this.name=t.name,this.type=t.type,L(void 0!==this.type,"invalid event type: "+t.type),this.state=null!=(s=t.state)?s:"live"}addMetric(e){this.metrics.push(e)}getId(){return this.id}getName(){return this.name}getType(){return this.type}getState(){return this.state}getMetrics(){const e=[];return this.metrics.forEach((t=>{e.push(t)})),e}}class yi extends _i{}class Si extends yi{constructor(e,t,i){super(e,t,i),__publicField(this,"pages"),__publicField(this,"code"),__publicField(this,"selector"),this.pages=t.pageIds.flatMap((e=>i.getPage(e).toArray())),this.pages.forEach((e=>{e.addEvent(this)})),void 0!==t.code&&(this.code=t.code),this.selector=t.selector}static filterEvents(e){return e.filter((e=>"click"===e.getType()))}getPages(){return this.pages}getCode(){return j.ofNullable(this.code)}getSelector(){return this.selector}executeCode(e){this.logger.debug("executeCode() if present");let t=!0;if(this.getCode().isPresent()){const s=this.getCode().get();try{this.logger.info(`Event ${this.id} code running`),t=e(s),this.logger.info(`Event ${this.id} code result => ${t}`)}catch(i){const e=new Ei(`Click event (${this.id}) code execution failed `,i);t=!1,this.logger.error(e,1007,{eventId:this.id})}}return t}}class wi extends _i{constructor(e,t,i){super(e,t,i),__publicField(this,"apiName"),this.apiName=t.apiName}static filterEvents(e){return e.filter((e=>"custom"===e.getType()))}getApiName(){return this.apiName}}class Ii extends _i{constructor(e,t,i){super(e,t,i),__publicField(this,"formId"),this.formId=t.formId}static filterEvents(e){return e.filter((e=>"hubspot"===e.getType()))}getFormId(){return this.formId}}class Ci extends _i{constructor(e,t,i){super(e,t,i),__publicField(this,"formId"),this.formId=t.formId}static filterEvents(e){return e.filter((e=>"marketo"===e.getType()))}getFormId(){return this.formId}}class Ti extends _i{static filterEvents(e){return e.filter((e=>"page_leave"===e.getType()))}constructor(e,t,i){super(e,t,i)}}class xi extends _i{constructor(e,t,i){super(e,t,i),__publicField(this,"customerEventName"),this.customerEventName=t.customerEventName}static filterEvents(e){return e.filter((e=>"shopify"===e.getType()))}getCustomerEventName(){return this.customerEventName}}class Ai extends _i{constructor(e,t,i){super(e,t,i)}}class Fi extends _i{constructor(e,t,i){super(e,t,i),__publicField(this,"pages"),__publicField(this,"code"),this.pages=t.pageIds.flatMap((e=>i.getPage(e).toArray())),this.pages.forEach((e=>{e.addEvent(this)})),void 0!==t.code&&(this.code=t.code)}static filterEvents(e){return e.filter((e=>"view"===e.getType()))}getPages(){return this.pages}getCode(){return j.ofNullable(this.code)}executeCode(e){this.logger.debug("executeCode() if present");let t=!0;if(this.getCode().isPresent()){const s=this.getCode().get();try{this.logger.info(`Event ${this.id} code running`),t=e(s),this.logger.info(`Event ${this.id} code result => ${t}`)}catch(i){const e=new Ei(`View event (${this.id}) code execution failed `,i);t=!1,this.logger.error(e,1011,{eventId:this.id})}}return t}}class ki extends yi{static filterEvents(e){return e.filter((e=>"wf_engagement_click"===e.getType()))}constructor(e,t,i){super(e,t,i)}getSelector(){return'[data-wf-element-id]:not([data-wf-element-id=""])[data-wf-ao-click-engagement-tracking="true"]'}}class Pi extends yi{constructor(e,t,i){super(e,t,i),__publicField(this,"selector"),this.selector=`[data-wf-event-ids*="${e}"]`}static filterEvents(e){return e.filter((e=>"wf_click"===e.getType()))}getSelector(){return this.selector}}class Mi{constructor(e,t){var i,s,n,r,o,a,l;__publicField(this,"data"),__publicField(this,"active"),__publicField(this,"encrypt"),__publicField(this,"schemaVersion"),__publicField(this,"id"),__publicField(this,"version"),__publicField(this,"name"),__publicField(this,"controlStickyConfig"),__publicField(this,"campaignControl"),__publicField(this,"controlTrafficPercentage"),__publicField(this,"collaborators"),__publicField(this,"excludeIps"),__publicField(this,"plugins"),__publicField(this,"metrics"),__publicField(this,"campaigns"),__publicField(this,"pages"),__publicField(this,"audiences"),__publicField(this,"experiences"),__publicField(this,"iEvents"),__publicField(this,"code"),__publicField(this,"css"),__publicField(this,"snippetDomain"),__publicField(this,"urlParamFilters"),__publicField(this,"customAttributeFilters"),__publicField(this,"googleAnalytics4Integration"),__publicField(this,"segmentAnalyticsIntegration"),__publicField(this,"mixpanelIntegration"),__publicField(this,"marketoIntegration"),__publicField(this,"salesforceIntegration"),__publicField(this,"shopifyIntegration"),__publicField(this,"firmographicIntegration"),__publicField(this,"sixsenseIntegration"),__publicField(this,"googleAdsIntegration"),__publicField(this,"demandbaseIntegration"),__publicField(this,"hubspotIntegration"),__publicField(this,"mfa"),__publicField(this,"changelogInternal"),__publicField(this,"changelogExternal"),__publicField(this,"crossOrigin"),__publicField(this,"origins"),__publicField(this,"vertical"),__publicField(this,"currentContract"),__publicField(this,"timeZone"),__publicField(this,"classicAccess"),__publicField(this,"salesforceAccountId"),__publicField(this,"autoUpdateOrigins"),__publicField(this,"roiScenario"),__publicField(this,"webflowSiteId"),__publicField(this,"limitedModeCondition"),this.data=e,this.active=e.active,this.encrypt=e.encrypt,this.schemaVersion=null!=(i=e.schemaVersion)?i:"1",this.id=e.id,this.version=e.__v,this.name=e.name,this.controlStickyConfig=t.buildControlStickyConfig(e.scon),this.campaignControl=e.ipcc,this.campaignControl||void 0===e.ictp||(this.controlTrafficPercentage=e.ictp),void 0!==e.code&&(this.code=e.code),void 0!==e.css&&(this.css=e.css),void 0!==e.snippetDomain&&(this.snippetDomain=e.snippetDomain);const c=[];void 0!==e.collaborators&&e.collaborators.forEach((e=>{c.push(t.buildCollaborator(e))})),this.collaborators=c,this.excludeIps=null!=(s=e.excludeIps)?s:[],this.plugins=null!=(n=e.plugins)?n:[],this.urlParamFilters=null!=(r=e.urlParamFilters)?r:[],this.customAttributeFilters=null!=(o=e.customAttributeFilters)?o:[],this.changelogInternal=null!=(a=e.changelogInternal)?a:[],this.changelogExternal=null!=(l=e.changelogExternal)?l:[],this.crossOrigin=t.buildCrossOrigin(e.crossOrigin),this.origins=void 0!==e.origins&&e.origins.length>0?e.origins.map((e=>t.buildOrigin(e))):[];const d=[];for(const[v,b]of Object.entries(e.audiences)){const e=t.buildAudience(v,b);d.push(e)}this.audiences=d;const u=[];for(const[v,b]of Object.entries(e.pages)){const e=t.buildPage(v,b);u.push(e)}this.pages=u;const h=[];for(const[v,b]of Object.entries(e.events)){const e=t.buildEvent(v,b);h.push(e)}this.iEvents=h;for(const[v,b]of Object.entries(e.pages))"metrics"in b&&b.metrics.forEach((e=>{t.getPage(v).ifPresent((i=>{i.addMetric(t.buildMetric(e,void 0,i))}))}));let g=[];void 0!==e.metrics&&(g=e.metrics.map((e=>t.buildMetric(e,void 0,void 0,this)))),this.metrics=g;const p=[];for(const[v,b]of Object.entries(e.campaigns)){const e=t.buildCampaign(v,b,this);p.push(e)}this.campaigns=p;const m=new Map;this.campaigns.forEach((e=>{e.getExperiences().forEach((e=>{m.set(e.getId(),e)}))})),this.experiences=m,void 0!==e.iint&&(void 0!==e.iint.ga4&&(this.googleAnalytics4Integration=t.buildGoogleAnalytics4Integration(e.iint.ga4)),void 0!==e.iint.sg&&(this.segmentAnalyticsIntegration=t.buildSegmentIntegration(e.iint.sg)),void 0!==e.iint.mp&&(this.mixpanelIntegration=t.buildMixpanelIntegration(e.iint.mp)),void 0!==e.iint.marketo&&(this.marketoIntegration=t.buildMarketoIntegration(e.iint.marketo)),void 0!==e.iint.salesforce&&(this.salesforceIntegration=t.buildSalesforceIntegration(e.iint.salesforce)),void 0!==e.iint.shopify&&(this.shopifyIntegration=t.buildShopifyIntegration(e.iint.shopify,this.metrics,this.pages,this.iEvents)),void 0!==e.iint.firmographic&&(this.firmographicIntegration=t.buildFirmographicIntegration(e.iint.firmographic)),void 0!==e.iint.sixsense&&(this.sixsenseIntegration=t.buildSixsenseIntegration(e.iint.sixsense)),void 0!==e.iint.googleAds&&(this.googleAdsIntegration=t.buildGoogleAdsIntegration(e.iint.googleAds)),void 0!==e.iint.demandbase&&(this.demandbaseIntegration=t.buildDemandbaseIntegration(e.iint.demandbase)),void 0!==e.iint.hubspot&&(this.hubspotIntegration=t.buildHubspotIntegration(e.iint.hubspot))),this.mfa=Boolean(e.mfa),void 0!==e.vertical&&(this.vertical=e.vertical),void 0!==e.currentContract&&(this.currentContract=e.currentContract),void 0!==e.timeZone&&(this.timeZone=e.timeZone),void 0!==e.classicAccess&&(this.classicAccess=e.classicAccess),void 0!==e.salesforceAccountId&&(this.salesforceAccountId=e.salesforceAccountId),this.autoUpdateOrigins=e.autoUpdateOrigins,void 0!==e.roiScenario&&(this.roiScenario=e.roiScenario),this.webflowSiteId=e.webflowSiteId,void 0!==e.limitedModeCondition&&(this.limitedModeCondition=t.buildCondition(e.limitedModeCondition))}getData(){return this.data}isActive(){return this.active}shouldEncrypt(){return this.encrypt}getSchemaVersion(){return this.schemaVersion}getId(){return this.id}getVersion(){return this.version}getName(){return this.name}getCollaborators(){return this.collaborators}getExcludeIps(){return this.excludeIps}getPlugins(){return this.plugins}getMetrics(){return this.metrics}getControlStickyConfig(){return this.controlStickyConfig}isCampaignControl(){return this.campaignControl}getControlTrafficPercentage(){return j.ofNullable(this.controlTrafficPercentage)}getCode(){return j.ofNullable(this.code)}getCss(){return j.ofNullable(this.css)}getSnippetDomain(){return j.ofNullable(this.snippetDomain)}getCampaigns(){return this.campaigns}getPages(){return this.pages}getAudiences(){return this.audiences}getEvents(){return this.iEvents}getCampaign(e){const t=this.campaigns.filter((t=>t.getId()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getExperience(e){return j.ofNullable(this.experiences.get(e))}getPage(e){const t=this.pages.filter((t=>t.getId()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getEvent(e){const t=this.iEvents.filter((t=>t.getId()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getCustomEvent(e){const t=this.iEvents.filter((t=>t instanceof wi&&t.getApiName()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getShopifyEvent(e){const t=this.iEvents.filter((t=>t instanceof xi&&t.getCustomerEventName()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getShopifyServerSideEvent(){return j.ofNullable(this.iEvents.find((e=>e instanceof Ai)))}getAudience(e){const t=this.audiences.filter((t=>t.getId()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}getIntegrationGoogle4Analytics(){return j.ofNullable(this.googleAnalytics4Integration)}getIntegrationSegmentAnalytics(){return j.ofNullable(this.segmentAnalyticsIntegration)}getIntegrationMixpanel(){return j.ofNullable(this.mixpanelIntegration)}getIntegrationMarketo(){return j.ofNullable(this.marketoIntegration)}getIntegrationSalesforce(){return j.ofNullable(this.salesforceIntegration)}getIntegrationShopify(){return j.ofNullable(this.shopifyIntegration)}getIntegrationFirmographic(){return j.ofNullable(this.firmographicIntegration)}getIntegrationSixsense(){return j.ofNullable(this.sixsenseIntegration)}getIntegrationGoogleAds(){return j.ofNullable(this.googleAdsIntegration)}getIntegrationDemandbase(){return j.ofNullable(this.demandbaseIntegration)}getIntegrationHubspot(){return j.ofNullable(this.hubspotIntegration)}getUrlParamFilters(){return this.urlParamFilters}getCustomAttributeFilters(){return this.customAttributeFilters}getChangelogInternal(){return this.changelogInternal}getChangelogExternal(){return this.changelogExternal}getCrossOrigin(){return this.crossOrigin}getOrigins(){return this.origins}isMfa(){return this.mfa}getVertical(){return j.ofNullable(this.vertical)}getCurrentContract(){return j.ofNullable(this.currentContract)}getTimeZone(){return j.ofNullable(this.timeZone)}hasClassicAccess(){return j.ofNullable(this.classicAccess).orElse(!1)}getSalesforceAccountId(){return j.ofNullable(this.salesforceAccountId)}shouldAutoUpdateOrigins(){return j.ofNullable(this.autoUpdateOrigins).orElse(!1)}getRoiScenario(){return j.ofNullable(this.roiScenario)}getWebflowSiteId(){return j.ofNullable(this.webflowSiteId)}getLimitedModeCondition(){return j.ofNullable(this.limitedModeCondition)}}class Di{constructor(e){__publicField(this,"type"),__publicField(this,"id"),this.type=e.type,this.id=e.id}getType(){return this.type}getId(){return j.ofNullable(this.id)}}class Ni extends Di{constructor(e){super(e),__publicField(this,"attributes");const t=__spreadValues({},e.attributes.css);this.attributes=__spreadProps(__spreadValues({},e.attributes),{css:t})}getAttributes(){return this.attributes}isReadyToApply(e){return this.getSelectorElements(e).length>0}getSelectorElements(e){return e.querySelectorAll(this.getSelector())}}class Oi extends Ni{constructor(e){super(e),__publicField(this,"selector"),this.selector=e.selector}getSelector(){return this.selector}}class Li extends Di{constructor(e){super(e),__publicField(this,"code"),__publicField(this,"css"),L(Boolean(e.code)||Boolean(e.css),'Either "code" or "css" must be specified'),this.code=e.code,this.css=e.css}getCode(){return j.ofNullable(this.code)}getCss(){return j.ofNullable(this.css)}isReadyToApply(e){return!0}}class Ri extends Ni{constructor(e){super(e),__publicField(this,"refId"),__publicField(this,"extensionName"),__publicField(this,"fieldName"),this.refId=e.refId,this.fieldName=e.fieldName,this.extensionName=e.extensionName}getSelector(){const e=`[data-${S}="${this.refId}"]`,t=`[data-${w}="${this.fieldName}"]`;return`${e}${t}, ${e} ${t}`}getRefId(){return this.refId}getExtensionName(){return this.extensionName}getFieldName(){return this.fieldName}}class Ui extends Di{constructor(e){super(e),__publicField(this,"name"),__publicField(this,"executorJson"),this.name=e.name,this.executorJson=e.executorJson}getName(){return this.name}getExecutorJson(){return this.executorJson}}function $i(e){return e.replace(/['\\]/g,(e=>"\\"+e))}function Vi(e,t=!0){return`document.querySelectorAll('${$i(e)}').length ${t?"> 0":"=== 1"}`}function Wi(e,t){const i=e.querySelectorAll(t),s=null==i?void 0:i[0];return 1===i.length&&void 0!==s?j.of(s):j.empty()}class zi extends Di{constructor(e){super(e),__publicField(this,"elementType"),__publicField(this,"targetPosition"),__publicField(this,"idAttribute"),this.targetPosition=e.targetPosition,this.elementType=e.elementType,this.idAttribute=e.idAttribute}getElementType(){return this.elementType}getTargetPosition(){return this.targetPosition}getIdAttribute(){return this.idAttribute}getTargetElement(e){return Wi(e,this.targetPosition.selector)}isReadyToApply(e){return this.getTargetElement(e).isPresent()}}class ji extends zi{constructor(e){super(e),__publicField(this,"size"),this.size=e.size}getSize(){return this.size}}class Bi extends Di{constructor(e){super(e),__publicField(this,"targetPosition"),__publicField(this,"selector"),__publicField(this,"moveId"),this.targetPosition=e.targetPosition,this.selector=e.selector,this.moveId=e.moveId}getTargetPosition(){return this.targetPosition}getSelector(){return this.selector}getMoveId(){return this.moveId}getSelectorElement(e){return Wi(e,this.selector)}getTargetElement(e){return Wi(e,this.targetPosition.selector)}isReadyToApply(e){return this.getTargetElement(e).isPresent()&&this.getSelectorElement(e).isPresent()}}class Gi extends Di{constructor(e){super(e)}}const Hi=e=>{if(!hs(e))return!1;const t=e;return"ATTRIBUTE"===t.type&&A(t.selector)&&Cs(t.attributes)},Qi=e=>Hi(e)&&y(e,["type","selector","attributes","id"]),Ki=new Set(["1","2","3","4","5","6"]),qi=e=>Ki.has(e),Yi=e=>{if(!hs(e))return!1;const t=e;return"INSERT_ELEMENT"===t.type&&M(t.elementType)&&x(t.targetPosition)&&b(t.idAttribute)},Ji=e=>!!Yi(e)&&("heading"!==e.elementType||qi(e.size)),Xi=["id","type","targetPosition","elementType","size","idAttribute"],Zi=e=>Ji(e)&&y(e,Xi),es=e=>m(e)&&Object.values(e).every((e=>v(e)||p(e)||g(e)||f(e))),ts=e=>m(e)&&(f(e.targetPosition)||x(e.targetPosition))&&es(e.fieldData)&&y(e,["targetPosition","fieldData"]),is=["id","type","name","executorJson"],ss=e=>{if(!hs(e))return!1;const t=e;return t.type===gt.EXTENSION&&b(t.name)&&ts(t.executorJson)},ns=e=>ss(e)&&y(e,is),rs=e=>{if(!hs(e))return!1;const t=e;return"EXTENSION_ATTRIBUTE"===t.type&&E(t.refId)&&b(t.extensionName)&&b(t.fieldName)&&Cs(t.attributes)},os=e=>rs(e)&&y(e,["type","attributes","id","refId","extensionName","fieldName"]),as=e=>m(e)&&e.type===gt.MOVE_ELEMENT&&E(e.moveId)&&A(e.selector)&&x(e.targetPosition),ls=["id","type","targetPosition","selector","moveId"],cs=e=>as(e)&&y(e,ls),ds=e=>us(e)&&y(e,["type"]),us=e=>m(e)&&e.type===gt.WEBFLOW,hs=e=>m(e)&&vs(e.type)&&(f(e.id)||b(e.id)),gs=e=>{if(!hs(e))return!1;const t=e,{code:i,css:s}=t;return"CUSTOM_CODE"===t.type&&(void 0!==i||void 0!==s)&&(f(i)||b(i))&&(f(s)||b(s))},ps=e=>gs(e)&&y(e,["type","id","code","css"]),ms=(()=>new Set([gt.ATTRIBUTE,gt.CUSTOM_CODE,gt.INSERT_ELEMENT,gt.MOVE_ELEMENT,gt.EXTENSION,gt.EXTENSION_ATTRIBUTE,gt.WEBFLOW]))(),vs=e=>ms.has(e),bs=new Set(ht),fs=e=>v(e)&&bs.has(e),Es=e=>m(e)&&b(e.name)&&fs(e.namespace)&&y(e,["name","namespace"]),_s=e=>m(e)&&Es(e.attrDefId)&&v(e.defaultString)&&y(e,["defaultString","attrDefId"]),ys=e=>m(e)&&"attribute"===e.type&&_s(e.value)&&y(e,["type","value"]),Ss=e=>m(e)&&"string"===e.type&&b(e.value)&&y(e,["type","value"]),ws=e=>Ss(e)||ys(e),Is=e=>v(e)||h(e)&&e.every((e=>ws(e))),Cs=e=>!!m(e)&&(!(!f(e.html)&&!Is(e.html)||!f(e.style)&&!v(e.style)||!f(e.className)&&!v(e.className)||!f(e.src)&&!v(e.src)||!f(e.alt)&&!v(e.alt)||!f(e.srcset)&&!v(e.srcset)||!f(e.sizes)&&!v(e.sizes)||!f(e.href)&&!v(e.href)||!f(e.value)&&!v(e.value)||!f(e.placeholder)&&!v(e.placeholder)||!f(e.maxlength)&&!p(e.maxlength)||!f(e.required)&&!g(e.required)||!f(e.poster)&&!v(e.poster)||!f(e.loop)&&!g(e.loop)||!f(e.autoplay)&&!g(e.autoplay)||!f(e.muted)&&!g(e.muted)||!f(e.controls)&&!g(e.controls))&&(!(!f(e.css)&&!k(e.css))&&y(e,["html","style","className","src","alt","srcset","sizes","href","css","value","placeholder","maxlength","required","poster","loop","autoplay","muted","controls"])));function Ts(e){if(Qi(e))return new Oi(e);if(ps(e))return new Li(e);if(cs(e))return new Bi(e);if(Zi(e))return"heading"===e.elementType?new ji(e):new zi(e);if(ns(e))return new Ui(e);if(os(e))return new Ri(e);if(ds(e))return new Gi(e);throw new D(`Invalid dom change type ${e.type}`)}class xs{constructor(e,t,i,s){var n,r,o,a;__publicField(this,"allVariationsMap",{}),__publicField(this,"id"),__publicField(this,"name"),__publicField(this,"type"),__publicField(this,"state"),__publicField(this,"pages"),__publicField(this,"condition"),__publicField(this,"audiences"),__publicField(this,"realPreconditions"),__publicField(this,"preconditions"),__publicField(this,"dependsOnExperienceIds"),__publicField(this,"dependsOnExperiences",[]),__publicField(this,"realVariations"),__publicField(this,"campaign"),__publicField(this,"ignore"),__publicField(this,"inclusionStickyConfig"),__publicField(this,"variationStickyConfig"),__publicField(this,"boundingSelectors"),__publicField(this,"playbookId"),__publicField(this,"note"),this.id=e,this.name=t.name,this.type=null!=(n=t.type)?n:"cc",this.state=t.state,this.inclusionStickyConfig=i.buildExperienceIncludeStickyConfig(t.sinc),this.variationStickyConfig=i.buildVariationStickyConfig(t.svar),this.ignore=t.ignore,this.playbookId=t.playbookId,this.note=t.note,this.pages=t.pageIds.flatMap((e=>i.getPage(e).toArray())),this.pages.forEach((e=>{e.addExperience(this)})),this.condition=i.buildConditionFromVariationOrExperience(t),this.audiences=Co.addExperienceToAudiences(this),this.dependsOnExperienceIds=null!=(r=t.dependsOnExperienceIds)?r:[],this.dependsOnExperiences=[];const l=[];for(const[c,d]of Object.entries(t.variations)){const e=i.buildVariation(c,d,this);l.push(e),this.allVariationsMap[c]=e}this.realVariations=l,this.boundingSelectors=null!=(o=t.boundingSelectors)?o:[],this.realPreconditions=null!=(a=t.preconditions)?a:[],this.preconditions=[...this.realPreconditions,...this.generatePreconditionsForBoundingSelectors()],this.campaign=s}inflateDependsOnExperiences(e){this.dependsOnExperienceIds.forEach((t=>{const i=e[t];void 0!==i&&this.dependsOnExperiences.push(i)}))}getId(){return this.id}getName(){return this.name}getType(){return this.type}isEnabled(){return"live"===this.state}getState(){return this.state}getPages(){return this.pages}getCondition(){return j.ofNullable(this.condition)}getAudiences(){return this.audiences}getPreconditions(){return this.preconditions}getBoundingSelectors(){return this.boundingSelectors}getRealPreconditions(){return this.realPreconditions}getDependsOnExperiences(){return this.dependsOnExperiences}getRealVariations(){return this.realVariations}getVariation(e){return j.ofNullable(this.allVariationsMap[e])}getCampaign(){return this.campaign}getInclusionStickyConfig(){return this.inclusionStickyConfig}getVariationStickyConfig(){return this.variationStickyConfig}getIgnore(){return this.ignore}getPlaybookId(){return j.ofNullable(this.playbookId)}getNote(){return j.ofNullable(this.note)}generatePreconditionsForBoundingSelectors(){const e=[];return this.boundingSelectors.forEach((t=>{e.push(Vi(t))})),e}}class As extends xs{constructor(e,t,i,s){super(e,t,i,s),__publicField(this,"logger"),__publicField(this,"trafficAllocation"),this.logger=i.getLogger(),this.trafficAllocation=t.trafficAllocation}static filterExperiences(e){return e.filter((e=>"ab"===e.getType()))}selectVariation(e){let t;for(const[i,s]of Object.entries(this.trafficAllocation))if(e>=s.lowerEndpoint&&e<s.upperEndpoint){t=i;break}return void 0!==t?this.getVariation(t):(this.logger.error(`Could not determine A/B variation for ${e}`,1012,{experienceId:this.getId()}),j.empty())}getTrafficAllocation(){const e={};for(const[t,i]of Object.entries(this.trafficAllocation))e[t]=__spreadValues({},i);return e}}class Fs extends xs{constructor(e,t,i,s){super(e,t,i,s),__publicField(this,"controlVariation");const n=i.buildControlVariation(this);this.controlVariation=n,this.allVariationsMap[n.getId()]=n}static filterExperiences(e){return e.filter((e=>"cc"===e.getType()))}getControlVariation(){return this.controlVariation}}class ks extends xs{constructor(e,t,i,s){super(e,t,i,s),__publicField(this,"logger"),__publicField(this,"variationPriority"),this.logger=i.getLogger(),this.variationPriority=t.variationPriority}static filterExperiences(e){return e.filter((e=>"rbp"===e.getType()))}getPrioritizedVariations(){const e=[];if(void 0!==this.variationPriority)for(const t of this.variationPriority)this.getVariation(t).ifPresent((t=>{e.push(t)})).ifAbsent((()=>{this.logger.debug("Could not find prioritized variation for RBP",1013,{experienceId:this.getId(),variationId:t})}));return e}}class Ps{constructor(e){__publicField(this,"enabled"),this.enabled=e.enabled}isEnabled(){return this.enabled}}class Ms{constructor(e){__publicField(this,"enabled"),this.enabled=e.enabled}isEnabled(){return this.enabled}}class Ds{constructor(e){__publicField(this,"enabled"),__publicField(this,"managedAccounts"),this.enabled=e.enabled,this.managedAccounts=e.managedAccounts}isEnabled(){return this.enabled}getManagedAccounts(){return this.managedAccounts}}class Ns{constructor(e){__publicField(this,"enabled"),__publicField(this,"autoConfig"),__publicField(this,"measurementIds"),this.enabled=e.enabled,this.autoConfig=e.autoConfig,this.measurementIds=e.measurementIds}isEnabled(){return this.enabled}isAutoConfig(){return this.autoConfig}getMeasurementIds(){return this.measurementIds}}class Os{constructor(e){__publicField(this,"enabled"),__publicField(this,"hubId"),__publicField(this,"objectsToIngest"),this.enabled=e.enabled,this.hubId=e.hubId,this.objectsToIngest=e.objectsToIngest}isEnabled(){return this.enabled}getHubId(){return this.hubId}getObjectsToIngest(){return this.objectsToIngest}}class Ls{constructor(e){__publicField(this,"enabled"),__publicField(this,"munchkinId"),__publicField(this,"fieldsToIngest"),__publicField(this,"listsToIngest"),this.enabled=e.enabled,this.munchkinId=e.munchkinId,this.fieldsToIngest=e.fieldsToIngest,this.listsToIngest=e.listsToIngest}isEnabled(){return this.enabled}getMunchkinId(){return this.munchkinId}getFieldsToIngest(){return this.fieldsToIngest}getListsToIngest(){return this.listsToIngest}}const Rs="Optimization Viewed";class Us{constructor(e){__publicField(this,"enabled"),__publicField(this,"eventName"),this.enabled=e.enabled,this.eventName=e.name}isEnabled(){return this.enabled}getEventName(){var e;return null!=(e=this.eventName)?e:Rs}}class $s{constructor(e){__publicField(this,"enabled"),__publicField(this,"objectsToIngest"),__publicField(this,"outboundReport"),this.enabled=e.enabled,this.objectsToIngest=e.objectsToIngest,this.outboundReport=e.outboundReport}isEnabled(){return this.enabled}getObjectsToIngest(){return this.objectsToIngest}getOutboundReport(){return j.ofNullable(this.outboundReport)}}class Vs{constructor(e){__publicField(this,"enabled"),__publicField(this,"eventName"),this.enabled=e.enabled,this.eventName=e.dim}isEnabled(){return this.enabled}getEventName(){return this.eventName}}class Ws{constructor(e,t,i){var s,n;__publicField(this,"logger"),__publicField(this,"id"),__publicField(this,"name"),__publicField(this,"type"),__publicField(this,"state"),__publicField(this,"iEvents",[]),__publicField(this,"experiences",[]),__publicField(this,"metrics",[]),__publicField(this,"code"),__publicField(this,"timeBudget"),__publicField(this,"changes"),this.logger=i.getLogger(),this.id=e,this.name=t.name,this.type=null!=(s=t.type)?s:"matching",void 0!==t.code&&(this.code=t.code),void 0!==t.timeBudget&&(this.timeBudget=t.timeBudget);const r=[];void 0!==t.changes&&t.changes.forEach((e=>{r.push(i.buildDomChange(e))})),this.changes=r,this.state=null!=(n=t.state)?n:"live"}addEvent(e){this.iEvents.push(e)}addMetric(e){this.metrics.push(e)}addExperience(e){this.experiences.push(e)}getId(){return this.id}getName(){return this.name}getType(){return this.type}getState(){return this.state}getCode(){return j.ofNullable(this.code)}getChanges(){return[...this.changes]}getExperiences(){return[...this.experiences]}getEvents(){return[...this.iEvents]}getTimeBudgetMs(){return j.ofNullable(this.timeBudget)}getMetrics(){return[...this.metrics]}getMetric(e){const t=this.metrics.filter((t=>t.getId()===e));return 0===t.length?j.empty():j.ofNullable(t[0])}executeCode(e){this.logger.debug("executeCode() if present");let t=!0;if(this.getCode().isPresent()){const s=this.getCode().get();try{this.logger.info(`Page ${this.id} code running`),t=e(s),this.logger.info(`Page ${this.id} code result => ${t}`)}catch(i){const e=new Ei(`Page (${this.id}) code execution failed`,i);t=!1,this.logger.error(e,1009,{pageId:this.id})}}return t}}class zs extends Ws{constructor(e,t,i){var s,n;super(e,t,i),__publicField(this,"templateUrl"),__publicField(this,"paramName"),__publicField(this,"paramValue"),this.templateUrl=t.templateUrl,this.paramName=null!=(s=t.paramName)?s:zs.defaultParamName,this.paramValue=null!=(n=t.paramValue)?n:zs.landingPageNameToParamValue(t.name)}static filterPages(e){return e.filter((e=>"landing"===e.getType()))}static removeWWW(e){return e.replace(/^www\./i,"")}static get defaultParamName(){return"page"}static landingPageNameToParamValue(e){return e.trim().replace(/[^\w\-._~!'()*]+/g,"-")}getTemplateUrl(){return this.templateUrl}getParamName(){return this.paramName}getParamValue(){return this.paramValue}getPreviewUrl(){const{name:e,value:t}=this.determineLandingParam(),i=`${encodeURIComponent(e)}=${encodeURIComponent(t)}`,s=this.templateUrl.indexOf("?");if(-1!==s)return this.templateUrl.slice(0,s+1)+i+"&"+this.templateUrl.slice(s+1);const n=this.templateUrl.indexOf("#");return-1!==n?this.templateUrl.slice(0,n)+"?"+i+this.templateUrl.slice(n):this.templateUrl+"?"+i}matches(e,t){this.logger.group("check page matches");try{const i=new URL(e),s=new URL(this.templateUrl);let n=!0;s.searchParams.forEach(((e,t)=>{i.searchParams.getAll(t).includes(e)||(n=!1)}));const r=j.ofNullable(s.hash).map((e=>e===i.hash)).orElse(!0),{name:o,value:a}=this.determineLandingParam(),l=i.searchParams.getAll(o).includes(a),c=zs.removeWWW(i.host)===zs.removeWWW(s.host),d=i.protocol===s.protocol&&i.port===s.port&&i.pathname===s.pathname&&c&&n&&l&&r,u=this.executeCode(t);return this.logger.debug(`didMatch: ${d.toString()}, didPassCode: ${u.toString()}`),this.logger.groupEnd(),d&&u}catch(i){const e=new Ei("Could not parse URL",i);return this.logger.warn(e,1018,{pageId:this.getId()}),!1}}determineLandingParam(){return{name:this.getParamName(),value:this.getParamValue()}}}const js=(e,t,i,s)=>{e.group("check page matches");const n=i.some((i=>{e.group("check url");const s=i.matches(t);return e.groupEnd(),s})),r=s();return e.debug(`didMatch: ${n.toString()}, didPassCode: ${r.toString()}`),e.groupEnd(),n&&r};class Bs extends Ws{constructor(e,t,i){super(e,t,i),__publicField(this,"urls"),__publicField(this,"previewUrl");const s=[];t.urls.forEach((e=>{s.push(i.buildUrl(e))})),this.urls=s,this.previewUrl=t.previewUrl}static filterPages(e){return e.filter((e=>"matching"===e.getType()))}getPreviewUrl(){return this.previewUrl}getUrls(){return this.urls}matches(e,t){return js(this.logger,e,this.urls,(()=>this.executeCode(t)))}}class Gs extends Ws{constructor(e,t,i){super(e,t,i),__publicField(this,"urls"),__publicField(this,"previewUrl");const s=[];t.urls.forEach((e=>{s.push(i.buildUrl(e))})),this.urls=s,this.previewUrl=t.previewUrl}static filterPages(e){return e.filter((e=>"shopify"===e.getType()))}getPreviewUrl(){return this.previewUrl}getUrls(){return this.urls}matches(e,t){return js(this.logger,e,this.urls,(()=>this.executeCode(t)))}}class Hs{constructor(e,t,i,s){let n;if(__publicField(this,"legacy"),__publicField(this,"shopifyMetric"),__publicField(this,"shopifyPages"),__publicField(this,"shopifyEvents"),__publicField(this,"shopifyCheckoutCompletedEvent"),__publicField(this,"shop"),__publicField(this,"snippet"),__publicField(this,"plus"),Hs.isLegacyShopifyIntegrationJson(e)){this.legacy=!0;const t=e;n={shop:`${t.shop}.myshopify.com`,snippet:t.theme,plus:!0};const s=[];i.forEach((e=>{t.pages.includes(e.getId())&&s.push(e)})),this.shopifyPages=s}else this.legacy=!1,n=e,this.shopifyPages=Gs.filterPages(i);this.shop=n.shop,this.snippet=n.snippet,this.plus=n.plus,this.shopifyEvents=xi.filterEvents(s);const r=this.shopifyEvents.find((e=>"checkout_completed"===e.getCustomerEventName()));N(r,'The shopify "checkout_completed" event must be present'),this.shopifyCheckoutCompletedEvent=r,this.shopifyMetric=t.find((e=>e.getIEvents().find((e=>e.getId()===this.shopifyCheckoutCompletedEvent.getId()))))}static isLegacyShopifyIntegrationJson(e){return void 0!==e.pixel}isPixelEnabled(){return!0}isLegacy(){return this.legacy}getShop(){return this.shop}getMetric(){return j.ofNullable(this.shopifyMetric)}getPages(){return this.shopifyPages}getEvents(){return this.shopifyEvents}getCheckoutCompletedEvent(){return this.shopifyCheckoutCompletedEvent}doesInjectSnippet(){return this.snippet}isShopifyPlus(){return this.plus}}class Qs{constructor(e){__publicField(this,"enabled"),__publicField(this,"oemEnabled"),this.enabled=e.enabled,this.oemEnabled=e.oemEnabled}isEnabled(){return this.enabled}isOemEnabled(){return this.oemEnabled}}class Ks{constructor(e,t,i,s,n){__publicField(this,"id"),__publicField(this,"name"),__publicField(this,"iEvents"),__publicField(this,"scope"),__publicField(this,"type"),__publicField(this,"countingMethod"),__publicField(this,"goal"),__publicField(this,"page"),__publicField(this,"campaign"),__publicField(this,"customer"),L(void 0===i||void 0===n),this.id=e.id,this.name=e.name,this.scope=e.scope,this.type=e.type,this.countingMethod=e.countingMethod,this.goal=e.isGoal,this.iEvents=e.eventIds.flatMap((e=>t.getEvent(e).toArray())),this.iEvents.forEach((e=>{e.addMetric(this)})),this.campaign=i,this.page=s,this.customer=n}getId(){return this.id}getName(){return this.name}getIEvents(){return this.iEvents}getScope(){return this.scope}getType(){return this.type}getCountingMethod(){return this.countingMethod}isGoal(){return this.goal}getCampaign(){return j.ofNullable(this.campaign)}getPage(){return j.ofNullable(this.page)}getCustomer(){return j.ofNullable(this.customer)}}class qs extends Ks{constructor(e,t,i,s,n){super(e,t,i,s,n),__publicField(this,"valueType"),this.valueType=e.valueType}getValueType(){return this.valueType}}class Ys extends qs{constructor(e,t,i,s,n){super(e,t,i,s,n),__publicField(this,"value"),this.value=e.value}getValue(){return this.value}}function Js(e,t=!1){return i=>i.map((t=>e(t))).orElse(t)}function Xs(e){switch(e.operator){case"includesAny":return Js((t=>t.some((t=>e.argument.includes(t)))));case"excludesAll":return Js((t=>!t.some((t=>e.argument.includes(t)))),!0);case vt:return e=>e.isPresent();case bt:return e=>!e.isPresent();default:throw new Error(`Unknown array enum operator: ${e.operator}`)}}function Zs(e){switch(e.operator){case"includesAny":return Js((t=>t.some((t=>e.argument.includes(t)))));case vt:return e=>e.isPresent();case bt:return e=>!e.isPresent();default:throw new Error(`Unknown array string operator: ${e.operator}`)}}function en(e){switch(e.operator){case"isTrue":return Js((e=>e));case"isFalse":return Js((e=>!e),!0);case vt:return e=>e.isPresent();case bt:return e=>!e.isPresent();default:throw new Error(`Unknown boolean operator: ${e.operator}`)}}function tn(e){switch(e.operator){case"eq":return Js((t=>t===e.argument));case"ne":return Js((t=>t!==e.argument),!0);case vt:return e=>e.isPresent();case bt:return e=>!e.isPresent();default:throw new Error(`Unknown enum operator: ${e.operator}`)}}function sn(e){switch(e.operator){case"==":return Js((t=>t===e.argument));case"!=":return Js((t=>t!==e.argument),!0);case">":return Js((t=>t>e.argument));case"<":return Js((t=>t<e.argument));case">=":return Js((t=>t>=e.argument));case"<=":return Js((t=>t<=e.argument));case vt:return e=>e.isPresent();case bt:return e=>!e.isPresent();default:throw new Error(`Unknown number operator: ${e.operator}`)}}function nn(e){switch(e.operator){case"eq":return Js((t=>t===e.argument));case"ne":return Js((t=>t!==e.argument),!0);case"substr":return Js((t=>t.includes(e.argument)));case"startsWith":return Js((t=>t.startsWith(e.argument)));case"endsWith":return Js((t=>t.endsWith(e.argument)));case"regex":return Js((t=>new RegExp(e.argument).test(t)));case vt:return e=>e.isPresent();case bt:return e=>!e.isPresent();default:throw new Error(`Unknown string operator: ${e.operator}`)}}function rn(e){switch(e.operator){case"withinPastDuration":return Js((t=>{const i=/^P(\d+)D$/.exec(e.argument);if(void 0===(null==i?void 0:i[1]))throw new Error(`Invalid unix time operator duration: ${e.argument}`);const s=24*Number(i[1])*60*60*1e3;return t>=Date.now()-s}));case"inBetween":return Js((t=>{const[i,s]=e.argument;return t>=i&&t<s}));case vt:return e=>e.isPresent();case bt:return e=>!e.isPresent();default:throw new Error(`Unknown unix time operator: ${e.operator}`)}}const on=e=>{const t=/[|\\{}()[\]^$+*?.-]/g;return e.replace(t,"\\$&")};class an{constructor(e){__publicField(this,"includeSubdomains"),__publicField(this,"domain"),__publicField(this,"port"),this.includeSubdomains=e.includeSubdomains,this.domain=e.domain,this.port=e.port}static isMatchingAllowedDomain(e,t){return new RegExp(`^${e}$`).test(t)}getIncludeSubdomains(){return this.includeSubdomains}getDomain(){return this.domain}getPort(){return j.ofNullable(this.port)}getDomainAndPort(){return void 0===this.port?this.domain:`${this.domain}:${this.port}`}isOriginAllowed(e){const t=this.getDomainAndPort();let i="https?://";return i+=this.includeSubdomains?"([^.]+\\.)?":"(www\\.)?",i+=on(t),an.isMatchingAllowedDomain(i,e)}}class ln{constructor(e,t,i){this.sticky=e,this.relativeExpirationTimeSec=t,this.absoluteExpirationTimeSec=i}isSticky(){return this.sticky}getRelativeExpirationTimeSec(){return j.ofNullable(this.relativeExpirationTimeSec)}getAbsoluteExpirationTimeSec(){return j.ofNullable(this.absoluteExpirationTimeSec)}isExpired(e,t){return void 0!==this.absoluteExpirationTimeSec&&t<this.absoluteExpirationTimeSec||void 0!==this.relativeExpirationTimeSec&&t+this.relativeExpirationTimeSec<e}}class cn{constructor(e,t){__publicField(this,"logger"),__publicField(this,"value"),__publicField(this,"match"),this.logger=t.getLogger(),this.value=e.value,this.match=e.match,L(void 0!==this.match,"invalid match type: "+e.match)}static simplifyUrl(e){let t=e;const i=t.indexOf("?");i>0&&(t=t.slice(0,Math.max(0,i)));const s=t.indexOf("#");s>0&&(t=t.slice(0,Math.max(0,s))),t.endsWith("/")&&(t=t.slice(0,Math.max(0,t.length-1)));const n=/^(?:https?:\/\/)?(?:www\.)?(.+)$/.exec(t);return void 0!==(null==n?void 0:n[1])&&(t=n[1]),t}getMatch(){return this.match}getValue(){return this.value}matches(e){let t=!1;switch(this.logger.debug("checking match: "+this.match+", value: "+this.value),this.match){case"simple":t=cn.simplifyUrl(this.value)===cn.simplifyUrl(e);break;case"regex":{const i=new RegExp(this.value);t=Boolean(i.test(e));break}case"exact":t=e===this.value;break;case"substring":t=e.includes(this.value);break;default:this.logger.error(`Unknown url match type: ${this.match}`,1010)}return this.logger.debug(t.toString()),t}}function dn(e){return"[object Array]"===Object.prototype.toString.call(e)}function un(e){return"[object Arguments]"===Object.prototype.toString.call(e)}function hn(e){return"[object Object]"===Object.prototype.toString.call(e)}function gn(e){return"[object Function]"===Object.prototype.toString.call(e)}function pn(e){return"[object HTMLImageElement]"===Object.prototype.toString.call(e)}function mn(e,t){const i=[];for(const s of e)t.includes(s)||i.push(s);for(const s of t)e.includes(s)||i.push(s);return i}function vn(e){return e.replace(/([a-z\d])([A-Z])/g,"$1-$2").toLowerCase()}function bn(e,t){const i=e.indexOf(t);i>-1&&e.splice(i,1)}function fn(e){return Object.fromEntries(Object.entries(e).map((([e,t])=>[t,e])))}const En={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"},_n=e=>String(e).replace(/[&<>"'`=/]/g,(e=>{var t;return null!=(t=En[e])?t:e})),yn=(e,t,i=!0)=>{let s="";return t.forEach((t=>{s+=Sn(e,t,i)})),s},Sn=(e,t,i=!0)=>{switch(t.type){case"string":return t.value;case"attribute":return wn(e,t.value,i);default:throw new Error(`Unsupported TemplateFragment type: ${t.type}`)}},wn=(e,{attrDefId:t,defaultString:i},s)=>{const n=new it(t);let r=e.getAttributeValue(n).orElse(i);return dn(r)&&(r=In(e,n,i)),s?_n(String(r)):String(r)},In=(e,t,i)=>e.getAttributeValue(t).flatMap((([e])=>j.ofNullable(e))).orElse(i);class Cn{constructor(e,t,i,s){__publicField(this,"attributeData"),__publicField(this,"environment"),__publicField(this,"localDocument"),__publicField(this,"parent"),__publicField(this,"changeIndex"),this.parent=e,this.changeIndex=t,this.attributeData=i,this.environment=s,this.localDocument=s.getWindow().document}getGuid(){return`${this.parent.getGuid()}-c${this.changeIndex}}`}getEntityIds(){return __spreadProps(__spreadValues({},this.parent.getEntityIds()),{changeId:String(this.changeIndex)})}getRegionSelectors(){return this.getSelectors().flatMap((e=>e.split(",")))}}const Tn="data-imize";function xn(e,t,i){var s;const n=e;n.imizeChangeData=null!=(s=n.imizeChangeData)?s:{},m(n.imizeChangeData)?n.imizeChangeData[t]=i:Mo.error("Invalid imizeChangeData value: must be an object",118)}function An(e,t){return j.ofNullable(On(e)[t])}function Fn(e,t){delete On(e)[t],0===Object.keys(On(e)).length&&Ln(e)}function kn(e){const t=e;return void 0!==t.imizeChangeData&&Object.keys(t.imizeChangeData).length>0}function Pn(e){return`${Tn}-${e}`}function Mn(e){return document.querySelectorAll(`[${Pn(e)}]`)}function Dn(e,t){e.setAttribute(Pn(t),t)}function Nn(e,t){e.removeAttribute(Pn(t))}function On(e){var t;const i=e;return m(i.imizeChangeData)||f(i.imizeChangeData)?null!=(t=i.imizeChangeData)?t:{}:(Mo.error("Invalid imizeChangeData value: must be undefined or an object",119),{})}function Ln(e){delete e.imizeChangeData}function Rn(e,t){const i={};for(const[s,n]of Object.entries(On(e)))n.type===t&&(i[s]=n.value);return i}function Un(e){return{getGuid:()=>`e${e.getExperience().getId()}-v${e.getId()}`,getEntityIds:()=>({variationId:e.getId(),experienceId:e.getExperience().getId()})}}function $n(e){return{getGuid:()=>`p${e.getId()}`,getEntityIds:()=>({pageId:e.getId()})}}function Vn(e){return{getGuid:()=>`ve${e.id}`,getEntityIds:()=>({})}}const Wn=class extends Cn{constructor(e,t,i,s,n){super(t,i,s,n),__publicField(this,"attributeChange"),this.attributeChange=e}static getIsValidAttribute(e,t){const i=e.tagName.toLowerCase();return Wn.VALID_TAGS_BY_NON_STANDARD_ATTRIBUTE[t].includes(i)}isReadyToApply(){return this.attributeChange.isReadyToApply(this.localDocument)}apply(e,t){Mo.group("EnhancedAttributeChange.apply()"),Mo.debug(`Applying change: ${Le(this.attributeChange)}`),t.registerSelectorAndExecute(this.attributeChange.getSelector(),(t=>{this.applyChanges(t,e)}),{shouldReapply:e=>this.shouldReapply(e)},this.getEntityIds()),Mo.groupEnd()}undo(){Mo.debug(`[EnhancedAttributeChange]: Undoing change: ${Le(this.attributeChange)}`),this.attributeChange.getId().ifPresent((e=>{const t=Mn(e);Array.prototype.forEach.call(t,(t=>{if(t instanceof HTMLElement){const i=An(t,e);if(i.isPresent()){const e=i.get().value;Object.keys(this.attributeChange.getAttributes()).forEach((i=>{switch(i){case"html":void 0!==e.html&&(t.innerHTML=this.renderContent(e.html));break;case"className":void 0!==e.className&&(t.className=e.className);break;case"alt":case"src":case"srcset":case"sizes":case"href":{const s=e[i];void 0===s?t.removeAttribute(i):t.setAttribute(i,s);break}case"css":case"style":void 0!==e.style&&(t.style.cssText=e.style);break;default:Mo.error(`[EnhancedAttributeChange]: Cannot undo unknown attribute: ${i}`,121,this.getEntityIds())}}))}else Mo.error(`[EnhancedAttributeChange]: Expected change data, but there was none: ${Le(this.attributeChange)}`,122,this.getEntityIds())}else Mo.error(new Error("[EnhancedAttributeChange]: Found matching element that is not an HTMLElement"),123,this.getEntityIds());Nn(t,e),Fn(t,e)}))}))}getSelectors(){return[this.attributeChange.getSelector()]}applyChanges(e,t){let i=!1;Mo.group(`EnhancedAttributeChange.applyChanges(${Le(this.attributeChange)})`),this.attributeChange.getId().ifPresent((t=>{Dn(e,t)}));const s={};Object.keys(this.attributeChange.getAttributes()).forEach((n=>{switch(n){case"html":{const{html:t}=this.attributeChange.getAttributes();void 0!==t&&(s.html=e.innerHTML,e.innerHTML=this.renderContent(t)),Mo.debug("Applied html");break}case"css":case"style":break;case"className":{const{className:t}=this.attributeChange.getAttributes();void 0!==t&&(s.className=e.className,e.className=t),Mo.debug("Applied className");break}case"src":this.applyNonStandardAttribute(n,e,s),pn(e)&&!e.complete&&(Mo.debug("attaching listener for img src loading"),i=!0,e.addEventListener("load",(()=>{try{Mo.debug("EnhancedAttributeChange() image loaded! calling onReadyToRender()"),t()}catch(e){const t=new Ei("Exception in image onload handler",e);Mo.error(t,236,this.getEntityIds())}}),{once:!0}));break;case"alt":case"srcset":case"sizes":case"href":this.applyNonStandardAttribute(n,e,s);break;default:Mo.error(`[EnhancedAttributeChange]: Cannot apply unknown attribute: ${n}`,120,this.getEntityIds())}}));const n=this.createCssText(e);void 0!==n&&(s.style=e.style.cssText,e.style.cssText=n,Mo.debug("Applied css")),this.attributeChange.getId().ifPresent((t=>{xn(e,t,{value:s,type:gt.ATTRIBUTE})})),i||(Mo.debug("EnhancedAttributeChange() not waiting; calling onReadyToRender()"),t()),Mo.debug("DOM modifications successfully applied for this change."),Mo.groupEnd()}applyNonStandardAttribute(e,t,i){const s=Wn.getIsValidAttribute(t,e),n=this.attributeChange.getAttributes()[e];if(s&&void 0!==n){const s=t.getAttribute(e);null!==s&&(i[e]=s),t.setAttribute(e,n)}else Mo.error(`This ${t.tagName} does not support this attribute: ${e}`,131,this.getEntityIds());Mo.debug(`Applied ${n}`)}createCssText(e){const{css:t,style:i}=this.attributeChange.getAttributes();if(f(t))return i;const s=null!=i?i:"";let n="";return Object.entries(t).forEach((([e,t])=>{const i=vn(e);if(!new RegExp(i+"\\s*:").test(s)){const i=t.includes("!important")?t:`${t} !important`;n+=`${vn(e)}:${i};`}})),f(i)?e.style.cssText+n:n+s}shouldReapply(e){for(const t in this.attributeChange.getAttributes())if(Object.prototype.hasOwnProperty.call(this.attributeChange.getAttributes(),t))switch(Mo.debug(`shouldReapply(): CHECKING "${t}"`),t){case"css":case"className":case"style":case"alt":Mo.debug(`shouldReapply(): SKIP (attribute "${t}" not checked)`);break;case"src":case"srcset":case"sizes":case"href":{const i=this.attributeChange.getAttributes()[t];if(void 0!==i){const s=e.getAttribute(t);if(""===i&&null===s){Mo.debug(`shouldReapply(): SKIP (value of "${t}" is null; configured value is empty string)`);break}if(i!==s)return Mo.debug(`shouldReapply(): YES (value of "${t}" changed from "${i}" to "${s}")`),!0;Mo.debug(`shouldReapply(): SKIP (value of "${t}" did not change)`);break}Mo.debug(`shouldReapply(): SKIP (configured value of "${t}" is undefined)`);break}case"html":{const i=this.attributeChange.getAttributes()[t],s=document.createElement("div");if(void 0!==i&&(s.innerHTML=this.renderContent(i)),e.textContent!==s.textContent)return Mo.debug(`shouldReapply(): YES (The visible text of the element has changed from "${s.textContent}" to "${e.textContent}")`),!0;Mo.debug(`shouldReapply(): SKIP (The text of the element has not changed; comparing configured "${s.textContent}" to current "${e.textContent}")`);break}default:Mo.error(`[EnhancedAttributeChange]: shouldReapply() found unknown attribute: ${t}`,201,this.getEntityIds())}return Mo.debug("shouldReapply(): NO (all attributes skipped)"),!1}renderContent(e){return"string"==typeof e?e:yn(this.attributeData,e,!0)}};let zn=Wn;__publicField(zn,"VALID_TAGS_BY_NON_STANDARD_ATTRIBUTE",{alt:["img"],src:["img"],srcset:["img"],sizes:["img"],href:["a"]});class jn extends Cn{constructor(e,t,i,s,n){super(t,i,s,n),__publicField(this,"customCodeChange"),this.customCodeChange=e}isReadyToApply(){return this.customCodeChange.isReadyToApply(this.localDocument)}apply(e,t){Mo.group("EnhancedCustomCodeChange.apply()"),Mo.debug(`Applying change: ${Le(this.customCodeChange)}`);try{this.customCodeChange.getCss().ifPresent((e=>{this.injectCss(e)})),t.detachObserver(),this.customCodeChange.getCode().ifPresent((e=>{this.runCode(e)}))}finally{t.attachObserver(),e(),Mo.groupEnd()}}undo(){}getSelectors(){return[]}runCode(e){try{Mo.info("Running code for CUSTOM_CODE change"),Dt.eval(e)}catch(t){throw new Ei("Code execution failed",t)}}injectCss(e){try{const t=this.customCodeChange.getId().orElseRun(zt);Mo.info("Injecting css for CUSTOM_CODE change"),Dt.injectCss(e,`dom-change-${t}`,document)}catch(t){throw new Ei("Css injection failed",t)}}}var Bn={};!function(e){var t=Object.defineProperty,i=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,r=(e,i,s)=>i in e?t(e,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[i]=s,o=(e,t,i)=>(r(e,"symbol"!=typeof t?t+"":t,i),i);Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let a=class extends Error{constructor(e){super(e),this.name="UnexpectedNullException"}};function l(e,t){if(null==e||"string"==typeof e&&0===e.length)throw new a(t);return e}class c extends Error{constructor(e){super(e),this.name="IllegalArgumentException"}}let d=class extends Error{constructor(e){super(e),this.name="NoSuchElementException"}};var u=Object.defineProperty,h=(e,t,i)=>{return r=i,(n="symbol"!=typeof t?t+"":t)in(s=e)?u(s,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[n]=r,i;var s,n,r};class g{static empty(){return v.INSTANCE}static of(e){return new p(l(e))}static ofNullable(e){return null==e||"string"==typeof e&&0===e.length?v.INSTANCE:new p(e)}}class p extends g{constructor(e){super(),h(this,"value"),this.value=e}isPresent(){return!0}ifPresent(e){return e(this.value),this}ifAbsent(e){return this}get(){return this.value}orElse(e){return this.value}orElseRun(e){return this.value}toArray(){return[this.value]}map(e){return g.ofNullable(e(this.value))}flatMap(e){return e(this.value)}toString(){return`Optional[${this.value.toString()}]`}}const m=class extends g{isPresent(){return!1}ifPresent(e){return this}ifAbsent(e){return e(),this}get(){throw new d("No value present")}orElse(e){return e}orElseRun(e){return e()}toArray(){return[]}map(e){return g.empty()}flatMap(e){return g.empty()}toString(){return"Optional.empty"}};let v=m;h(v,"INSTANCE",new m);const b="imize-extension";var f=Object.prototype.toString,E=Array.isArray||function(e){return"[object Array]"===f.call(e)};function _(e){return"function"==typeof e}function y(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function S(e,t){return null!=e&&"object"==typeof e&&t in e}var w=RegExp.prototype.test,I=/\S/;function C(e){return t=I,i=e,!w.call(t,i);var t,i}var T={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"},x=/\s*/,A=/\s+/,F=/\s*=/,k=/\s*\}/,P=/#|\^|\/|>|\{|&|=|!/;function M(e){this.string=e,this.tail=e,this.pos=0}function D(e,t){this.view=e,this.cache={".":this.view},this.parent=t}function N(){this.templateCache={_cache:{},set:function(e,t){this._cache[e]=t},get:function(e){return this._cache[e]},clear:function(){this._cache={}}}}M.prototype.eos=function(){return""===this.tail},M.prototype.scan=function(e){var t=this.tail.match(e);if(!t||0!==t.index)return"";var i=t[0];return this.tail=this.tail.substring(i.length),this.pos+=i.length,i},M.prototype.scanUntil=function(e){var t,i=this.tail.search(e);switch(i){case-1:t=this.tail,this.tail="";break;case 0:t="";break;default:t=this.tail.substring(0,i),this.tail=this.tail.substring(i)}return this.pos+=t.length,t},D.prototype.push=function(e){return new D(e,this)},D.prototype.lookup=function(e){var t,i,s,n=this.cache;if(n.hasOwnProperty(e))t=n[e];else{for(var r,o,a,l=this,c=!1;l;){if(e.indexOf(".")>0)for(r=l.view,o=e.split("."),a=0;null!=r&&a<o.length;)a===o.length-1&&(c=S(r,o[a])||(i=r,s=o[a],null!=i&&"object"!=typeof i&&i.hasOwnProperty&&i.hasOwnProperty(s))),r=r[o[a++]];else r=l.view[e],c=S(l.view,e);if(c){t=r;break}l=l.parent}n[e]=t}return _(t)&&(t=t.call(this.view)),t},N.prototype.clearCache=function(){void 0!==this.templateCache&&this.templateCache.clear()},N.prototype.parse=function(e,t){var i=this.templateCache,s=e+":"+(t||O.tags).join(":"),n=void 0!==i,r=n?i.get(s):void 0;return null==r&&(r=function(e,t){if(!e)return[];var i,s,n,r=!1,o=[],a=[],l=[],c=!1,d=!1,u="",h=0;function g(){if(c&&!d)for(;l.length;)delete a[l.pop()];else l=[];c=!1,d=!1}function p(e){if("string"==typeof e&&(e=e.split(A,2)),!E(e)||2!==e.length)throw new Error("Invalid tags: "+e);i=new RegExp(y(e[0])+"\\s*"),s=new RegExp("\\s*"+y(e[1])),n=new RegExp("\\s*"+y("}"+e[1]))}p(t||O.tags);for(var m,v,b,f,_,S,w=new M(e);!w.eos();){if(m=w.pos,b=w.scanUntil(i))for(var I=0,T=b.length;I<T;++I)C(f=b.charAt(I))?(l.push(a.length),u+=f):(d=!0,r=!0,u+=" "),a.push(["text",f,m,m+1]),m+=1,"\n"===f&&(g(),u="",h=0,r=!1);if(!w.scan(i))break;if(c=!0,v=w.scan(P)||"name",w.scan(x),"="===v?(b=w.scanUntil(F),w.scan(F),w.scanUntil(s)):"{"===v?(b=w.scanUntil(n),w.scan(k),w.scanUntil(s),v="&"):b=w.scanUntil(s),!w.scan(s))throw new Error("Unclosed tag at "+w.pos);if(_=">"==v?[v,b,m,w.pos,u,h,r]:[v,b,m,w.pos],h++,a.push(_),"#"===v||"^"===v)o.push(_);else if("/"===v){if(!(S=o.pop()))throw new Error('Unopened section "'+b+'" at '+m);if(S[1]!==b)throw new Error('Unclosed section "'+S[1]+'" at '+m)}else"name"===v||"{"===v||"&"===v?d=!0:"="===v&&p(b)}if(g(),S=o.pop())throw new Error('Unclosed section "'+S[1]+'" at '+w.pos);return function(e){for(var t,i=[],s=i,n=[],r=0,o=e.length;r<o;++r)switch((t=e[r])[0]){case"#":case"^":s.push(t),n.push(t),s=t[4]=[];break;case"/":n.pop()[5]=t[2],s=n.length>0?n[n.length-1][4]:i;break;default:s.push(t)}return i}(function(e){for(var t,i,s=[],n=0,r=e.length;n<r;++n)(t=e[n])&&("text"===t[0]&&i&&"text"===i[0]?(i[1]+=t[1],i[3]=t[3]):(s.push(t),i=t));return s}(a))}(e,t),n&&i.set(s,r)),r},N.prototype.render=function(e,t,i,s){var n=this.getConfigTags(s),r=this.parse(e,n),o=t instanceof D?t:new D(t,void 0);return this.renderTokens(r,o,i,e,s)},N.prototype.renderTokens=function(e,t,i,s,n){for(var r,o,a,l="",c=0,d=e.length;c<d;++c)a=void 0,"#"===(o=(r=e[c])[0])?a=this.renderSection(r,t,i,s,n):"^"===o?a=this.renderInverted(r,t,i,s,n):">"===o?a=this.renderPartial(r,t,i,n):"&"===o?a=this.unescapedValue(r,t):"name"===o?a=this.escapedValue(r,t,n):"text"===o&&(a=this.rawValue(r)),void 0!==a&&(l+=a);return l},N.prototype.renderSection=function(e,t,i,s,n){var r=this,o="",a=t.lookup(e[1]);if(a){if(E(a))for(var l=0,c=a.length;l<c;++l)o+=this.renderTokens(e[4],t.push(a[l]),i,s,n);else if("object"==typeof a||"string"==typeof a||"number"==typeof a)o+=this.renderTokens(e[4],t.push(a),i,s,n);else if(_(a)){if("string"!=typeof s)throw new Error("Cannot use higher-order sections without the original template");null!=(a=a.call(t.view,s.slice(e[3],e[5]),(function(e){return r.render(e,t,i,n)})))&&(o+=a)}else o+=this.renderTokens(e[4],t,i,s,n);return o}},N.prototype.renderInverted=function(e,t,i,s,n){var r=t.lookup(e[1]);if(!r||E(r)&&0===r.length)return this.renderTokens(e[4],t,i,s,n)},N.prototype.indentPartial=function(e,t,i){for(var s=t.replace(/[^ \t]/g,""),n=e.split("\n"),r=0;r<n.length;r++)n[r].length&&(r>0||!i)&&(n[r]=s+n[r]);return n.join("\n")},N.prototype.renderPartial=function(e,t,i,s){if(i){var n=this.getConfigTags(s),r=_(i)?i(e[1]):i[e[1]];if(null!=r){var o=e[6],a=e[5],l=e[4],c=r;0==a&&l&&(c=this.indentPartial(r,l,o));var d=this.parse(c,n);return this.renderTokens(d,t,i,c,s)}}},N.prototype.unescapedValue=function(e,t){var i=t.lookup(e[1]);if(null!=i)return i},N.prototype.escapedValue=function(e,t,i){var s=this.getConfigEscape(i)||O.escape,n=t.lookup(e[1]);if(null!=n)return"number"==typeof n&&s===O.escape?String(n):s(n)},N.prototype.rawValue=function(e){return e[1]},N.prototype.getConfigTags=function(e){return E(e)?e:e&&"object"==typeof e?e.tags:void 0},N.prototype.getConfigEscape=function(e){return e&&"object"==typeof e&&!E(e)?e.escape:void 0};var O={name:"mustache.js",version:"4.2.0",tags:["{{","}}"],clearCache:void 0,escape:void 0,parse:void 0,render:void 0,Scanner:void 0,Context:void 0,Writer:void 0,set templateCache(e){L.templateCache=e},get templateCache(){return L.templateCache}},L=new N;O.clearCache=function(){return L.clearCache()},O.parse=function(e,t){return L.parse(e,t)},O.render=function(e,t,i,s){if("string"!=typeof e)throw new TypeError('Invalid template! Template should be a "string" but "'+(E(n=e)?"array":typeof n)+'" was given as the first argument for mustache#render(template, view, partials)');var n;return L.render(e,t,i,s)},O.escape=function(e){return String(e).replace(/[&<>"'`=\/]/g,(function(e){return T[e]}))},O.Scanner=M,O.Context=D,O.Writer=N;const R="standard",U="custom",$="url_param",V="marketo",W="salesforce",z="firmographic",j="6sense",B="googleAds",G="demandbase",H=[R,U,$,V,W,z,j,B,G];function Q(e){return"object"==typeof e&&e instanceof Error?e:new Error(e)}function K(){return function(e){let t=Date.now();return e.replace(/[xy]/g,(e=>{const i=Math.trunc((t+16*Math.random())%16);return t=Math.floor(t/16),("x"===e?i:3&i|8).toString(16)}))}("xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx")}class q{constructor(e,t){o(this,"storage"),o(this,"namespace"),this.storage=e,this.namespace=t}static get delimiter(){return"."}write(e,t){this.storage.write(this.getFullKey(e),t)}read(e){return this.storage.read(this.getFullKey(e))}delete(e){this.storage.delete(this.getFullKey(e))}getFullKey(e){return`${this.namespace}${q.delimiter}${e}`}}class Y{constructor(e,t){o(this,"logger"),o(this,"namespace"),this.logger=e,this.namespace=t}getLevel(){return this.logger.getLevel()}error(e,t,i){this.logger.error(this.getFullMessage(e),t,i)}warn(e,t,i){this.logger.warn(this.getFullMessage(e),t,i)}info(e,t,i){this.logger.info(this.getFullMessage(e),t,i)}debug(e,t,i){this.logger.debug(this.getFullMessage(e),t,i)}group(e){this.logger.group(this.getFullMessage(e))}groupEnd(){this.logger.groupEnd()}time(e){this.logger.time(this.getFullMessage(e))}timeEnd(e){this.logger.timeEnd(this.getFullMessage(e))}getFullMessage(e){return`[${this.namespace}]: ${e}`}}e.ATTRIBUTE_NAMESPACES=H,e.ATTRIBUTE_NAMESPACE_CUSTOM=U,e.ATTRIBUTE_NAMESPACE_DEMANDBASE=G,e.ATTRIBUTE_NAMESPACE_FIRMOGRAPHIC=z,e.ATTRIBUTE_NAMESPACE_GOOGLE_ADS=B,e.ATTRIBUTE_NAMESPACE_MARKETO=V,e.ATTRIBUTE_NAMESPACE_SALESFORCE=W,e.ATTRIBUTE_NAMESPACE_SIXSENSE=j,e.ATTRIBUTE_NAMESPACE_STANDARD=R,e.ATTRIBUTE_NAMESPACE_URL_PARAM=$,e.Extension=class{constructor(e){o(this,"config"),this.config=e}getName(){return this.config.name}getDescription(){return this.config.description}getLabel(){return this.config.label}getIconImgSrc(){return g.ofNullable(this.config.iconImageSrc)}getCss(){return g.ofNullable(this.config.css)}getDom(){return void 0===this.config.dom?g.empty():g.of({html:this.config.dom.html,targetPosition:g.ofNullable(this.config.dom.targetPosition)})}getFields(){return this.config.fields}getViewData(e,t){var i,s;return null==(s=(i=this.config).viewDataFn)?void 0:s.call(i,e,t)}getRunFn(){var e;return g.ofNullable(null==(e=this.config.code)?void 0:e.run)}getResetFn(){var e;return g.ofNullable(null==(e=this.config.code)?void 0:e.reset)}},e.ExtensionExecutor=class{constructor(e,t,i,s=K()){let n;o(this,"extension"),o(this,"runtime"),o(this,"extensionStorage"),o(this,"extensionLogger"),o(this,"config"),o(this,"styleElementId"),o(this,"instanceId"),o(this,"targetPositionTuple"),o(this,"viewData"),e.getDom().ifPresent((e=>{!function(e,t){if(l(e),!e)throw new c(t)}(e.targetPosition.isPresent()||void 0!==i.targetPosition,"ExtensionExecutor expects a TargetPosition for extensions that use HTML"),void 0===i.targetPosition?(t.logger.debug("Using TargetPosition from Extension config"),n=[e.html,e.targetPosition.get()]):(t.logger.debug("Using TargetPosition from Executor config"),n=[e.html,i.targetPosition])})),this.targetPositionTuple=n,this.extension=e,this.runtime=t,this.extensionStorage=new q(t.storage,e.getName()),this.extensionLogger=new Y(t.logger,`Extension: ${e.getName()}`),this.config=i,this.instanceId=s,this.styleElementId=`extension-${e.getName()}`,this.viewData=this.extension.getViewData(this.fieldData,this.runtime)}get fieldData(){const e=((e,t)=>{for(var o in t||(t={}))s.call(t,o)&&r(e,o,t[o]);if(i)for(var o of i(t))n.call(t,o)&&r(e,o,t[o]);return e})({},this.config.fieldData);return this.extension.getFields().forEach((t=>{"defaultValue"in t&&void 0===e[t.name]&&(e[t.name]=t.defaultValue)})),e}run(e){this.runtime.logger.debug(`ExtensionExecutor(${this.extension.getName()}): Calling run()`),this.injectCss(),this.insertElement((()=>{this.runCode(),void 0!==e&&e()}))}reset(){this.runtime.logger.debug(`ExtensionExecutor(${this.extension.getName()}): Calling reset()`),this.resetCode(),this.removeElement(),this.removeCss()}getTargetPosition(){return this.getTargetPositionTuple().isPresent()?g.of(this.getTargetPositionTuple().get()[1]):g.empty()}getTargetElement(){if(this.getTargetPosition().isPresent()){const{selector:e}=this.getTargetPosition().get(),t=this.runtime.localWindow.document.querySelectorAll(e);return t.length>1&&this.runtime.logger.error(`Expected exactly one target element; found ${t.length} elements matching selector "${e}"`,7e3),g.ofNullable(t[0])}return g.empty()}injectCss(){this.extension.getCss().ifPresent((e=>{this.runtime.logger.debug(`ExtensionExecutor(${this.extension.getName()}): Injecting CSS`),this.runtime.injectCss(e,this.styleElementId)}))}insertElement(e){this.getTargetPositionTuple().ifPresent((t=>{const[,{selector:i}]=t;this.getTargetElement().ifPresent((s=>{this.runtime.logger.debug(`ExtensionExecutor(${this.extension.getName()}): Inserting Extension HTML at "${i}" ${JSON.stringify(this.fieldData,void 0,2)}`);try{this.insertElementAtTarget(t,s)}catch(n){const e=Q(n);this.runtime.logger.error(e,7001)}e()})).ifAbsent((()=>{this.runtime.logger.error(`ExtensionExecutor(${this.extension.getName()}): Could not find a TargetElement for selector ${i}`,7002),e()}))})).ifAbsent((()=>{e()}))}insertElementAtTarget(e,t){const[i,{insertPosition:s}]=e,n=O.render(i,{extension:{fieldData:this.fieldData,viewData:this.viewData}}),r=this.createElement(n);t.insertAdjacentElement(s,r)}runCode(){this.extension.getRunFn().ifPresent((e=>{e(this.getContext())}))}resetCode(){this.extension.getResetFn().ifPresent((e=>{e(this.getContext())}))}removeElement(){this.getTargetPositionTuple().isPresent()&&Array.prototype.forEach.call(this.getElements(),(e=>{e.remove()}))}removeCss(){this.extension.getCss().isPresent()&&this.runtime.removeCss(this.styleElementId)}getTargetPositionTuple(){return g.ofNullable(this.targetPositionTuple)}getElements(){return this.getTargetPositionTuple().isPresent()?[...this.runtime.localWindow.document.querySelectorAll(`[data-${b}="${this.instanceId}"]`)]:[]}getContext(){return{fieldData:this.fieldData,localWindow:this.runtime.localWindow,logger:this.extensionLogger,storage:this.extensionStorage}}createElement(e){const t=function(e,t){const i=t.createElement("template");return i.innerHTML=e.trim(),[...i.content.childNodes]}(e,this.runtime.localWindow.document);if(1!==t.length)throw new Error(`Unable to create a single DOM Node from the extension HTML for "${this.extension.getName()}"`);const[i]=t;if(!(i instanceof HTMLElement))throw new TypeError(`The root Node of the tree constructed from the extension HTML for "${this.extension.getName()}" is not an instance of an Element`);return i.setAttribute(`data-${b}`,this.instanceId),i}},e.exceptionToError=Q}(Bn);class Gn extends Cn{constructor(e,t,i,s,n,r,o){super(t,i,s,n),__publicField(this,"insertExtensionChange"),__publicField(this,"extensionManager"),__publicField(this,"runtime"),__publicField(this,"_executor"),this.insertExtensionChange=e,this.extensionManager=o;const a=n.getWindow();this.runtime={localWindow:a,logger:Mo,storage:r,injectCss(e,t){Dt.injectCss(e,t,a.document)},removeCss(e){Dt.removeCss(e,a.document)}}}isReadyToApply(){return!this.getExecutor().getTargetPosition().isPresent()||this.getExecutor().getTargetElement().isPresent()}apply(e,t){Mo.group("EnhancedExtensionChange.apply()"),Mo.debug(`Applying change: ${Le(this.insertExtensionChange)}`),this.getExecutor().getTargetPosition().ifPresent((({selector:i})=>{t.registerSelectorAndExecute(i,(()=>{this.getExecutor().run(e)}),{shouldReapply:this.shouldReapply.bind(this)},this.getEntityIds())})),Mo.groupEnd()}undo(){Mo.group("EnhancedExtensionChange.undo()"),Mo.debug(`Undoing change: ${Le(this.insertExtensionChange)}`),this.getExecutor().reset(),Mo.groupEnd()}getSelectors(){return[]}shouldReapply(){return!this.getExecutor().getTargetPosition().isPresent()||!this.getExecutor().getTargetElement().isPresent()}getExecutor(){if(void 0===this._executor){const e=this.extensionManager.getExtension(this.insertExtensionChange.getName());if(!e.isPresent())throw new Error(`Could not find extension: ${this.insertExtensionChange.getName()}`);this._executor=new Bn.ExtensionExecutor(e.get(),this.runtime,this.insertExtensionChange.getExecutorJson(),this.insertExtensionChange.getId().map((e=>e)).orElse(void 0))}return this._executor}}class Hn extends Cn{constructor(e,t,i,s,n){super(t,i,s,n),__publicField(this,"insertElementChange"),this.insertElementChange=e}isReadyToApply(){return this.insertElementChange.isReadyToApply(this.localDocument)}apply(e,t){Mo.group("EnhancedInsertElementChange.apply()"),Mo.debug(`Applying change: ${Le(this.insertElementChange)}`);const i=this.createInsertionElement();t.registerSelectorAndExecute(this.insertElementChange.getTargetPosition().selector,(()=>{this.insertElement(i,e)}),{shouldReapply:this.shouldReapply.bind(this)},this.getEntityIds()),Mo.groupEnd()}undo(){Mo.group("EnhancedInsertElementChange.undo()"),Mo.debug(`Undoing change: ${Le(this.insertElementChange)}`),this.insertElementChange.getId().ifPresent((e=>{const t=Mn(e);1===t.length?t[0].remove():Mo.warn(`Unexpected error when undoing insertElementChange. Auto-generated attribute\n        selector expected 1 element match but found ${t.length}`,191,this.getEntityIds())})),Mo.groupEnd()}getSelectors(){return[]}insertElement(e,t){this.removeExistingElements(),this.insertElementChange.getTargetElement(this.localDocument).ifPresent((i=>{i.insertAdjacentElement(this.insertElementChange.getTargetPosition().insertPosition,e),t()})).ifAbsent((()=>{throw new Error("Unexpected error: targetElement not found")})),this.insertElementChange.getId().ifPresent((t=>{xn(e,t,{value:this.insertElementChange.getElementType(),type:gt.INSERT_ELEMENT}),Dn(e,t)}))}getInsertedElements(){return document.querySelectorAll(`#${this.insertElementChange.getIdAttribute()}`)}removeExistingElements(){const e=this.getInsertedElements();Array.prototype.forEach.call(e,(e=>{e.remove()}))}createInsertionElement(){let e;if(this.insertElementChange instanceof ji)e=`h${this.insertElementChange.getSize()}`;else switch(this.insertElementChange.getElementType()){case"image":e="img";break;case"video":e="video";break;default:e="div"}const t=document.createElement(e);return t.id=this.insertElementChange.getIdAttribute(),t}shouldReapply(){return 0===this.getInsertedElements().length}}const Qn=`data-${"imize-move-id"}`;class Kn extends Cn{constructor(e,t,i,s,n){super(t,i,s,n),__publicField(this,"moveElementChange"),__publicField(this,"hasLoggedMutationWarning",!1),this.moveElementChange=e}static createAndInsertPlaceholderElement(e){const t=document.createElement("imizeplaceholder");return t.style.display="none",t.hidden=!0,e.after(t),t}isReadyToApply(){return this.moveElementChange.isReadyToApply(this.localDocument)}apply(e,t){Mo.group("EnhancedMoveElementChange.apply()"),Mo.debug(`Applying change: ${Le(this.moveElementChange)}`),t.registerSelectorAndExecute(this.moveElementChange.getSelector(),(()=>{}),{shouldReapply:this.shouldReapply.bind(this)},this.getEntityIds()),this.moveElement(e),Mo.groupEnd()}undo(){Mo.group("EnhancedMoveElementChange.undo()"),Mo.debug(`Undoing change: ${Le(this.moveElementChange)}`),this.moveElementChange.getId().ifPresent((e=>{const t=Mn(e);if(1===t.length){const i=t[0];An(i,e).ifPresent((({value:e})=>{null!==e.parentElement&&e.parentElement.replaceChild(i,e)})).ifAbsent((()=>{Mo.warn(`Unexpected error when undoing moveElementChange. getChangeData(${e}): could not find data`,195,this.getEntityIds())})),Nn(i,e),Fn(i,e),i.removeAttribute(Qn)}else Mo.warn(`Unexpected error when undoing moveElementChange. Auto-generated attribute\n        selector expected 1 element match but found ${t.length}`,196,this.getEntityIds())})),Mo.groupEnd()}getSelectors(){return[this.moveElementChange.getSelector()]}getMovedElement(){const e=document.querySelectorAll(`[${Qn}="${this.moveElementChange.getMoveId()}"]`);return 1===e.length?j.ofNullable(e[0]):j.empty()}shouldReapply(){return this.hasLoggedMutationWarning||(this.getMovedElement().ifPresent((()=>{Mo.warn("[EnhancedMoveElementChange]: A mutation was observed for a move element change, however the moved element appears to still exists on the page",225,this.getEntityIds())})).ifAbsent((()=>{Mo.warn("[EnhancedMoveElementChange]: A mutation was observed for a move element change and the moved element could not be found on the page.",226,this.getEntityIds())})),this.hasLoggedMutationWarning=!0),!1}moveElement(e){try{this.moveElementChange.getSelectorElement(this.localDocument).ifPresent((e=>{this.moveElementChange.getTargetElement(this.localDocument).ifPresent((t=>{if(e.contains(t))throw new Error("The target element cannot be a child of the selector element");this.moveElementChange.getId().ifPresent((t=>{Dn(e,t);const i=Kn.createAndInsertPlaceholderElement(e);xn(e,t,{value:i,type:gt.MOVE_ELEMENT})})),t.insertAdjacentElement(this.moveElementChange.getTargetPosition().insertPosition,e),e.setAttribute(Qn,this.moveElementChange.getMoveId())})).ifAbsent((()=>{throw new Error("Unexpected error: target element could not be found)")}))})).ifAbsent((()=>{throw new Error("Unexpected error: selector element could not be found)")}))}finally{e()}}}const qn=`${xe}hidden-variation`,Yn="data-intellimize-var-";class Jn extends Cn{constructor(e,t,i,s,n){super(t,i,s,n),__publicField(this,"webflowChange"),__publicField(this,"experienceId"),__publicField(this,"variationId"),__publicField(this,"optimizationWrappersSelector"),__publicField(this,"elementsWithStyleVariationsSelector"),this.webflowChange=e;const r=t.getEntityIds();this.experienceId=N(r.experienceId,"experienceId must be defined"),this.variationId=N(r.variationId,"variationId must be defined"),this.optimizationWrappersSelector=`[${Ae}${this.experienceId}]`,this.elementsWithStyleVariationsSelector=`[${Fe}${this.experienceId}]`}isReadyToApply(){return this.localDocument.querySelectorAll("body").length>0}apply(e,t){Mo.group("EnhancedWebflowChange.apply()"),Mo.debug(`Applying change: ${Le(this.webflowChange)} - ${this.optimizationWrappersSelector},${this.elementsWithStyleVariationsSelector}`),t.registerSelectorAndExecute("body",(()=>{this.localDocument.querySelectorAll(`[${this.getWebflowExperienceAttribute()}]`).forEach((e=>{e.setAttribute(qn,"")})),this.localDocument.querySelectorAll(`[${this.getWebflowVariationAttribute()}]`).forEach((e=>{e.removeAttribute(qn)})),this.environment.getWindow().document.body.setAttribute(this.getIntellimizeVisibleVariationAttribute(),""),e()}),{shouldReapply:()=>this.shouldReapply()},this.getEntityIds()),Mo.groupEnd()}undo(){}getSelectors(){return[this.optimizationWrappersSelector,this.elementsWithStyleVariationsSelector]}getWebflowExperienceAttribute(){return`${Ae}${this.experienceId}`}getWebflowVariationAttribute(){return`${ke}${this.variationId}`}getIntellimizeVisibleVariationAttribute(){return`${Yn}${this.variationId}`}shouldReapply(){const{body:e}=this.environment.getWindow().document;for(const t of this.localDocument.querySelectorAll(`[${this.getWebflowExperienceAttribute()}]`)){const i=t.hasAttribute(this.getWebflowVariationAttribute());if(Mo.debug(`CHECKING SHOULDREAPPLY: ${i}, ${t.hasAttribute(qn)}, ${e.hasAttribute(this.getIntellimizeVisibleVariationAttribute())}, ${t.outerHTML}`),!i&&!t.hasAttribute(qn))return Mo.debug(`shouldReapply(): YES (The [${qn}] attribute is missing from a non-selected variation)`),!0;if(i&&t.hasAttribute(qn))return Mo.debug(`shouldReapply(): YES (The [${qn}] attribute is present on the selected variation)`),!0}return e.hasAttribute(this.getIntellimizeVisibleVariationAttribute())?(Mo.debug("shouldReapply(): NO (All data attributes are as expected)"),!1):(Mo.debug(`shouldReapply(): YES (The [${Yn}${this.variationId}] attribute is missing from the body)`),!0)}}function Xn(e,t,i,s,n,r,o){switch(e.getType()){case gt.ATTRIBUTE:case gt.EXTENSION_ATTRIBUTE:return new zn(e,t,i,s,n);case gt.CUSTOM_CODE:return new jn(e,t,i,s,n);case gt.INSERT_ELEMENT:return new Hn(e,t,i,s,n);case gt.EXTENSION:return new Gn(e,t,i,s,n,r,o);case gt.MOVE_ELEMENT:return new Kn(e,t,i,s,n);case gt.WEBFLOW:return new Jn(e,t,i,s,n);default:throw new Error(`Unknown DomChangeType: ${e.getType()}`)}}class Zn extends Rt{constructor(e,t,i,s){super(),__publicField(this,"elementObserver"),__publicField(this,"entityIds"),__publicField(this,"workQueue",[]),__publicField(this,"readyToRender"),__publicField(this,"isTaskDone",!1),this.elementObserver=s,this.readyToRender=t,this.entityIds=i,this.workQueue=e.map((e=>({enhancedChange:e,applied:!1,rendered:!1})))}toString(){return`ChangelistTask(count:${this.workQueue.length}, ${Le(this.entityIds)})`}cleanup(){this.isTaskDone=!0,this.readyToRenderCallbackIfDone()}inTransaction(){return!0}isUnloading(){return!1}getEntityIds(){return this.entityIds}run(){for(const t of this.workQueue)if(!t.applied)try{if(!t.enhancedChange.isReadyToApply())return Nt.TRY_LATER;t.enhancedChange.apply((()=>{t.rendered=!0,this.readyToRenderCallbackIfDone()}),this.elementObserver),t.applied=!0}catch(e){const i=new Ei(`Failed to apply change: ${Le(t.enhancedChange.getEntityIds())}`,e);return Mo.error(i,125,t.enhancedChange.getEntityIds()),this.isTaskDone=!0,this.readyToRenderCallbackIfDone(),Nt.FAILED}return this.isTaskDone=!0,this.readyToRenderCallbackIfDone(),Nt.SUCCEEDED}readyToRenderCallbackIfDone(){if(!this.isTaskDone)return;this.workQueue.filter((e=>e.applied)).every((e=>__async(this,null,(function*(){return e.rendered}))))&&this.readyToRender()}}class er extends Rt{constructor(e,t){super(),__publicField(this,"variation"),__publicField(this,"environment"),__publicField(this,"browserStorage"),__publicField(this,"extensionManager"),__publicField(this,"browserEventLogger"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"pageContext"),__publicField(this,"attributeData"),__publicField(this,"user"),__publicField(this,"decisionContext"),__publicField(this,"startPageviewId"),__publicField(this,"override"),__publicField(this,"statusModel"),__publicField(this,"getVariationRecordedCallbackTuples"),__publicField(this,"elementObserver"),__publicField(this,"hider"),this.variation=N(e),this.environment=N(t.environment),this.browserStorage=N(t.browserStorage),this.extensionManager=N(t.extensionManager),this.browserEventLogger=N(t.browserEventLogger),this.customerStorage=N(t.customerStorage),this.attributeStorage=N(t.attributeStorage),this.activityStorage=N(t.activityStorage),this.pageContext=N(t.pageContext),this.attributeData=N(t.attributeData),this.decisionContext=N(t.decisionContext),this.user=N(t.user),this.override=N(t.override),this.statusModel=N(t.statusModel),this.startPageviewId=N(t.startPageviewId),this.hider=N(t.hider),this.getVariationRecordedCallbackTuples=N(t.getVariationRecordedCallbackTuples),this.elementObserver=N(t.elementObserver)}toString(){return`ExecuteVariationTask(${this.variation.getId()})`}getEntityIds(){return{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()}}inTransaction(){return this.isReadyToExecute()}isUnloading(){return!1}getNotDoneDependencies(){return this.findDependentPageChangelistTasks().filter((e=>!e.isDone()))}run(){let e=Nt.TRY_LATER;return this.isReadyToExecute()&&(e=this.executeVariation()),e}onReadyToRender(){this.hider.revealExperience(this.variation.getExperience(),this.browserStorage,this.attributeData)}isReadyToExecute(){const e=this.passDependencies(),t=this.passExperiencePreconditions(),i=this.passVariationPreconditions(),s=this.hasAppliedPageChanges();return e&&t&&i&&s}passDependencies(){const e=this.variation.getExperience();Mo.group("check experience dependsOnExperiences "+e.getId());const t=e.getDependsOnExperiences().every((e=>!this.decisionContext.isDecidingExperience(e)));return Mo.debug(t.toString()),Mo.groupEnd(),this.statusModel.setExperience(e,"dependsOnExperiences",t),t}passExperiencePreconditions(){const e=this.variation.getExperience();Mo.group("check experience preconditions "+e.getId());const t=e.getPreconditions().every((t=>{let i=!1;try{i=Dt.evalBoolean(t),Mo.info(`Experience ${e.getId()} precondition => ${i}`)}catch(s){const t=new Ei(`Experience (${e.getId()}) precondition code execution failed`,s);i=!1,Mo.error(t,15,{experienceId:e.getId()})}return i}));return Mo.debug(t.toString()),Mo.groupEnd(),this.statusModel.setExperience(e,"preconditions",t),t}passVariationPreconditions(){const e=this.variation.getExperience();Mo.group("check variations preconditions "+this.variation.getId());const t=this.variation.getPreconditions().every((t=>{let i=!1;try{i=Dt.evalBoolean(t),Mo.info(`Variation ${e.getId()}/${this.variation.getId()} precondition => ${i}`)}catch(s){const t=new Ei(`Variation (${this.variation.getId()}) precondition code execution failed`,s);i=!1,Mo.error(t,16,{experienceId:e.getId(),variationId:this.variation.getId()})}return i}));return Mo.debug(t.toString()),Mo.groupEnd(),this.statusModel.setVariation(this.variation,"preconditions",t),t}hasAppliedPageChanges(){return this.findDependentPageChangelistTasks().every((e=>e.isDone()))}findDependentPageChangelistTasks(){return this.variation.getExperience().getPages().map((e=>this.pageContext.getPageChangelistTask(e))).filter((e=>e.isPresent())).map((e=>e.get()))}}class tr{constructor(e,t,i,s){__publicField(this,"iEvent"),__publicField(this,"actionId"),__publicField(this,"eventInstanceId"),__publicField(this,"eventValue"),__publicField(this,"nativeClickEvent"),this.iEvent=e,this.actionId=t,this.eventInstanceId=`${e.getId()}:${t}`,this.eventValue=i,this.nativeClickEvent=s}getEvent(){return this.iEvent}getEventId(){return this.iEvent.getId()}getActionId(){return this.actionId}getEventInstanceId(){return this.eventInstanceId}getEventValue(){return j.ofNullable(this.eventValue)}getNativeMouseEvent(){return j.ofNullable(this.nativeClickEvent)}toString(){return`ConversionEventContext(${this.getEventId()}, ${this.getEventInstanceId()})`}}function ir(e){return e.getIntegrationDemandbase().map((e=>e.isEnabled())).orElse(!1)}function sr(e){return e.getIntegrationFirmographic().map((e=>e.isEnabled())).orElse(!1)}const nr=["marketo","salesforce","firmographic","6sense","googleAds","demandbase","hubspot"],rr=(e,t)=>`i${e}${t}`,or={adGroup:rr("ga","ag"),campaign:rr("ga","cm"),network:rr("ga","nt"),adType:rr("ga","at"),keyword:rr("ga","kw"),creative:rr("ga","cr"),matchType:rr("ga","mt")};function ar(e){return e.getIntegrationGoogleAds().map((e=>e.isEnabled())).orElse(!1)}function lr(e,t,i){if(!Et(e,i).orElse(!1)){const i=cr(e);t.setDataAndMetadata("googleAds",i,void 0)}}function cr(e){const t={};return Object.entries(or).forEach((([i,s])=>{e.getHostingPageUrl().getQueryParam(s,b).ifPresent((e=>{t[i]=e}))})),t}function dr(e){return e.getIntegrationHubspot().map((e=>e.isEnabled())).orElse(!1)}function ur(e){return e.readCookie(Ce)}function hr(e){return"hsFormCallback"===e.data.type&&"onFormSubmitted"===e.data.eventName}function gr(e){return e.getIntegrationMarketo().map((e=>e.isEnabled())).orElse(!1)}function pr(e){return e.readCookie(Ie)}function mr(e){var t,i;return void 0!==(null==(t=null==e?void 0:e.lead)?void 0:t.fields)||void 0!==(null==(i=null==e?void 0:e.lead)?void 0:i.lists)}function vr(e){var t,i;return void 0===(null==(t=null==e?void 0:e.lead)?void 0:t.fields)&&void 0===(null==(i=null==e?void 0:e.lead)?void 0:i.lists)}function br(e){if(void 0===e)return j.empty();if(mr(e))return j.of(e);if(vr(e)){const t=e,{smartLists:i,staticLists:s}=t,n=__objRest(t,["smartLists","staticLists"]),r=(null!=i?i:[]).map((e=>({type:"smart",id:Number(e)}))),o=(null!=s?s:[]).map((e=>({type:"static",id:Number(e)})));return j.of({lead:{fields:n,lists:[...r,...o]}})}return j.empty()}const fr=rr("sf","lh"),Er=rr("sf","ch");function _r(e){return e.getIntegrationSalesforce().map((e=>e.isEnabled())).orElse(!1)}function yr(e){const t=e.getHostingPageUrl().getQueryParam(fr,b),i=e.getHostingPageUrl().getQueryParam(Er,b);if(t.isPresent()||i.isPresent()){const e={};return t.ifPresent((t=>{e.leadIdHash=t})),i.ifPresent((t=>{e.contactIdHash=t})),j.of(e)}return j.empty()}function Sr(e){return e.getIntegrationSixsense().map((e=>e.isEnabled())).orElse(!1)}const wr={"6sense":e=>Sr(e),demandbase:e=>ir(e),firmographic:e=>sr(e),googleAds:e=>ar(e),marketo:e=>gr(e),salesforce:e=>_r(e),hubspot:e=>dr(e)};class Ir extends Rt{constructor(e,t,i){super(),__publicField(this,"conversionEventContext"),__publicField(this,"browserEventLogger"),__publicField(this,"customerStorage"),__publicField(this,"trackBrowserEvents",{}),__publicField(this,"hasSentGoals",!1),this.conversionEventContext=N(e),this.browserEventLogger=N(t),this.customerStorage=N(i)}toString(){return"GoalTask("+this.conversionEventContext.toString()+")"}getEntityIds(){return{eventId:this.conversionEventContext.getEventId()}}inTransaction(){return!0}isUnloading(){return!1}run(){this.hasSentGoals||this.sendGoalsOnce();if(Object.keys(this.trackBrowserEvents).map((e=>this.trackBrowserEvents[e])).some((e=>void 0===e||!Rt.isDone(e))))return Mo.debug("Waiting for goal event delivery confirmation"),Nt.TRY_LATER;return Object.keys(this.trackBrowserEvents).map((e=>this.trackBrowserEvents[e])).every((e=>void 0!==e&&e===Nt.SUCCEEDED))?Nt.SUCCEEDED:Nt.FAILED}sendGoalsOnce(){Mo.debug("sendGoalsOnce()"),this.hasSentGoals=!0;const e="click"!==this.conversionEventContext.getEvent().getType();let t=0;this.conversionEventContext.getEvent().getMetrics().forEach((i=>{i.getCampaign().ifPresent((s=>{s.getExperiences().forEach((s=>{this.customerStorage.getVariation(s).ifPresent((n=>{const r=i.getId()+":"+n.getId();this.trackBrowserEvents[r]=Nt.TRY_LATER,Mo.info(`Sending goal ${i.getId()} event`),t++,this.browserEventLogger.sendGoal(n,i,this.conversionEventContext,e).then((()=>{Mo.info(`Goal ${i.getId()} event delivery success`),this.trackBrowserEvents[r]=Nt.SUCCEEDED})).catch((e=>{const t=new Ei(`Goal ${i.getId()} event delivery failed`,e);Mo.error(t,78,{eventId:this.conversionEventContext.getEventId(),experienceId:s.getId(),variationId:n.getId()}),this.trackBrowserEvents[r]=Nt.FAILED}))}))}))}))})),Mo.debug(`Sent ${t} goal events`)}}class Cr extends Rt{constructor(e,t,i,s,n){super(),__publicField(this,"conversionEventContext"),__publicField(this,"browserEventLogger"),__publicField(this,"customer"),__publicField(this,"customerStorage"),__publicField(this,"statusModel"),__publicField(this,"conversionSent",!1),__publicField(this,"conversionStatus",Nt.NOT_STARTED),__publicField(this,"goalTask"),this.conversionEventContext=N(e),this.browserEventLogger=N(t),this.customer=N(i),this.customerStorage=N(s),this.statusModel=N(n)}toString(){return`ConversionEventTask(${this.conversionEventContext.toString()})`}getEntityIds(){return{eventId:this.conversionEventContext.getEventId()}}inTransaction(){return!this.conversionSent}isUnloading(){return!1}run(){return this.sendOnce(),this.scheduleGoalTaskOnce(),Rt.isDone(this.conversionStatus)?!this.customer.hasClassicAccess()||void 0!==this.goalTask&&Rt.isDone(this.goalTask.getStatus())?this.conversionStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:Nt.FAILED:(Mo.debug("Waiting for goal task event confirmation"),Nt.TRY_LATER):(Mo.debug("Waiting for conversion event delivery confirmation"),Nt.TRY_LATER)}sendOnce(){if(!this.conversionSent){Mo.info(`Sending conversion event ${this.conversionEventContext.toString()}`);const e=this.conversionEventContext.getEvent(),t=[],i=e.getMetrics().map((i=>{const s=Bt();return t.push({metric:i,modelEventId:s}),{metric:i,event:e,status:Nt.WAITING,id:s}}));this.statusModel.setMetricEventData(i),this.browserEventLogger.sendConversion(this.conversionEventContext).then((()=>{Mo.info(`Conversion event ${this.conversionEventContext.toString()} delivery success`),this.conversionStatus=Nt.SUCCEEDED,this.statusModel.setMetricEventData(t.map((({modelEventId:t,metric:i})=>({metric:i,event:e,status:Nt.SUCCEEDED,id:t}))))})).catch((i=>{const s=new Ei(`Conversion event ${this.conversionEventContext.toString()} delivery failed`,i);Mo.warn(s,76,{eventId:this.conversionEventContext.getEventId()}),this.conversionStatus=Nt.FAILED,this.statusModel.setMetricEventData(t.map((({modelEventId:t,metric:i})=>({metric:i,event:e,status:Nt.FAILED,id:t}))))})),this.conversionSent=!0}}scheduleGoalTaskOnce(){this.customer.hasClassicAccess()&&void 0===this.goalTask&&(this.goalTask=new Ir(this.conversionEventContext,this.browserEventLogger,this.customerStorage),this.manager.addTask(this.goalTask))}}class Tr extends Rt{constructor(e,t,i,s,n,r){super(),__publicField(this,"sent",!1),__publicField(this,"deliveryStatus",Nt.NOT_STARTED),__publicField(this,"environment"),__publicField(this,"customer"),__publicField(this,"userDomain1"),__publicField(this,"userId1"),__publicField(this,"userDomain2"),__publicField(this,"userId2"),this.environment=e,this.customer=t,this.userDomain1=i,this.userId1=s,this.userDomain2=n,this.userId2=r}static get ENDPOINT(){return"/user-alias"}toString(){return`UserAliasTask(userDomain1: ${this.userDomain1}, userId1: ${this.userId1}, userDomain2: ${this.userDomain2}, userId2: ${this.userId2})`}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.deliveryStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:this.deliveryStatus===Nt.FAILED?Nt.FAILED:(Mo.debug("Waiting for user alias delivery confirmation"),Nt.TRY_LATER)}sendOnce(){this.sent||(Mo.info(`Sending request to set up user alias for userDomain1: ${this.userDomain1}, userId1: ${this.userId1}, userDomain2: ${this.userDomain2}, userId2: ${this.userId2}`),this.environment.fetch(se,Tr.ENDPOINT,{method:"POST",headers:{"Content-Type":"text/plain;charset=UTF-8"},body:Re({customerId:this.customer.getId(),userDomain1:this.userDomain1,userId1:this.userId1,userDomain2:this.userDomain2,userId2:this.userId2})}).then((()=>{this.deliveryStatus=Nt.SUCCEEDED,Mo.info(`Successfully set up user alias for userDomain1: ${this.userDomain1}, userId1: ${this.userId1}, userDomain2: ${this.userDomain2}, userId2: ${this.userId2}`)})).catch((e=>{const t=fi(e);Mo.error(t,92),this.deliveryStatus=Nt.FAILED})),this.sent=!0)}}function xr(e){if("string"==typeof e){const t=e.search(/[.\-\d]/g);if(-1===t)return!1;const i=e.slice(Math.max(0,t));if(!/^-?\d*\.?\d{0,2}$/.test(i))return!1}else{if("number"!=typeof e)return!1;{const t=String(e);if(!/^-?\d*\.?\d{0,2}$/.test(t))return!1}}return!0}function Ar(e){return"string"==typeof e&&/^[\u0021-\u007E]{1,300}$/.test(e)}function Fr(e){return hn(e)&&("number"==typeof e.amount||"string"==typeof e.amount)&&"string"==typeof e.currencyCode}function kr(e,t,i,s,n,r,o,a,l,c,d){Mr(e,t).ifPresent((({eventName:u,eventActionId:h,eventValue:g})=>{var p;if(i.getShopifyEvent(u).ifPresent((u=>{const p=new tr(u,h,g,void 0);try{o.add(Gr.buildShopifyActivity(e,t,p,i,s,a,r,n))}catch(v){const e=new Ei("Failed to build Shopify Activity",v);Mo.error(e,234)}const m=new Cr(p,d,i,n,l);c.addTask(m)})).ifAbsent((()=>{Mo.error(`Shopify pixel event not configured for customer: "${e.name}"`,252)})),"checkout_completed"===u){const t=null==(p=e.data.checkout.order)?void 0:p.id;void 0===t?Mo.error("Received a checkout_completed event without an order id",250):i.getShopifyServerSideEvent().ifPresent((()=>{const e=new Tr(s,i,"__shopifyOrder",t,"intellimize",a.getId());c.addTask(e)})).ifAbsent((()=>{Mo.error("Shopify server-side event not configured for customer",253)}))}}))}function Pr(e,t,i){let s;switch(e.name){case"collection_viewed":{const t=e;s=__spreadProps(__spreadValues({},i),{eventType:"s",customerEventName:"collection_viewed",data:$r(t.data.collection)});break}case"product_viewed":{const n=e;s=__spreadProps(__spreadValues({},i),{eventType:"s",customerEventName:"product_viewed",data:Ur(n.data.productVariant,Nr("product_viewed"),t)});break}case"checkout_started":{const n=e;s=__spreadProps(__spreadValues({},i),{eventType:"s",customerEventName:"checkout_started",data:Vr(n.data.checkout,Nr("checkout_started"),t)});break}case"checkout_completed":{const n=e;s=__spreadProps(__spreadValues({},i),{eventType:"s",customerEventName:"checkout_completed",data:Vr(n.data.checkout,Nr("checkout_completed"),t)});break}case"product_added_to_cart":{const n=e;s=__spreadProps(__spreadValues({},i),{eventType:"s",customerEventName:"product_added_to_cart",data:Wr(n.data.cartLine,Nr("product_added_to_cart"),t)});break}case"search_submitted":{const t=e;s=__spreadProps(__spreadValues({},i),{eventType:"s",customerEventName:"search_submitted",data:zr(t.data.searchResult)});break}default:throw new Error(`Unsupported customer event: ${e.name}`)}return Mo.debug(`Shopify activity (${e.name}): ${Le(s.data)}`),s}function Mr(e,t){var i,s;let n;if(!hn(e))return Rr("Not an object"),j.empty();if(!b(e.name))return Rr(`Event name is not a valid event name: "${e.name}"`),j.empty();if(!Dr(e.name))return j.empty();if(!Ar(e.id))return Rr(`Event ID is not a valid action id: "${e.id}"`),j.empty();if(!f(e.data)){if(!hn(e.data))return Rr(`Event data is defined, and is not an object: "${Le(e.data)}"`),j.empty();Or(e.name,e.data,t).ifPresent((e=>{n=e}))}let r=e.id;if("checkout_completed"===e.name){const t=e;(null==(s=null==(i=t.data)?void 0:i.checkout)?void 0:s.order)?r=t.data.checkout.order.id:Mo.error("Received a checkout_completed event without an order id",249)}return j.of({eventName:e.name,eventActionId:r,eventValue:n})}function Dr(e){return pt.includes(e)}function Nr(e){switch(e){case"product_viewed":return"productVariant.price.amount";case"product_added_to_cart":return"cartLine.merchandise.price.amount";case"checkout_started":case"checkout_completed":return"checkout.subtotalPrice.amount";default:throw new Error(`Cannot get event value path for ${e}`)}}function Or(e,t,i){let s,n;function r(e){return hn(e)?!!Fr(e.subtotalPrice)||(Rr(`Checkout data exists, but subtotal price is not valid: "${Le(e.subtotalPrice)}"`),!1):(Rr("Checkout data is not an object"),!1)}switch(e){case"checkout_started":if(!r(t.checkout))return j.empty();s=t.checkout.subtotalPrice.amount,n=Nr("checkout_started");break;case"checkout_completed":if(!r(t.checkout))return j.empty();s=t.checkout.subtotalPrice.amount,n=Nr("checkout_completed");break;case"product_added_to_cart":if(!hn(t.cartLine))return Rr("CartLine data is not an object"),j.empty();if(!hn(t.cartLine.merchandise))return Rr("CartLine data exists, but does not include a product variant"),j.empty();if(!Fr(t.cartLine.merchandise.price))return Rr(`CartLine ProductVariant data exists, but price is not valid: "${Le(t.cartLine.merchandise.price)}"`),j.empty();s=t.cartLine.merchandise.price.amount,n=Nr("product_added_to_cart");break;case"product_viewed":if(!hn(t.productVariant))return Rr("ProductVariant data is not an object"),j.empty();if(!Fr(t.productVariant.price))return Rr(`ProductVariant data exists, but price is not valid: "${Le(t.productVariant.price)}"`),j.empty();s=t.productVariant.price.amount,n=Nr("product_viewed");break;default:return j.empty()}const o=Lr(s,n,i);return xr(o)?j.of(o):(Rr(`Event value is not valid: "${s}"`),j.empty())}function Lr(e,t,i){let s=Number(e)/i;return/^-?\d*\.\d{3,}$/.test(String(s))&&(Mo.debug(`Found currency with > 2 decimal places: "${t}" = "${s}"`),s=Math.round(100*(Number(s)+Number.EPSILON))/100),s}function Rr(e){Mo.error(`Invalid Shopify Pixel Event: ${e}`,233)}function Ur(e,t,i){var s,n;return{productVariantPriceAdjusted:Lr(e.price.amount,t,i),productVariantPrice:e.price.amount,productVariantPriceCode:e.price.currencyCode,productVariantSku:null!=(s=e.sku)?s:void 0,productVariantId:e.id,productVariantTitle:e.title,productId:e.product.id,productTitle:e.product.title,productType:null!=(n=e.product.type)?n:void 0}}function $r(e){return{collectionId:e.id}}function Vr(e,t,i){const s=[],n=[];return e.lineItems.forEach((({variant:e})=>{null!==e&&(s.push(e.id),n.push(e.product.id))})),{checkoutSubtotalAdjusted:Lr(e.subtotalPrice.amount,t,i),checkoutSubtotal:e.subtotalPrice.amount,checkoutSubtotalCode:e.subtotalPrice.currencyCode,productVariantIds:s,productIds:n}}function Wr(e,t,i){return __spreadValues({cartTotalAdjusted:Lr(e.cost.totalAmount.amount,t,i),cartTotal:e.cost.totalAmount.amount,cartTotalCode:e.cost.totalAmount.currencyCode},Ur(e.merchandise,t,i))}function zr(e){return{searchQuery:e.query}}function jr(){return{"data.productVariantPriceAdjusted":"spvpa","data.productVariantPrice":"spvp","data.productVariantPriceCode":"spvpc","data.productVariantSku":"spvsku","data.productVariantId":"spvid","data.productVariantTitle":"spvt","data.productId":"spid","data.productTitle":"spt","data.productType":"spty","data.collectionId":"scid","data.checkoutSubtotalAdjusted":"scosta","data.checkoutSubtotal":"scost","data.checkoutSubtotalCode":"scostc","data.productVariantIds":"spvids","data.productIds":"spids","data.cartTotalAdjusted":"scata","data.cartTotal":"scat","data.cartTotalCode":"scatc","data.searchQuery":"ssq"}}const Br=class{constructor(e,t,i,s){__publicField(this,"storageKey"),__publicField(this,"browserStorage"),__publicField(this,"environment"),__publicField(this,"override"),this.storageKey=de+e.getId(),this.browserStorage=t,this.environment=i,this.override=s}static buildPageviewActivity(e,t,i,s,n){return __spreadValues({eventType:"pv"},Br.setCommonActivityFields(e,t,i,s,n))}static buildViewActivity(e,t,i,s,n){const r=e.getExperience(),o=r.getCampaign(),a=o.getCustomer();return __spreadValues({eventType:"v",campaignId:o.getId(),experienceId:r.getId(),variationId:e.getId()},Br.setCommonActivityFields(a,t,i,s,n))}static buildConversionActivity(e,t,i,s,n,r){const o=__spreadValues({eventType:"c",actionId:e.getActionId(),eventId:e.getEventId(),eventInstanceId:e.getEventInstanceId()},Br.setCommonActivityFields(t,i,s,n,r));return e.getEventValue().ifPresent((e=>{o.eventValue=Br.transformEventValue(e)})),o}static buildShopifyActivity(e,t,i,s,n,r,o,a){return Pr(e,t,Br.buildConversionActivity(i,s,n,r,o,a))}static setCommonActivityFields(e,t,i,s,n){const r={userVisitStatus:n.getUserVisitStatus(i),userBucket:i.getUserBucket(),pageviewId:t.getPageviewId(),sessionId:n.updateAndGetSessionId(),time:t.getNowUnixTimeMs(),timeZone:t.getTimeZone()};n.getCustomerControlStatus(e).ifPresent((e=>{r.controlStatus=e})),t.getReferrerUrl().ifPresent((e=>{r.referrerUrl=e.getRawUrl()})),r.hostingPageUrl=t.getHostingPageUrl().getRawUrl(),t.getHtmlLang().ifPresent((e=>{r.htmlLang=e}));const o=s.getFlattenedInternalAttributes();Object.keys(o).length>0&&(r.trafficSource=o.ts,r.utmTerm=o.utt,r.utmMedium=o.utm,r.utmSource=o.uts,r.utmContent=o.utcn,r.utmCampaign=o.utcm);const a=s.getFlattenedCustomAttributes();Object.keys(a).length>0&&(r.customAttributes=a);const l=t.getWebflowPageMetadata();return void 0!==l.domain&&(r.webflowDomain=l.domain),void 0!==l.siteId&&(r.webflowSiteId=l.siteId),void 0!==l.pageId&&(r.webflowPageId=l.pageId),void 0!==l.collectionId&&(r.webflowCollectionId=l.collectionId),void 0!==l.itemSlug&&(r.webflowItemSlug=l.itemSlug),r}static transformEventValue(e){if("string"==typeof e)return Number.parseFloat(e)}add(e){if(this.override.saveActivity().orElse(!0)){const t=this.compressActivity(e),i=this.getAllCompressed();i.push(t),this.save(i)}}getAll(){return this.getAllCompressed().map((e=>this.decompressActivity(e)))}backoffAndSave(e){e.shift(),this.save(e)}save(e){const t=Re(e);if(t.length>Br.CHARACTER_LIMIT&&e.length>0)this.backoffAndSave(e);else try{this.browserStorage.write(this.storageKey,t)}catch(i){if(!(Pt(i)&&e.length>0))throw i;Mo.warn("localStorage quota exceeded. Removing old activity",220),this.backoffAndSave(e)}}getAllCompressed(){let e=[];try{e=this.browserStorage.read(this.storageKey).map((e=>{const t=Ue(e);if(!Array.isArray(t))throw new TypeError("Expected an array");return t})).orElse([])}catch(i){e=[];const t=new Ei("Could not read Activities from localStorage",i);Mo.error(t,221)}const t=this.environment.getNowUnixTimeMs()-Br.TIME_LIMIT_MS;return e.filter((e=>e.t>=t))}compressActivity(e){const t={};for(const[i,s]of Object.entries(e))if("data"===i&&m(s))for(const[e,n]of Object.entries(s)){const i=`data.${e}`;void 0===Br.KEY_MAP[i]?(Mo.error(`Unknown key ${i} in Activity`,222),t[i]=n):t[Br.KEY_MAP[i]]=n}else void 0===Br.KEY_MAP[i]?(Mo.error(`Unknown key ${i} in Activity`,222),t[i]=s):t[Br.KEY_MAP[i]]=s;return t}decompressActivity(e){const t={};for(const[i,s]of Object.entries(e))if(void 0===Br.REVERSE_KEY_MAP[i])Mo.error(`Unknown key ${i} in CompressedActivity`,223),t[i]=s;else if(Br.REVERSE_KEY_MAP[i].startsWith("data.")){const e=Br.REVERSE_KEY_MAP[i].split(".");2===e.length&&(void 0===t.data&&(t.data={}),t.data[e[1]]=s)}else t[Br.REVERSE_KEY_MAP[i]]=s;return t}};let Gr=Br;__publicField(Gr,"TIME_LIMIT_MS",15552e6),__publicField(Gr,"CHARACTER_LIMIT",1048576),__publicField(Gr,"KEY_MAP",__spreadValues({eventType:"et",customerEventName:"en",campaignId:"cmid",experienceId:"eid",variationId:"vid",actionId:"acid",eventId:"evid",eventInstanceId:"eii",eventValue:"ev",userVisitStatus:"uvs",userBucket:"ub",pageviewId:"pvid",sessionId:"sid",controlStatus:"cs",referrerUrl:"rurl",hostingPageUrl:"hpurl",time:"t",timeZone:"tz",trafficSource:"ts",utmTerm:"utt",utmMedium:"utm",utmSource:"uts",utmContent:"utcn",utmCampaign:"utcm",customAttributes:"ca",htmlLang:"hl",webflowDomain:"wfd",webflowSiteId:"wfsid",webflowPageId:"wfpid",webflowCollectionId:"wfcid",webflowItemSlug:"wfis"},jr())),__publicField(Gr,"REVERSE_KEY_MAP",fn(Br.KEY_MAP));class Hr extends Rt{constructor(e){super(),__publicField(this,"callbacks",[]),this.callbacks=e}toString(){return"ActivateTask()"}inTransaction(){return!1}isUnloading(){return!0}addCallback(e){this.callbacks.push(e)}run(){return this.manager.getClient().reRun(this.callbacks),Nt.SUCCEEDED}}class Qr extends Rt{constructor(e){super(),__publicField(this,"callbacks",[]),this.callbacks=null!=e?e:[]}toString(){return"RequestActivateTask()"}inTransaction(){return!1}isUnloading(){return!1}addCallback(e){this.callbacks.push(e)}run(){if(0===this.manager.findTasks((e=>e instanceof Hr&&!e.isDone())).length){const e=new Hr(this.callbacks);this.manager.addTask(e)}else Mo.debug("Not scheduling new ActivateTask since one is already running");return Nt.SUCCEEDED}}class Kr extends Qr{constructor(e,t,i){super(i),__publicField(this,"environment"),__publicField(this,"startTimeMs"),this.environment=e,this.resetExpiration(),t.requestAnimationFrame().orElse(!0)&&this.environment.getWindow().requestAnimationFrame((()=>{this.handleRaf()})),this.environment.getWindow().requestAnimationFrame((()=>{this.handleRaf()}))}toString(){return"RequestActivateTaskOnUrlChange()"}resetExpiration(){Mo.debug("RequestActivateTask.resetExpiration()"),this.startTimeMs=this.environment.getNowUnixTimeMs()}run(){return this.environment.hasHostingPageUrlChanged()?super.run():(Mo.debug("URL has not changed yet"),this.hasExceededTimeBudget()?(Mo.info(`activate() timeout due to URL not changing after ${Z}ms`),Nt.SUCCEEDED):Nt.TRY_LATER)}handleRaf(){this.manager&&this.manager.runNow(),Rt.isDone(this.getStatus())||this.environment.getWindow().requestAnimationFrame((()=>{this.handleRaf()}))}hasExceededTimeBudget(){const e=this.environment.getNowUnixTimeMs(),t=e-this.startTimeMs,i=t>Z;return Mo.debug("hasExceededTimeBudget: "+e+" - "+this.startTimeMs+" ("+t+") > "+Z+"? => "+i),i}}class qr extends Rt{constructor(e,t,i,s,n,r,o,a,l,c,d,u,h,g,p,m,v){super(),__publicField(this,"environment"),__publicField(this,"browserStorage"),__publicField(this,"extensionManager"),__publicField(this,"browserEventLogger"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"customerStorage"),__publicField(this,"decisionContext"),__publicField(this,"pageContext"),__publicField(this,"attributeData"),__publicField(this,"user"),__publicField(this,"override"),__publicField(this,"statusModel"),__publicField(this,"hider"),__publicField(this,"getVariationRecordedCallbackTuples"),__publicField(this,"elementObserver"),__publicField(this,"startPageviewId"),__publicField(this,"startTimeMs"),__publicField(this,"executeVariationTasks",{}),__publicField(this,"didHide",!1),this.environment=N(e),this.browserStorage=N(t),this.extensionManager=N(i),this.environment=N(e),this.browserEventLogger=N(s),this.attributeStorage=N(n),this.activityStorage=N(r),this.customerStorage=N(o),this.decisionContext=N(a),this.pageContext=N(l),this.attributeData=N(c),this.user=N(d),this.override=N(u),this.statusModel=N(h),this.hider=N(g),this.getVariationRecordedCallbackTuples=N(m),this.elementObserver=N(v),this.startTimeMs=this.environment.getNowUnixTimeMs(),this.startPageviewId=e.getPageviewId(),this.override.requestAnimationFrame().orElse(p)&&this.environment.getWindow().requestAnimationFrame((()=>{this.handleRaf()}))}static get RAF_RUNTIME_MS(){return 3e4}toString(){return`ExecuteExperiencesTask(${this.startPageviewId})`}inTransaction(){return!1}isUnloading(){return!1}getStartPageviewId(){return this.startPageviewId}hideExperiencesOnce(){this.didHide||(this.hider.hideExperiences(this.decisionContext.getExperiences(),this.browserStorage,this.attributeData),this.hider.revealDocumentIfAllRegionsHidden(),this.didHide=!0)}run(){return this.decisionContext.getExperiences().forEach((e=>{if(this.decisionContext.hasNext(e.getId())){const t=this.decisionContext.getFirst(e.getId());void 0===this.executeVariationTasks[e.getId()]&&(Mo.debug(`Queueing ExecuteVariationTask for ExperienceID: ${e.getId()}, Variation ID: ${t.getId()}`),this.executeVariationTasks[e.getId()]=t.buildExecutionTask({environment:this.environment,browserStorage:this.browserStorage,extensionManager:this.extensionManager,browserEventLogger:this.browserEventLogger,customerStorage:this.customerStorage,attributeStorage:this.attributeStorage,activityStorage:this.activityStorage,pageContext:this.pageContext,attributeData:this.attributeData,decisionContext:this.decisionContext,user:this.user,override:this.override,statusModel:this.statusModel,startPageviewId:this.startPageviewId,hider:this.hider,getVariationRecordedCallbackTuples:this.getVariationRecordedCallbackTuples,elementObserver:this.elementObserver}),this.manager.addTask(this.executeVariationTasks[e.getId()]));const i=this.executeVariationTasks[e.getId()];i.getStatus()===Nt.FAILED||i.getStatus()===Nt.CANCELED?this.handleOnExperienceError(e):i.getStatus()===Nt.SUCCEEDED&&this.handleOnExperienceSuccess(e)}})),this.decisionContext.isEmpty()?Nt.SUCCEEDED:Nt.TRY_LATER}handleOnExperienceSuccess(e){this.decisionContext.removeExperience(e.getId())}handleOnExperienceError(e){this.decisionContext.removeExperience(e.getId()),this.hider.revealExperience(e,this.browserStorage,this.attributeData)}handleRaf(){this.manager&&this.manager.runNow(),this.environment.getNowUnixTimeMs()-this.startTimeMs<qr.RAF_RUNTIME_MS&&!Rt.isDone(this.getStatus())&&this.environment.getWindow().requestAnimationFrame((()=>{this.handleRaf()}))}}const Yr=class extends Rt{constructor(e,t,i,s,n){super(),__publicField(this,"instanceNumber"),__publicField(this,"pageviewId"),__publicField(this,"customer"),__publicField(this,"environment"),__publicField(this,"customerStorage"),__publicField(this,"success"),__publicField(this,"startTimeMs"),this.instanceNumber=++Yr.instanceCount,this.environment=e,this.pageviewId=t,this.customerStorage=i,this.customer=s,this.success=n}static get DEPENDENCIES_TIME_BUDGET_MS(){return 3e3}toString(){return`SelectedVariationsCallbackTask(${this.pageviewId}, ${this.instanceNumber})`}inTransaction(){return!0}isUnloading(){return!1}getNotDoneDependencies(){const e=[],t=this.getRunningDependentRecordTasks();Array.prototype.push.apply(e,t);const i=this.getDependentExecuteExperiencesTask();return i.length>0&&!i[0].isDone()&&Array.prototype.push.apply(e,i),e}run(){this.startTimeMs||(this.startTimeMs=this.environment.getNowUnixTimeMs());const e=this.getRunningDependentRecordTasks();if(e.length>0)return Mo.debug("Waiting for "+e[0].toString()+", "+Nt[e[0].getStatus()]),Nt.TRY_LATER;const t=this.getDependentExecuteExperiencesTask();if(t.length>1)return Mo.error(`Found multiple ExecuteExperiencesTasks for pageviewId ${this.pageviewId}`,89),Nt.FAILED;if(0===t.length)return Nt.TRY_LATER;const i=t[0];return Rt.isDone(i.getStatus())?(this.doCallback(),Nt.SUCCEEDED):this.hasExceededTimeBudget()?(Mo.debug(`Time budget of ${Yr.DEPENDENCIES_TIME_BUDGET_MS} ms exceeded while waiting for ExecuteVariationTasks to complete; triggering success callbacks now`),this.doCallback(),Nt.SUCCEEDED):(Mo.debug("Waiting for "+i.toString()+", "+Nt[i.getStatus()]),Nt.TRY_LATER)}getRunningDependentRecordTasks(){return this.manager.findTasks((e=>e instanceof uo&&!e.isDone()))}getDependentExecuteExperiencesTask(){return this.manager.findTasks((e=>e instanceof qr&&this.pageviewId===e.getStartPageviewId()))}doCallback(){const e=this.customerStorage.getPageviewSelections(this.pageviewId),t=this.generateResponse(e);Mo.debug("Callback data: "+Le(t));try{this.success.call(void 0,t)}catch(i){const e=new Ei("Caught exception in customer callback",i);Mo.error(e,8)}}getTimeRemainingMs(){const e=this.environment.getNowUnixTimeMs()-this.startTimeMs,t=Yr.DEPENDENCIES_TIME_BUDGET_MS;return Mo.debug("getTimeRemainingMs elapsed: "+e+", budget: "+t),t-e}hasExceededTimeBudget(){return this.getTimeRemainingMs()<0}};let Jr=Yr;__publicField(Jr,"instanceCount",0);class Xr extends Jr{toString(){return"SelectedVariationIdsCallbackTask("+this.pageviewId+","+this.instanceNumber+")"}generateResponse(e){const t={};return e.forEach((e=>{t[e.experienceId]=e.variationId})),t}}class Zr extends Jr{toString(){return"SelectedVariationNamesCallbackTask("+this.pageviewId+","+this.instanceNumber+")"}generateResponse(e){const t={};return e.forEach((e=>{e.campaignId in t||(t[e.campaignId]={});const i={};i.varId=e.variationId,this.customer.getCampaign(e.campaignId).ifPresent((s=>{t[e.campaignId].campaignName=s.getName(),s.getExperience(e.experienceId).ifPresent((t=>{i.expName=t.getName(),t.getVariation(e.variationId).ifPresent((e=>{i.varName=e.getName()}))}))})),t[e.campaignId][e.experienceId]=i})),t}}class eo{constructor(e,t,i,s,n,r,o,a,l,c,d,u,h,g,p,m){__publicField(this,"environment"),__publicField(this,"extensionManager"),__publicField(this,"browserEventLogger"),__publicField(this,"taskManager"),__publicField(this,"consentManagerApis"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"integrationDataStorage"),__publicField(this,"customer"),__publicField(this,"user"),__publicField(this,"override"),__publicField(this,"statusModel"),__publicField(this,"callQueue"),__publicField(this,"readyCallbackQueue"),__publicField(this,"addVariationRecordedCallbackTuple"),__publicField(this,"initialized"),this.environment=N(e),this.extensionManager=t,this.browserEventLogger=N(i),this.taskManager=N(s),this.consentManagerApis=n,this.customerStorage=N(r),this.attributeStorage=N(o),this.activityStorage=N(a),this.integrationDataStorage=N(l),this.customer=N(c),this.user=N(d),this.override=N(u),this.statusModel=N(h),this.callQueue=N(g),this.readyCallbackQueue=N(p),this.addVariationRecordedCallbackTuple=N(m),this.initialized=!1}static validatePushCommand(e){return e&&"object"==typeof e&&e.constructor===Array?!(!e[0]||"string"!=typeof e[0])||(eo.consoleError(`intellimize.push(): First parameter must be a string: ${Le(e)}`,114),!1):(eo.consoleError(`intellimize.push(): Parameter must be an array: ${Le(e)}`,113),!1)}static validateReadyCallback(e){return!!gn(e)||(eo.consoleError("ready() was called with a non-function argument",115),!1)}static consoleError(e,t){console.error(e),Mo.error(e,t)}static validateAttributeScope(e){return"user"===e||"pageview"===e}static validateAttributes(e){return null!=e&&hn(e)}static filterAttributePairs(e){for(const t in e){if(!Object.prototype.hasOwnProperty.call(e,t))continue;if(!eo.validateAttributeName(t)){eo.consoleError(`setAttributes() invalid attribute ${t}`,96),delete e[t];continue}const i=eo.transformAttributeValue(e[t]);eo.validateAttributeValue(i)?e[t]=i:(eo.consoleError(`setAttributes() invalid attribute ${t}: ${e[t]}`,97),delete e[t])}}static validateAttributeName(e){return/^[\u0020-\u003C\u003E-\u007E]{1,40}$/.test(e)}static transformAttributeValue(e){let t=e;return"boolean"==typeof t&&(t=t.toString()),"number"==typeof t&&(t=t.toString()),null===t&&(t=""),void 0===t&&(t=""),"string"==typeof t&&t.length>255&&(t=t.slice(0,255)),t}static validateAttributeValue(e){return void 0!==e&&"string"==typeof e&&e.length>=0&&e.length<256}static validateAttributeNames(e){if(void 0===e||!Array.isArray(e))return!1;for(const t of e)if(!eo.validateAttributeName(t))return!1;return!0}static validateCustomEventApiName(e){return/^[\u0021-\u007E]{1,40}$/.test(e)}static validateCustomEventProperties(e){const t=new Set(["value","actionId"]);return!!Object.keys(e).every((e=>t.has(e)))&&(!(void 0!==e.value&&!xr(e.value))&&!(void 0!==e.actionId&&!Ar(e.actionId)))}static validateCustomerUserId(e){return/^[\u0021-\u007E]{1,300}$/.test(e)}static validateCustomerUserDomain(e){return"intellimize"!==e&&!e.startsWith("__")&&/^[\u0021-\u007E]{1,50}$/.test(e)}static doCallback(e){void 0!==e&&Dt.executeFunction(e,5)}get isInitialized(){return this.initialized}initialize(){const e=this,t=this.environment.getWindow(),{intellimize:i,wf:s}=t;if(void 0===i)return void Mo.error("window.intellimize is undefined: Cannot initialize ExternalApi",218);i.allowUserTracking=this.consentManagerApis.allowUserTracking.bind(this.consentManagerApis),i.denyUserTracking=this.consentManagerApis.denyUserTracking.bind(this.consentManagerApis),i.getUserTrackingChoice=this.consentManagerApis.getUserTrackingChoice.bind(this.consentManagerApis),void 0===s&&Mo.debug("window.wf is undefined: Can only initialize External API on the window.intellimize namespace"),i.getSelectedVariationIds=(e,t)=>{"function"==typeof e?void 0===t||"function"==typeof t?this.getSelectedVariationIds(e,t):eo.consoleError("intellimize.getSelectedVariationIds() second parameter must be an error function",103):eo.consoleError("intellimize.getSelectedVariationIds() first parameter must be a success function",102)},i.getSelectedVariationNames=(e,t)=>{"function"==typeof e?void 0===t||"function"==typeof t?this.getSelectedVariationNames(e,t):eo.consoleError("intellimize.getSelectedVariationNames() second parameter must be an error function",105):eo.consoleError("intellimize.getSelectedVariationNames() First parameter must be a success function",104)},i.onVariationRecorded=function(t,i){e.onVariationRecorded(t.bind(this),void 0===i?void 0:i.bind(this))},void 0!==s&&(s.onVariationRecorded=function(t,i){e.onVariationRecorded(t.bind(this),void 0===i?void 0:i.bind(this))},s.allowUserTracking=i.allowUserTracking,s.denyUserTracking=i.denyUserTracking,s.getUserTrackingChoice=i.getUserTrackingChoice);const n=(e,t)=>{this.setAttributes(e,t)};i.setAttributes=n,void 0!==s&&(s.setAttributes=n);const r=(e,t)=>this.getAttributes(e,t);i.getAttributes=r,void 0!==s&&(s.getAttributes=r);const o=e=>this.getAllAttributes(e);i.getAllAttributes=o,void 0!==s&&(s.getAllAttributes=o);const a=(e,t)=>{this.deleteAttributes(e,t)};i.deleteAttributes=a,void 0!==s&&(s.deleteAttributes=a);const l=e=>{this.deleteAllAttributes(e)};i.deleteAllAttributes=l,void 0!==s&&(s.deleteAllAttributes=l),i.getIntegrationData=e=>this.getIntegrationData(e),i.sendEvent=(e,t,i)=>{this.sendEvent(e,t,i)},i.activate=e=>{this.activate(e)},i.setUserId=(e,t)=>{this.setCustomerUserId(e,t)},i.getActivities=()=>this.getActivities(),i.registerExtension=e=>{Mo.debug(`intellimize.registerExtension(${e.getName()})`),this.extensionManager.registerExtension(e)},i.push=e=>{eo.validatePushCommand(e)&&this.push(e)};const c=e=>{eo.validateReadyCallback(e)&&this.ready(e)};i.ready=c,void 0!==s&&(s.ready=c),this.callQueue.forEach((e=>{e&&"object"==typeof e&&e.constructor===Array?"push"!==e[0]?this.push(e):eo.consoleError('intellimize.push() Refusing to process command "push", to prevent infinite recursion.',10):eo.consoleError("intellimize.push() Parameter must be an array: "+Le(e),9)})),this.readyCallbackQueue.forEach(((e,t)=>{Mo.debug(`ready() executing registered callback #${t+1}`),this.ready(e)})),this.initialized=!0}getSelectedVariationIds(e,t){Mo.debug("getSelectedVariationIds()"),Mo.warn("getSelectedVariationIds",160);const i=this.environment.getPageviewId(),s=new Xr(this.environment,i,this.customerStorage,this.customer,e);this.taskManager.addTask(s)}getSelectedVariationNames(e,t){Mo.debug("getSelectedVariationNames()"),Mo.warn("getSelectedVariationNames",161);const i=this.environment.getPageviewId(),s=new Zr(this.environment,i,this.customerStorage,this.customer,e);this.taskManager.addTask(s)}onVariationRecorded(e,t){Mo.debug("onVariationRecorded()"),Mo.warn("onVariationRecorded",203),"function"==typeof e?void 0===t||"function"==typeof t?this.addVariationRecordedCallbackTuple([e,j.ofNullable(t)]):eo.consoleError("onVariationRecorded() second parameter must be an error function",164):eo.consoleError("onVariationRecorded() first parameter must be a success function",163)}setAttributes(e,t){Mo.debug(`setAttributes(${e}, ${Le(t)})`),eo.validateAttributeScope(e)?eo.validateAttributes(t)?(eo.filterAttributePairs(t),this.attributeStorage.setCustomAttributes(e,t)):eo.consoleError("setAttributes() invalid attributes parameter",110):eo.consoleError("setAttributes() invalid scope parameter",109)}getAttributes(e,t){return Mo.debug(`getAttributes(${e}, ${t})`),eo.validateAttributeScope(e)?eo.validateAttributeNames(t)?this.attributeStorage.getCustomAttributes(e,t):(eo.consoleError("getAttributes() invalid names parameter",100),{}):(eo.consoleError("getAttributes() invalid scope parameter",99),{})}getAllAttributes(e){return Mo.debug(`getAllAttributes(${e}`),eo.validateAttributeScope(e)?this.attributeStorage.getCustomAttributes(e):(eo.consoleError("getAllAttributes() invalid scope parameter",98),{})}deleteAttributes(e,t){Mo.debug(`deleteAttributes(${e}, ${t})`),eo.validateAttributeScope(e)?eo.validateAttributeNames(t)?this.attributeStorage.deleteCustomAttributes(e,t):eo.consoleError("deleteAttributes() invalid names parameter",95):eo.consoleError("deleteAttributes() invalid scope parameter",94)}deleteAllAttributes(e){Mo.debug(`deleteAllAttributes(${e})`),eo.validateAttributeScope(e)?this.attributeStorage.deleteCustomAttributes(e):eo.consoleError("deleteAllAttributes() invalid scope parameter",20)}getIntegrationData(e){var t;if(Mo.debug(`getIntegrationData(${e})`),"marketoLead"===e){return null!=(t=this.integrationDataStorage.getData("marketo").flatMap((e=>br(e))).orElse({lead:{fields:{},lists:[]}}).lead)?t:{}}return nr.includes(e)?this.integrationDataStorage.getData(e).flatMap((e=>j.ofNullable(e))).orElse({}):(eo.consoleError("intellimize.getData() invalid integration name parameter",101),{})}sendEvent(e,t,i){return Mo.debug(`sendEvent(${e}, ${Le(t)} )`),eo.validateCustomEventApiName(e)?void 0===t||eo.validateCustomEventProperties(t)?void this.customer.getCustomEvent(e).flatMap((t=>this.override.isEventLive(t).orElse("live"===t.getState())?j.ofNullable(t):(Mo.debug(`sendEvent(${e}) event is not live`),j.empty()))).ifPresent((e=>{const s=void 0===t?void 0:t.value;let n=void 0===t?void 0:t.actionId;void 0===n&&(n=this.environment.generateActionId());const r=new tr(e,n,s,void 0);this.activityStorage.add(Gr.buildConversionActivity(r,this.customer,this.environment,this.user,this.attributeStorage,this.customerStorage));const o=new Cr(r,this.browserEventLogger,this.customer,this.customerStorage,this.statusModel);void 0!==i&&o.onDone(i),this.taskManager.addTask(o)})).ifAbsent((()=>{eo.consoleError(`intellimize.sendEvent() apiName not found "${e}"`,19),eo.doCallback(i)})):(eo.consoleError(`intellimize.sendEvent() invalid properties "${Le(t)}"`,108),void eo.doCallback(i)):(eo.consoleError(`intellimize.sendEvent() invalid apiName "${e}"`,107),void eo.doCallback(i))}activate(e){Mo.debug("activate()");const t=this.taskManager.findTasks((e=>e instanceof Hr&&!e.isDone()));if(t.length>0){Mo.debug("Ignoring activate() since there is an outstanding ActivateTask");const i=t[0];return void(void 0!==e&&i.addCallback(e))}const i=this.taskManager.findTasks((e=>e instanceof Kr&&!e.isDone()));if(i.length>0){Mo.debug("Extending activate() expiration time");const t=i[0];return void 0!==e&&t.addCallback(e),void t.resetExpiration()}const s=void 0===e?[]:[e],n=new Kr(this.environment,this.override,s);this.taskManager.addTask(n)}setCustomerUserId(e,t){if(Mo.debug(`setCustomerUserId(${e}, ${t})`),!eo.validateCustomerUserDomain(e))return void eo.consoleError("intellimize.setCustomerUserId() invalid user domain",111);if(!eo.validateCustomerUserId(t))return void eo.consoleError("intellimize.setCustomerUserId() invalid user id",112);const i=new Tr(this.environment,this.customer,"intellimize",this.user.getId(),e,t);this.taskManager.addTask(i)}getActivities(){return Mo.debug("getActivities()"),this.activityStorage.getAll()}push(e){Mo.debug("push("+e[0]+")");const t=this.environment.getWindow();try{if(void 0===t.intellimize)return void Mo.error("push() called, but window.intellimize undefined",30);const i=e.shift();if(void 0===t.intellimize[i])return void eo.consoleError(`intellimize.${i}() is not a valid function`,106);t.intellimize[i].apply(void 0,e)}catch(i){const e=new Ei("Error executing push()",i);Mo.error(e,3)}}ready(e){eo.doCallback(e)}}const to=/^G-[\dA-Z]+$/,io=e=>"string"==typeof e&&to.test(e),so=e=>{const t=[],i=e=>{io(e)&&!t.includes(e)&&(Mo.debug(`Found GA4 Measurement ID: ${e}`),t.push(e))},s=e.google_tag_data;m(s)&&Object.values(s).forEach((e=>{m(e)&&Object.keys(e).forEach((e=>{i(e)}))}));const n=e.google_tag_manager;m(n)&&Object.values(n).forEach((e=>{m(e)&&Object.values(e).forEach((e=>{h(e)&&e.forEach((({message:e})=>{un(e)&&"config"===e[0]&&i(e[1])}))}))}));const{dataLayer:r}=e;return h(r)&&r.forEach((e=>{m(e)?Object.values(e).forEach((e=>{un(e)&&"config"===e[0]&&i(e[1])})):un(e)&&"config"===e[0]&&i(e[1])})),{ga4:t}};class no extends Rt{constructor(e,t,i){super(),__publicField(this,"targetUrl"),__publicField(this,"environment"),__publicField(this,"variation"),this.targetUrl=e,this.environment=t,this.variation=i}toString(){return"RedirectTask("+this.targetUrl+")"}inTransaction(){return!1}isUnloading(){return!0}getRedirectVariation(){return this.variation}run(){return Mo.info("Executing redirect to "+this.targetUrl),this.environment.redirect(this.targetUrl),Nt.SUCCEEDED}}class ro extends Rt{constructor(e,t,i,s){super(),__publicField(this,"environment"),__publicField(this,"ga4Integration"),__publicField(this,"variation"),__publicField(this,"override"),__publicField(this,"states",{ga4State:"notStarted"}),__publicField(this,"ga4MeasurementIdQueue",[]),__publicField(this,"taskStartTime"),this.environment=e,this.ga4Integration=t,this.variation=i,this.override=s,L(!t.isPresent()||t.get().isEnabled())}static get GA4_EVENT_NAME(){return"variation_viewed"}static get WAIT_FOR_GA_TIMEOUT_MS(){return 3e3}static get WAIT_FOR_PROPERTY_IDS_MS(){return 5e3}static isSent(e){return"sent"===e||ro.isCompleted(e)}static isCompleted(e){return ro.isSuccessful(e)||"failed"===e}static isSuccessful(e){return"delivered"===e||"skipped"===e}cleanup(){Mo.group("GoogleAnalyticsTask.cleanup()");const e=this.ga4Integration.map((e=>e.isEnabled())).orElse(!1),t=this.manager.findTasks((e=>e instanceof no)).map((e=>e.getRedirectVariation().getId())),i=`GoogleAnalyticsTask did not complete. Enabled: { ga4: ${e} }, GaStates: ${Le(this.states)}, ga4MeasurementIdQueue: ${Le(this.ga4MeasurementIdQueue)}, redirectVariationId(s): ${Le(t)}, Task ran for: ${this.getTimePassedMs()}ms`;Mo.error(i,258),Mo.groupEnd()}toString(){return"GoogleAnalyticsTask("+this.variation.getId()+")"}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.isAllCompleted()?this.isAllSuccessful()?Nt.SUCCEEDED:Nt.FAILED:(Mo.debug("Waiting for GA delivery confirmation"),Nt.TRY_LATER)}isAllSent(){return ro.isSent(this.states.ga4State)}isAllCompleted(){return ro.isCompleted(this.states.ga4State)}isAllSuccessful(){return ro.isSuccessful(this.states.ga4State)}setState(e,t){this.states[e]!==t&&(Mo.debug(`Transitioning ${e} from ${this.states[e]} to ${t}`),this.states[e]=this.isValidStateTransition(this.states[e],t)?t:"failed")}isValidStateTransition(e,t){switch(e){case"notStarted":if("polling"===t||"skipped"===t)return!0;break;case"polling":if("sent"===t||"failed"===t)return!0;break;case"sent":if("delivered"===t)return!0}return this.sendError(`Invalid state transition. Cannot transition from "${e}" to "${t}".`,189),!1}sendOnce(){if(!this.isAllSent()){void 0===this.taskStartTime&&(this.taskStartTime=this.environment.getNowDate(),Mo.debug(`GoogleAnalyticsTask start time: ${this.taskStartTime.getTime()}`));const e=this.getTimePassedMs();if(this.override.sendGoogleAnalytics().orElse(!0)){this.ga4Integration.ifPresent((()=>{Mo.debug("GoogleAnalytics4 - integration is enabled")})).ifAbsent((()=>{Mo.debug("Google Analytics 4 - integration is not enabled"),this.setState("ga4State","skipped")}));this.waitForDataLayer(e).ifPresent((t=>{this.ga4Integration.ifPresent((i=>{if(i.isAutoConfig()){Mo.debug("GoogleAnalytics4 - AutoConfig is enabled");const s=this.waitForMeasurementId(e);s.length>0&&this.sendToGa4Once(i,t,s)}else Mo.debug("GoogleAnalytics4 - AutoConfig is disabled"),this.sendToGa4Once(i,t)}))}))}else Mo.debug("Google Analytics - Override behavior has prevented all events from being sent"),this.setState("ga4State","skipped")}}sendError(e,t){Mo.error(e,t,{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()})}shimGtag(e){return function(){e.push(arguments)}}waitForDataLayer(e){const t=this.environment.getWindow();if(ro.isSent(this.states.ga4State)||ro.isCompleted(this.states.ga4State)||this.setState("ga4State","polling"),void 0===t.dataLayer)Mo.debug(`window.dataLayer not defined after ${e} ms`);else{if(dn(t.dataLayer))return j.of(t.dataLayer);Mo.debug("window.dataLayer is not an array")}return e>=ro.WAIT_FOR_GA_TIMEOUT_MS&&(ro.isCompleted(this.states.ga4State)||this.setState("ga4State","failed"),this.sendError(`window.dataLayer not present within ${ro.WAIT_FOR_GA_TIMEOUT_MS} ms`,185)),j.empty()}waitForMeasurementId(e){const t=so(this.environment.getWindow());return 0===t.ga4.length?(Mo.debug(`Auto-configured Measurement ID not present after ${e} ms`),this.setState("ga4State","polling"),e>=ro.WAIT_FOR_PROPERTY_IDS_MS&&(this.setState("ga4State","failed"),this.sendError(`GoogleAnalytics4 - Auto-configured Measurement ID is not present within ${ro.WAIT_FOR_PROPERTY_IDS_MS} ms`,187)),[]):t.ga4}sendToGa4Once(e,t,i){if(!ro.isSent(this.states.ga4State)){const s=this.shimGtag(t),n=null!=i?i:e.getMeasurementIds(),r={experienceId:this.variation.getExperience().getId(),experienceName:this.variation.getExperience().getName(),experienceType:this.variation.getExperience().getType(),variationId:this.variation.getId(),variationName:this.variation.getName(),ccStatus:this.variation.getId()===mt?"holdout":"optimized",send_to:n,event_callback:e=>{io(e)&&(this.handleGaEventSuccess(`GA4 configuration (iint.ga4) + gtag.js. Measurement ID: ${e}`),this.checkDelivered("ga4State",e))}};Mo.debug(`Sending GA4 event using gtag.js: ${Le(r)}`),this.ga4MeasurementIdQueue.push(...n),Mo.debug("Google Analytics 4 payload sent"),this.setState("ga4State","sent"),s("event",ro.GA4_EVENT_NAME,r)}}handleGaEventSuccess(e){Mo.debug(`Google Analytics payload delivery complete - using ${e}`)}checkDelivered(e,t){const i=this.ga4MeasurementIdQueue;bn(i,t),0===i.length&&this.setState(e,"delivered")}getTimePassedMs(){if(void 0===this.taskStartTime)return 0;return this.environment.getNowDate().getTime()-this.taskStartTime.getTime()}}class oo extends Rt{constructor(e,t,i,s){super(),__publicField(this,"environment"),__publicField(this,"mixpanelIntegration"),__publicField(this,"variation"),__publicField(this,"override"),__publicField(this,"sent",!1),__publicField(this,"delivered",!1),__publicField(this,"timedOut",!1),__publicField(this,"taskStartTime"),this.environment=e,this.mixpanelIntegration=t,this.variation=i,this.override=s}static get WAIT_FOR_MIXPANEL_TIMEOUT_MS(){return 3e3}toString(){return"MixpanelTask("+this.variation.getId()+")"}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.timedOut?Nt.FAILED:this.sent?this.delivered?Nt.SUCCEEDED:(Mo.debug("Waiting for Mixpanel delivery confirmation"),Nt.TRY_LATER):Nt.TRY_LATER}sendOnce(){if(!this.sent){void 0===this.taskStartTime&&(this.taskStartTime=this.environment.getNowDate(),Mo.debug(`MixpanelTask start time: ${this.taskStartTime.getTime()}`));if(this.environment.getNowDate().getTime()-this.taskStartTime.getTime()>=oo.WAIT_FOR_MIXPANEL_TIMEOUT_MS)return Mo.error(`MixpanelTask - window.mixpanel.track not present within ${oo.WAIT_FOR_MIXPANEL_TIMEOUT_MS} ms`,199),void(this.timedOut=!0);if(this.override.sendMixpanel().orElse(!0)){const e=this.environment.getWindow();if(void 0===e.mixpanel)return void Mo.debug("window.mixpanel not defined");if(void 0===e.mixpanel.track)return void Mo.debug("window.mixpanel.track not defined");const t=this.mixpanelIntegration.getEventName(),i={[`${this.variation.getExperience().getName()} (${this.variation.getExperience().getId()})`]:`${this.variation.getName()} (${this.variation.getId()})`};e.mixpanel.track(t,i,{send_immediately:!0},(()=>{Mo.debug("Mixpanel payload delivery complete"),this.delivered=!0})),Mo.debug("Mixpanel payload sent")}else this.delivered=!0,Mo.debug("Skipping Mixpanel");this.sent=!0}}}class ao extends Rt{constructor(e,t,i,s){super(),__publicField(this,"environment"),__publicField(this,"segmentIntegration"),__publicField(this,"variation"),__publicField(this,"override"),__publicField(this,"sent",!1),__publicField(this,"delivered",!1),__publicField(this,"timedOut",!1),__publicField(this,"onTrack",!1),__publicField(this,"onReady",!1),__publicField(this,"trackCallback",!1),__publicField(this,"taskStartTime"),this.environment=e,this.segmentIntegration=t,this.variation=i,this.override=s}static get WAIT_FOR_SEGMENT_TIMEOUT_MS(){return 3e3}toString(){return"SegmentAnalyticsTask("+this.variation.getId()+")"}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.timedOut?Nt.FAILED:this.sent?this.delivered||this.onReady&&this.onTrack&&this.trackCallback?Nt.SUCCEEDED:(Mo.debug("Waiting for Segment delivery confirmation"),Nt.TRY_LATER):Nt.TRY_LATER}sendOnce(){if(!this.sent){void 0===this.taskStartTime&&(this.taskStartTime=this.environment.getNowDate(),Mo.debug(`SegmentAnalyticsTask start time: ${this.taskStartTime.getTime()}`));if(this.environment.getNowDate().getTime()-this.taskStartTime.getTime()>=ao.WAIT_FOR_SEGMENT_TIMEOUT_MS)return Mo.error(`SegmentAnalyticsTask - window.analytics.track not present within ${ao.WAIT_FOR_SEGMENT_TIMEOUT_MS} ms`,200),void(this.timedOut=!0);if(this.override.sendSegmentAnalytics().orElse(!0)){const e=this.environment.getWindow();if(void 0===e.analytics)return void Mo.debug("window.analytics not defined");if(void 0===e.analytics.track)return void Mo.debug("window.analytics.track not defined");const t=this.segmentIntegration.getEventName();e.analytics.on("track",((e,i)=>{e===t&&void 0!==i.variationId&&i.variationId===this.variation.getId()&&(Mo.debug("Segment on track event received"),this.onTrack=!0)}));const i={campaignId:this.variation.getExperience().getCampaign().getId(),campaignName:this.variation.getExperience().getCampaign().getName(),experimentId:this.variation.getExperience().getId(),experimentName:this.variation.getExperience().getName(),variationName:this.variation.getName(),variationId:this.variation.getId(),nonInteraction:1};e.analytics.track(t,i,(()=>{Mo.debug("Segment track() callback called"),this.trackCallback=!0})),e.analytics.ready((()=>{Mo.debug("Segment ready() callback called"),this.onReady=!0}))}else this.delivered=!0;Mo.debug("Segment payload sent"),this.sent=!0}}}class lo extends Rt{constructor(e,t,i){super(),__publicField(this,"variation"),__publicField(this,"browserEventLogger"),__publicField(this,"decisionContext"),__publicField(this,"sent",!1),__publicField(this,"deliveryStatus",Nt.NOT_STARTED),this.variation=N(e),this.browserEventLogger=N(t),this.decisionContext=N(i)}toString(){return"ViewEventTask("+this.variation.getId()+")"}getEntityIds(){return{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()}}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.deliveryStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:this.deliveryStatus===Nt.FAILED?Nt.FAILED:(Mo.debug("Waiting for view event delivery confirmation"),Nt.TRY_LATER)}sendOnce(){this.sent||(Mo.info(`Variation ${this.variation.getExperience().getId()}/${this.variation.getId()} sending view event`),this.browserEventLogger.sendView(this.variation,this.decisionContext).then((()=>{Mo.info(`Variation ${this.variation.getExperience().getId()}/${this.variation.getId()} view event delivery success`),this.deliveryStatus=Nt.SUCCEEDED})).catch((e=>{const t=new Ei(`Variation ${this.variation.getExperience().getId()}/${this.variation.getId()} view event delivery failed`,e);Mo.warn(t,93,{experienceId:this.variation.getExperience().getId(),variationId:this.variation.getId()}),this.deliveryStatus=Nt.FAILED})),this.sent=!0)}}class co extends Rt{constructor(e,t){super(),__publicField(this,"environment"),__publicField(this,"viewabilityTimeMs"),__publicField(this,"startViewableTimeMs"),__publicField(this,"ignoreCallback",!1),__publicField(this,"activeEventListener",!1),this.environment=e,this.viewabilityTimeMs=t}toString(){return"ViewabilityTask()"}inTransaction(){return!1}isUnloading(){return!1}cleanup(){this.ignoreCallback=!0}run(){const e=this.environment.getNowUnixTimeMs();if(!this.environment.isNowDocumentHidden().orElse(!0)){void 0===this.startViewableTimeMs&&(this.startViewableTimeMs=e);const t=e-this.startViewableTimeMs;return t>=this.viewabilityTimeMs?(Mo.debug(`page viewable for ${t}ms > threshold ${this.viewabilityTimeMs}ms`),Nt.SUCCEEDED):(Mo.debug(`page viewable for ${t}ms < threshold ${this.viewabilityTimeMs}ms`),Nt.TRY_LATER)}return this.startViewableTimeMs=void 0,this.activeEventListener||(Mo.debug("page not viewable, scheduling visibility change listener"),this.environment.addListenerVisibilityChange((()=>{try{this.ignoreCallback?Mo.debug("visibility change event - ignoring"):(Mo.debug("visibility change event - ask TM for polling"),this.setStatusTryLater(),this.manager.runNow())}catch(e){const t=new Ei("Exception in ViewabilityTask visibilitychange handler",e);Mo.error(t,238)}})),this.activeEventListener=!0),Nt.WAITING}}class uo extends Rt{constructor(e,t,i,s,n,r,o,a,l,c,d){super(),__publicField(this,"variation"),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"user"),__publicField(this,"decisionContext"),__publicField(this,"startPageviewId"),__publicField(this,"override"),__publicField(this,"viewabilityTask"),__publicField(this,"getVariationRecordedCallbackTuples"),__publicField(this,"haveUpdatedCustomerStorage",!1),__publicField(this,"haveUpdatedActivityStorage",!1),__publicField(this,"haveScheduledGoogleAnalytics",!1),__publicField(this,"googleAnalyticsTask"),__publicField(this,"haveScheduledSegmentAnalytics",!1),__publicField(this,"segmentAnalyticsTask"),__publicField(this,"haveScheduledMixpanel",!1),__publicField(this,"mixpanelTask"),__publicField(this,"viewEventTask"),__publicField(this,"haveRunSuccessCallbacks",!1),this.variation=N(e),this.environment=N(t),this.browserEventLogger=N(i),this.customerStorage=N(s),this.attributeStorage=N(n),this.activityStorage=N(r),this.user=N(o),this.decisionContext=N(a),this.startPageviewId=N(l),this.override=N(c),this.getVariationRecordedCallbackTuples=N(d)}toString(){return"RecordVariationTask("+this.variation.getId()+")"}getEntityIds(){return{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()}}inTransaction(){return!1}isUnloading(){return!1}getNotDoneDependencies(){const e=[];return void 0===this.googleAnalyticsTask||this.googleAnalyticsTask.isDone()||e.push(this.googleAnalyticsTask),void 0===this.segmentAnalyticsTask||this.segmentAnalyticsTask.isDone()||e.push(this.segmentAnalyticsTask),void 0===this.mixpanelTask||this.mixpanelTask.isDone()||e.push(this.mixpanelTask),void 0===this.viewEventTask||this.viewEventTask.isDone()||e.push(this.viewEventTask),void 0===this.viewabilityTask||this.viewabilityTask.isDone()||e.push(this.viewabilityTask),e}updateCustomerStorageOnce(){this.haveUpdatedCustomerStorage||(Mo.debug("RecordVariationTask.updateCustomerStorageOnce() "+this.variation.getId()),this.decisionContext.isStickyVariation(this.variation)?this.customerStorage.saveVariationStickySelection(this.variation,this.environment.getPageviewId()):this.customerStorage.saveVariationPredictionSelection(this.variation,this.decisionContext.getModelVersion(this.variation.getExperience().getId()),this.environment.getPageviewId()),this.haveUpdatedCustomerStorage=!0)}updateActivityStorageOnce(){if(!this.haveUpdatedActivityStorage){Mo.debug(`RecordVariationTask.haveUpdatedActivityStorage() ${this.variation.getId()}`);const e=Gr.buildViewActivity(this.variation,this.environment,this.user,this.attributeStorage,this.customerStorage);this.activityStorage.add(e),this.haveUpdatedActivityStorage=!0}}sendGoogleAnalyticsOnce(){if(!this.haveScheduledGoogleAnalytics){const e=this.variation.getExperience().getCampaign().getCustomer().getIntegrationGoogle4Analytics(),t=e.isPresent()&&e.get().isEnabled();t&&(this.googleAnalyticsTask=new ro(this.environment,t?e:j.empty(),this.variation,this.override),this.manager.addTask(this.googleAnalyticsTask)),this.haveScheduledGoogleAnalytics=!0}}sendSegmentAnalyticsOnce(){this.haveScheduledSegmentAnalytics||(this.variation.getExperience().getCampaign().getCustomer().getIntegrationSegmentAnalytics().ifPresent((e=>{e.isEnabled()&&(this.segmentAnalyticsTask=new ao(this.environment,e,this.variation,this.override),this.manager.addTask(this.segmentAnalyticsTask))})),this.haveScheduledSegmentAnalytics=!0)}sendMixpanelOnce(){this.haveScheduledMixpanel||(this.variation.getExperience().getCampaign().getCustomer().getIntegrationMixpanel().ifPresent((e=>{e.isEnabled()&&(this.mixpanelTask=new oo(this.environment,e,this.variation,this.override),this.manager.addTask(this.mixpanelTask))})),this.haveScheduledMixpanel=!0)}sendViewEventOnce(){void 0===this.viewEventTask&&(this.viewEventTask=new lo(this.variation,this.browserEventLogger,this.decisionContext),this.manager.addTask(this.viewEventTask))}checkDependency(e){switch(e.getStatus()){case Nt.NOT_STARTED:case Nt.TRY_LATER:case Nt.WAITING:return Nt.TRY_LATER;case Nt.FAILED:case Nt.CANCELED:return Nt.FAILED;case Nt.SUCCEEDED:return Nt.SUCCEEDED;default:return Mo.error(`Unrecognized status from dependency ${e.getStatus()}`,88,{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()}),Nt.FAILED}}getRecordViewStatus(){if(!this.haveUpdatedCustomerStorage)return Mo.debug("Waiting for CustomerStorage to be updated"),Nt.TRY_LATER;if(!this.haveScheduledGoogleAnalytics)return Mo.debug("Waiting for GoogleAnalytics to be scheduled"),Nt.TRY_LATER;if(void 0!==this.googleAnalyticsTask){const e=this.checkDependency(this.googleAnalyticsTask);if(e!==Nt.SUCCEEDED)return Mo.debug(`GoogleAnalytics returned ${Nt[e]}`),e}if(!this.haveScheduledSegmentAnalytics)return Mo.debug("Waiting for SegmentAnalytics to be scheduled"),Nt.TRY_LATER;if(void 0!==this.segmentAnalyticsTask){const e=this.checkDependency(this.segmentAnalyticsTask);if(e!==Nt.SUCCEEDED)return Mo.debug(`SegmentAnalytics returned ${Nt[e]}`),e}if(!this.haveScheduledMixpanel)return Mo.debug("Waiting for Mixpanel to be scheduled"),Nt.TRY_LATER;if(void 0!==this.mixpanelTask){const e=this.checkDependency(this.mixpanelTask);if(e!==Nt.SUCCEEDED)return Mo.debug(`Mixpanel returned ${Nt[e]}`),e}if(void 0===this.viewEventTask)return Mo.debug("Waiting for ViewEvent to be scheduled"),Nt.TRY_LATER;const e=this.checkDependency(this.viewEventTask);return e!==Nt.SUCCEEDED?(Mo.debug(`ViewEvent returned ${Nt[e]}`),e):Nt.SUCCEEDED}doSuccessCallbacksOnce(){this.haveRunSuccessCallbacks||(this.getVariationRecordedCallbackTuples().forEach((e=>{const t=e[0];try{t({experienceId:this.variation.getExperience().getId(),experienceName:this.variation.getExperience().getName(),experienceType:this.variation.getExperience().getType(),variationId:this.variation.getId(),variationName:this.variation.getName(),ccStatus:this.variation.getId()===mt?"holdout":"optimized"})}catch(i){const e=new Ei("Caught exception in onVariationRecorded success callback",i);eo.consoleError(e.message,165)}})),this.haveRunSuccessCallbacks=!0)}scheduleViewabilityOnce(e){void 0===this.viewabilityTask&&(this.viewabilityTask=new co(this.environment,e),this.manager.addTask(this.viewabilityTask))}}class ho extends uo{toString(){return"RecordCodeVariationTask("+this.variation.getId()+")"}run(){this.startPageviewId!==this.environment.getPageviewId()&&Mo.error(`RecordCodeVariationTask executing, but pageviewId has changed from ${this.startPageviewId} to ${this.environment.getPageviewId()}`,85,{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()}),this.scheduleViewabilityOnce(ee);const e=j.ofNullable(this.viewabilityTask).map((e=>e.getStatus())).orElse(Nt.NOT_STARTED);return Rt.isDone(e)?e!==Nt.SUCCEEDED?(Mo.info(`Variation ${this.variation.getExperience().getId()}/${this.variation.getId()} aborting record view event since viewability did not succeed`),Nt.CANCELED):(this.updateCustomerStorageOnce(),this.updateActivityStorageOnce(),this.sendGoogleAnalyticsOnce(),this.sendSegmentAnalyticsOnce(),this.sendMixpanelOnce(),this.sendViewEventOnce(),this.doSuccessCallbacksOnce(),this.getRecordViewStatus()):(Mo.debug("RecordCodeVariationTask waiting for viewability"),Nt.TRY_LATER)}}class go extends er{constructor(){super(...arguments),__publicField(this,"changelistTask")}toString(){return`ExecuteChangelistVariationTask(${this.variation.getId()})`}getNotDoneDependencies(){return void 0===this.changelistTask||this.changelistTask.isDone()?[]:[this.changelistTask]}isReadyToExecute(){if(void 0===this.changelistTask){const e=this.passDependencies(),t=this.passExperiencePreconditions(),i=this.passVariationPreconditions(),s=this.hasAppliedPageChanges();return e&&t&&i&&s}return!0}executeVariation(){if(void 0===this.changelistTask){const e=this.variation.getChanges().map(((e,t)=>Xn(e,Un(this.variation),t,this.attributeData,this.environment,this.browserStorage,this.extensionManager)));this.changelistTask=new Zn(e,(()=>{this.onReadyToRender()}),{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()},this.elementObserver),this.manager.addTask(this.changelistTask)}if(this.changelistTask.isDone()){if(this.changelistTask.getStatus()===Nt.FAILED||this.changelistTask.getStatus()===Nt.CANCELED)return this.statusModel.setVariation(this.variation,"failChange",!0),Nt.FAILED;if(this.changelistTask.getStatus()===Nt.SUCCEEDED)return this.manager.addTask(new ho(this.variation,this.environment,this.browserEventLogger,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.decisionContext,this.startPageviewId,this.override,this.getVariationRecordedCallbackTuples)),this.statusModel.setVariation(this.variation,"apply",!0),Nt.SUCCEEDED}return Nt.TRY_LATER}}class po{constructor(e,t,i,s){__publicField(this,"experience"),__publicField(this,"id"),__publicField(this,"name"),__publicField(this,"state"),__publicField(this,"condition"),__publicField(this,"audiences"),__publicField(this,"preconditions"),this.id=e,this.name=t.name,this.state=t.state,this.preconditions=t.preconditions,this.condition=i.buildConditionFromVariationOrExperience(t),this.audiences=Co.addVariationToAudiences(this),this.experience=s}getId(){return this.id}getName(){return this.name}isEnabled(){return"live"===this.state}getState(){return this.state}getPreconditions(){return this.preconditions}getCondition(){return j.ofNullable(this.condition)}getAudiences(){return this.audiences}getExperience(){return this.experience}}class mo extends po{constructor(e,t,i,s){super(e,t,i,s),__publicField(this,"changes");const n=[];void 0!==t.changes&&t.changes.forEach((e=>{n.push(i.buildDomChange(e))})),this.changes=n}buildExecutionTask(e){return new go(this,e)}getSelectors(e,t,i,s){const n={};return this.changes.forEach(((r,o)=>{Xn(r,Un(this),o,s,e,i,t).getRegionSelectors().forEach((e=>{n[e]=!0}))})),Object.keys(n)}getChanges(){return this.changes}}class vo extends er{toString(){return`ExecuteNoChangeVariationTask(${this.variation.getId()})`}executeVariation(){return this.onReadyToRender(),this.manager.addTask(new ho(this.variation,this.environment,this.browserEventLogger,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.decisionContext,this.startPageviewId,this.override,this.getVariationRecordedCallbackTuples)),this.statusModel.setVariation(this.variation,"apply",!0),Nt.SUCCEEDED}}class bo extends po{buildExecutionTask(e){return new vo(this,e)}getSelectors(){return[]}}class fo extends po{getSelectors(){return["body"]}generateRedirectUrl(e){return this.generateRawRedirectUrl(e).map((t=>this.getRedirectUrlWithMergedQueryParams(t,e)))}getRedirectUrlWithMergedQueryParams(e,t){const i=t.getHostingPageUrl();try{const t=new URL(e,i.getRawUrl());return Object.entries(i.getAllQueryParamsUnsafe()).forEach((([e,i])=>{t.searchParams.has(e)||t.searchParams.set(e,i)})),t.toString()}catch(s){Mo.warn(`redirectUrl ${e} is invalid`,268)}return e}}class Eo extends uo{constructor(e,t,i,s,n,r,o,a,l,c,d,u,h){super(e,t,i,s,n,r,o,a,l,c,d),__publicField(this,"redirectUrl"),__publicField(this,"onReadyToRender"),__publicField(this,"startTimeMs"),__publicField(this,"redirectScheduled",!1),this.redirectUrl=N(u),this.onReadyToRender=N(h)}static get REDIRECT_TIME_BUDGET_MS(){return 3e3}toString(){return"RecordRedirectVariationTask("+this.variation.getId()+")"}cleanup(){this.onReadyToRender()}run(){this.startPageviewId!==this.environment.getPageviewId()&&Mo.error(`RecordRedirectVariationTask executing, but pageviewId has changed from ${this.startPageviewId} to ${this.environment.getPageviewId()}`,87,{variationId:this.variation.getId(),experienceId:this.variation.getExperience().getId()}),this.scheduleViewabilityOnce(te);const e=j.ofNullable(this.viewabilityTask).map((e=>e.getStatus())).orElse(Nt.NOT_STARTED);if(!Rt.isDone(e))return Mo.debug("RecordCodeVariationTask waiting for viewability"),Nt.TRY_LATER;if(e!==Nt.SUCCEEDED)return Mo.info(`Variation ${this.variation.getExperience().getId()}/${this.variation.getId()} aborting record view event since viewability did not succeed`),Nt.CANCELED;this.startTimeMs||(this.startTimeMs=this.environment.getNowUnixTimeMs());let t=this.runSteps();return!Rt.isDone(t)&&this.hasExceededTimeBudget()&&(this.scheduleRedirectOnce(),t=Nt.FAILED),this.doSuccessCallbacksOnce(),t}runSteps(){this.updateCustomerStorageOnce(),this.updateActivityStorageOnce(),this.sendGoogleAnalyticsOnce(),this.sendSegmentAnalyticsOnce(),this.sendMixpanelOnce(),this.sendViewEventOnce();const e=this.getRecordViewStatus();return Rt.isDone(e)?(this.scheduleRedirectOnce(),Nt.SUCCEEDED):e}scheduleRedirectOnce(){if(!this.redirectScheduled){const e=new no(this.redirectUrl,this.environment,this.variation);this.manager.addTask(e),this.redirectScheduled=!0,this.environment.getWindow().setTimeout((()=>{this.onReadyToRender()}),this.getTimeRemainingMs()+K)}}getTimeRemainingMs(){const e=this.environment.getNowUnixTimeMs()-this.startTimeMs,t=Eo.REDIRECT_TIME_BUDGET_MS;return Mo.debug("getTimeRemainingMs elapsed: "+e+", budget: "+t),t-e}hasExceededTimeBudget(){return this.getTimeRemainingMs()<0}}class _o extends er{toString(){return`ExecuteUrlRedirectVariationTask(${this.variation.getId()})`}executeVariation(){const e=this.variation.generateRedirectUrl(this.environment);return e.isPresent()?(this.manager.addTask(new Eo(this.variation,this.environment,this.browserEventLogger,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.decisionContext,this.startPageviewId,this.override,this.getVariationRecordedCallbackTuples.bind(this),e.get(),this.onReadyToRender.bind(this))),this.statusModel.setVariation(this.variation,"apply",!0),Nt.SUCCEEDED):(this.onReadyToRender(),Nt.FAILED)}}class yo extends fo{constructor(e,t,i,s){super(e,t,i,s),__publicField(this,"redirect"),void 0!==t.redirect&&(this.redirect=t.redirect)}buildExecutionTask(e){return new _o(this,e)}generateRawRedirectUrl(e){var t;let i;try{Mo.info(`Variation ${this.getExperience().getId()}/${this.getId()} running redirect code`),i=Dt.evalString(null!=(t=this.redirect)?t:""),Mo.info(`Redirect URL is ${i}`)}catch(s){const e=new Ei(`Variation (${this.getId()}) redirect code execution failed`,s);Mo.error(e,18,{variationId:this.getId(),experienceId:this.getExperience().getId()}),i=void 0}return j.ofNullable(i)}}class So extends er{toString(){return`ExecuteWebflowRedirectVariationTask(${this.variation.getId()})`}executeVariation(){try{return this.variation.generateRedirectUrl(this.environment).ifPresent((e=>{this.manager.addTask(new Eo(this.variation,this.environment,this.browserEventLogger,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.decisionContext,this.startPageviewId,this.override,this.getVariationRecordedCallbackTuples.bind(this),e,this.onReadyToRender.bind(this)))})).ifAbsent((()=>{this.onReadyToRender(),this.manager.addTask(new ho(this.variation,this.environment,this.browserEventLogger,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.decisionContext,this.startPageviewId,this.override,this.getVariationRecordedCallbackTuples.bind(this)))})),this.statusModel.setVariation(this.variation,"apply",!0),Nt.SUCCEEDED}catch(e){return this.onReadyToRender(),Nt.FAILED}}}const wo=`${xe}redirect`;class Io extends fo{buildExecutionTask(e){return new So(this,e)}generateRawRedirectUrl(e){var t;let i;const s=this.getExperience().getId(),n=this.getId(),{head:r}=e.getWindow().document,o=r.querySelector(`meta[${Ae}${s}][${ke}${n}]`);if(null===o)return j.empty();const a=null!=(t=o.getAttribute(wo))?t:void 0;if(void 0===a){const e=`Webflow redirect variation (${s}/${n}) missing ${wo} attribute value`;throw Mo.error(e,268),new Error(e)}try{i=Dt.evalString(a)}catch(l){const e=`Webflow redirect variation (${s}/${n}) ${wo} attribute value eval failed`;throw Mo.error(e,269),new Ei(e,l)}return j.ofNullable(i)}}class Co{constructor(e){__publicField(this,"logger"),__publicField(this,"experienceMap",{}),__publicField(this,"pageMap",{}),__publicField(this,"audienceMap",{}),__publicField(this,"eventMap",{}),this.logger=e}static buildStickyConfiguration(e,t){let i,s,n;return void 0!==e&&(void 0!==e.s&&(i=e.s),void 0!==e.r&&(s=e.r),void 0!==e.a&&(n=e.a)),void 0===i&&(i=t.s),void 0===s&&void 0!==t.r&&(s=t.r),void 0===n&&void 0!==t.a&&(n=t.a),new ln(i,s,n)}static addExperienceToAudiences(e){const t=[];return e.getCondition().ifPresent((i=>{if(i instanceof ot)t.push(Co.addExperienceToAudience(e,i));else if(i instanceof rt||i instanceof dt)i.getConditions().forEach((i=>{if(i instanceof ot)t.push(Co.addExperienceToAudience(e,i));else if(i instanceof ct){const s=i.getCondition();s instanceof ot&&(s.getAudience().addExperience(e),t.push(s.getAudience()))}}));else if(i instanceof ct){const s=i.getCondition();s instanceof ot&&(s.getAudience().addExperience(e),t.push(s.getAudience()))}})),t}static addVariationToAudiences(e){const t=[];return e.getCondition().ifPresent((i=>{if(i instanceof ot)t.push(Co.addVariationToAudience(e,i));else if(i instanceof rt||i instanceof dt)i.getConditions().forEach((i=>{if(i instanceof ot)t.push(Co.addVariationToAudience(e,i));else if(i instanceof ct){const s=i.getCondition();s instanceof ot&&(s.getAudience().addVariation(e),t.push(s.getAudience()))}}));else if(i instanceof ct){const s=i.getCondition();s instanceof ot&&(s.getAudience().addVariation(e),t.push(s.getAudience()))}})),t}static addExperienceToAudience(e,t){const i=t.getAudience();return t.getAudience().addExperience(e),i}static addVariationToAudience(e,t){const i=t.getAudience();return t.getAudience().addVariation(e),i}static audienceIdToConditionJson(e){let t=!1,i=e;e.startsWith("!")&&(t=!0,i=e.slice(1));const s={audienceId:i,type:"audience"};let n=s;return t&&(n={type:"logic-not",condition:s}),n}getLogger(){return this.logger}buildCustomer(e){const t=new Mi(e,this);return t.getCampaigns().forEach((e=>{e.getExperiences().forEach((e=>{e.inflateDependsOnExperiences(this.experienceMap)}))})),t}buildCollaborator(e){return new vi(e)}buildCrossOrigin(e){return new bi(e)}buildOrigin(e){return new an(e)}buildPage(e,t){const i=this.pageMap[e];if(void 0!==i)return this.logger.warn(`Trying to build page twice ${e}`,1e3,{pageId:e}),i;const s=this.buildTypedPage(e,t);return this.pageMap[e]=s,s}buildTypedPage(e,t){return"matching"===t.type?new Bs(e,t,this):"landing"===t.type?new zs(e,t,this):"shopify"===t.type?new Gs(e,t,this):new Bs(e,t,this)}getPage(e){return j.ofNullable(this.pageMap[e]).ifAbsent((()=>{this.logger.error(`Could not find page in builder map ${e}`,1001,{pageId:e})}))}buildAudience(e,t){const i=this.audienceMap[e];if(void 0!==i)return this.logger.warn(`Trying to build audience twice ${e}`,1002,{audienceId:e}),i;const s=new ut(e,t,this);return this.audienceMap[e]=s,s}buildConditionFromVariationOrExperience({audienceIds:e,condition:t}){const i=[];if(void 0!==e&&e.length>0&&e.forEach((e=>{i.push(Co.audienceIdToConditionJson(e))})),void 0!==t&&("logic-and"===t.type?i.push(...t.conditions):i.push(t)),0===i.length)return;const[s]=i;return 1===i.length&&void 0!==s?this.buildCondition(s):this.buildCondition({conditions:i,type:"logic-and"})}buildCondition(e,t){switch(e.type){case"logic-not":return new ct(e,this);case"logic-and":return new rt(e,this);case"logic-or":return new dt(e,this);case"comparison-number":return new lt(e,new it(e.attrDefId),sn({operator:e.operator,argument:e.value}));case"comparison-string":return new lt(e,new it(e.attrDefId),nn({operator:e.operator,argument:e.value}));case"comparison-enum":return new lt(e,new it(e.attrDefId),tn({operator:e.operator,argument:e.value}));case"comparison-boolean":return new lt(e,new it(e.attrDefId),en({operator:e.operator,argument:void 0}));case"comparison-array-string":return new lt(e,new it(e.attrDefId),Zs({operator:e.operator,argument:e.value}));case"comparison-array-enum":return new lt(e,new it(e.attrDefId),Xs({operator:e.operator,argument:e.value}));case"audience":return new ot(e,this,t);case"code":return new at(e);case"activity":return new nt(e,this.buildActivityFilter(e.filter),(()=>this.buildAggregator(e.aggregation)),sn(e.operation),this.getLogger());default:throw new Error(`Unknown ConditionType: ${e.type}`)}}buildActivityFilter(e){switch(e.type){case"logic-not":return new gi(e,this);case"logic-and":return new ui(e,this);case"logic-or":return new pi(e,this);case"comparison-number":return new hi(this.getActivityPathValue(e.fieldName,["number"]),sn(e.operation));case"comparison-string":return new hi(this.getActivityPathValue(e.fieldName,["string"]),nn(e.operation));case"comparison-enum":return new hi(this.getActivityPathValue(e.fieldName,["string"]),tn(e.operation));case"comparison-array-enum":return new hi(this.getActivityPathValue(e.fieldName,["array-enum"]),Xs(e.operation));case"comparison-unix-time":return new hi(this.getActivityPathValue(e.fieldName,["number"]),rn(e.operation));default:throw new Error(`Unknown activity filter type: ${e.type}`)}}buildAggregator(e){switch(e.type){case"count":return new ai;case"sum":return new di(this.getActivityPathValue(e.fieldName,["number"]));case"mean":return new ci(this.getActivityPathValue(e.fieldName,["number"]));case"countDistinct":return new li(this.getActivityPathValue(e.fieldName,["string","number"]));default:throw new Error(`Unknown aggregator type: ${e.type}`)}}getAudience(e){return j.ofNullable(this.audienceMap[e]).orElseRun((()=>(this.logger.error(`Could not find audience in builder map ${e}`,1003,{audienceId:e}),new ut(e,{name:`Missing Audience ${e}`,code:'throw new Error("Missing audience");'},this))))}buildEvent(e,t){const i=this.eventMap[e];if(void 0!==i)return this.logger.warn(`Trying to build event twice ${e}`,1004,{eventId:e}),i;const s=this.buildTypedEvent(e,t);return this.eventMap[e]=s,s}buildTypedEvent(e,t){if("click"===t.type)return new Si(e,t,this);if("view"===t.type)return new Fi(e,t,this);if("custom"===t.type)return new wi(e,t,this);if("shopify"===t.type)return new xi(e,t,this);if("shopify_server_side"===t.type)return new Ai(e,t,this);if("marketo"===t.type)return new Ci(e,t,this);if("hubspot"===t.type)return new Ii(e,t,this);if("wf_engagement_click"===t.type)return new ki(e,t,this);if("wf_click"===t.type)return new Pi(e,t,this);if("page_leave"===t.type)return new Ti(e,t,this);throw new D(`Invalid event type ${t.type}`)}getEvent(e){return j.ofNullable(this.eventMap[e]).ifAbsent((()=>{this.logger.error(`Could not find event in builder map ${e}`,1005,{eventId:e})}))}buildCampaign(e,t,i){return new mi(e,t,this,i)}buildExperience(e,t,i){const s=this.experienceMap[e];if(void 0!==s)return this.logger.warn(`Trying to build experience twice ${e}`,1006,{experienceId:e}),s;const n=this.buildTypedExperience(e,t,i);return this.experienceMap[e]=n,n}buildTypedExperience(e,t,i){return"ab"===t.type?new As(e,t,this,i):"rbp"===t.type?new ks(e,t,this,i):new Fs(e,t,this,i)}buildMetric(e,t,i,s){return L(Boolean(t)&&void 0===s&&void 0===i||Boolean(s)&&void 0===t&&void 0===i||Boolean(i)&&void 0===s&&void 0===t),"value"===e.type?"static"===e.valueType?new Ys(e,this,t,i,s):new qs(e,this,t,i,s):new Ks(e,this,t,i,s)}buildUrl(e){return new cn(e,this)}buildVariation(e,t,i){return this.buildTypedVariation(e,ni(t),i)}buildTypedVariation(e,t,i){return"cl"===t.type?new mo(e,t,this,i):"urlrd"===t.type?new yo(e,t,this,i):"wfrd"===t.type?new Io(e,t,this,i):new bo(e,t,this,i)}buildControlVariation(e){const t={name:"Intellimize Control",state:"live",preconditions:[]};return this.buildVariation(mt,t,e)}buildControlStickyConfig(e){return Co.buildStickyConfiguration(e,{s:!0})}buildExperienceIncludeStickyConfig(e){return Co.buildStickyConfiguration(e,{s:!0})}buildVariationStickyConfig(e){return Co.buildStickyConfiguration(e,{s:!0,r:302400})}buildGoogleAnalytics4Integration(e){return new Ns(e)}buildSegmentIntegration(e){return new Vs(e)}buildMixpanelIntegration(e){return new Us(e)}buildMarketoIntegration(e){return new Ls(e)}buildSalesforceIntegration(e){return new $s(e)}buildShopifyIntegration(e,t,i,s){return new Hs(e,t,i,s)}buildFirmographicIntegration(e){return new Ms(e)}buildSixsenseIntegration(e){return new Qs(e)}buildGoogleAdsIntegration(e){return new Ds(e)}buildDemandbaseIntegration(e){return new Ps(e)}buildHubspotIntegration(e){return new Os(e)}buildDomChange(e){return Ts(e)}getActivityPathValue(e,t){return i=>{let s=e,n=i;for(;void 0!==n&&void 0!==s;){const i=s.split(".");if(!(i.length>1&&void 0!==i[0])){const i=n[s];let r=typeof i;return h(i)&&i.every((e=>"string"==typeof e))&&(r="array-enum"),t.includes(r)?j.ofNullable(i):(void 0!==i&&this.logger.warn(`Field "${e}" is not an allowed type (wanted ${t.join(", ")}; found ${r})`,1017),j.empty())}n=n[i[0]],s=i.slice(1).join(".")}return j.empty()}}}function To(e){return new ko(e)}const xo={[i.LogLevel.OFF]:"o",[i.LogLevel.ERROR]:"e",[i.LogLevel.WARN]:"w",[i.LogLevel.INFO]:"i",[i.LogLevel.DEBUG]:"d",[i.LogLevel.TRACE]:"t"},Ao=["audienceId","campaignId","experienceId","variationId","eventId","pageId","changeId"],Fo={audienceId:"aid",campaignId:"cmid",experienceId:"eid",variationId:"vid",eventId:"ei",pageId:"pid",changeId:"chid"};class ko{constructor(e){__publicField(this,"minimumLogLevel"),__publicField(this,"customer"),__publicField(this,"environment"),__publicField(this,"serverContext"),__publicField(this,"user"),this.minimumLogLevel=e}static get LOGGING_APP_STRING(){return"client"}static get PATH(){return"/clientlogger"}configureCustomer(e){this.customer=e}configureEnvironment(e){this.environment=e}configureServerContext(e){this.serverContext=e}configureUser(e){this.user=e}getLevel(){return this.minimumLogLevel}error(e,t,s){this.log(i.LogLevel.ERROR,e,t,s)}warn(e,t,s){this.log(i.LogLevel.WARN,e,t,s)}info(e,t,s){this.log(i.LogLevel.INFO,e,t,s)}debug(e,t,s){this.log(i.LogLevel.DEBUG,e,t,s)}group(e){}groupEnd(){}time(e){}timeEnd(e){}log(e,t,i,s){this.minimumLogLevel<=e&&this.send(__spreadProps(__spreadValues(__spreadValues(__spreadValues({},this.buildCommonPayload()),this.entityIdsToPayload(s)),this.messageToPayload(t)),{app:ko.LOGGING_APP_STRING,mc:i,ll:xo[e]}))}buildCommonPayload(){const e={v:Q};return void 0!==this.customer&&(e.cid=this.customer.getId()),void 0!==this.serverContext&&(e.rid=this.serverContext.requestId),void 0!==this.environment&&(e.pvid=this.environment.getPageviewId(),e.hpurl=this.environment.getHostingPageUrl().getRawUrl(),e.lut=this.environment.getNowUnixTimeMs(),e.ltz=this.environment.getTimeZone()),void 0!==this.user&&(e.uid=this.user.getId()),e}messageToPayload(e){return e instanceof Ei?{m:e.message,en:e.getCause().name,es:e.getCause().stack}:e instanceof Error?{m:e.message,en:e.name,es:e.stack}:{m:e}}entityIdsToPayload(e){const t={};return void 0!==e&&Ao.forEach((i=>{void 0!==e[i]&&(t[Fo[i]]=e[i])})),t}convertToStringValues(e){return Object.assign({},...Object.entries(e).map((([e,t])=>({[e]:String(t)}))))}send(e){const t=yt(se,ko.PATH),i=Re(this.convertToStringValues(e));void 0===this.environment?navigator.sendBeacon(t,i):this.environment.sendBeacon(t,i)}}class Po{constructor(){__publicField(this,"consoleLogger"),__publicField(this,"serverLogger"),__publicField(this,"isLogging",!1),this.consoleLogger=Qe(),this.serverLogger=To(Pe)}configureCustomer(e){this.serverLogger.configureCustomer(e)}configureEnvironment(e){this.serverLogger.configureEnvironment(e)}configureServerContext(e){this.serverLogger.configureServerContext(e)}configureUser(e){this.serverLogger.configureUser(e)}getLevel(){return this.consoleLogger.getLevel()}error(e,t,i,s){this.isLogging||(this.isLogging=!0,this.consoleLogger.error(e,t,i,s),this.serverLogger.error(e,t,i),this.isLogging=!1)}warn(e,t,i,s){this.isLogging||(this.isLogging=!0,this.consoleLogger.warn(e,t,i,s),this.serverLogger.warn(e,t,i),this.isLogging=!1)}info(e,t,i,s){this.isLogging||(this.isLogging=!0,this.consoleLogger.info(e,t,i,s),this.serverLogger.info(e,t,i),this.isLogging=!1)}debug(e,t,i,s){this.isLogging||(this.isLogging=!0,this.consoleLogger.debug(e,t,i,s),this.serverLogger.debug(e,t,i),this.isLogging=!1)}group(e,t){this.isLogging||(this.isLogging=!0,this.consoleLogger.group(e,t),this.isLogging=!1)}groupEnd(e){this.isLogging||(this.isLogging=!0,this.consoleLogger.groupEnd(e),this.isLogging=!1)}time(e,t){this.isLogging||(this.isLogging=!0,this.consoleLogger.time(e,t),this.isLogging=!1)}timeEnd(e,t){this.isLogging||(this.isLogging=!0,this.consoleLogger.timeEnd(e,t),this.isLogging=!1)}}const Mo=new Po,Do="Other";class No{constructor(e,t,i,s,n,r,o){__publicField(this,"environment"),__publicField(this,"attributeStorage"),__publicField(this,"override"),__publicField(this,"customerStorage"),__publicField(this,"integrationDataStorage"),__publicField(this,"user"),__publicField(this,"data"),__publicField(this,"serverContext"),this.environment=e,this.serverContext=t,this.attributeStorage=i,this.override=s,this.customerStorage=n,this.integrationDataStorage=r,this.user=o,this.reinitialize()}reinitialize(){this.data=this.withAttributeDataOverrides({location:this.initializeLocationData(),utmParams:this.initializeUtmParamData(),userAgent:this.initializeUserAgentData(),dayPart:this.environment.getDayPart(),weekPart:this.environment.getWeekPart(),trafficSource:this.initializeTrafficSourceData(),custom:this.initializeCustomData(),urlParam:this.initializeUrlParamData(),userVisitStatus:this.initializeUserVisitStatusData(),userBucket:this.initializeUserBucketData(),marketo:this.initializeMarketoData(),salesforce:this.initializeSalesforceData(),firmographic:this.initializeFirmographicData(),"6sense":this.initializeSixsenseData(),googleAds:this.initializeGoogleAdsData(),demandbase:this.initializeDemandbaseData(),hubspot:this.initializeHubspotData(),htmlLang:this.initializeHtmlLangData()}),Mo.debug(`[AttributeData] Initialized: ${Le(this.data)}`)}getLocationData(){return this.data.location}getUtmParamsData(){return this.data.utmParams}getUserAgentData(){return this.data.userAgent}getDayPartData(){return this.data.dayPart}getWeekPartData(){return this.data.weekPart}getTrafficSourceData(){return this.data.trafficSource}getCustomData(){return this.data.custom}getUrlParamData(){return this.data.urlParam}getUserVisitStatusData(){return this.data.userVisitStatus}getUserBucketData(){return this.data.userBucket}getMarketoData(){return this.data.marketo}getSalesforceData(){return this.data.salesforce}getFirmographicData(){return this.data.firmographic}getSixsenseData(){return this.data["6sense"]}getGoogleAdsData(){return this.data.googleAds}getDemandbaseData(){return this.data.demandbase}getHubspotData(){return this.data.hubspot}getHtmlLangData(){return this.data.htmlLang}getAttributeValue(e){switch(e.getNamespace()){case"standard":switch(e.getName()){case"utm_source":return j.ofNullable(this.getUtmParamsData().utmSource);case"utm_medium":return j.ofNullable(this.getUtmParamsData().utmMedium);case"utm_campaign":return j.ofNullable(this.getUtmParamsData().utmCampaign);case"utm_content":return j.ofNullable(this.getUtmParamsData().utmContent);case"utm_term":return j.ofNullable(this.getUtmParamsData().utmTerm);case"country":return j.ofNullable(this.getLocationData().country);case"state":return j.ofNullable(this.getLocationData().state);case"subdivision":return j.ofNullable(this.getLocationData().subdivision);case"city":return j.ofNullable(this.getLocationData().city);case"postal":return j.ofNullable(this.getLocationData().postal);case"dma":return j.ofNullable(this.getLocationData().dma);case"timezone":return j.ofNullable(this.getLocationData().tz);case"device_type":return j.ofNullable(this.getUserAgentData().deviceType);case"day_part":return j.ofNullable(this.getDayPartData());case"week_part":return j.ofNullable(this.getWeekPartData());case"traffic_source":return j.ofNullable(this.getTrafficSourceData());case"user_visit_status":return j.ofNullable(this.getUserVisitStatusData());case"user_bucket":return j.ofNullable(this.getUserBucketData());case"html_lang":return j.ofNullable(this.getHtmlLangData());default:return Mo.error(`Standard attribute name (${e.getName()}) not supported`,214),j.empty()}case"url_param":return j.ofNullable(this.getUrlParamData()[e.getName()]);case"custom":return j.ofNullable(this.getCustomData()[e.getName()]);case"marketo":return j.ofNullable(this.getMarketoData()[e.getName()]);case"salesforce":return j.ofNullable(this.getSalesforceData()[e.getName()]);case"firmographic":return j.ofNullable(this.getFirmographicData()[e.getName()]);case"6sense":return j.ofNullable(this.getSixsenseData()[e.getName()]);case"googleAds":return j.ofNullable(this.getGoogleAdsData()[e.getName()]);case"demandbase":return j.ofNullable(this.getDemandbaseData()[e.getName()]);case"hubspot":return j.ofNullable(this.getHubspotData()[e.getName()]);default:return Mo.error(`Attribute namespace (${e.getNamespace()}) not supported`,215),j.empty()}}setServerContext(e){this.serverContext=e,this.reinitialize()}initializeCustomData(){return this.attributeStorage.getFlattenedCustomAttributes()}initializeUrlParamData(){return this.environment.getHostingPageUrl().getAllQueryParamsUnsafe()}initializeUserVisitStatusData(){return this.customerStorage.getUserVisitStatus(this.user)}initializeUserBucketData(){return this.user.getUserBucket()}initializeLocationData(){var e,t,i,s,n,r;const o=null!=(t=null==(e=this.serverContext)?void 0:e.location)?t:{},a=null!=(s=null!=(i=o.subdivision2Iso)?i:o.subdivision1Iso)?s:Do,l=[];void 0!==o.countryIso&&(void 0!==o.subdivision1Iso&&l.push(`${o.countryIso}-${o.subdivision1Iso}`),void 0!==o.subdivision2Iso&&l.push(`${o.countryIso}-${o.subdivision2Iso}`));let c=Do;return void 0!==o.countryIso&&void 0!==o.postalCode&&(c=`${o.countryIso}-${o.postalCode}`),{country:null!=(n=o.countryIso)?n:Do,state:a,subdivision:l,city:null!=(r=o.geonameId)?r:Do,dma:void 0===o.dmaCode?Do:o.dmaCode.toString(),postal:c,tz:this.environment.getTimeZone()}}initializeUserAgentData(){var e,t,i,s,n,r,o,a,l,c,d;const u=null!=(t=null==(e=this.serverContext)?void 0:e.userAgent)?t:{};let h,g,p;switch(u.deviceClass){case"Desktop":case"Set-top box":case"TV":case"Game Console":default:h="D";break;case"Mobile":case"Phone":case"Handheld Game Console":h="P";break;case"Tablet":case"eReader":h="T"}switch(u.agentName){case"Firefox":case"Gecko":case"Camino":case"SeaMonkey":g="Firefox";break;case"Chrome":case"Chrome Webview":case"Chromium":case"Epiphany":g="Chrome";break;case"Edge":case"Edge Webview":g="Edge";break;case"Safari":case"Mobile Safari":g="Safari";break;case"Internet Explorer":case"IE Mobile":case"Internet Explorer Webview":g="IE";break;default:g=Do}switch(u.osName){case"Mac OS X":p="Macintosh";break;case"Windows Phone":p="WindowsPhone";break;case"Windows NT":case"Windows 9x":case"Windows CE":p="Windows";break;case"Android":p="Android";break;case"Linux":case"Fedora":p="Linux";break;case"iOS":p="iOS";break;default:p=Do}return{deviceType:h,browser:g,os:p,deviceClass:null!=(i=u.deviceClass)?i:Do,deviceName:null!=(s=u.deviceName)?s:Do,deviceBrand:null!=(n=u.deviceBrand)?n:Do,osClass:null!=(r=u.osClass)?r:Do,osName:null!=(o=u.osName)?o:Do,osVersion:null!=(a=u.osVersion)?a:Do,agentClass:null!=(l=u.agentClass)?l:Do,agentName:null!=(c=u.agentName)?c:Do,agentVersionMajor:null!=(d=u.agentVersionMajor)?d:Do}}initializeUtmParamData(){const e=this.attributeStorage.getInternalAttributes("user",De);return{utmSource:e.uts,utmMedium:e.utm,utmCampaign:e.utcm,utmContent:e.utcn,utmTerm:e.utt}}initializeTrafficSourceData(){const{ts:e}=this.attributeStorage.getInternalAttributes("user",["ts"]);return null!=e?e:"OT"}initializeMarketoData(){return this.integrationDataStorage.getData("marketo").map((e=>null!=e?e:{})).orElse({})}initializeSalesforceData(){return this.integrationDataStorage.getData("salesforce").map((e=>{const t={};return void 0!==e&&Object.entries(e).forEach((([e,i])=>{Object.entries(i).forEach((([i,s])=>{t[`${e}.${i}`]=s}))})),t})).orElse({})}initializeFirmographicData(){return this.integrationDataStorage.getData("firmographic").map((e=>null!=e?e:{})).orElse({})}initializeSixsenseData(){return this.integrationDataStorage.getData("6sense").map((e=>null!=e?e:{})).orElse({})}initializeGoogleAdsData(){return this.integrationDataStorage.getData("googleAds").map((e=>null!=e?e:{})).orElse({})}initializeDemandbaseData(){return this.integrationDataStorage.getData("demandbase").map((e=>{const t={};return void 0!==e&&Object.entries(e).forEach((([e,i])=>{"custom"===e?Object.entries(i).forEach((([e,i])=>{t[`custom.${e}`]=i})):t[e]=i})),t})).map((e=>null!=e?e:{})).orElse({})}initializeHubspotData(){return this.integrationDataStorage.getData("hubspot").map((e=>{const t={};return void 0!==e&&Object.entries(e).forEach((([e,i])=>{Object.entries(i).forEach((([i,s])=>{t[`${e}.${i}`]=s}))})),t})).orElse({})}initializeHtmlLangData(){return this.environment.getHtmlLang().orElse("")}withAttributeDataOverrides(e){const t=this.override.getAttributeDataOverrides();return this.overrideObject(e.location,t.location),this.overrideObject(e.utmParams,t.utmParams),this.overrideObject(e.userAgent,t.userAgent),void 0!==t.dayPart&&(e.dayPart=t.dayPart),void 0!==t.weekPart&&(e.weekPart=t.weekPart),void 0!==t.trafficSource&&(e.trafficSource=t.trafficSource),this.overrideObject(e.custom,t.custom),this.overrideObject(e.urlParam,t.urlParam),void 0!==t.userVisitStatus&&(e.userVisitStatus=t.userVisitStatus),void 0!==t.userBucket&&(e.userBucket=t.userBucket),e}overrideObject(e,t){Object.entries(t).forEach((([t,i])=>{void 0!==i&&(e[t]=i)}))}}class Oo{constructor(e){__publicField(this,"timestamp"),this.timestamp=e.getNowUnixTime()}get id(){return"missing-policy"}getId(){return this.id}getTimestamp(){return this.timestamp}toJson(){return Re({id:this.id,timestamp:this.timestamp})}}function Lo(e){const t=[...e];let i,s=t.length;for(;s>0;)i=Math.floor(Math.random()*s),s--,[t[s],t[i]]=[t[i],t[s]];return t}class Ro{constructor(e){__publicField(this,"id"),__publicField(this,"timestamp"),__publicField(this,"experiences",{}),this.id=N(e.id),this.timestamp=N(e.timestamp),N(e.experiences),Object.keys(e.experiences).forEach((t=>{this.experiences[t]={modelVersion:N(e.experiences[t].modelVersion),variationId:N(e.experiences[t].variationId)}}))}static buildDefaultPolicy(e,t){const i={};return Object.keys(t.candidates).forEach((e=>{const s=t.candidates[e];void 0!==s&&Lo(s).forEach((t=>{(t.isSticky||void 0===i[e])&&(i[e]={modelVersion:"default-model-version",variationId:t.variationId})}))})),new Ro({id:"default-policy",timestamp:e.getNowUnixTime(),experiences:i})}static buildControlPolicy(e){return new Uo(e)}getId(){return this.id}getTimestamp(){return this.timestamp}getExperienceModelVersion(e){return void 0===this.experiences[e]?(Mo.info(`PolicyV5 could not find experience ${e}`),"NA"):this.experiences[e].modelVersion}getExperienceVariationId(e){return void 0===this.experiences[e]?(Mo.info(`PolicyV5 could not find experience ${e}`),"NA"):this.experiences[e].variationId}toJson(){return Re({id:this.id,timestamp:this.timestamp,experiences:this.experiences})}}class Uo extends Ro{constructor(e){super({id:"control-policy",timestamp:e.getNowUnixTime(),experiences:{}})}getExperienceModelVersion(e){return"control-experience"}getExperienceVariationId(e){return mt}}const $o=`${Te}NativeIdPath`,Vo=`${Te}ElementId`,Wo=`${Te}CmsContext`,zo=`${Te}ComponentContext`;class jo{constructor(e,t,i,s,n,r,o,a,l){__publicField(this,"customer"),__publicField(this,"environment"),__publicField(this,"user"),__publicField(this,"attributeStorage"),__publicField(this,"pageContext"),__publicField(this,"customerStorage"),__publicField(this,"override"),__publicField(this,"policy"),__publicField(this,"serverContext"),this.customer=N(e),this.environment=N(t),this.user=N(i),this.attributeStorage=N(s),this.pageContext=N(n),this.serverContext=N(r),this.customerStorage=N(o),this.policy=null!=a?a:new Oo(t),this.override=N(l)}setServerContext(e){this.serverContext=e}setPolicy(e){this.policy=e}sendPageview(){return __async(this,null,(function*(){Mo.debug("sendPageview()");const e=this.buildPageviewEvent();return this.send(e,!0)}))}sendContext(e){return __async(this,null,(function*(){Mo.debug("sendContext()");const t=this.buildContextEvent(e);return this.send(t,!0)}))}sendConversion(e){return __async(this,null,(function*(){Mo.debug("sendConversion()");const t=this.buildConversionEvent(e);return this.send(t,!0)}))}sendView(e,t){return __async(this,null,(function*(){Mo.debug("sendView()");const i=this.buildViewEvent(e,t);return this.send(i,!0)}))}sendGoal(e,t,i,s){return __async(this,null,(function*(){Mo.debug("sendGoal()");const n=this.buildGoalEvent(e,t,i);return this.send(n,s)}))}sendPageLeave(e){return __async(this,null,(function*(){Mo.debug("sendPageleave()");const t=this.buildPageLeaveEvent(e);yield this.send(t,!0)}))}static get PATH(){return"/logger"}buildPageviewEvent(){Mo.debug("buildPageviewEvent()");const e={eventType:"pv"};return this.buildCommonEvent(e),e.pageviewId=this.environment.getPageviewId(),e.sessionId=this.customerStorage.updateAndGetSessionId(),e.policyId=this.policy.getId(),e.policyTimestamp=this.policy.getTimestamp(),this.customerStorage.getCustomerControlStatus(this.customer).ifPresent((t=>{e.controlStatus=t})),e}buildContextEvent(e){Mo.debug("buildContextEvent()");const t={eventType:"ctx",pageviewId:this.environment.getPageviewId(),customerId:this.customer.getId(),userId:this.user.getId()},i=e.getAllData();return Object.keys(i).length>0&&(t.integrationData=i),t}buildConversionEvent(e){Mo.debug("buildConversionEvent()");const t={eventType:"c"};return this.buildCommonEvent(t),this.populateConversionInfo(t,e),t.pageviewId=this.environment.getPageviewId(),t.sessionId=this.customerStorage.updateAndGetSessionId(),t.policyId=this.policy.getId(),t.policyTimestamp=this.policy.getTimestamp(),this.customerStorage.getCustomerControlStatus(this.customer).ifPresent((e=>{t.controlStatus=e})),t}buildViewEvent(e,t){Mo.debug("buildViewEvent()");const i={eventType:"v"};this.buildBaseEvent(i,e);const s=e.getExperience(),n=s.getCampaign(),r=n.getCustomer();return t.isStickyVariation(e)?this.customerStorage.getVariationPredictionModelVersion(e).ifPresent((e=>{i.modelVersion=e})):i.modelVersion=t.getModelVersion(s.getId()),i.isPrediction=!t.isStickyVariation(e)&&e.getId()!==mt,i.isVariationSticky=t.isStickyVariation(e),i.isCampaignFirstTime=this.customerStorage.updateAndGetIsCampaignFirstTimeView(n,this.environment.getInitializeUnixTime()),i.isVariationFirstTime=this.customerStorage.updateAndGetIsVariationFirstTimeView(e,this.environment.getInitializeUnixTime()),i.pageviewId=this.environment.getPageviewId(),i.sessionId=this.customerStorage.updateAndGetSessionId(),i.policyId=this.policy.getId(),i.policyTimestamp=this.policy.getTimestamp(),this.customerStorage.getCustomerControlStatus(r).ifPresent((e=>{i.controlStatus=e})),i}buildGoalEvent(e,t,i){Mo.debug("buildGoalEvent()");const s={eventType:"ocg"};this.buildBaseEvent(s,e);const n=e.getExperience().getCampaign();return s.isCampaignFirstTime=this.customerStorage.updateAndGetIsCampaignFirstTimeGoal(n,t,this.environment.getInitializeUnixTime()),s.isVariationFirstTime=this.customerStorage.updateAndGetIsVariationFirstTimeGoal(e,t,this.environment.getInitializeUnixTime()),this.customerStorage.getVariationPredictionModelVersion(e).ifPresent((e=>{s.modelVersion=e})),s.metricId=t.getId(),s.isGoal=t.isGoal(),s.goalPageviewId=this.environment.getPageviewId(),s.goalSessionId=this.customerStorage.updateAndGetSessionId(),this.populateConversionInfo(s,i),this.customerStorage.getVariationPredictionPageviewId(e).ifPresent((e=>{s.pageviewId=e})),this.customerStorage.getVariationPredictionSessionId(e).ifPresent((e=>{s.sessionId=e})),s.policyId=this.policy.getId(),s.policyTimestamp=this.policy.getTimestamp(),this.customerStorage.getCustomerControlStatus(n.getCustomer()).ifPresent((e=>{s.controlStatus=e})),s}buildPageLeaveEvent(e){Mo.debug("buildPageleaveEvent()");const t=this.buildConversionEvent(e),i=this.environment.getPageviewId();return t.pageviewId=i,t.eventInstanceId=e.getEventInstanceId(),t.maxScrollDepth=Math.floor(this.environment.getScrollDepth()),t.timeOnPage=Math.floor(this.environment.getTimeOnPageMs()),t.bodyOffsetHeight=Math.floor(this.environment.getBodyOffsetHeight().orElse(0)),t.initialBodyOffsetHeight=Math.floor(this.environment.getInitialBodyOffsetHeight().orElse(0)),t.eventId=e.getEventId(),t}populateConversionInfo(e,t){e.actionId=t.getActionId();const i=t.getEvent();i instanceof yi&&t.getNativeMouseEvent().ifPresent((t=>{this.getNearestAnchorHref(t).ifPresent((t=>{e.clickTargetUrl=t})),this.getWebflowEngagementClickTrackedElement(i,t).ifPresent((t=>{var i,s;if(this.getWebflowEventLabel(t).ifPresent((t=>{e.webflowEventLabel=t})),e.webflowElementId=t.dataset[Vo],void 0!==t.dataset[$o]&&(e.webflowNativeIdPath=t.dataset[$o]),void 0!==t.dataset[zo]){const s=this.parseValidWebflowComponentContext(t.dataset[zo]);s.isPresent()?e.webflowComponentContext=s.get():Mo.error(`Provided Webflow component context is invalid or does not match the expected type of WebflowComponentContext: ${null!=(i=t.dataset[zo])?i:"undefined"}`,266)}if(void 0!==t.dataset[Wo]){const i=this.parseValidWebflowCmsContext(t.dataset[Wo]);i.isPresent()?e.webflowCmsContext=i.get():Mo.error(`Provided Webflow CMS context is invalid or does not match the expected type of WebflowCmsContext: ${null!=(s=t.dataset[Wo])?s:"undefined"}`,267)}}))})),e.eventId=t.getEventId(),e.eventInstanceId=t.getEventInstanceId(),t.getEventValue().ifPresent((t=>{e.eventValue=t}))}parseValidWebflowComponentContext(e){let t;try{t=JSON.parse(decodeURIComponent(e))}catch(i){return j.empty()}if(!Array.isArray(t))return j.empty();for(const s of t){if("object"!=typeof s||null===s)return j.empty();const{componentId:e,instanceId:t}=s;if("string"!=typeof e||"string"!=typeof t)return j.empty()}return j.of(t)}parseValidWebflowCmsContext(e){let t;try{t=JSON.parse(decodeURIComponent(e))}catch(i){return j.empty()}if(!Array.isArray(t))return j.empty();for(const s of t){if("object"!=typeof s||null===s)return j.empty();const{collectionId:e,itemId:t}=s;if("string"!=typeof e||"string"!=typeof t)return j.empty()}return j.of(t)}getNearestAnchorHref(e){var t,i;let s,n=e.target;for(;void 0!==n;){if(n instanceof HTMLAnchorElement){s=null!=(t=n.getAttribute("href"))?t:void 0;break}n=null!=(i=n.parentNode)?i:void 0}return j.ofNullable(s)}getWebflowEventLabel(e){let t;return this.getInnerText(e).ifPresent((e=>{const i=e.slice(0,100).trim().replace(/\s+/g," ");i.length>0&&(t=i)})),void 0===t&&this.getAriaLabel(e).ifPresent((e=>{t=e})),void 0===t&&this.getElementId(e).ifPresent((e=>{t=e})),void 0===t&&this.getChildImgElement(e).ifPresent((i=>{this.getAriaLabel(i).ifPresent((e=>{t=e})),void 0===t&&this.getImgAlt(i).ifPresent((e=>{t=e})),void 0===t&&this.getElementId(i).ifPresent((e=>{t=e})),void 0===t&&e instanceof HTMLAnchorElement&&this.getAnchorHrefAttr(e).ifPresent((e=>{t=e})),void 0===t&&this.getImgSrcAttr(i).ifPresent((e=>{t=e}))})).ifAbsent((()=>{e instanceof HTMLAnchorElement&&this.getAnchorHrefAttr(e).ifPresent((e=>{t=e}))})),j.ofNullable(t)}getChildImgElement(e){return 1===e.children.length&&e.children[0]instanceof HTMLImageElement?j.of(e.children[0]):j.empty()}getInnerText(e){return j.ofNullable(e.innerText)}getAriaLabel(e){return j.ofNullable(e.ariaLabel)}getElementId(e){return j.ofNullable(e.id)}getImgAlt(e){return j.ofNullable(e.alt)}getImgSrcAttr(e){return j.ofNullable(e.getAttribute("src"))}getAnchorHrefAttr(e){const t=e.getAttribute("href");return"#"===t?j.empty():j.ofNullable(t)}getWebflowEngagementClickTrackedElement(e,t){if(!(e instanceof ki))return j.empty();const i=t.target.closest(e.getSelector());return i instanceof HTMLElement?j.of(i):j.empty()}buildBaseEvent(e,t){const i=t.getExperience(),s=i.getCampaign();this.buildCommonEvent(e),e.campaignId=s.getId(),e.experienceId=i.getId(),e.variationId=t.getId()}buildCommonEvent(e){e.userId=this.user.getId(),e.isFirstTimeUser=this.user.isNewVisitor(),e.userVisitStatus=this.customerStorage.getUserVisitStatus(this.user),e.userBucket=this.user.getUserBucket(),e.intellimizeClientIp=this.serverContext.clientIp,e.customerId=this.customer.getId(),this.environment.getNowVisibilityState().ifPresent((t=>{e.visibilityState=t})),e.clientVersion=Q,e.requestId=this.serverContext.requestId,this.environment.getReferrerUrl().ifPresent((t=>{St.sanitizeUrl(t.getRawUrl()).ifPresent((t=>{e.referrerUrl=t})),e.referrerUrlEnc=t.getRawUrl()})),St.sanitizeUrl(this.environment.getHostingPageUrl().getRawUrl()).ifPresent((t=>{e.hostingPageUrl=t})),e.hostingPageUrlEnc=this.environment.getHostingPageUrl().getRawUrl(),e.intellimizeUserAgentDigest=this.serverContext.userAgentDigest,e.time=this.environment.getNowUnixTimeMs(),e.timeZone=this.environment.getTimeZone(),this.environment.getWidowWidth().ifPresent((t=>{e.windowWidth=t})),this.environment.getWidowHeight().ifPresent((t=>{e.windowHeight=t})),this.environment.getScreenOrientation().ifPresent((t=>{e.screenOrientation=t})),this.environment.getHtmlLang().ifPresent((t=>{e.htmlLang=t}));const t=this.attributeStorage.getFlattenedInternalAttributes();Object.keys(t).length>0&&(e.internalAttributes=t);const i=this.attributeStorage.getFlattenedCustomAttributes();Object.keys(i).length>0&&(e.customAttributes=i);const s=this.pageContext.getPages().map((e=>e.getId()));s.length>0&&(e.pageIds=s);const n=this.pageContext.getAudiences().map((e=>e.getId()));n.length>0&&(e.audienceIds=n);const r=this.environment.getWebflowPageMetadata();void 0!==r.domain&&(e.webflowDomain=r.domain),void 0!==r.siteId&&(e.webflowSiteId=r.siteId),void 0!==r.pageId&&(e.webflowPageId=r.pageId),void 0!==r.collectionId&&(e.webflowCollectionId=r.collectionId),void 0!==r.itemSlug&&(e.webflowItemSlug=r.itemSlug)}send(e,t){return __async(this,null,(function*(){const i=Date.now();Mo.debug(`send(async: ${t}) ${i}`);if(("pv"===e.eventType?this.override.sendPageviewBrowserEvents():this.override.sendNonPageviewBrowserEvents()).orElse(!0)){if(t){const t=yt(se,jo.PATH),i=Re(e);yield this.sendBeacon(t,i)}else yield this.environment.legacySynchronousXhr(se,jo.PATH,{method:"POST",body:Re(e)});Mo.debug("BrowserEvent sent")}else Mo.debug("BrowserEvent send skipped")}))}sendBeacon(e,t){return __async(this,null,(function*(){return new Promise(((i,s)=>{this.environment.sendBeacon(e,t)?i():s()}))}))}}class Bo{constructor(){__publicField(this,"onVariationRecordedCallbackTuples",[])}getVariationRecordedCallbackTuples(){return this.onVariationRecordedCallbackTuples}addVariationRecordedCallbackTuple(e){this.onVariationRecordedCallbackTuples.push(e)}}class Go{constructor(e,t){__publicField(this,"attributeData"),__publicField(this,"activityStorage"),this.attributeData=e,this.activityStorage=t}evalBoolean(e){Mo.info(`ConditionEvaluationRuntime.evalBoolean(${e.slice(0,10)})`);const t=Dt.evalBoolean(e);return Mo.info(`ConditionEvaluationRuntime.evalBoolean() => ${t}`),t}getAttributeValue(e){return this.attributeData.getAttributeValue(e)}getActivities(){return this.activityStorage.getAll()}}const Ho=e=>"0"===e||"1"===e;function Qo(e){return t=>new RegExp("^"+e+"$").test(t)}const Ko=e=>"true"===e||"false"===e,qo=e=>null!==e&&"object"==typeof e&&(!("reload"in e)||void 0===e.reload||"boolean"==typeof e.reload),Yo={reload:!1},Jo={reload:!1,activate:!1},Xo=qo,Zo=e=>qo(e)&&(!("activate"in e)||void 0===e.activate||"boolean"==typeof e.activate),ea=[Ee,_e,ye],ta=e=>"string"==typeof e&&ea.includes(e);class ia{constructor(e,t,i){__publicField(this,"environment"),__publicField(this,"taskManager"),__publicField(this,"customer"),__publicField(this,"userTrackingChoiceStorageKey"),__publicField(this,"legacyUserOptOutKey"),this.environment=N(e),this.taskManager=N(i),this.customer=N(t),this.userTrackingChoiceStorageKey=`${he}${t.getId()}`,this.legacyUserOptOutKey=`${me}${this.customer.getId()}`,this.migrateLegacyTrackingApisAndStorageKeys(),this.initialize()}static isTrackingPolicy(e){return e===ve||e===be||e===fe}getUserTrackingChoice(){return this.readUserTrackingChoice().orElse(ye)}shouldTrack(){const e=this.readTrackingPolicy().orElse(be);if(e===ve)return!0;const t=e===be,i=this.getUserTrackingChoice();return i===ye?t:i===Ee}allowUserTracking(e){if(this.getUserTrackingChoice()===Ee)return;const{reload:t=!1,activate:i=!1}=Zo(e)?e:Jo;this.writeUserTrackingChoice(Ee),t?this.environment.getWindow().location.reload():i&&this.activate()}denyUserTracking(e){if(this.getUserTrackingChoice()===_e)return;const{reload:t=!1}=Xo(e)?e:Yo;this.writeUserTrackingChoice(_e),this.clearLocalIntellimizeStorage(),t&&this.environment.getWindow().location.reload()}checkAndProcessOptOutFromQueryParam(){this.environment.getHostingPageUrl().getQueryParam("intellimize_opt_out",Qo("true")).ifPresent((()=>{this.environment.getWindow().confirm("Are you sure you want to opt out of Intellimize for this domain?")&&(this.denyUserTracking({reload:!1}),this.environment.getWindow().alert("You have successfully opted out of Intellimize for this domain."))}))}migrateLegacyTrackingApisAndStorageKeys(){const{Webflow:e}=this.environment.getWindow();void 0!==(null==e?void 0:e.analytics)&&(e.analytics.optIn=e=>{var t;const i=null==(t=null==e?void 0:e.reload)||t;this.allowUserTracking({reload:i})},e.analytics.optOut=e=>{var t;const i=null==(t=null==e?void 0:e.reload)||t;this.denyUserTracking({reload:i})},e.analytics.getIsOptedOut=()=>{const e=this.getUserTrackingChoice();if("none"!==e)return e===_e}),this.environment.readLocalStorage(pe).ifPresent((e=>{ia.isTrackingPolicy(e)&&(this.environment.writeLocalStorage(ge,e),this.environment.readLocalStorage(this.legacyUserOptOutKey).ifPresent((t=>{e===be&&"true"===t&&this.denyUserTracking({reload:!1})})).ifAbsent((()=>{e===fe&&this.allowUserTracking({reload:!1})})))})).ifAbsent((()=>{this.readTrackingPolicy().ifAbsent((()=>{this.environment.readLocalStorage(this.legacyUserOptOutKey).ifPresent((e=>{"true"===e&&this.denyUserTracking({reload:!1})}))}))}))}initialize(){this.shouldTrack()||this.clearLocalIntellimizeStorage()}readTrackingPolicy(){return this.environment.readLocalStorage(ge).flatMap((e=>j.ofNullable(ia.isTrackingPolicy(e)?e:void 0)))}readUserTrackingChoice(){return this.environment.readLocalStorage(this.userTrackingChoiceStorageKey).map((e=>ta(e)?e:"none"))}writeUserTrackingChoice(e){this.environment.writeLocalStorage(this.userTrackingChoiceStorageKey,e)}clearLocalIntellimizeStorage(){Ft(this.customer).forEach((e=>{this.environment.deleteLocalStorage(e)}))}activate(){this.taskManager.addTask(new Qr)}}const sa=["mutations"],na={debug(e,t,i){Mo.debug(e,t,i,sa)},group(e){Mo.group(e,sa)},groupEnd(){Mo.groupEnd(sa)},time(e){Mo.time(e,sa)},timeEnd(e){Mo.timeEnd(e,sa)}};class ra{attachObserver(){}detachObserver(){}registerSelectorAndExecute(e,t,i,s){const n=document.querySelectorAll(e);na.debug(`Found ${n.length} matching elements`),n.length>0?n.forEach((e=>{na.debug(`Processing element: ${Ve(e)}`),t(e)})):na.debug(`No matching elements for selector: ${e}`)}reset(){}}class oa{constructor(){__publicField(this,"state",0),this.state=0}markHiddenIfUnknown(){0===this.state&&(this.state=1)}markRevealed(){this.state=2}isHidden(){return 1===this.state}isRevealed(){return 2===this.state}}class aa extends oa{constructor(e,t,i,s,n){super(),__publicField(this,"experience"),__publicField(this,"environment"),__publicField(this,"extensionManager"),__publicField(this,"browserStorage"),__publicField(this,"attributeData"),this.experience=e,this.environment=t,this.extensionManager=i,this.browserStorage=s,this.attributeData=n}static getId(e){return`exp-${e.getId()}`}getId(){return aa.getId(this.experience)}getSelectors(){const e={};return this.experience.getRealVariations().forEach((t=>{t.getSelectors(this.environment,this.extensionManager,this.browserStorage,this.attributeData).forEach((t=>{e[t]=!0}))})),this.experience.getBoundingSelectors().forEach((t=>{e[t]=!0})),Object.keys(e)}getEntityIds(){return{experienceId:this.experience.getId()}}}class la extends oa{constructor(e,t,i,s,n){super(),__publicField(this,"page"),__publicField(this,"environment"),__publicField(this,"extensionManager"),__publicField(this,"browserStorage"),__publicField(this,"attributeData"),this.page=e,this.environment=t,this.extensionManager=i,this.browserStorage=s,this.attributeData=n}static getId(e){return`page-${e.getId()}`}getId(){return la.getId(this.page)}getSelectors(){const e={};return this.page.getChanges().forEach(((t,i)=>{Xn(t,$n(this.page),i,this.attributeData,this.environment,this.browserStorage,this.extensionManager).getRegionSelectors().forEach((t=>{e[t]=!0}))})),Object.keys(e)}getEntityIds(){return{pageId:this.page.getId()}}}class ca{constructor(e,t){__publicField(this,"environment"),__publicField(this,"extensionManager"),__publicField(this,"regions",{}),this.environment=e,this.extensionManager=t}registerExperience(e,t,i){const s=aa.getId(e);return void 0===this.regions[s]&&(this.regions[s]=new aa(e,this.environment,this.extensionManager,t,i)),this.regions[s]}registerPage(e,t,i){const s=la.getId(e);return void 0===this.regions[s]&&(this.regions[s]=new la(e,this.environment,this.extensionManager,t,i)),this.regions[s]}getAll(){return Object.values(this.regions)}}const da=class{constructor(e,t,i=!1){__publicField(this,"localWindow"),__publicField(this,"skipRegionHiding"),__publicField(this,"regionRegistry"),__publicField(this,"experienceRegionsHidden",!1),__publicField(this,"pageRegionsHidden",!1),this.localWindow=e.getWindow(),this.skipRegionHiding=i,this.regionRegistry=new ca(e,t)}static revealAll(e){da._revealDocument(e),da._revealAllRegions(e)}static get ANTI_FLICKER_CLASS_NAME(){return"anti-flicker"}static get REGION_TIMEOUT_MS(){return 3e3}static get RENDERING_ATTRIBUTE_NAME(){return`${da.ATTRIBUTE_NAME_PREFIX}rendering`}static getRegionAttribute(e){return`${da.ATTRIBUTE_NAME_PREFIX}${e.getId()}`}static _revealDocument(e){Mo.debug(`Hider.revealDocument() - before:${e.documentElement.className}`),e.documentElement.className=e.documentElement.className.replace(new RegExp(` ?${da.ANTI_FLICKER_CLASS_NAME}`,"g"),""),Mo.debug(`Hider.revealDocument() - after:${e.documentElement.className}`)}static _revealAllRegions(e){Mo.info(`Removing ${da.RENDERING_ATTRIBUTE_NAME} attribute`),e.documentElement.removeAttribute(da.RENDERING_ATTRIBUTE_NAME)}revealDocument(){da._revealDocument(this.localWindow.document)}revealDocumentIfAllRegionsHidden(){this.experienceRegionsHidden&&this.pageRegionsHidden&&this.revealDocument()}startTimedRegionHider(){Mo.debug("Hider.startTimedRegionHider()"),this.localWindow.document.documentElement.setAttribute(da.RENDERING_ATTRIBUTE_NAME,"true"),this.localWindow.setTimeout((()=>{da._revealAllRegions(this.localWindow.document),this.regionRegistry.getAll().forEach((e=>{e.isHidden()&&Mo.warn(`Region (${e.getId()}) was hidden at timeout`,209,e.getEntityIds())}))}),da.REGION_TIMEOUT_MS)}hideExperiences(e,t,i){for(const s of e)Mo.debug(`Hider - hide experience ${s.getId()}`),this.regionRegistry.registerExperience(s,t,i);this.setRegionHidingCss(),this.experienceRegionsHidden=!0}hidePages(e,t,i){for(const s of e)Mo.debug(`Hider - hide page ${s.getId()}`),this.regionRegistry.registerPage(s,t,i);this.setRegionHidingCss(),this.pageRegionsHidden=!0}revealExperience(e,t,i){Mo.debug(`Hider - reveal experience ${e.getId()}`),this.revealRegion(this.regionRegistry.registerExperience(e,t,i)),this.revealAllIfExperienceRegionsVisible()}revealPage(e,t,i){Mo.debug(`Hider - reveal page ${e.getId()}`),this.revealRegion(this.regionRegistry.registerPage(e,t,i))}revealAllIfExperienceRegionsVisible(){this.regionRegistry.getAll().every((e=>e.isRevealed()))&&da.revealAll(this.localWindow.document)}revealRegion(e){const t=e.getSelectors();if(t.length>0){const i=this.localWindow.document.querySelectorAll(t.join(", "));Array.prototype.forEach.call(i,(t=>{t.setAttribute(`${da.getRegionAttribute(e)}`,"true")}))}e.markRevealed()}setRegionHidingCss(){if(this.skipRegionHiding)return;const e=[];this.regionRegistry.getAll().forEach((t=>{t.getSelectors().forEach((i=>{const s=`html[${da.RENDERING_ATTRIBUTE_NAME}] ${i}:not([${da.getRegionAttribute(t)}])`;e.push(s,`${s} *`)}))}));const t=e.length>0?`${e.join(", ")} { visibility: hidden !important; opacity: 0 !important; }`:"";Dt.injectCss(t,"anti-flicker-regions",this.localWindow.document),this.regionRegistry.getAll().forEach((e=>{e.markHiddenIfUnknown()}))}};let ua=da;__publicField(ua,"ATTRIBUTE_NAME_PREFIX","data-intellimize-anti-flicker-");const ha=2,ga=1,pa=ga,ma=class{constructor(){__publicField(this,"mutationObserver"),__publicField(this,"mutationHandlers",{}),__publicField(this,"currentUrl"),this.mutationObserver=new MutationObserver((e=>{if(Object.keys(this.mutationHandlers).length>0){if(na.group("MutationObserver.observe() - window.location.href: "+window.location.href),this.currentUrl||(this.currentUrl=window.location.href),this.currentUrl!==window.location.href)return na.debug("MutationObserver skip processing since URL changed"),void na.groupEnd();ma.containsValidMutations(e)?(na.debug("mutationList contains at least one valid mutation; processing store"),this.processStore()):na.debug("mutationList contains no valid mutations; skipping store processing"),na.groupEnd()}})),this.reset()}static get CALLBACK_MAX_COUNT(){return 25}static get MISSING_ELEMENT_MAX_COUNT(){return 5}static get CALLBACK_THROTTLE_MS(){return 50}static selectorExists(e){try{return Boolean(document.querySelector(e))}catch(t){return!1}}static isProcessed(e,t){var i,s,n,r;switch(pa){case ga:return"string"==typeof(null==(i=e.imizeObserverProcessed)?void 0:i[t])||!0===(null==(s=e.imizeObserverProcessed)?void 0:s[t])?(na.debug('isProcessed (OPT_CHECK_BOOL): imizeObserverProcessed is "true"'),!0):(na.debug('isProcessed (OPT_CHECK_BOOL): imizeObserverProcessed is NOT "true"'),!1);case ha:return"string"==typeof(null==(n=e.imizeObserverProcessed)?void 0:n[t])||!0===(null==(r=e.imizeObserverProcessed)?void 0:r[t])?e.imizeObserverProcessed[t]===e.outerHTML?(na.debug("isProcessed (OPT_COMPARE_HTML): imizeObserverProcessed outerHTML matches"),!0):(na.debug("isProcessed (OPT_COMPARE_HTML): imizeObserverProcessed outerHTML DOES NOT match"),na.debug(`outerHTML: ${e.outerHTML}`),na.debug(`imizeObserverProcessed: ${e.imizeObserverProcessed[t]}`),e.imizeObserverProcessed[t]=!1,!1):(na.debug("isProcessed (OPT_COMPARE_HTML): imizeObserverProcessed does not exist on the element"),!1);default:return na.debug("isProcessed (OPT_ALWAYS_CALLBACK): skipping check"),!1}}static setIsProcessed(e,t){switch(void 0===e.imizeObserverProcessed&&(e.imizeObserverProcessed={}),pa){case ga:e.imizeObserverProcessed[t]=!0;break;case ha:e.imizeObserverProcessed[t]=e.outerHTML}}static isValidNodeNameMutation(e){var t;if(ma.INVALID_NODE_NAMES.includes(e.target.nodeName))return na.debug(`Invalid mutation - Node is one of ${Le(ma.INVALID_NODE_NAMES)}`),!1;if(!0===(null==(t=e.attributeName)?void 0:t.startsWith(ua.ATTRIBUTE_NAME_PREFIX)))return!1;if("childList"===e.type){if(Array.prototype.every.call([...Array.prototype.slice.call(e.addedNodes),...Array.prototype.slice.call(e.removedNodes)],(e=>ma.INVALID_NODE_NAMES.includes(e.nodeName))))return na.debug("Invalid mutation - All nodes in child list contain one of "+Le(ma.INVALID_NODE_NAMES)),!1}return!0}static isUnchangedAttributeMutation(e){const{target:t,type:i,attributeName:s,oldValue:n}=e;if(t instanceof HTMLElement&&"attributes"===i&&s){const e=t.attributes.getNamedItem(s);if(null!==e&&e.value===n)return na.debug(`Invalid mutation - value for attribute "${s}" hasn't changed (value: "${n}")`),!0}return!1}static isStatusModuleNode(e){const t="intellimize____status-module-root";return e instanceof HTMLElement&&(e.id===t?(na.debug("Invalid mutation - Node is part of the status module"),!0):null!==e.parentNode&&ma.isStatusModuleNode(e.parentNode))}static containsValidMutations(e){na.group("containsValidMutations()");const t=e.every((e=>(na.debug(`Mutation: ${$e(e)}`),!(ma.isValidNodeNameMutation(e)&&!ma.isUnchangedAttributeMutation(e)&&!ma.isStatusModuleNode(e.target))||(na.debug("Found valid mutation"),!1))));return na.groupEnd(),!t}registerSelectorAndExecute(e,t,i,s){if(!ma.selectorExists(e))throw new Error(`Selector "${e}" does not exist in the DOM`);const n=zt();this.mutationHandlers[n]={selector:e,callback:t,options:i,startTime:new Date,mutationCnt:0,missingElementCnt:0,entityIds:s},na.group(`registerSelectorAndExecute(${n})`),na.debug("Manually calling handleObservation()"),this.detachObserver(),this.handleObservation(n,!1),this.attachObserver(),na.groupEnd()}attachObserver(){na.debug("Attaching observer to document"),this.mutationObserver.observe(document.documentElement,ma.observerOpts)}detachObserver(){na.debug("Detaching observer from document"),this.mutationObserver.disconnect()}reset(){na.debug("Resetting ElementObserver state"),this.mutationHandlers={},this.currentUrl=void 0,this.detachObserver()}processStore(){na.group("ReapplyElementObserver.processStore()"),na.debug(`mutationHandlers: ${Le(this.mutationHandlers)}`),this.detachObserver(),Object.keys(this.mutationHandlers).forEach((e=>{this.handleObservation(e,!0)})),this.attachObserver(),na.groupEnd()}handleObservation(e,t){const i=this.mutationHandlers[e];if(na.group(`handleObservation(${e})`),t&&i.options.observationTimeMs&&Date.now()-i.startTime.getTime()>i.options.observationTimeMs)return 0===i.mutationCnt?void 0!==i.options.onTimeout&&(Mo.error(`MutationHandler timed out for ${i.selector}. observationTimeMs (${i.options.observationTimeMs}) has passed.`,140,i.entityIds),i.options.onTimeout()):Mo.warn(`Observation cancelled for ${i.selector}. observationTimeMs (${i.options.observationTimeMs}) has passed.`,141,i.entityIds),this.cancelObservation(e),void na.groupEnd();if(i.mutationCnt>=ma.CALLBACK_MAX_COUNT)return na.debug(`Reached CALLBACK_MAX_COUNT (${ma.CALLBACK_MAX_COUNT})`),this.cancelObservation(e),void na.groupEnd();if(void 0!==i.lastCallbackTime){const e=Date.now()-i.lastCallbackTime.getTime();if(e<ma.CALLBACK_THROTTLE_MS)return na.debug(`Throttling callback (${ma.CALLBACK_THROTTLE_MS}ms). Only (${e})ms have elapsed`),void na.groupEnd()}const s=document.querySelectorAll(i.selector);if(na.debug(`Found ${s.length} matching elements`),s.length>0){const n=t=>!ma.isProcessed(t,e)||void 0!==i.options.shouldReapply&&i.options.shouldReapply(t);Array.prototype.forEach.call(s,(s=>{if(na.debug(`Processing element: ${Ve(s)}`),!t||n(s)){na.debug("isProcessed: false");let n="Triggering callback for element";t&&(i.mutationCnt+=1,i.lastCallbackTime=new Date,n+=` (mutationCnt: ${i.mutationCnt})`),na.debug(n),i.callback(s),ma.setIsProcessed(s,e),i.options.once&&this.cancelObservation(e)}else na.debug("isProcessed: true (skipping)")}))}else Mo.warn(`Could not find any matching elements for ${i.selector}`,139,i.entityIds),i.missingElementCnt+=1,i.missingElementCnt>=ma.MISSING_ELEMENT_MAX_COUNT&&(na.debug(`Reached MISSING_ELEMENT_MAX_COUNT (${ma.MISSING_ELEMENT_MAX_COUNT})`),this.cancelObservation(e));na.groupEnd()}cancelObservation(e){delete this.mutationHandlers[e]}};let va=ma;__publicField(va,"INVALID_NODE_NAMES",["HTML","SCRIPT","LINK","STYLE","HEAD","IFRAME","TITLE","META"]),__publicField(va,"observerOpts",{subtree:!0,childList:!0,attributes:!0,characterData:!0,attributeOldValue:!0,characterDataOldValue:!0});class ba{constructor(e){__publicField(this,"rawUrl"),__publicField(this,"urlObject"),this.rawUrl=N(e),this.urlObject=new URL(e)}getRawUrl(){return this.rawUrl}getOrigin(){return this.urlObject.origin}getQueryParam(e,t){let i=j.ofNullable(this.urlObject.searchParams.get(e));return i.isPresent()&&!t(i.get())&&(Mo.error(`Validator failed for ${e}`,28),i=j.empty()),i}getAllQueryParamsUnsafe(){const e={};for(const[t,i]of this.urlObject.searchParams.entries())t.length>0&&i.length>0&&!Object.prototype.hasOwnProperty.call(e,t)&&(e[t]=i);return e}}var fa=(e=>(e[e.LN=0]="LN",e[e.EA=1]="EA",e[e.DT=2]="DT",e[e.EE=3]="EE",e[e.NT=4]="NT",e))(fa||{}),Ea=(e=>(e[e.WD=0]="WD",e[e.WE=1]="WE",e))(Ea||{});const _a=`${Te}Domain`,ya=`${Te}Site`,Sa=`${Te}Page`,wa=`${Te}Collection`,Ia=`${Te}ItemSlug`,Ca=`${Te}Experiences`;class Ta{constructor(e,t){__publicField(this,"localWindow"),__publicField(this,"nativeFetch"),__publicField(this,"nativeSendBeacon"),__publicField(this,"timeZone"),__publicField(this,"date"),__publicField(this,"windowWidth"),__publicField(this,"windowHeight"),__publicField(this,"initialBodyOffsetHeight"),__publicField(this,"webflowPageMetadata"),__publicField(this,"pageviewId"),__publicField(this,"visibilityChangeName"),__publicField(this,"isHidden"),__publicField(this,"hostingPageUrl"),__publicField(this,"referrerUrl"),__publicField(this,"htmlLang"),__publicField(this,"maxDepth"),__publicField(this,"mostRecentActivity"),__publicField(this,"timeOnPageStart"),__publicField(this,"previouslyElapsedTimeOnPage"),__publicField(this,"hasInitialized"),this.nativeFetch=e.fetch.bind(e),this.nativeSendBeacon=e.navigator.sendBeacon.bind(e.navigator),this.timeZone=t,this.localWindow=e,this.visibilityChangeName="visibilitychange",this.isHidden=()=>!1,this.hasInitialized=!1,this.reinitialize()}reinitialize(){Mo.debug("Environment.reinitialize()"),this.hostingPageUrl=new ba(this.localWindow.location.href),this.referrerUrl=b(this.localWindow.document.referrer)?new ba(this.localWindow.document.referrer):void 0,this.htmlLang=this.localWindow.document.documentElement.lang,void 0!==this.localWindow.document.hidden?(this.isHidden=()=>this.localWindow.document.hidden,this.visibilityChangeName="visibilitychange"):void 0!==this.localWindow.document.msHidden?(this.isHidden=()=>this.localWindow.document.msHidden,this.visibilityChangeName="msvisibilitychange"):void 0!==this.localWindow.document.webkitHidden&&(this.isHidden=()=>this.localWindow.document.webkitHidden,this.visibilityChangeName="webkitvisibilitychange"),this.windowWidth=this.localWindow.innerWidth,this.windowHeight=this.localWindow.innerHeight,this.cleanupOldListeners(),this.maxDepth=0,this.mostRecentActivity=void 0,this.timeOnPageStart=void 0,this.previouslyElapsedTimeOnPage=0,this.hasInitialized=!1,this.initializeTrackers(),this.date=this.getNowDate(),this.pageviewId=zt(),this.reinitializeWebflowPageMetadata()}getHostingPageUrl(){return this.hostingPageUrl}getReferrerUrl(){return j.ofNullable(this.referrerUrl)}hasHostingPageUrlChanged(){const e=new ba(this.localWindow.location.href);return this.hostingPageUrl.getRawUrl()!==e.getRawUrl()}getTimeZone(){return this.timeZone}getDayPart(){const e=this.getInitializeDate().getHours();let t;return t=e>=0&&e<7?0:e>=7&&e<10?1:e>=10&&e<17?2:e>=17&&e<20?3:4,fa[t]}getWeekPart(){const e=this.getInitializeDate().getDay();return Ea[0===e||6===e?1:0]}getInitializeDate(){return new Date(this.date.getTime())}getInitializeUnixTime(){return Math.round(this.date.getTime()/K)}getNowDate(){return new Date}getNowUnixTime(){return Math.round(this.getNowUnixTimeMs()/K)}getNowUnixTimeMs(){return this.getNowDate().getTime()}getWidowWidth(){return j.ofNullable(this.windowWidth)}getWidowHeight(){return j.ofNullable(this.windowHeight)}getBodyOffsetHeight(){var e;return j.ofNullable(null==(e=this.localWindow.document.body)?void 0:e.offsetHeight)}getInitialBodyOffsetHeight(){return j.ofNullable(this.initialBodyOffsetHeight)}getScrollDepth(){var e;return null!=(e=this.maxDepth)?e:0}getTimeOnPageMs(){var e;const t=null!=(e=this.previouslyElapsedTimeOnPage)?e:0;return void 0===this.timeOnPageStart?t:this.getNowUnixTimeMs()-this.timeOnPageStart+t}isActive(){var e;return null==(e=this.checkIsActive())||e}getScreenOrientation(){return void 0!==this.windowHeight&&void 0!==this.windowWidth?this.windowWidth>this.windowHeight?j.of("landscape"):j.of("portrait"):j.empty()}getPageviewId(){return this.pageviewId}getHtmlLang(){return j.ofNullable(this.htmlLang)}writeCookie(e){Mo.debug("Environment.writeCookie("+e+")"),this.localWindow.document.cookie=e}readAllCookies(){return j.ofNullable(this.localWindow.document.cookie)}readCookie(e){const t=e+"=";let i=j.empty();return this.readAllCookies().ifPresent((e=>{const s=e.split(";");for(let n of s){for(;n.startsWith(" ");)n=n.slice(1);n.startsWith(t)&&(i=j.of(n.slice(t.length)))}})),i}writeLocalStorage(e,t){this.localWindow.localStorage.setItem(e,t)}readLocalStorage(e){return j.ofNullable(this.localWindow.localStorage.getItem(e))}deleteLocalStorage(e){this.localWindow.localStorage.removeItem(e)}writeSessionStorage(e,t){this.localWindow.sessionStorage.setItem(e,t)}readSessionStorage(e){return j.ofNullable(this.localWindow.sessionStorage.getItem(e))}deleteSessionStorage(e){this.localWindow.sessionStorage.removeItem(e)}readLocalStorageMatching(e){const t={};return Object.keys(this.localWindow.localStorage).forEach((i=>{e.test(i)&&(t[i]=this.readLocalStorage(i).get())})),t}handleVisibilityChange(){if(this.isNowDocumentHidden().orElse(!1))this.stopViewingTimer();else{if(!this.hasInitialized)return void this.setInitialTrackingValuesIfVisible();this.startViewingTimer(),this.handleActivity()}}updateMaxDepth(e){Mo.debug(`Environment.updateMaxDepth(${e})`),this.getScrollDepth()<e&&(this.maxDepth=e)}setScrollDepth(){this.updateMaxDepth(this.localWindow.innerHeight+this.localWindow.scrollY)}getReadyState(){return this.localWindow.document.readyState}getWindow(){return this.localWindow}getNowVisibilityState(){return"document"in this.localWindow&&"visibilityState"in this.localWindow.document?j.ofNullable(this.localWindow.document.visibilityState):j.empty()}isNowDocumentHidden(){return"document"in this.localWindow?j.ofNullable(this.isHidden()):j.empty()}addListener(e,t){this.localWindow.addEventListener(e,t)}addListenerVisibilityChange(e){this.localWindow.document.addEventListener(this.visibilityChangeName,e,{once:!0,capture:!1})}addListenerVisibilityChangeOngoing(e){this.localWindow.document.addEventListener(this.visibilityChangeName,e,!1)}addUnloadHandler(e){this.localWindow.addEventListener("beforeunload",e,!1)}addListenerScroll(e){this.localWindow.document.addEventListener("scroll",e)}addListenerOnReadyStateChange(e){this.localWindow.document.addEventListener("readystatechange",e)}addListenerOnActivity(e){this.localWindow.addEventListener("mousemove",e),this.localWindow.addEventListener("keydown",e),this.localWindow.addEventListener("click",e),this.localWindow.document.addEventListener("scroll",e)}redirect(e){this.localWindow.document.location.replace(e)}generateActionId(){return this.getNowUnixTime()+"-"+Bt()}injectScript(e,t=!1){const i=this.localWindow.document.createElement("script");i.async=t,i.src=e;const s=this.localWindow.document.querySelectorAll("script")[0];s.parentNode.insertBefore(i,s)}sendBeacon(e,t){return this.nativeSendBeacon(e,t)}fetch(e,t){return __async(this,arguments,(function*(e,t,i={}){const s=yt(e,t);return this.nativeFetch(s,i).then((e=>{if(!e.ok)throw new xa(s,e.status);if(204!==e.status)return e.json()}))}))}setInitialTrackingValuesIfVisible(){Mo.group("Environment.setInitialTrackingValues()"),this.isNowDocumentHidden().orElse(!1)?Mo.debug("document is hidden, skipping initial tracking values"):(Mo.debug("document is visible, setting initial tracking values"),this.setScrollDepth(),this.startViewingTimer(),this.handleActivity(),this.hasInitialized=!0)}legacySynchronousXhr(e,t){return __async(this,arguments,(function*(e,t,i={}){return new Promise(((s,n)=>{var r;const o=yt(e,t),a=new XMLHttpRequest;a.open(null!=(r=i.method)?r:"GET",o,!1),a.addEventListener("load",(()=>{a.status>=200&&a.status<=299?s():n(new xa(o,a.status))})),a.addEventListener("error",(()=>{n(new xa(o,a.status))})),a.send(i.body)}))}))}getWebflowPageMetadata(){return this.webflowPageMetadata}checkIsActive(){return Mo.debug("Environment.isActive()"),this.getTimeSinceMostRecentActivity()<1e3*Y}handleActivity(){Mo.debug("Environment.handleActivity()"),this.mostRecentActivity=this.getNowUnixTimeMs()}setInitialBodyOffsetHeight(){this.initialBodyOffsetHeight=this.getBodyOffsetHeight().orElse(0)}startViewingTimer(){Mo.debug("Environment.startViewingTimer()"),void 0===this.timeOnPageStart&&(this.timeOnPageStart=this.getNowUnixTimeMs())}stopViewingTimer(){var e;Mo.debug("Environment.stopViewingTimer()"),void 0!==this.timeOnPageStart&&(this.previouslyElapsedTimeOnPage=this.getNowUnixTimeMs()-this.timeOnPageStart+(null!=(e=this.previouslyElapsedTimeOnPage)?e:0),this.timeOnPageStart=void 0,Mo.debug(`Environment.stopViewingTimer() elapsed: ${this.previouslyElapsedTimeOnPage}`))}getTimeSinceMostRecentActivity(){var e;return Mo.debug("Environment.getTimeSinceMostRecentActivity()"),this.getNowUnixTimeMs()-(null!=(e=this.mostRecentActivity)?e:this.getNowUnixTimeMs())}reinitializeWebflowPageMetadata(){var e;const t=this.localWindow.document.documentElement.dataset[_a],i=this.localWindow.document.documentElement.dataset[ya],s=this.localWindow.document.documentElement.dataset[Sa],n=this.localWindow.document.documentElement.dataset[wa],r=this.localWindow.document.documentElement.dataset[Ia],o=(null!=(e=this.localWindow.document.documentElement.dataset[Ca])?e:"").split(",");this.webflowPageMetadata={domain:t,pageId:s,siteId:i,collectionId:n,itemSlug:r,experienceIds:o}}cleanupListenerVisibilityChangeOngoing(){this.localWindow.document.removeEventListener(this.visibilityChangeName,this.handleVisibilityChange)}cleanupListenerScroll(){this.localWindow.document.removeEventListener("scroll",this.setScrollDepth)}cleanupListenerOnActivity(){this.localWindow.removeEventListener("mousemove",this.handleActivity),this.localWindow.removeEventListener("keydown",this.handleActivity),this.localWindow.removeEventListener("click",this.handleActivity),this.localWindow.document.removeEventListener("scroll",this.handleActivity)}cleanupOldListeners(){Mo.debug("Environment.cleanupOldListeners()"),this.cleanupListenerVisibilityChangeOngoing(),this.cleanupListenerScroll(),this.cleanupListenerOnActivity()}initializeTrackers(){this.setInitialBodyOffsetHeight(),this.setInitialTrackingValuesIfVisible(),this.addListenerVisibilityChangeOngoing((()=>{this.handleVisibilityChange()})),this.addListenerScroll((()=>{this.setScrollDepth()})),this.addListenerOnActivity((()=>{this.handleActivity()}))}}class xa extends Error{constructor(e,t){super(`(${t}) ${e.toString()}`),this.name="JsonFetchError"}}const Aa=()=>{const e={};return{getExtension:t=>j.ofNullable(e[t]),registerExtension(t){e[t.getName()]=t}}},Fa=e=>{if(hn(e)){const t=Object.keys(e);if(4===t.length){if(0===mn(["p","r","push","ready"],t).length&&dn(e.p)&&dn(e.r)&&gn(e.push)&&gn(e.ready))return!0}}return!1},ka=e=>{if(hn(e)){const t=Object.keys(e);if(2===t.length){if(0===mn(["r","ready"],t).length&&dn(e.r)&&gn(e.ready))return!0}}return!1};class Pa{constructor(e){__publicField(this,"environment"),__publicField(this,"callQueue"),__publicField(this,"readyCallbackQueue"),this.environment=e,this.callQueue=[],this.readyCallbackQueue=[]}initialize(){const e=this.environment.getWindow();if(void 0!==e.intellimize)if(dn(e.intellimize))this.setCallQueue(e.intellimize);else if(hn(e.intellimize))if(Fa(e.intellimize)){const t=e.intellimize;this.setCallQueue(t.p),this.addToReadyCallbackQueue(t.r)}else eo.consoleError("window.intellimize is an object that does not match the expected definition",116);else eo.consoleError("window.intellimize is not an object or an array",117);if(void 0!==e.wf)if(hn(e.wf))if(ka(e.wf)){const t=e.wf;this.addToReadyCallbackQueue(t.r)}else eo.consoleError("window.wf is an object that does not match the expected definition",264);else eo.consoleError("window.wf is not an object",263);e.intellimize={ready:this.ready.bind(this),push:this.push.bind(this)},e.wf={ready:this.ready.bind(this)}}extractCallQueue(){const{callQueue:e}=this;return this.callQueue=[],e}extractReadyCallbackQueue(){const{readyCallbackQueue:e}=this;return this.readyCallbackQueue=[],e}ready(e){eo.validateReadyCallback(e)&&(Mo.debug("Registering ready() callback"),this.readyCallbackQueue.push(e))}push(e){eo.validatePushCommand(e)&&(Mo.debug("Adding command to call queue"),this.callQueue.push([...e]))}setCallQueue(e){Mo.debug("Storing callQueue"),this.callQueue=[],e.forEach((e=>{eo.validatePushCommand(e)&&this.callQueue.push([...e])}))}addToReadyCallbackQueue(e){Mo.debug("Appending to readyCallbackQueue"),e.forEach((e=>{eo.validateReadyCallback(e)&&this.readyCallbackQueue.push(e)}))}}class Ma{constructor(e,t,i,s,n,r,o,a,l,c,d){__publicField(this,"customer"),__publicField(this,"user"),__publicField(this,"environment"),__publicField(this,"extensionManager"),__publicField(this,"taskManager"),__publicField(this,"browserStorage"),__publicField(this,"customerStorage"),__publicField(this,"externalApi"),__publicField(this,"attributeData"),__publicField(this,"elementObserver"),__publicField(this,"storageKey"),__publicField(this,"serverContext"),this.customer=e,this.user=t,this.environment=i,this.extensionManager=s,this.serverContext=n,this.taskManager=r,this.browserStorage=o,this.customerStorage=a,this.externalApi=l,this.attributeData=c,this.elementObserver=d,this.storageKey=ne+e.getId()}initialize(){if(!this.externalApi.isInitialized)throw new Error("ExternalApi needs to be initialized before calling InternalApi.initialize()");const e=this.environment.getWindow(),{intellimize:t}=e;void 0!==t?(t.plugins={},t.setLocalState=(e,t)=>{Mo.info(`intellimize.setLocalState(${e}, ${t})`),this.setLocalState(e,t)},t.getLocalState=e=>{const t=this.getLocalState(e);return Mo.info(`intellimize.getLocalState(${e}) => ${t}`),t},t.deleteLocalState=e=>{Mo.info(`intellimize.deleteLocalState(${e})`),this.deleteLocalState(e)},t.getSessionId=()=>(Mo.info("intellimize.getSessionId()"),this.getSessionId()),t.getUserId=()=>(Mo.info("intellimize.getUserId()"),this.getUserId()),t.getPageviewId=()=>(Mo.info("intellimize.getPageviewId()"),this.getPageviewId()),t.logErr=e=>{Mo.error(`Customer error: ${e}`,1)},t.log=(e,t,s)=>{const n=1e4;s&&(!/^\d{5}$/.test(`${s}`)||s<10001||s>10999)&&(Mo.error(`Called intellimize.log with an invalid message code. (Wanted: 10001 < code < 10999. Found: ${s})`,998),s=n);const r=null!=s?s:n;switch(t){case i.LogLevel.TRACE:case i.LogLevel.DEBUG:return void Mo.debug(e,r);case i.LogLevel.INFO:return void Mo.info(e,r);case i.LogLevel.WARN:return void Mo.warn(e,r);case i.LogLevel.ERROR:return void Mo.error(e,r);default:Mo.error(`Called intellimize.log with an invalid LogLevel (${t}). Treating as "debug".`,999),Mo.debug(e,r)}},t.applyChange=e=>{const t=Xn(Ts(e),Vn(e),0,this.attributeData,this.environment,this.browserStorage,this.extensionManager);return t.isReadyToApply()?(t.apply((()=>{}),this.elementObserver),F.APPLIED):F.UNAPPLIED},t.undoChange=e=>(Xn(Ts(e),Vn(e),0,this.attributeData,this.environment,this.browserStorage,this.extensionManager).undo(),F.UNAPPLIED),t.hasChangeData=e=>kn(e),t.getChangeTypeData=(e,t)=>Rn(e,t),t.hasStarted=()=>{const e=this.taskManager.hasStarted();return Mo.debug(`intellimize.hasStarted() => ${e}`),e},t.isDone=()=>{const e=this.taskManager.isDone();return Mo.debug(`intellimize.isDone() => ${e}`),e},t.onDone=e=>{Mo.debug("intellimize.onDone(callbackFunction)"),this.taskManager.onDone(e)},t.isRestarting=()=>{const e=this.taskManager.getClient().isRestarting();return Mo.debug(`intellimize.isRestarting() => ${e}`),e},Mo.getLevel()<=i.LogLevel.INFO&&(e[e.atob("aWNqc24=")]=this.customer.getData(),e[e.atob("aXBqc24=")]=()=>this.browserStorage.read(le+this.customer.getId()).map((e=>Ue(je(this.environment,e)))).orElse({})),this.reinitialize()):Mo.error("window.intellimize is undefined: Cannot initialize InternalApi",219)}reinitialize(){this.exposeAttributeData()}setServerContext(e){this.serverContext=e}setLocalState(e,t){const i=this.readState();i[e]=t,this.writeState(i)}getLocalState(e){return this.readState()[e]}deleteLocalState(e){const t=this.readState();delete t[e],this.writeState(t)}getSessionId(){return this.customerStorage.updateAndGetSessionId()}getUserId(){return this.user.getId()}getPageviewId(){return this.environment.getPageviewId()}readState(){let e={};return this.browserStorage.read(this.storageKey).ifPresent((t=>{try{e=Ue(t)}catch(i){const e=new Ei("Could not parse internal api storage",i);Mo.error(e,33)}})),e}writeState(e){const t=Re(e);this.browserStorage.write(this.storageKey,t)}exposeAttributeData(){var e;const t=this.environment.getWindow(),i=e=>{if(void 0!==e.cityName)return e.cityName.replace(/\s/g,"").toUpperCase()};t.iiloc={country:this.serverContext.location.countryIso,region:null!=(e=this.serverContext.location.subdivision2Iso)?e:this.serverContext.location.subdivision1Iso,city:i(this.serverContext.location),dma:this.serverContext.location.dmaCode,postal:this.serverContext.location.postalCode,tz:this.environment.getTimeZone()};const s=this.attributeData.getUserAgentData(),n=this.attributeData.getTrafficSourceData();t.icntxtlftrs={IP:this.serverContext.clientIp,D:s.deviceType,B:s.browser,OS:s.os,DC:s.deviceClass,DN:s.deviceName,DB:s.deviceBrand,OC:s.osClass,ON:s.osName,OV:s.osVersion,AC:s.agentClass,AN:s.agentName,AV:s.agentVersionMajor,TS:n};const r=this.attributeData.getUtmParamsData();t.iutmprms={uts:r.utmSource,utm:r.utmMedium,utcm:r.utmCampaign,utcn:r.utmContent,utt:r.utmTerm}}}const Da="UTC";let Na,Oa=!0;function La(e){var t;if(void 0!==Na)return Na;if(Oa){if("undefined"===(null==(t=e.Intl)?void 0:t.DateTimeFormat))return void Ra();const i=e.Intl.DateTimeFormat();return void 0===i||void 0===i.resolvedOptions?void Ra():(Na=e.Intl,Na)}}function Ra(){Oa=!1,Mo.error("Intl.DateTimeFormat().resolvedOptions() is not supported by the browser",1014)}function Ua(e){const t=La(e);let i="";return void 0!==t&&(i=t.DateTimeFormat().resolvedOptions().timeZone),void 0===i||""===i?Da:i}class $a{constructor(e,t,i){__publicField(this,"isEditorMode"),__publicField(this,"_shouldRun"),__publicField(this,"_originAllowed"),__publicField(this,"experienceVariations",{}),__publicField(this,"_allPagesLive"),__publicField(this,"_allAudiencesLive"),__publicField(this,"_allEventsLive"),__publicField(this,"_passExperienceAudience"),__publicField(this,"_passVariationAudience"),__publicField(this,"_isControl"),__publicField(this,"_saveCustomerStorage"),__publicField(this,"_sendBrowserEvents"),__publicField(this,"_sendGoogleAnalytics"),__publicField(this,"_sendSegmentAnalytics"),__publicField(this,"_sendMixpanel"),__publicField(this,"_showStatusModule"),__publicField(this,"_showBasesite"),__publicField(this,"_expires"),__publicField(this,"_storageAndOptimizationRestricted"),__publicField(this,"attributeData",{location:{},utmParams:{},userAgent:{},dayPart:void 0,weekPart:void 0,trafficSource:void 0,custom:{},urlParam:{},userVisitStatus:void 0,userBucket:void 0}),__publicField(this,"_requestAnimationFrame"),__publicField(this,"consentManagerApis");const s=_t(t,e.getHostingPageUrl().getOrigin());s.isPresent()&&!s.get()&&(this._originAllowed=!1);const n=e.getHostingPageUrl().getQueryParam("intellimize_no_run",Qo("true")).isPresent(),r=e.getHostingPageUrl().getQueryParam("imize_editor",Qo("true")).isPresent();if(this.consentManagerApis=i,r)return void(this.isEditorMode=!0);n&&!r&&(this._shouldRun=!1);const o=e.getWindow().iOverride;if(void 0!==o){if(void 0!==o.experienceVariations){const{experienceVariations:e}=o;Object.keys(e).forEach((t=>{var i;null==(i=e[t])||i.forEach((e=>{t in this.experienceVariations||(this.experienceVariations[t]={}),this.experienceVariations[t][e]=!0}))}))}this._allPagesLive=o.allPagesLive,this._allAudiencesLive=o.allAudiencesLive,this._allEventsLive=o.allEventsLive,this._passExperienceAudience=o.passExperienceAudience,this._passVariationAudience=o.passVariationAudience,this._isControl=o.isControl,this._saveCustomerStorage=o.saveCustomerStorage,this._sendBrowserEvents=o.sendBrowserEvents,this._sendGoogleAnalytics=o.sendGoogleAnalytics,this._sendSegmentAnalytics=o.sendSegmentAnalytics,this._sendMixpanel=o.sendMixpanel,this._showStatusModule=o.showStatusModule,this._expires=o.expires,this._storageAndOptimizationRestricted=o.storageAndOptimizationRestricted,this.consentManagerApis=i}e.readLocalStorage("intellimize_override").ifPresent((e=>{let t={};try{t=Ue(e)}catch(i){const e=new Ei("Could not parse override",i);Mo.error(e,192)}void 0!==t.raf&&(this._requestAnimationFrame=t.raf)})),e.getHostingPageUrl().getQueryParam("iev",Qo("[0-9;:]+")).ifPresent((e=>{this.experienceVariations={},e.split(";").forEach((t=>{const i=t.split(":");if(2===i.length){const e=i[0],t=i[1];e in this.experienceVariations||(this.experienceVariations[e]={}),this.experienceVariations[e][t]=!0}else Mo.error(`Override could not parse iev ${e}`,34)})),void 0!==o&&0!==Object.keys(o).length||(this._passExperienceAudience=!0,this._passVariationAudience=!0,this._saveCustomerStorage=!1,this._sendBrowserEvents=!1),this._isControl=!1,this._sendGoogleAnalytics=!1,this._sendSegmentAnalytics=!1,this._sendMixpanel=!1,this._showStatusModule=!0})),e.getHostingPageUrl().getQueryParam("showBasesite",Qo("1")).ifPresent((()=>{this._showBasesite=!0,this._showStatusModule=!0})),e.getHostingPageUrl().getQueryParam("iea",Ho).ifPresent((e=>{"1"===e&&(this._passExperienceAudience=void 0)})),e.getHostingPageUrl().getQueryParam("iva",Ho).ifPresent((e=>{"1"===e&&(this._passVariationAudience=void 0)})),e.getHostingPageUrl().getQueryParam("is",Ho).ifPresent((e=>{"1"===e?this._showStatusModule=!0:"0"===e&&(this._showStatusModule=!1)}));let a=!1;[["ictxt_city","city","([A-Z0-9]){1,40}"],["ictxt_region","state","([a-zA-Z0-9]){1,40}"],["ictxt_country","country","[A-Z]{2}"],["ictxt.city","city","([A-Z0-9]){1,40}"],["ictxt.region","state","([a-zA-Z0-9]){1,40}"],["ictxt.country","country","[A-Z]{2}"]].forEach((t=>{e.getHostingPageUrl().getQueryParam(t[0],Qo(t[2])).ifPresent((e=>{this.attributeData.location[t[1]]=e,a=!0}))}));[["ictxt_d","deviceType","[DPT]"]].forEach((t=>{e.getHostingPageUrl().getQueryParam(t[0],Qo(t[2])).ifPresent((e=>{this.attributeData.userAgent[t[1]]=e,a=!0}))})),e.getHostingPageUrl().getQueryParam("ictxt_ts",Qo("[A-Z]{2}")).ifPresent((e=>{this.attributeData.trafficSource=e,a=!0})),e.getHostingPageUrl().getQueryParam("raf",Ko).ifPresent((e=>{"true"===e?this._requestAnimationFrame=!0:"false"===e&&(this._requestAnimationFrame=!1)})),a&&(this._isControl=!1,this._saveCustomerStorage=!1,this._sendBrowserEvents=!1,this._sendGoogleAnalytics=!1,this._sendSegmentAnalytics=!1,this._sendMixpanel=!1)}getExpirationMs(){return j.ofNullable(this._expires)}setExpiration(e){this._expires=e}getOverrideJson(){const e={allPagesLive:this._allPagesLive,allAudiencesLive:this._allAudiencesLive,allEventsLive:this._allEventsLive,isControl:this._isControl,passExperienceAudience:this._passExperienceAudience,passVariationAudience:this._passVariationAudience,saveCustomerStorage:this._saveCustomerStorage,sendBrowserEvents:this._sendBrowserEvents,sendGoogleAnalytics:this._sendGoogleAnalytics,sendMixpanel:this._sendMixpanel,showStatusModule:this._showStatusModule},t={};return Object.keys(this.experienceVariations).forEach((e=>{t[e]=Object.keys(this.experienceVariations[e])})),t&&Object.keys(t).length>0&&(e.experienceVariations=t),Object.keys(e).forEach((t=>{void 0===e[t]&&delete e[t]})),e}isPageLive(e){return j.ofNullable(this._allPagesLive)}isAudienceLive(e){return j.ofNullable(this._allAudiencesLive)}isEventLive(e){return j.ofNullable(this._allEventsLive)}isExperienceEnabled(e){return this.hasExperienceVariationsSet()?j.of(this.isExperienceSet(e)):j.empty()}passExperienceCondition(){return j.ofNullable(this._passExperienceAudience)}isExperienceIncluded(e){return this.hasExperienceVariationsSet()?j.of(this.isExperienceSet(e)):j.empty()}hasExperienceIds(){if(this.hasExperienceVariationsSet()){const e=Object.keys(this.experienceVariations);return Mo.debug("hasExperienceIds => "+e+" (override)"),j.of(e)}return j.empty()}hasVariationIds(e){const t=e.getId();if(this.hasExperienceVariationsSet()&&t in this.experienceVariations){const e=Object.keys(this.experienceVariations[t]);return Mo.debug("hasVariationIds => "+e+" (override)"),j.of(e)}return j.empty()}isVariationEnabled(e){return this.hasExperienceVariationsSet()?j.of(this.isVariationSet(e)):j.empty()}passVariationCondition(){return j.ofNullable(this._passVariationAudience)}isControl(){return j.ofNullable(this._isControl)}saveCustomerStorage(){const e=j.ofNullable(this._saveCustomerStorage);return e.ifPresent((e=>{Mo.debug(`save CustomerStorage => ${e} (override)`)})),e}sendGoogleAnalytics(){const e=j.ofNullable(this._sendGoogleAnalytics);return e.ifPresent((e=>{Mo.debug(`send GoogleAnalytics => ${e} (override)`)})),e}sendSegmentAnalytics(){const e=j.ofNullable(this._sendSegmentAnalytics);return e.ifPresent((e=>{Mo.debug(`send SegmentAnalytics => ${e} (override)`)})),e}sendMixpanel(){const e=j.ofNullable(this._sendMixpanel);return e.ifPresent((e=>{Mo.debug(`send Mixpanel => ${e} (override)`)})),e}getAttributeDataOverrides(){return __spreadValues({},this.attributeData)}showStatusModule(){return j.ofNullable(this._showStatusModule)}isExperienceSet(e){return!0!==this._showBasesite&&e.getId()in this.experienceVariations}isVariationSet(e){var t;const i=e.getExperience().getId(),s=e.getId();return Boolean(null==(t=this.experienceVariations[i])?void 0:t[s])}showBasesite(){return j.ofNullable(this._showBasesite)}requestAnimationFrame(){return j.ofNullable(this._requestAnimationFrame)}hasExperienceVariationsSet(){return!0===this._showBasesite||Object.keys(this.experienceVariations).length>0}shouldRun(){return j.ofNullable(this._shouldRun)}isStorageAndOptimizationRestricted(){var e;return null!=(e=this._storageAndOptimizationRestricted)?e:!this.consentManagerApis.shouldTrack()}shouldHide(){return!0===this.isEditorMode||!1===this._originAllowed||this.isStorageAndOptimizationRestricted()?j.of(!1):j.empty()}useStorageType(){return!0===this.isEditorMode||!1===this._originAllowed||this.hasExperienceVariationsSet()?j.of("SessionStorage"):j.empty()}useElementObserver(){return!0===this.isEditorMode||!1===this._originAllowed?j.of(!1):j.empty()}injectVisualEditor(){return!0===this.isEditorMode?j.of(!0):j.empty()}shouldRunTasks(){let e;return(!0===this.isEditorMode||!1===this._originAllowed||this.isStorageAndOptimizationRestricted())&&(e=!1),void 0!==e&&Mo.debug(`send BrowserEvent => ${e} (override)`),j.ofNullable(e)}sendNonPageviewBrowserEvents(){let e;return(!1===this._sendBrowserEvents||!1===this._originAllowed||!0===this.isEditorMode||this.isStorageAndOptimizationRestricted())&&(e=!1),void 0!==e&&Mo.debug(`send BrowserEvent => ${e} (override)`),j.ofNullable(e)}sendPageviewBrowserEvents(){let e;return(!1===this._sendBrowserEvents||!0===this.isEditorMode||this.isStorageAndOptimizationRestricted())&&(e=!1),void 0!==e&&Mo.debug(`send BrowserEvent => ${e} (override)`),j.ofNullable(e)}shouldCheckOptOut(){return!0===this.isEditorMode||!1===this._originAllowed?j.of(!1):j.empty()}saveActivity(){let e;return(!1===this._sendBrowserEvents||!1===this._originAllowed||!0===this.isEditorMode||this.isStorageAndOptimizationRestricted())&&(e=!1),void 0!==e&&Mo.debug(`save activity => ${e} (override)`),j.ofNullable(e)}}class Va{constructor(e,t,i,s,n,r,o,a,l,c,d){__publicField(this,"environment"),__publicField(this,"browserStorage"),__publicField(this,"extensionManager"),__publicField(this,"customer"),__publicField(this,"conditionEvaluationRuntime"),__publicField(this,"attributeData"),__publicField(this,"override"),__publicField(this,"taskManager"),__publicField(this,"elementObserver"),__publicField(this,"hider"),__publicField(this,"statusModel"),__publicField(this,"pages",[]),__publicField(this,"audiences",[]),__publicField(this,"pageviewId"),__publicField(this,"pageChangelistTasks",{}),this.environment=e,this.browserStorage=t,this.extensionManager=i,this.customer=s,this.conditionEvaluationRuntime=n,this.attributeData=r,this.override=o,this.taskManager=a,this.hider=l,this.elementObserver=c,this.statusModel=d}reinitialize(){const e=this.environment.getPageviewId();this.pageviewId===e&&Mo.error("PageContext.reinitialize() called more than once per pageview",244),this.pages=this.determineMatchingPages(),this.hider.hidePages(this.pages,this.browserStorage,this.attributeData),this.hider.revealDocumentIfAllRegionsHidden(),this.pageChangelistTasks=this.schedulePageChangeListTasks(),this.audiences=this.determineMatchingAudiences(),this.pageviewId=e}getPages(){return this.pageviewId!==this.environment.getPageviewId()&&Mo.error("PageContext.getPages() called before initialization",151),this.pages}getPageChangelistTask(e){return this.pageviewId!==this.environment.getPageviewId()&&Mo.error("PageContext.getPageChangeListTask() called before initialization",243,{pageId:e.getId()}),j.ofNullable(this.pageChangelistTasks[e.getId()])}getAudiences(){return this.pageviewId!==this.environment.getPageviewId()&&Mo.error("PageContext.getAudiences() called before initialization",152),this.audiences}determineMatchingPages(){const e=this.customer.getPages().filter((e=>this.override.isPageLive(e).orElse("live"===e.getState()))),t=this.environment.getHostingPageUrl().getRawUrl(),i=e.filter((e=>e.matches(t,Dt.evalBoolean))),s=zs.filterPages(i);return s.length>0?s:i}schedulePageChangeListTasks(){const e={};return this.override.shouldRunTasks().orElse(!0)&&this.pages.forEach((t=>{const i=t.getChanges().map(((e,i)=>Xn(e,$n(t),i,this.attributeData,this.environment,this.browserStorage,this.extensionManager)));if(i.length>0){const s=new Zn(i,(()=>{this.hider.revealPage(t,this.browserStorage,this.attributeData)}),{pageId:t.getId()},this.elementObserver);e[t.getId()]=s,this.taskManager.addTask(s),this.statusModel.setPage(t,"status",s.getStatus()),s.onDone((()=>{this.statusModel.setPage(t,"status",s.getStatus())}))}else this.hider.revealPage(t,this.browserStorage,this.attributeData)})),e}determineMatchingAudiences(){return this.customer.getAudiences().filter((e=>this.override.isAudienceLive(e).orElse("live"===e.getState()))).filter((e=>{let t=!1;try{t=e.getCondition().evaluate(this.conditionEvaluationRuntime)}catch(i){t=!1;const s=new Ei("Audience condition evaluation failed ",i);Mo.error(s,205,{audienceId:e.getId()})}return Mo.debug(`check audience: ${e.getName()} (${e.getId()}) => ${t}`),t}))}}class Wa{update(){}}class za{constructor(e,t,i,s){__publicField(this,"environment"),__publicField(this,"localStorage"),__publicField(this,"customer"),__publicField(this,"override"),__publicField(this,"overrideModule"),__publicField(this,"eventLogStorageKey"),__publicField(this,"expVarModel",{campaigns:{}}),__publicField(this,"pageModel",{}),__publicField(this,"eventModel",{count:0,campaigns:{}}),this.customer=t,this.environment=e,this.localStorage=s,this.override=i,this.eventLogStorageKey=`${we}event_log_${t.getId()}`,this.overrideModule=new Wa(this.environment,i,this.customer,this.reinitializeGoalEvents.bind(this)),this.initializeGoalEvents()}reinitializeExperienceVariations(){this.expVarModel={campaigns:{}},this.overrideModule.update({experienceVariations:this.expVarModel})}reinitializeGoalEvents(){this.eventModel={count:0,campaigns:{}},this.writeEventModelToLocalStorage(),this.initializeGoalEvents(),this.overrideModule.update({goalEvents:this.eventModel})}initializeGoalEvents(){this.eventModel=this.readEventModelFromLocalStorage();const e=this.environment.getHostingPageUrl().getRawUrl();this.customer.getEvents().forEach((t=>{let i="custom"===t.getType();(t instanceof Si||t instanceof Fi)&&(i=t.getPages().some((t=>t.matches(e,(()=>!0))))),i&&t.getMetrics().forEach((e=>{e.getCampaign().ifPresent((t=>{this.getCampaignEventData(t).metrics[e.getId()]=this.getMetricEventData(e)}))}))})),this.overrideModule.update({goalEvents:this.eventModel})}setCustomer(e,t){this.expVarModel[e]=t,this.overrideModule.update({experienceVariations:this.expVarModel})}setCampaign(e,t,i){const s=this.getCampaignData(e),n=s[t];s[t]=i,n!==i&&this.overrideModule.update({experienceVariations:this.expVarModel})}setExperience(e,t,i){const s=this.getExperienceData(e),n=s[t];s[t]=i,n!==i&&!0===(null==s?void 0:s.enabled)&&this.overrideModule.update({experienceVariations:this.expVarModel})}setVariation(e,t,i){const s=this.getVariationData(e),n=s[t];s[t]=i,n!==i&&this.overrideModule.update({experienceVariations:this.expVarModel})}setPage(e,t,i){this.getPageData(e)[t]=i,this.overrideModule.update({pagesChanges:this.pageModel})}setExperienceSortedVariations(e,t){this.getExperienceData(e).variationsSorted=t,this.overrideModule.update({experienceVariations:this.expVarModel})}setMetricEventData(e){this.eventModel=this.readEventModelFromLocalStorage(),e.forEach((({metric:e,event:t,status:i,id:s})=>{if(!e.getCampaign().isPresent())return;const n=this.getCampaignEventData(e.getCampaign().get()),r=this.getMetricEventData(e);void 0===r.events[s]?(r.events[s]={name:t.getName(),type:t.getType(),status:i,instanceId:s},this.eventModel.count+=1,n.count+=1,r.count+=1):(r.events[s].status=i,r.events[s].timestamp=i===Nt.SUCCEEDED?(new Date).toISOString():void 0,r.count=Object.keys(r.events).length)})),this.overrideModule.update({goalEvents:this.eventModel}),this.writeEventModelToLocalStorage()}getCampaignData(e){return void 0===this.expVarModel.campaigns[e.getId()]&&(this.expVarModel.campaigns[e.getId()]={campaign:e,experiences:{}}),this.expVarModel.campaigns[e.getId()]}getExperienceData(e){const t=this.getCampaignData(e.getCampaign());return void 0===t.experiences&&(t.experiences={}),void 0===t.experiences[e.getId()]&&(t.experiences[e.getId()]={experience:e,variations:{},variationsSorted:[]}),t.experiences[e.getId()]}getVariationData(e){const t=this.getExperienceData(e.getExperience());return void 0===t.variations&&(t.variations={}),void 0===t.variations[e.getId()]&&(t.variations[e.getId()]={variation:e}),t.variations[e.getId()]}getPageData(e){var t;const i=null!=(t=this.pageModel[e.getId()])?t:{page:e,status:Nt.NOT_STARTED};return this.pageModel[e.getId()]=i,i}getMetricEventData(e){if(!e.getCampaign().isPresent())throw new $("Metric not in campaign");const t=this.getCampaignEventData(e.getCampaign().get());return void 0===t.metrics[e.getId()]&&(t.metrics[e.getId()]=this.getInitialMetricEventData(e)),t.metrics[e.getId()]}getCampaignEventData(e){return void 0===this.eventModel.campaigns[e.getId()]&&(this.eventModel.campaigns[e.getId()]={name:e.getName(),count:0,metrics:{}}),this.eventModel.campaigns[e.getId()]}getMetricMeasurement(e){const t=e.getCountingMethod(),i=e.getScope();let s,n;return e instanceof Ys?(n=`$${(e.getValue()/100).toFixed(2)}`,s=`${t} static value per ${i}`):s=e instanceof qs?`${t} dynamic value per ${i}`:`${t} conversion per ${i}`,{measurement:s,value:n}}getInitialMetricEventData(e){return __spreadValues({name:e.getName(),isGoal:e.isGoal(),events:{},count:0},this.getMetricMeasurement(e))}shouldUseLocalStorage(){let e=!1;return this.override.getExpirationMs().ifPresent((t=>{e=t>Date.now()})),e}deleteEventModelFromLocalStorage(){this.localStorage.delete(this.eventLogStorageKey)}writeEventModelToLocalStorage(){this.shouldUseLocalStorage()&&this.localStorage.write(this.eventLogStorageKey,Re(this.eventModel))}readEventModelFromLocalStorage(){let e=this.eventModel;return this.shouldUseLocalStorage()?this.localStorage.read(this.eventLogStorageKey).ifPresent((t=>{try{e=Ue(t)}catch(i){}})):this.deleteEventModelFromLocalStorage(),e}}class ja{reinitializeExperienceVariations(){}reinitializeGoalEvents(){}setCustomer(e,t){}setCampaign(e,t,i){}setExperience(e,t,i){}setVariation(e,t,i){}setPage(e,t,i){}setExperienceSortedVariations(e,t){}setMetricEventData(e){}}class Ba{constructor(e,t,i,s){__publicField(this,"storageKey"),__publicField(this,"environment"),__publicField(this,"browserStorage"),__publicField(this,"user"),__publicField(this,"pageviewAttributes",{id:void 0,custom:{},internal:{}}),this.storageKey=re+e.getId(),this.browserStorage=i,this.environment=t,this.user=s}setCustomAttributes(e,t){const i=this.getAttributeData(e);Object.keys(t).forEach((e=>{i.custom[e]=t[e]})),this.saveAttributeData(e,i)}setInternalAttributes(e,t){const i=this.getAttributeData(e);Object.keys(t).forEach((e=>{i.internal[e]=t[e]})),this.saveAttributeData(e,i)}getCustomAttributes(e,t){const i=this.getAttributeData(e),s={};return Object.keys(i.custom).forEach((e=>{(void 0===t||t.includes(e))&&(s[e]=i.custom[e])})),s}getInternalAttributes(e,t){const i=this.getAttributeData(e),s={};return Object.keys(i.internal).forEach((e=>{(void 0===t||t.includes(e))&&(s[e]="object"==typeof i.internal[e]?Ue(Re(i.internal[e])):i.internal[e])})),s}getFlattenedCustomAttributes(){const e=this.getCustomAttributes("user"),t=this.getCustomAttributes("pageview");return Object.keys(t).forEach((i=>{e[i]=t[i]})),e}getFlattenedInternalAttributes(){const e=this.getInternalAttributes("user"),t=this.getInternalAttributes("pageview");return Object.keys(t).forEach((i=>{e[i]=t[i]})),e}deleteCustomAttributes(e,t){const i=this.getAttributeData(e);for(const s in i.custom)Object.prototype.hasOwnProperty.call(i.custom,s)&&(void 0===t||t.includes(s))&&delete i.custom[s];this.saveAttributeData(e,i)}deleteInternalAttributes(e,t){const i=this.getAttributeData(e);void 0===t?i.internal={}:t.forEach((e=>{delete i.internal[e]})),this.saveAttributeData(e,i)}getAttributeData(e){let t,i={id:void 0,custom:{},internal:{}};return"pageview"===e?(i=this.pageviewAttributes,t=this.environment.getPageviewId()):"user"===e?(i=this.readBrowserStorage(),t=this.user.getId()):Mo.error(`Tried to get attribute data for invalid scope ${e}`,49),t!==i.id&&(Mo.debug(`Mismatching IDs ("${t}" != "${i.id}"). Resetting AttributeData`),i.id=t,i.internal={},i.custom={}),i}saveAttributeData(e,t){"pageview"===e||("user"===e?this.saveBrowserStorage(t):Mo.error(`Tried to save attribute data for invalid scope ${e}`,50))}readBrowserStorage(){let e={id:void 0,custom:{},internal:{}};return this.browserStorage.read(this.storageKey).ifPresent((t=>{try{e=Ue(t)}catch(i){const e=new Ei("Could not parse attribute storage",i);Mo.error(e,51)}})),e}saveBrowserStorage(e){const t=Re(e);this.browserStorage.write(this.storageKey,t)}}class Ga{constructor(e,t,i,s){__publicField(this,"storageKey"),__publicField(this,"browserStorage"),__publicField(this,"environment"),__publicField(this,"override"),this.storageKey=ae+e.getId(),this.browserStorage=i,this.environment=t,this.override=s}static get VERSION(){return 2}static getPath(e,t){if(0===t.length)return e;const i=t[0];return i in e||(e[i]={}),t.splice(0,1),Ga.getPath(e[i],t)}getCustomerControlStatus(e){if(!e.isCampaignControl()){const e=this.readState();if(!0===e.c)return j.of("c");if(!1===e.c)return j.of("i");this.override.saveCustomerStorage().orElse(!0)&&Mo.warn("Attempting to retrieve control value, but not set",52)}return j.empty()}updateAndGetControl(e){Mo.debug("updateAndGetControl(campaign)");const t=e.getCustomer();return t.isCampaignControl()?this.setAndGetCampaignControl(e):this.setAndGetCustomerControl(t)}setAndGetCustomerControl(e){L(!e.isCampaignControl());const t=this.readState(),i=t,s=e.getControlTrafficPercentage().get();return void 0!==t.cpg&&Object.keys(t.cpg).forEach((e=>{void 0!==t.cpg&&(delete t.cpg[e].c,delete t.cpg[e].ct)})),this.setAndGetControl(e,i,t,s)}updateAndGetExperienceInclusion(e){const t=this.environment.getInitializeUnixTime(),i=this.readState(),s=e.getId(),n=e.getCampaign().getId(),r=Ga.getPath(i,["cpg",n,"exp",s]);if(e.getInclusionStickyConfig().isSticky()&&void 0!==r.i&&void 0!==r.it){const i=r.it;if(!e.getInclusionStickyConfig().isExpired(t,i)){const t=r.i;return Mo.info(`Experience ${e.getId()} sticky inclusion decision => ${t}`),t}}const o=Math.random()<1-e.getIgnore()/1e4;return r.i=o,r.it=t,this.writeState(i),Mo.info(`Experience ${e.getId()} new inclusion decision => ${o}`),o}getStickyVariation(e){Mo.group("getStickyVariation(experience:"+e.getId()+")");const t=this.environment.getInitializeUnixTime(),i=this.readState(),s=e.getId(),n=e.getCampaign().getId(),r=Ga.getPath(i,["cpg",n,"exp",s]);if(e.getVariationStickyConfig().isSticky()&&void 0!==r.v&&void 0!==r.vt){const i=r.vt;if(!e.getVariationStickyConfig().isExpired(t,i))return Mo.debug("found sticky, non-expired variation: "+r.v),Mo.groupEnd(),e.getVariation(r.v)}return Mo.groupEnd(),j.empty()}getVariation(e){Mo.group("getVariation(experience:"+e.getId()+")");const t=this.readState(),i=e.getId(),s=e.getCampaign().getId(),n=Ga.getPath(t,["cpg",s,"exp",i]);return void 0!==n.v?(Mo.groupEnd(),e.getVariation(n.v)):(Mo.groupEnd(),j.empty())}saveVariationStickySelection(e,t){const i=e.getExperience().getId(),s=e.getExperience().getCampaign().getId(),n=this.readState(),r=Ga.getPath(n,["s"]),o=Ga.getPath(n,["cpg",s,"exp",i]);e.getId()!==o.v&&Mo.error(`Updating variation page view id in local storage, but variation changed from ${o.v} to ${e.getId()}`,53,{campaignId:s,experienceId:i,variationId:e.getId()}),o.v=e.getId(),o.gpvid=t,o.gsid=r.id,this.writeState(n)}saveVariationPredictionSelection(e,t,i){const s=e.getExperience().getId(),n=e.getExperience().getCampaign().getId(),r=this.readState(),o=Ga.getPath(r,["s"]),a=Ga.getPath(r,["cpg",n,"exp",s]);a.v=e.getId(),a.vt=this.environment.getNowUnixTime(),a.mv=t,a.gpvid=i,a.gsid=o.id,this.writeState(r)}deleteExperiencePredictionSelection(e){const t=e.getId(),i=e.getCampaign().getId(),s=this.readState(),n=Ga.getPath(s,["cpg",i,"exp",t]);0!==Object.keys(n).length&&(delete n.v,delete n.vt,delete n.mv,delete n.gpvid,delete n.gsid,this.writeState(s))}updateAndGetIsCampaignFirstTimeView(e,t){return this.updateAndGetIsFirstTime([e.getId(),"v"],t)}updateAndGetIsCampaignFirstTimeGoal(e,t,i){return this.updateAndGetIsFirstTime([e.getId(),t.getId(),"g"],i)}updateAndGetIsVariationFirstTimeView(e,t){return this.updateAndGetIsFirstTime([e.getExperience().getCampaign().getId(),e.getExperience().getId(),e.getId(),"v"],t)}updateAndGetIsVariationFirstTimeGoal(e,t,i){return this.updateAndGetIsFirstTime([e.getExperience().getCampaign().getId(),t.getId(),e.getExperience().getId(),e.getId(),"g"],i)}getVariationPredictionModelVersion(e){const t=this.readState(),i=Ga.getPath(t,["cpg",e.getExperience().getCampaign().getId(),"exp",e.getExperience().getId()]);return j.ofNullable(i.mv)}getVariationPredictionPageviewId(e){const t=this.readState(),i=Ga.getPath(t,["cpg",e.getExperience().getCampaign().getId(),"exp",e.getExperience().getId()]);return j.ofNullable(i.gpvid)}getVariationPredictionSessionId(e){const t=this.readState(),i=Ga.getPath(t,["cpg",e.getExperience().getCampaign().getId(),"exp",e.getExperience().getId()]);return j.ofNullable(i.gsid)}getPageviewSelections(e){const t=[],i=this.readState();return void 0!==i.cpg&&Object.keys(i.cpg).forEach((s=>{if(void 0!==i.cpg){const n=i.cpg[s];"exp"in n&&Object.keys(n.exp).forEach((i=>{const r=n.exp[i];void 0!==r.v&&"gpvid"in r&&r.gpvid===e&&t.push({campaignId:s,experienceId:i,variationId:r.v})}))}})),t}getSessionId(){const e=this.readState(),t=Ga.getPath(e,["s"]);return j.ofNullable(t.id)}updateAndGetSessionId(){const e=this.environment.getNowUnixTime(),t=this.readState(),i=Ga.getPath(t,["s"]);return void 0===i.st&&(i.st=e),(void 0===i.id||void 0===i.t||e-i.t>Y||e-i.st>J)&&(Mo.debug("Starting a new session"),i.id=this.generateSessionId(),i.st=e),Mo.debug("Extending current session"),i.t=e,this.writeState(t),i.id}getUserVisitStatus(e){return this.isFirstSession(e)?"N":"R"}isFirstSession(e){const t=e.getFirstVisitTimeSec();this.updateAndGetSessionId();const i=this.readState(),s=Ga.getPath(i,["s"]).st;return Mo.info(`user.getId(): ${e.getId()}`),Mo.info(`userCreationTimeSec: ${t}`),Mo.info(`currentSessionStartTimeSec: ${s}`),Math.abs(t-s)<Y/2}setAndGetCampaignControl(e){const t=e.getCustomer();L(t.isCampaignControl());const i=this.readState(),s=Ga.getPath(i,["cpg",e.getId()]),n=e.getControlTrafficPercentage().get();return delete i.c,delete i.ct,this.setAndGetControl(t,s,i,n)}setAndGetControl(e,t,i,s){const n=this.environment.getInitializeUnixTime();if(e.getControlStickyConfig().isSticky()||Mo.error("Control is not sticky, but should be",54),e.getControlStickyConfig().isSticky()&&void 0!==t.c&&void 0!==t.ct){const i=t.ct;if(!e.getControlStickyConfig().isExpired(n,i))return Mo.debug("control: "+t.c+" (sticky decision)"),t.c;Mo.error(`Control sticky decision expired. now:${n}, last:${i}`,55)}void 0!==t.c&&Mo.error(`Making new control decision, but c is present: ${t.c}`,56),void 0!==t.ct&&Mo.error(`Making new control decision, but ct is present: ${t.ct}`,57);const r=Math.random()<s;return t.c=r,t.ct=n,this.writeState(i),Mo.debug("control: "+t.c),r}generateSessionId(){return this.environment.getNowUnixTime()+"-"+jt()}updateAndGetIsFirstTime(e,t){let i=!1;const s=this.readState(),n=Ga.getPath(s,["uu"]),r=e.join(":");return r in n?n[r]===t&&(i=!0):(n[r]=t,this.writeState(s),i=!0),i}readState(){let e={};return this.browserStorage.read(this.storageKey).ifPresent((t=>{try{e=Ue(t)}catch(i){const e=new Ei("Could not parse customer storage",i);Mo.error(e,58)}})),U(!Object.prototype.hasOwnProperty.call(e,"vr")||e.vr===Ga.VERSION),e}writeState(e){e.vr=Ga.VERSION;const t=Re(e);this.override.saveCustomerStorage().orElse(!0)&&this.browserStorage.write(this.storageKey,t)}}class Ha{constructor(e,t){__publicField(this,"customer"),__publicField(this,"storageKey"),__publicField(this,"browserStorage"),this.customer=e,this.storageKey=`${oe}${e.getId()}`,this.browserStorage=t,this.initialize()}setDataAndMetadata(e,t,i){const s=this.getAllDataAndMetadata();s.data[e]=t,s.meta[e]=i,this.setAll(s)}getData(e){const t=this.getAllData()[e];return void 0===t||0===Object.keys(t).length?j.empty():j.ofNullable(t)}getAllData(){return this.getAllDataAndMetadata().data}getMetadata(e){const t=this.getAllMetdata()[e];return void 0===t||0===Object.keys(t).length?j.empty():j.ofNullable(t)}getAllMetdata(){return this.getAllDataAndMetadata().meta}setAllDataAndMetadata(e,t){const i={data:e,meta:t};this.setAll(i)}deleteAll(){this.browserStorage.delete(this.storageKey)}getAllDataAndMetadata(){const e=this.browserStorage.read(this.storageKey);let t=this.emptyStorage();if(e.ifPresent((e=>{try{t=Ue(e)}catch(i){const e=new Ei("Could not parse integration data storage",i);Mo.error(e,59),this.deleteAll()}})),null==t||0===Object.keys(t).length)t=this.emptyStorage();else{const e=t;hn(e)&&void 0===e.__marketoLead||(t=this.emptyStorage())}return void 0===t.meta&&(t.meta={}),mr(t.data.marketo)&&(t.data.marketo={}),t}setAll(e){const t=this.emptyStorage();nr.forEach((i=>{wr[i](this.customer)&&(t.data[i]=e.data[i],t.meta[i]=e.meta[i])})),this.browserStorage.write(this.storageKey,Re(t))}emptyStorage(){return{data:{},meta:{}}}initialize(){const e=this.getAllDataAndMetadata();nr.forEach((t=>{wr[t](this.customer)||(delete e.data[t],delete e.meta[t])})),this.setAll(e)}}class Qa{constructor(e){if(__publicField(this,"environment"),!Mt(e,"localStorage"))throw new Error("LocalStorage is not available");this.environment=e}write(e,t){kt(e)||Mo.error(`Writing an unknown key to LocalStorage: ${e}`,180),this.environment.writeLocalStorage(e,t)}read(e){return this.environment.readLocalStorage(e)}delete(e){this.environment.deleteLocalStorage(e)}}const Ka={requestId:"00000000-0000-1000-a000-000000000000",clientIp:"0.0.0.0",userAgentDigest:"00000000000000000000000000000000",userAgent:{},location:{}};class qa{constructor(e,t,i){__publicField(this,"storageKey"),__publicField(this,"browserStorage"),__publicField(this,"override"),this.storageKey=ce+e.getId(),this.browserStorage=t,this.override=i}static fromJson(e){try{const t=Ue(e);return j.of(t)}catch(t){const e=new Ei("Could not build server context from localStorage",t);Mo.error(e,157)}return j.empty()}set(e){this.browserStorage.write(this.storageKey,Re(e))}get(){return this.override.isStorageAndOptimizationRestricted()?j.ofNullable(Ka):this.browserStorage.read(this.storageKey).flatMap((e=>qa.fromJson(e)))}clear(){this.browserStorage.delete(this.storageKey)}}class Ya{constructor(){__publicField(this,"storage"),__publicField(this,"restricted",!0),this.storage=new Yt}isRestricted(){return this.restricted}write(e,t){this.storage.write(e,t)}read(e){return this.storage.read(e)}delete(e){this.storage.delete(e)}unrestrictProxyAndReplaceBrowserStorage(e){this.restricted?(this.storage=e,this.restricted=!1):Mo.warn("StorageRestrictionProxy: Storage already restricted, should only be called once",275)}}var Ja={},Xa={get exports(){return Ja},set exports(e){Ja=e}},Za={};!function(e){function t(e,t){var i,s,n,r,o,a,l,c;for(i=3&e.length,s=e.length-i,n=t,o=3432918353,a=461845907,c=0;c<s;)l=255&e.charCodeAt(c)|(255&e.charCodeAt(++c))<<8|(255&e.charCodeAt(++c))<<16|(255&e.charCodeAt(++c))<<24,++c,n=27492+(65535&(r=5*(65535&(n=(n^=l=(65535&(l=(l=(65535&l)*o+(((l>>>16)*o&65535)<<16)&4294967295)<<15|l>>>17))*a+(((l>>>16)*a&65535)<<16)&4294967295)<<13|n>>>19))+((5*(n>>>16)&65535)<<16)&4294967295))+((58964+(r>>>16)&65535)<<16);switch(l=0,i){case 3:l^=(255&e.charCodeAt(c+2))<<16;case 2:l^=(255&e.charCodeAt(c+1))<<8;case 1:n^=l=(65535&(l=(l=(65535&(l^=255&e.charCodeAt(c)))*o+(((l>>>16)*o&65535)<<16)&4294967295)<<15|l>>>17))*a+(((l>>>16)*a&65535)<<16)&4294967295}return n^=e.length,n=2246822507*(65535&(n^=n>>>16))+((2246822507*(n>>>16)&65535)<<16)&4294967295,n=3266489909*(65535&(n^=n>>>13))+((3266489909*(n>>>16)&65535)<<16)&4294967295,(n^=n>>>16)>>>0}e.exports=t}({get exports(){return Za},set exports(e){Za=e}});var el={};!function(e){function t(e,t){for(var i,s=e.length,n=t^s,r=0;s>=4;)i=1540483477*(65535&(i=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+((1540483477*(i>>>16)&65535)<<16),n=1540483477*(65535&n)+((1540483477*(n>>>16)&65535)<<16)^(i=1540483477*(65535&(i^=i>>>24))+((1540483477*(i>>>16)&65535)<<16)),s-=4,++r;switch(s){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+((1540483477*(n>>>16)&65535)<<16)}return n=1540483477*(65535&(n^=n>>>13))+((1540483477*(n>>>16)&65535)<<16),(n^=n>>>15)>>>0}e.exports=t}({get exports(){return el},set exports(e){el=e}});var tl=Za,il=el;Xa.exports=tl;var sl=Ja.murmur3=tl;function nl(e,t){return sl(e,t)}Ja.murmur2=il;const rl=__pow(2,32);function ol(e,t){const i=(e>>>0)/rl;return Math.floor(i*t)}class al{constructor(e,t,i){__publicField(this,"browserStorage"),__publicField(this,"environment"),__publicField(this,"storageKey"),__publicField(this,"id"),__publicField(this,"reinitializeUser",(()=>{this.id=this.getInitialUserId()})),this.browserStorage=e,this.environment=t,this.storageKey=`${ue}${i.getId()}`,this.id=this.getInitialUserId()}getId(){if(void 0===this.id)throw new R("user id not defined");const e=this.browserStorage.read(this.storageKey);return e.isPresent()?e.get()!==this.id&&Mo.error(`User id in memory (${this.id}) not the same as user id in localStorage (${e.get()})`,62):Mo.error("User id is missing from localStorage",61),this.id}isNewVisitor(){const e=this.getFirstVisitTimeSec();return this.environment.getNowUnixTime()-e<X}isReturningVisitor(){return!this.isNewVisitor()}getFirstVisitTimeSec(){const e=this.getId().split(".",2);return Number(e[1])}getUserBucket(){const e=100,t=ol(nl(this.getId(),1),e);return Math.floor(t+1)}generateId(){const e=this.environment.getNowUnixTime();return`${Bt()}.${e}`}getInitialUserId(){let e;return this.browserStorage.read(this.storageKey).ifPresent((t=>{e=t,Mo.debug(`User: Found existing User ID: ${e}`)})),void 0===e&&(e=this.generateId(),Mo.debug(`User: No existing User ID; generated new ID: ${e}`),this.browserStorage.write(this.storageKey,e)),e}}class ll{constructor(e,t){__publicField(this,"client"),__publicField(this,"environment"),__publicField(this,"taskQueue",[]),__publicField(this,"completedTasks",[]),__publicField(this,"isRunning",!1),__publicField(this,"isStopping",!1),__publicField(this,"timeoutId"),__publicField(this,"queueChecksum"),__publicField(this,"doneCallbacks",[]),__publicField(this,"latestQueueChecksum"),__publicField(this,"latestQueueTimeMs"),this.client=e,this.environment=t}onDone(e){this.doneCallbacks.push(e)}isDone(){return 0===this.taskQueue.length}hasStarted(){return this.taskQueue.length>0||this.completedTasks.length>0}getClient(){return this.client}addTask(e){Mo.debug("TaskManager.addTask("+e.toString()+")"),this.isStopping?Mo.warn(`Rejecting addTask(${e.toString()}) request since TaskManager is stopping`,35,e.getEntityIds()):(e.setManager(this),this.handleNotDone(e),this.queueChecksum=void 0,this.runAfter(0))}runNow(){Mo.debug("TaskManager run now - synchronously"),this.eventLoop()}findTasks(e){return[...this.taskQueue,...this.completedTasks].filter((t=>e(t)))}stop(){Mo.debug("TaskManager.stop()"),this.isStopping=!0,this.runAfter(0)}eventLoop(){if(Mo.group("TaskManager.eventLoop()"),Mo.time("TaskManager.eventLoop()"),this.isRunning)return Mo.debug("TaskManager called again when already running. Back to work..."),void Mo.groupEnd();if(this.isRunning=!0,this.clearTimeout(),this.taskQueue.length>0&&void 0!==this.latestQueueChecksum&&void 0!==this.latestQueueTimeMs&&this.environment.getNowUnixTimeMs()-this.latestQueueTimeMs>NO_PROGRESS_TOLERANCE_TIME_MS&&!this.isQueueBlocked()){const e=this.extractVictim();void 0===e?Mo.error("Unable to extract latest task when trying to make progress",37):(Mo.error(`Unable to make progress. Canceling task ${e.toString()}. Queue: ${this.queueToString()}`,36,e.getEntityIds()),this.cancelTask(e))}if(this.isStopping?this.stopTasks():this.runTasks(),this.taskQueue.length>0){const e=this.computeChecksum();e!==this.latestQueueChecksum&&(this.latestQueueChecksum=e,this.latestQueueTimeMs=this.environment.getNowUnixTimeMs())}else this.latestQueueChecksum=void 0,this.latestQueueTimeMs=void 0;if(this.taskQueue.length>0)this.isQueueBlocked()?(Mo.debug("TaskManager queue is blocked on WAITING tasks. Sleeping."),this.clearTimeout(),this.latestQueueChecksum=void 0,this.latestQueueTimeMs=void 0):(Mo.debug("TaskManager schedule callback for later"),this.runAfter(q));else if(this.isStopping)Mo.debug("TaskManager schedule callback for now"),this.runAfter(0);else{for(Mo.group("TaskManager done"),this.clearTimeout(),this.latestQueueChecksum=void 0,this.latestQueueTimeMs=void 0;this.doneCallbacks.length>0;){const e=this.doneCallbacks.shift();void 0!==e&&(Mo.group("executing done callback"),Dt.executeFunction(e,155),Mo.groupEnd())}Mo.groupEnd()}this.isRunning=!1,Mo.timeEnd("TaskManager.eventLoop()"),Mo.groupEnd()}extractVictim(){let e;for(let t=0;t<this.taskQueue.length;t++)this.taskQueue[t].isBlocked()||(void 0===e||this.taskQueue[t].getId()>this.taskQueue[e].getId())&&(e=t);if(void 0!==e)return this.taskQueue.splice(e,1)[0]}stopTasks(){Mo.group("TaskManager.stopTasks()");for(const e of this.completedTasks)e.cleanup();for(;this.taskQueue.length>0;){const e=this.taskQueue.shift();void 0!==e&&(Mo.error(`Stopping and had to cancel task ${e.toString()}`,38,e.getEntityIds()),this.cancelTask(e))}for(;this.completedTasks.length>0;)this.completedTasks.shift();Mo.debug("Cleanup of all Tasks complete. taskQueue and completedTasks are now empty"),Mo.groupEnd(),this.isStopping=!1}cancelTask(e){void 0!==e&&(Mo.debug("Canceling task "+e.toString()),e.cancel(),this.handleDone(e))}handleNotDone(e){this.taskQueue.push(e)}handleDone(e){this.completedTasks.push(e),this.queueChecksum=void 0}runTasks(){Mo.group("TaskManager.runTasks()"),this.queueChecksum=this.computeChecksum();let e=!0;for(;0!==this.taskQueue.length;){const t=this.getUnloadingTasks();if(!e&&0===t.length)break;Mo.debug(`task queue: ${this.queueToString()}`);const i=this.taskQueue.shift();if(void 0===i)continue;let s=!1,n=!1;t.length>0&&(i.isUnloading()&&e?s=!0:0===i.getNotDoneDependencies().length&&(n=!0));const r=this.computeChecksum();let o;s?(Mo.debug(`Skipping unloading task ${i.toString()}. TM in unloading state and still making progress`),o=Nt.TRY_LATER):i.getStatus()===Nt.WAITING?(Mo.debug(`Skipping waiting task ${i.toString()}`),o=Nt.WAITING):(Mo.debug(`TaskManager running ${i.toString()}`),Mo.time(`Task running ${i.toString()}`),o=i.runWrapper(),Mo.timeEnd(`Task running ${i.toString()}`),Mo.debug("TaskManager "+i.toString()+" returned "+Nt[o]));const a=this.computeChecksum();if(n&&r!==a&&(n=!1),n&&!i.isDone())i.inTransaction()&&Mo.warn(`Unloading ${t[0].toString()} and transaction task is not done. Canceling ${i.toString()}`,39,i.getEntityIds()),this.cancelTask(i);else switch(o){case Nt.TRY_LATER:this.handleNotDone(i);break;case Nt.FAILED:case Nt.SUCCEEDED:this.handleDone(i);break;case Nt.CANCELED:this.handleDone(i),Mo.debug("Task self-canceled "+i.toString());break;case Nt.WAITING:this.handleNotDone(i);break;default:this.handleDone(i),Mo.error(`Invalid task status ${o} for ${i.toString()}`,40,i.getEntityIds())}const l=this.computeChecksum();if(l!==this.queueChecksum){if(this.isStopping)break;void 0===this.queueChecksum&&(this.queueChecksum=l)}else e=!1}Mo.debug("No more work to do now."),Mo.groupEnd()}getUnloadingTasks(){return this.taskQueue.filter((e=>e.isUnloading()))}clearTimeout(){void 0!==this.timeoutId&&(Mo.debug("TaskManager clearing timeoutId: "+this.timeoutId),this.environment.getWindow().clearTimeout(this.timeoutId),this.timeoutId=void 0)}runAfter(e){Mo.debug("TaskManager runAfter("+e+"ms)"),this.clearTimeout(),this.timeoutId=this.environment.getWindow().setTimeout((()=>{try{Mo.debug("TaskManager callback triggered"),this.eventLoop()}catch(e){const t=new Ei("TaskManager exception",e);Mo.error(t,41)}}),e),Mo.debug("TaskManager set new timer: "+this.timeoutId)}computeChecksum(){return this.taskQueue.map((e=>e.getId().toString())).join("-")}queueToString(){return this.taskQueue.map((e=>e.toString())).join(", ")}isQueueBlocked(){return this.taskQueue.every((e=>e.isBlocked()))}}class cl extends Rt{constructor(e,t,i){super(),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"integrationDataStorage"),__publicField(this,"sent",!1),__publicField(this,"deliveryStatus",Nt.NOT_STARTED),this.environment=N(e),this.browserEventLogger=N(t),this.integrationDataStorage=N(i)}toString(){return`ContextEventTask(${this.environment.getPageviewId()})`}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.deliveryStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:this.deliveryStatus===Nt.FAILED?Nt.FAILED:(Mo.debug("Waiting for context event delivery confirmation"),Nt.TRY_LATER)}sendOnce(){this.sent||(Mo.info("Sending context event"),this.browserEventLogger.sendContext(this.integrationDataStorage).then((()=>{Mo.info(`Context ${this.environment.getPageviewId()} event delivery success`),this.deliveryStatus=Nt.SUCCEEDED})).catch((e=>{const t=new Ei(`Context ${this.environment.getPageviewId()} event delivery failed`,e);Mo.warn(t,224),this.deliveryStatus=Nt.FAILED})),this.sent=!0)}}class dl extends Rt{constructor(e,t,i){super(),__publicField(this,"pageLeaveEventContext"),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"sent",!1),__publicField(this,"deliveryStatus",Nt.NOT_STARTED),this.environment=N(e),this.browserEventLogger=N(t),this.pageLeaveEventContext=N(i)}toString(){return`PageLeaveEventTask(${this.environment.getPageviewId()})`}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.deliveryStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:this.deliveryStatus===Nt.FAILED?Nt.FAILED:(Mo.debug("Waiting for page leave event delivery confirmation"),Nt.TRY_LATER)}sendOnce(){this.sent||(Mo.info("Sending page leave event"),this.browserEventLogger.sendPageLeave(this.pageLeaveEventContext).then((()=>{Mo.info(`Page leave ${this.environment.getPageviewId()} event delivery success`),this.deliveryStatus=Nt.SUCCEEDED})).catch((e=>{const t=new Ei(`Page leave ${this.environment.getPageviewId()} event delivery failed`,e);Mo.warn(t,273),this.deliveryStatus=Nt.FAILED})),this.sent=!0)}}class ul{constructor({environment:e,browserEventLogger:t,customer:i,customerStorage:s,attributeStorage:n,activityStorage:r,user:o,override:a,pageContext:l,statusModel:c,taskManager:d}){__publicField(this,"override"),__publicField(this,"pageContext"),__publicField(this,"environment"),__publicField(this,"customer"),__publicField(this,"taskManager"),__publicField(this,"browserEventLogger"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"user"),__publicField(this,"statusModel"),this.environment=N(e),this.browserEventLogger=N(t),this.customer=N(i),this.customerStorage=N(s),this.attributeStorage=N(n),this.activityStorage=N(r),this.user=N(o),this.override=N(a),this.pageContext=N(l),this.statusModel=N(c),this.taskManager=N(d)}cleanup(){}sendConversionEvent(e,t,i){Mo.group(`sendConversionEvent(${e.getName()}, ${t})`);const s=new tr(e,t,void 0,i);this.activityStorage.add(Gr.buildConversionActivity(s,this.customer,this.environment,this.user,this.attributeStorage,this.customerStorage));const n=new Cr(s,this.browserEventLogger,this.customer,this.customerStorage,this.statusModel);this.taskManager.addTask(n),Mo.groupEnd()}}const hl=6e4;class gl extends ul{constructor({environment:e,browserEventLogger:t,customer:i,customerStorage:s,attributeStorage:n,activityStorage:r,user:o,override:a,pageContext:l,statusModel:c,taskManager:d}){super({environment:e,browserEventLogger:t,customer:i,customerStorage:s,attributeStorage:n,activityStorage:r,user:o,override:a,pageContext:l,statusModel:c,taskManager:d}),__publicField(this,"pollIntervalId"),__publicField(this,"event"),__publicField(this,"sent"),this.sent=!1}initialize(e){Mo.group("InstrumentPageLeaveEvent.initialize()");const t=Ti.filterEvents(e);t.length>0&&(this.event=t[0],t.length>1&&Mo.error(`Expected exactly one PageLeaveEvent, but found ${t.length}`,274),this.instrumentPageLeaveEvent()),Mo.groupEnd()}cleanup(){var e;Mo.group("InstrumentPageLeaveEvent.cleanup()");try{this.environment.getWindow().clearInterval(this.pollIntervalId),this.environment.getWindow().removeEventListener("beforeunload",this.sendPageLeaveEvent)}catch(t){const i=new Ei("Failed to clear inactivity tracker interval",t);Mo.error(i,272,{eventId:null==(e=this.event)?void 0:e.getId()})}Mo.groupEnd()}sendPageLeaveEvent(){if(Mo.group("InstrumentPageLeaveEvent.sendPageLeaveEvent()"),this.sent)return void Mo.debug("Page leave event already sent");if(void 0===this.event)return void Mo.debug("No page leave event found");const e=new tr(this.event,this.environment.getPageviewId(),void 0,void 0);this.taskManager.addTask(new dl(this.environment,this.browserEventLogger,e)),this.sent=!0,Mo.groupEnd()}instrumentPageLeaveEvent(){Mo.group("InstrumentPageLeaveEvent.instrumentPageLeaveEvent()"),this.pollIntervalId=this.environment.getWindow().setInterval((()=>{this.pollActivityStatus()}),hl),this.environment.addUnloadHandler((()=>{var e;try{this.sendPageLeaveEvent()}catch(t){const i=new Ei("Exception in InstrumentPageLeaveEvent unload handler",t);Mo.error(i,271,{eventId:null==(e=this.event)?void 0:e.getId()})}})),Mo.groupEnd()}pollActivityStatus(){Mo.group("InstrumentPageLeaveEvent.pollActivityStatus()"),this.environment.isActive()?Mo.debug("Page is active"):(Mo.debug("Page is inactive"),this.handleTimerExpiration()),Mo.groupEnd()}handleTimerExpiration(){Mo.debug("InstrumentPageLeaveEvent.handleTimerExpiration()"),this.environment.getWindow().clearInterval(this.pollIntervalId),this.sendPageLeaveEvent()}}function pl(e,t){return t.filter((t=>vl(e,t)))}function ml(e,t){return t.some((t=>vl(e,t)))}function vl(e,t){return e.getPages().some((e=>e.getId()===t.getId()))}class bl extends ul{constructor(){super(...arguments),__publicField(this,"registeredClickEvents",[])}cleanup(){Mo.group("InstrumentClickEvent.cleanup()"),this.registeredClickEvents.forEach((([e,t])=>{Mo.debug('removing handler: events="'+this.getEventName()+'", selector="'+e.getSelector()+'"');try{this.environment.getWindow().document.removeEventListener("click",t,!0)}catch(i){const t=new Ei("Failed to unregister click event",i);Mo.error(t,194,{eventId:e.getId()})}})),Mo.groupEnd()}instrumentClickEvent(e){Mo.group("instrumentClickEvent("+e.getName()+")"),Mo.info(`Instrument click event ${e.getId()}, selector ${e.getSelector()}`),Mo.debug('adding handler: events="'+this.getEventName()+'", selector="'+e.getSelector()+'"');const t=t=>{try{if(t.target instanceof Element&&t.target.closest(e.getSelector())instanceof Element){if(!(t.target instanceof HTMLElement||t.target instanceof SVGElement))return void Mo.warn(`Click event triggered for event ID ${e.getId()} (selector: "${e.getSelector()}"), but target is neither an HTMLElement or SVGElement`,259);const i=this.environment.generateActionId();this.handleClickEvent(e,i,t)}}catch(i){const t=new Ei("Failed to handle click event",i);Mo.error(t,167,{eventId:e.getId()})}};try{this.environment.getWindow().document.addEventListener("click",t,!0)}catch(i){const t=new Ei("Failed to register click event",i);Mo.error(t,193,{eventId:e.getId()})}this.registeredClickEvents.push([e,t]),Mo.groupEnd()}getEventName(){return"click."+this.environment.getPageviewId()}}class fl extends bl{initialize(e){Mo.group("InstrumentStandardClickEvents.initialize()"),Si.filterEvents(e).filter((e=>ml(this.pageContext,e.getPages()))).forEach((e=>{this.instrumentClickEvent(e)})),Mo.groupEnd()}handleClickEvent(e,t,i){Mo.group(`handleClickEvent(${e.getName()}, ${t})`),Mo.debug("handleClickEvent run code");e.executeCode(Dt.evalBoolean)?(this.sendConversionEvent(e,t,i),Mo.groupEnd(),this.taskManager.runNow()):Mo.groupEnd()}}class El extends ul{initialize(e){Mo.group("InstrumentViewEvents.initialize()");const t=this.environment.getPageviewId();Fi.filterEvents(e).filter((e=>ml(this.pageContext,e.getPages()))).forEach((e=>{this.handleViewEvent(e,t)}))}handleViewEvent(e,t){Mo.group(`handleViewEvent(${e.getName()}, ${t})`),Mo.debug("handleViewEvent run code");e.executeCode(Dt.evalBoolean)?(this.sendConversionEvent(e,t),Mo.groupEnd()):Mo.groupEnd()}}class _l extends bl{initialize(e){Mo.group("InstrumentWebflowClickEngagementEvents.initialize()");const t=ki.filterEvents(e);if(t.length>0){const e=t[0];t.length>1&&Mo.error(`Expected exactly one WebflowClickEngagementEvent, but found ${t.length}`,256),this.instrumentClickEvent(e)}Mo.groupEnd()}handleClickEvent(e,t,i){Mo.group(`handleWebflowClickEngagementEvent(${e.getName()}, ${t})`),this.sendConversionEvent(e,t,i),Mo.groupEnd(),this.taskManager.runNow()}}class yl extends bl{initialize(e){Mo.group("InstrumentWebflowClickEvents.initialize()"),Pi.filterEvents(e).forEach((e=>{this.instrumentClickEvent(e)})),Mo.groupEnd()}handleClickEvent(e,t,i){Mo.group(`handleWebflowClickEvent(${e.getName()}, ${t})`),this.sendConversionEvent(e,t,i),Mo.groupEnd(),this.taskManager.runNow()}}class Sl extends Rt{constructor(e,t,i,s,n,r,o,a,l,c){super(),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"customer"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"user"),__publicField(this,"override"),__publicField(this,"pageContext"),__publicField(this,"instrumentations",[]),__publicField(this,"statusModel"),this.environment=N(e),this.browserEventLogger=N(t),this.customer=N(i),this.customerStorage=N(s),this.attributeStorage=N(n),this.activityStorage=N(r),this.user=N(o),this.override=N(a),this.pageContext=N(l),this.statusModel=N(c)}toString(){return"ConversionEventInstrumentationTask()"}inTransaction(){return!0}isUnloading(){return!1}cleanup(){this.instrumentations.forEach((e=>{e.cleanup()}))}run(){return this.instrumentEvents(),Nt.SUCCEEDED}instrumentEvents(){Mo.group("instrumentEvents()");const e=this.customer.getEvents().filter((e=>this.override.isEventLive(e).orElse("live"===e.getState()))),t={environment:this.environment,browserEventLogger:this.browserEventLogger,customer:this.customer,customerStorage:this.customerStorage,attributeStorage:this.attributeStorage,activityStorage:this.activityStorage,user:this.user,override:this.override,pageContext:this.pageContext,statusModel:this.statusModel,taskManager:this.manager},i=new fl(t);i.initialize(e),this.instrumentations.push(i);const s=new yl(t);s.initialize(e),this.instrumentations.push(s);const n=new El(t);n.initialize(e),this.instrumentations.push(n);const r=new _l(t);r.initialize(e),this.instrumentations.push(r);const o=new gl(t);o.initialize(e),this.instrumentations.push(o),Mo.groupEnd()}}const wl=class extends Rt{constructor(e,t,i,s){super(),__publicField(this,"environment"),__publicField(this,"customer"),__publicField(this,"endpoint"),__publicField(this,"getCookie"),__publicField(this,"startTimeMs"),__publicField(this,"sent",!1),__publicField(this,"deliveryStatus",Nt.NOT_STARTED),this.environment=e,this.customer=t,this.endpoint=i,this.getCookie=s,this.startTimeMs=this.environment.getNowUnixTimeMs()}toString(){return`CookieAssociatorTask(${this.endpoint})`}inTransaction(){return!0}isUnloading(){return!1}run(){if(this.sendOnce(),this.deliveryStatus===Nt.SUCCEEDED)return Nt.SUCCEEDED;if(this.deliveryStatus===Nt.FAILED)return Nt.FAILED;return this.environment.getNowUnixTimeMs()-this.startTimeMs>=wl.WAIT_FOR_COOKIE_MS?(Mo.warn(`Cookie not present on the page within ${wl.WAIT_FOR_COOKIE_MS}ms (${this.endpoint} )`,81),Nt.SUCCEEDED):Nt.TRY_LATER}sendOnce(){if(this.sent)return;const e=this.getCookie();if(!e.isPresent())return;const t=e.get(),i=this.customer.getId();Mo.info(`Sending id association request. Customer: ${i}`),this.environment.fetch(se,this.endpoint,{method:"POST",headers:{"Content-Type":"text/plain;charset=UTF-8"},body:Re({customerId:this.customer.getId(),cookie:t})}).then((()=>{Mo.info("Successfully sent id association request"),this.deliveryStatus=Nt.SUCCEEDED})).catch((e=>{const t=new Ei(`Error calling ${this.endpoint} `,e);Mo.error(t,82),this.deliveryStatus=Nt.FAILED})),this.sent=!0}};let Il=wl;__publicField(Il,"WAIT_FOR_COOKIE_MS",5e3);class Cl extends Rt{constructor(e){super(),__publicField(this,"elementObserver"),this.elementObserver=e}toString(){return"ElementObserverTask()"}inTransaction(){return!1}isUnloading(){return!1}cleanup(){Mo.group("ElementObserverTask.cleanup()"),this.elementObserver.reset(),Mo.groupEnd()}run(){return Mo.debug("ElementObserverTask no-op done"),Nt.SUCCEEDED}}class Tl extends Rt{constructor(e,t,i,s,n,r,o,a,l){if(super(),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"customer"),__publicField(this,"user"),__publicField(this,"override"),__publicField(this,"activityStorage"),__publicField(this,"attributeStorage"),__publicField(this,"customerStorage"),__publicField(this,"statusModel"),!dr(i))throw new Error("Hubspot integration must be enabled for the customer to use the HubspotFormsTask");this.environment=N(e),this.browserEventLogger=N(t),this.customer=N(i),this.user=N(s),this.override=N(n),this.activityStorage=N(r),this.attributeStorage=N(o),this.customerStorage=N(a),this.statusModel=N(l)}toString(){return"HubspotFormsTask()"}inTransaction(){return!1}isUnloading(){return!1}run(){return this.attachFormHandler(),Nt.SUCCEEDED}attachFormHandler(){window.addEventListener("message",(e=>{if(hr(e)){const t=e.data.id;Mo.debug(`handler fired for hubspot form; triggering lead association request (Form ID: ${t})`);const i=this.environment.generateActionId();this.findEvents(t).forEach((e=>{this.sendConversionEvent(e,i)}));const s=new Il(this.environment,this.customer,"/hubspot-id-associator",(()=>ur(this.environment)));this.manager.addTask(s)}}))}findEvents(e){return Ii.filterEvents(this.customer.getEvents()).filter((e=>this.override.isEventLive(e).orElse("live"===e.getState()))).filter((t=>t.getFormId()===e))}sendConversionEvent(e,t){Mo.debug("Hubspot form submission send conversion event");const i=new tr(e,t,void 0,void 0);this.activityStorage.add(Gr.buildConversionActivity(i,this.customer,this.environment,this.user,this.attributeStorage,this.customerStorage));const s=new Cr(i,this.browserEventLogger,this.customer,this.customerStorage,this.statusModel);this.manager.addTask(s)}}const xl=class extends Rt{constructor(e,t,i,s,n,r,o,a,l){if(super(),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"customer"),__publicField(this,"user"),__publicField(this,"override"),__publicField(this,"activityStorage"),__publicField(this,"attributeStorage"),__publicField(this,"customerStorage"),__publicField(this,"statusModel"),__publicField(this,"startTimeMs"),__publicField(this,"trackedForms",[]),!gr(i))throw new Error("Marketo integration must be enabled for the customer to use the MarketoFormsTask");this.environment=N(e),this.browserEventLogger=N(t),this.customer=N(i),this.user=N(s),this.override=N(n),this.activityStorage=N(r),this.attributeStorage=N(o),this.customerStorage=N(a),this.statusModel=N(l),this.startTimeMs=this.environment.getNowUnixTimeMs()}toString(){return"MarketoFormsTask()"}inTransaction(){return!1}isUnloading(){return!1}run(){const e=this.environment.getNowUnixTimeMs(),t=this.environment.getWindow();if(void 0!==t.MktoForms2)return Mo.debug("marketo forms library ready"),this.attachFormHandlers(t.MktoForms2),Nt.SUCCEEDED;return e-this.startTimeMs>=xl.WAIT_FOR_MKTOFORMS2_MS?(Mo.debug(`marketo forms library not present on the page within ${xl.WAIT_FOR_MKTOFORMS2_MS}ms`),Nt.SUCCEEDED):(Mo.debug("marketo forms library not present; Will try again later."),Nt.TRY_LATER)}attachFormHandlers(e){e.whenReady((e=>{const t=String(e.getId());this.trackedForms.includes(t)?Mo.debug(`marketo form already tracked (Form ID: ${t})`):(this.trackedForms.push(t),Mo.debug(`attaching success handler to marketo (Form ID: ${t})`),e.onSuccess((()=>{Mo.debug(`success handler fired for marketo form; triggering lead association request (Form ID: ${t})`);const e=this.environment.generateActionId();this.findEvents(t).forEach((t=>{this.sendConversionEvent(t,e)}));const i=new Il(this.environment,this.customer,"/marketo-id-associator",(()=>pr(this.environment)));this.manager.addTask(i)})))}))}findEvents(e){return Ci.filterEvents(this.customer.getEvents()).filter((e=>this.override.isEventLive(e).orElse("live"===e.getState()))).filter((t=>t.getFormId()===e))}sendConversionEvent(e,t){Mo.debug("Marketo form submission send conversion event");const i=new tr(e,t,void 0,void 0);this.activityStorage.add(Gr.buildConversionActivity(i,this.customer,this.environment,this.user,this.attributeStorage,this.customerStorage));const s=new Cr(i,this.browserEventLogger,this.customer,this.customerStorage,this.statusModel);this.manager.addTask(s)}};let Al=xl;__publicField(Al,"WAIT_FOR_MKTOFORMS2_MS",5e3);class Fl extends Rt{toString(){return"PageUnloadingTask()"}inTransaction(){return!1}isUnloading(){return!0}run(){return Mo.debug("page unload no-op done"),Nt.SUCCEEDED}}class kl extends Rt{constructor(e,t){super(),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"sent",!1),__publicField(this,"deliveryStatus",Nt.NOT_STARTED),this.environment=N(e),this.browserEventLogger=N(t)}toString(){return`PageviewEventTask(${this.environment.getPageviewId()})`}inTransaction(){return!0}isUnloading(){return!1}run(){return this.sendOnce(),this.deliveryStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:this.deliveryStatus===Nt.FAILED?Nt.FAILED:(Mo.debug("Waiting for pageview event delivery confirmation"),Nt.TRY_LATER)}sendOnce(){this.sent||(Mo.info("Sending pageview event"),this.browserEventLogger.sendPageview().then((()=>{Mo.info(`Pageview ${this.environment.getPageviewId()} event delivery success`),this.deliveryStatus=Nt.SUCCEEDED})).catch((e=>{const t=new Ei(`Pageview ${this.environment.getPageviewId()} event delivery failed`,e);Mo.warn(t,83),this.deliveryStatus=Nt.FAILED})),this.sent=!0)}}var Pl=(e=>(e[e.TS=0]="TS",e[e.TZ=1]="TZ",e[e.DP=2]="DP",e[e.WP=3]="WP",e[e.US=4]="US",e[e.UM=5]="UM",e[e.UCM=6]="UCM",e[e.UT=7]="UT",e[e.UCN=8]="UCN",e[e.UPN=9]="UPN",e[e.UPNV=10]="UPNV",e[e.CAN=11]="CAN",e[e.CANV=12]="CANV",e[e.PID=13]="PID",e[e.IFTU=14]="IFTU",e[e.AID=15]="AID",e))(Pl||{});const Ml="=",Dl="-";class Nl{static buildBrowserContext(e,t,i,s,n){const r=t.getInternalAttributes("user"),o=[];n.getExperiences().forEach((e=>{if("cc"!==e.getType()){const t=n.getVariationIds(e.getId());1===t.length?o.push(t[0]):Mo.error("Non-cc experience does not have exactly one variation selected",181,{experienceId:e.getId()})}}));const a={isFirstTimeUser:i.isNewVisitor(),timestamp:e.getNowUnixTime(),customAttributes:t.getFlattenedCustomAttributes(),pageIds:s.getPages().map((e=>e.getId())),audienceIds:s.getAudiences().map((e=>e.getId())),selectedVariationIds:o};return void 0!==r.ts&&(a.trafficSource=r.ts),a.timeZone=e.getTimeZone(),void 0!==r.uts&&(a.utmSource=r.uts),void 0!==r.utm&&(a.utmMedium=r.utm),void 0!==r.utcm&&(a.utmCampaign=r.utcm),void 0!==r.utt&&(a.utmTerm=r.utt),void 0!==r.utcn&&(a.utmContent=r.utcn),a.hostingPageUrl=e.getHostingPageUrl().getRawUrl(),a}static buildFeatures(e,t,i,s){const n={},r=t.getInternalAttributes("user");return Nl.addFeature(n,0,Nl.getFeatureFromAttribute(r.ts)),Nl.addFeature(n,4,Nl.getFeatureFromAttribute(r.uts)),Nl.addFeature(n,5,Nl.getFeatureFromAttribute(r.utm)),Nl.addFeature(n,6,Nl.getFeatureFromAttribute(r.utcm)),Nl.addFeature(n,7,Nl.getFeatureFromAttribute(r.utt)),Nl.addFeature(n,8,Nl.getFeatureFromAttribute(r.utcn)),Nl.addFeature(n,9,Nl.getUrlParameterName(e)),Nl.addFeature(n,10,Nl.getUrlParameterNameValue(e)),Nl.addFeature(n,1,Nl.getTimeZoneValue(e)),Nl.addFeature(n,2,Nl.getDayPartValue(e)),Nl.addFeature(n,3,Nl.getWeekPartValue(e)),Nl.addFeature(n,11,Nl.getCustomAttributeName(t)),Nl.addFeature(n,12,Nl.getCustomAttributeNameValue(t)),Nl.addFeature(n,13,Nl.getPageId(s)),Nl.addFeature(n,14,Nl.getIsFirstTimeUser(i)),Nl.addFeature(n,15,Nl.getAudienceId(s)),n}static getFeatureFromAttribute(e){return j.ofNullable(e).toArray()}static getTimeZoneValue(e){return[e.getTimeZone()]}static getDayPartValue(e){return[e.getDayPart()]}static getWeekPartValue(e){return[e.getWeekPart()]}static getUrlParameterName(e){return Object.keys(e.getHostingPageUrl().getAllQueryParamsUnsafe()).filter((e=>""!==e))}static getUrlParameterNameValue(e){const t=e.getHostingPageUrl().getAllQueryParamsUnsafe(),i=[];return Object.keys(t).forEach((e=>{i.push(e+Dl+t[e])})),i}static getCustomAttributeName(e){const t=e.getFlattenedCustomAttributes();return Object.keys(t)}static getCustomAttributeNameValue(e){const t=e.getFlattenedCustomAttributes();return Object.keys(t).map((e=>e+Dl+t[e]))}static getPageId(e){return e.getPages().map((e=>e.getId()))}static getIsFirstTimeUser(e){return[e.isNewVisitor().toString()]}static getAudienceId(e){return e.getAudiences().map((e=>e.getId()))}static buildUnaryFeature(e,t){return Pl[e]+Ml+t}static addFeature(e,t,i){i.forEach((i=>{e[Nl.buildUnaryFeature(t,i)]=!0}))}}class Ol{constructor(){__publicField(this,"variationLists",{}),__publicField(this,"experiences",{}),__publicField(this,"stickyVariations",{}),__publicField(this,"modelVersions",{})}unshift(e){const t=e.getExperience(),i=t.getId();void 0===this.variationLists[i]&&(this.variationLists[i]=[]),this.variationLists[i].unshift(e),this.experiences[i]=t}isEmpty(){return!Object.keys(this.variationLists).some((e=>this.variationLists[e].length>0))}getExperience(e){return this.experiences[e]}getExperiences(){return Object.keys(this.variationLists).map((e=>this.experiences[e]))}setModelVersion(e,t){this.modelVersions[e]=t}getModelVersion(e){return this.modelVersions[e]}sortVariations(e,t){var i;null==(i=this.variationLists[e])||i.sort(t)}prioritizeStickyVariation(e,t){if(Mo.group("prioritizeVariation(experience:"+e+", variation:"+t+")"),e in this.variationLists){let i=-1;this.variationLists[e].forEach(((e,s)=>{e.getId()===t&&(i=s)})),Mo.debug("found variation index? => "+i),-1!==i&&(this.variationLists[e]=[this.variationLists[e][i]],e in this.stickyVariations||(this.stickyVariations[e]={}),this.stickyVariations[e][t]=!0)}Mo.groupEnd()}getVariationIds(e){var t,i;return null!=(i=null==(t=this.variationLists[e])?void 0:t.map((e=>e.getId())))?i:[]}hasNext(e){const t=this.variationLists[e];return void 0!==t&&t.length>0}shift(e){var t;if(!(e in this.variationLists))throw new $("No value present");const i=null==(t=this.variationLists[e])?void 0:t.shift();if(this.removeIfEmpty(e),void 0!==i)return i;throw new $("No value present")}getFirst(e){var t;const i=this.variationLists[e];if(void 0===i)throw new $("No experience present");if(0===i.length)throw new $("No variations present");const s=null==(t=this.variationLists[e])?void 0:t[0];if(void 0!==s)return s;throw new $("No value present")}removeVariation(e,t){t.getId();const i=this.variationLists[e];if(void 0!==i){let s=-1;i.forEach(((e,i)=>{e.getId()===t.getId()&&(s=i)})),-1!==s&&(i.splice(s,1),this.removeIfEmpty(e))}}removeExperience(e){e in this.experiences&&delete this.experiences[e],e in this.variationLists&&delete this.variationLists[e]}isDecidingExperience(e){return e.getId()in this.experiences}isStickyVariation(e){var t;const i=e.getId(),s=e.getExperience().getId();return Boolean(null==(t=this.stickyVariations[s])?void 0:t[i])}toString(){let e="{\n";return Object.keys(this.variationLists).forEach((t=>{e+="  "+t+": ",e+="["+this.variationLists[t].map((e=>e.getId())).join(", ")+"]",e+="\n"})),e+="}\n",e}removeIfEmpty(e){const t=this.variationLists[e];void 0!==t&&0===t.length&&this.removeExperience(e)}}class Ll extends Rt{constructor(e,t,i,s,n,r,o,a,l,c,d,u,h,g,p,m,v){super(),__publicField(this,"environment"),__publicField(this,"browserStorage"),__publicField(this,"extensionManager"),__publicField(this,"browserEventLogger"),__publicField(this,"customer"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"user"),__publicField(this,"override"),__publicField(this,"pageContext"),__publicField(this,"attributeData"),__publicField(this,"statusModel"),__publicField(this,"elementObserver"),__publicField(this,"runtime"),__publicField(this,"hider"),__publicField(this,"decisionContext"),__publicField(this,"getVariationRecordedCallbackTuples"),__publicField(this,"getEligibleCc",!1),__publicField(this,"selectAb",!1),__publicField(this,"selectRbp",!1),__publicField(this,"haveRunCampaignCode",{}),this.environment=N(e),this.browserStorage=N(t),this.extensionManager=N(i),this.browserEventLogger=N(s),this.customer=N(n),this.customerStorage=N(r),this.attributeStorage=N(o),this.activityStorage=N(a),this.user=N(l),this.override=N(c),this.pageContext=N(d),this.attributeData=N(u),this.statusModel=N(h),this.runtime=N(g),this.hider=N(p),this.getVariationRecordedCallbackTuples=N(m),this.elementObserver=N(v),this.decisionContext=new Ol}toString(){return"SelectVariationsTask()"}inTransaction(){return!1}isUnloading(){return!1}selectAbExperienceVariationsOnce(){this.selectAb||(Mo.time("selectAbExperienceVariationsOnce"),Mo.group("selectAbExperienceVariationsOnce()"),this.customer.getCampaigns().forEach((e=>{As.filterExperiences(e.getExperiences()).filter((e=>this.isExperienceEligible(e))).forEach((t=>{let i;if(this.runCampaignCodeOnce(e),this.override.hasVariationIds(t).isPresent()){const e=this.override.hasVariationIds(t).get();i=t.getVariation(e[0])}else{const e=1e3,s=ol(nl(t.getId()+this.user.getId(),1),e);i=t.selectVariation(s)}i.ifPresent((e=>{this.isVariationEligible(e)?(this.decisionContext.unshift(e),this.decisionContext.setModelVersion(t.getId(),"ab")):Mo.error("Selected A/B variation is not eligible",182,{experienceId:t.getId(),variationId:e.getId()})})).ifAbsent((()=>{Mo.error("Failed to select variation for A/B experience",183,{experienceId:t.getId()})}))}))})),Mo.groupEnd(),Mo.timeEnd("selectAbExperienceVariationsOnce"),this.selectAb=!0)}selectRbpExperienceVariationsOnce(){this.selectRbp||(Mo.time("selectRbpExperienceVariationsOnce"),Mo.group("selectRbpExperienceVariationsOnce()"),this.customer.getCampaigns().forEach((e=>{ks.filterExperiences(e.getExperiences()).filter((e=>this.isExperienceEligible(e))).forEach((t=>{this.runCampaignCodeOnce(e);for(const e of t.getPrioritizedVariations())if(this.isVariationEligible(e)){this.decisionContext.unshift(e),this.decisionContext.setModelVersion(t.getId(),"rbp");break}void 0===this.decisionContext.getExperience(t.getId())&&Mo.info("Failed to select variation for RBP",184,{experienceId:t.getId()})}))})),Mo.groupEnd(),Mo.timeEnd("selectRbpExperienceVariationsOnce"),this.selectRbp=!0)}getCcEligibleVariationsOnce(){this.getEligibleCc||(Mo.time("getCcEligibleVariationsOnce"),Mo.group("getCcEligibleVariationsOnce()"),this.customer.getCampaigns().forEach((e=>{Fs.filterExperiences(e.getExperiences()).filter((e=>this.isExperienceEligible(e))).forEach((t=>{this.runCampaignCodeOnce(e);const i=this.override.isControl().orElseRun((()=>this.customerStorage.updateAndGetControl(e)));if(this.statusModel.setCampaign(e,"control",i),i){const e=t.getControlVariation();this.decisionContext.unshift(e)}else t.getRealVariations().forEach((e=>{this.isVariationEligible(e)&&this.decisionContext.unshift(e)}))}))})),Mo.groupEnd(),Mo.timeEnd("getCcEligibleVariationsOnce"),this.getEligibleCc=!0)}isExperienceEligible(e){Mo.group("isExperienceEligible(experience:"+e.getId()+")"),Mo.debug("check experience enabled");let t=" (override)";const i=this.override.isExperienceEnabled(e).orElseRun((()=>(t="",e.isEnabled())));if(Mo.info(`Experience ${e.getId()} enabled => ${i}${t}`),this.statusModel.setExperience(e,"enabled",i),!i)return this.customerStorage.deleteExperiencePredictionSelection(e),Mo.groupEnd(),!1;let s=!1;Mo.debug(`Experience URL match (at least one match), URL: ${this.environment.getHostingPageUrl().getRawUrl()}`);const n=pl(this.pageContext,e.getPages());if(Mo.debug("url match => "+(n.length>0)),this.statusModel.setExperience(e,"urlMatch",n.length>0),n.length>0&&(Mo.info(`Experience ${e.getId()} URL check, URL:${this.environment.getHostingPageUrl().getRawUrl()} Page:${n[0].getName()} => true`),s=!0),!s){const{experienceIds:t}=this.environment.getWebflowPageMetadata();t.length>0&&(Mo.debug("Webflow Page Metadata Experience ID check"),t.includes(e.getId())&&(Mo.debug("Webflow experience => true"),s=!0))}if(!s)return Mo.groupEnd(),!1;Mo.debug("check experience condition");let r=" (override)";const o=this.override.passExperienceCondition().orElseRun((()=>(r="",Mo.debug("evaluating experience condition"),e.getCondition().map((t=>{let i=!1;try{i=t.evaluate(this.runtime)}catch(s){const t=new Ei("Experience condition evaluation failed",s);i=!1,Mo.error(t,206,{experienceId:e.getId()})}return i})).orElse(!0))));if(Mo.info(`Experience ${e.getId()} condition passed => ${o}${r}`),this.statusModel.setExperience(e,"passCondition",o),!o)return Mo.groupEnd(),this.customerStorage.deleteExperiencePredictionSelection(e),!1;Mo.debug("check experience inclusion");let a=" (override)";const l=this.override.isExperienceIncluded(e).orElseRun((()=>(a="",this.customerStorage.updateAndGetExperienceInclusion(e))));return Mo.info(`Experience ${e.getId()} inclusion => ${l}${a}`),this.statusModel.setExperience(e,"inclusion",l),l?(Mo.debug("isExperienceEligible => true"),Mo.groupEnd(),!0):(Mo.groupEnd(),this.customerStorage.deleteExperiencePredictionSelection(e),!1)}isVariationEligible(e){Mo.group("isVariationEligible(variation:"+e.getId()+")"),Mo.debug("check variation enabled");let t=" (override)";const i=this.override.isVariationEnabled(e).orElseRun((()=>(t="",e.isEnabled())));if(Mo.info(`Variation ${e.getExperience().getId()}/${e.getId()} enabled => ${i}${t}`),this.statusModel.setVariation(e,"enabled",i),!i)return Mo.groupEnd(),!1;Mo.debug("check variation condition");let s=" (override)";const n=this.override.passVariationCondition().orElseRun((()=>(s="",e.getCondition().map((t=>{let i=!1;try{i=t.evaluate(this.runtime)}catch(s){const t=new Ei("Variation condition evaluation failed ",s);i=!1,Mo.error(t,207,{experienceId:e.getExperience().getId(),variationId:e.getId()})}return i})).orElse(!0))));return Mo.info(`Variation ${e.getExperience().getId()}/${e.getId()} condition passed => ${n}${s}`),this.statusModel.setVariation(e,"passCondition",n),n?(Mo.debug("isVariationEligible => true"),Mo.groupEnd(),!0):(Mo.groupEnd(),!1)}runCampaignCodeOnce(e){const t=e.getId();this.haveRunCampaignCode[t]||(this.haveRunCampaignCode[t]=!0,e.getCss().ifPresent((i=>{try{Mo.info(`Injecting campaign ${e.getId()} css`),Dt.injectCss(i,`campaign-${t}`,this.environment.getWindow().document),this.statusModel.setCampaign(e,"css",!0)}catch(s){const t=new Ei(`Campaign (${e.getId()}) css injection failed`,s);Mo.error(t,90,{campaignId:e.getId()}),this.statusModel.setCampaign(e,"css",!1)}})),e.getCode().ifPresent((t=>{try{Mo.info(`Running campaign ${e.getId()} code`),Dt.eval(t),this.statusModel.setCampaign(e,"code",!0)}catch(i){const t=new Ei(`Campaign (${e.getId()}) code execution failed`,i);Mo.error(t,14,{campaignId:e.getId()}),this.statusModel.setCampaign(e,"code",!1)}})))}}class Rl extends Ll{constructor(){super(...arguments),__publicField(this,"policyCallbackFunctions",[]),__publicField(this,"executeExperiencesTask"),__publicField(this,"policy"),__publicField(this,"sent",!1),__publicField(this,"apiStatus",Nt.WAITING)}static get PREDICTION_ENDPOINT(){return"/prediction"}toString(){return"SelectVariationsTaskV5()"}onPolicyReady(e){void 0===this.policy?this.policyCallbackFunctions.push(e):e(this.policy)}run(){return this.selectAbExperienceVariationsOnce(),this.selectRbpExperienceVariationsOnce(),this.getCcEligibleVariationsOnce(),Mo.debug("decisionContext: "+this.decisionContext),void 0===this.executeExperiencesTask&&(this.executeExperiencesTask=new qr(this.environment,this.browserStorage,this.extensionManager,this.browserEventLogger,this.attributeStorage,this.activityStorage,this.customerStorage,this.decisionContext,this.pageContext,this.attributeData,this.user,this.override,this.statusModel,this.hider,!0,this.getVariationRecordedCallbackTuples,this.elementObserver)),this.executeExperiencesTask.hideExperiencesOnce(),this.sendOnce(),this.apiStatus===Nt.FAILED?Nt.FAILED:this.apiStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:Nt.TRY_LATER}buildRequest(){const e=this.user.getId(),t=this.customerStorage.updateAndGetSessionId(),i=this.override.isControl().orElseRun((()=>this.customerStorage.setAndGetCustomerControl(this.customer))),s={};Fs.filterExperiences(this.decisionContext.getExperiences()).forEach((e=>{const t=e.getId(),i=this.customerStorage.getStickyVariation(e).map((e=>e.getId())).orElse("NEVER_MATCH");s[t]=this.decisionContext.getVariationIds(t).map((e=>({variationId:e,isSticky:i===e})))}));const n=Nl.buildBrowserContext(this.environment,this.attributeStorage,this.user,this.pageContext,this.decisionContext);return{userId:e,sessionId:t,isControl:i,candidates:s,context:n}}sendOnce(){if(this.sent)return;Mo.debug("SelectVariationsTaskV5.sendOnce()");const e=this.buildRequest();this.environment.fetch(ie,`${Rl.PREDICTION_ENDPOINT}/${this.customer.getId()}`,{method:"POST",headers:{"Content-Type":"text/plain;charset=UTF-8"},body:Re(e)}).then((t=>{if(Xt(t)){const e=this.environment.getHostingPageUrl().getOrigin();console.warn(`${e} is not allowed to receive this data. If you’re expecting data to work here, check the Allowed Domains list at https://app.intellimize.com`)}Mo.debug("SelectVariationsTaskV5.sendOnce() => got reply");const i=this.override.isControl().orElseRun((()=>this.customerStorage.setAndGetCustomerControl(this.customer)));this.policy=i?Ro.buildControlPolicy(this.environment):Xt(t)?Ro.buildDefaultPolicy(this.environment,e):new Ro(t.policy),this.prioritizeCcVariations(this.policy),this.apiStatus=Nt.SUCCEEDED,this.executePolicyReadyFunctions(this.policy),this.manager.addTask(this.executeExperiencesTask),this.manager.runNow()})).catch((e=>{const t=new Ei("Caught exception in prediction call",e);Mo.error(t,148),this.apiStatus=Nt.FAILED})),this.sent=!0}prioritizeCcVariations(e){Mo.debug("SelectVariationsTaskV5.prioritizeCcVariations()"),Fs.filterExperiences(this.decisionContext.getExperiences()).forEach((t=>{const i=t.getId(),s=e.getExperienceVariationId(i);let n;for(;this.decisionContext.hasNext(i);){const e=this.decisionContext.shift(i);e.getId()===s&&(n=e)}void 0===n?Mo.error("Prediction API did not select an eligible variation",149):(this.decisionContext.unshift(n),this.customerStorage.getStickyVariation(this.decisionContext.getExperience(i)).ifPresent((e=>{Mo.info(`Variation ${i}/${e.getId()} selection was sticky`),this.decisionContext.prioritizeStickyVariation(i,e.getId())})),this.decisionContext.setModelVersion(i,e.getExperienceModelVersion(i))),this.statusModel.setExperienceSortedVariations(this.decisionContext.getExperience(i),this.decisionContext.getVariationIds(i))})),Mo.debug(`decisionContext: ${this.decisionContext}`)}executePolicyReadyFunctions(e){Mo.debug("SelectVariationsTaskV5.executePolicyReadyFunctions()");for(let t=this.policyCallbackFunctions.shift();void 0!==t;t=this.policyCallbackFunctions.shift())t(e)}}class Ul extends Rt{constructor(e,t,i,s){super(),__publicField(this,"environment"),__publicField(this,"customer"),__publicField(this,"integrationDataStorage"),__publicField(this,"user"),__publicField(this,"apiStatus",Nt.NOT_STARTED),__publicField(this,"sent",!1),this.environment=e,this.customer=t,this.user=i,this.integrationDataStorage=s}static get ENDPOINT(){return"/context-v2"}toString(){return"ServerContextTask()"}inTransaction(){return!1}isUnloading(){return!1}run(){return this.sendOnce(),this.apiStatus===Nt.SUCCEEDED?Nt.SUCCEEDED:this.apiStatus===Nt.FAILED?Nt.FAILED:(Mo.debug("Waiting for server response"),Nt.TRY_LATER)}buildRequestBody(){return this.getCommonRequestBody()}handleResponse(e,t){this.integrationDataStorage.setDataAndMetadata("marketo",t.integrationData.marketo,void 0===e.marketo?void 0:{})}getCommonRequestBody(){const e={clientVersion:Q,userId:this.user.getId()};return gr(this.customer)&&pr(this.environment).ifPresent((t=>{e.marketo={cookie:t}})),e}sendOnce(){if(!this.sent){const e=this.buildRequestBody();this.environment.fetch(ie,`${Ul.ENDPOINT}/${this.customer.getId()}`,{method:"POST",headers:{"Content-Type":"text/plain;charset=UTF-8"},body:Re(e)}).then((t=>{if(Xt(t)){const t=this.environment.getHostingPageUrl().getOrigin();console.warn(`${t} is not allowed to receive this data. If you’re expecting data to work here, check the Allowed Domains list at https://app.intellimize.com`),this.handleResponse(e,__spreadProps(__spreadValues({},Ka),{integrationData:{}}))}else Mo.info("Successfully retrieved ServerContext"),this.handleResponse(e,t);this.apiStatus=Nt.SUCCEEDED,this.manager.runNow()})).catch((e=>{const t=new Ei("Caught exception in server context task",e);Mo.error(t,156),this.apiStatus=Nt.FAILED}))}this.sent=!0}}class $l extends Ul{constructor(e,t,i,s,n,r){super(e,t,i,s),__publicField(this,"serverContextStorage"),__publicField(this,"isNewSession"),this.serverContextStorage=n,this.isNewSession=r}static get DOMAIN_NOT_AVAILABLE(){return"NA"}static shouldWaitForServerContext(e,t,i,s,n){var r,o,a;let l=!1;_r(e)&&yr(t).ifPresent((e=>{const t=i.getMetadata("salesforce").orElse({});void 0!==(null==e?void 0:e.leadIdHash)&&(null==t?void 0:t.leadIdHash)!==e.leadIdHash&&(l=!0),void 0!==(null==e?void 0:e.contactIdHash)&&(null==t?void 0:t.contactIdHash)!==e.contactIdHash&&(l=!0)}));const c=i.getAllMetdata(),d=sr(e)&&void 0===(null==(r=c.firmographic)?void 0:r.domain)||Sr(e)&&void 0===(null==(o=c["6sense"])?void 0:o.domain)||ir(e)&&void 0===(null==(a=c.demandbase)?void 0:a.website),u=!s.get().map((e=>Object.keys(null==e?void 0:e.location).length>0&&Object.keys(null==e?void 0:e.userAgent).length>0)).orElse(!1);return l||d||u||n}toString(){return"ServerContextTaskV5()"}buildRequestBody(){const e=this.getCommonRequestBody();if(this.serverContextStorage.get().map((e=>e.clientIp)).ifPresent((t=>{e.clientIp=t})),_r(this.customer)){const t=this.integrationDataStorage.getMetadata("salesforce").orElse({}),i=yr(this.environment),s=null==t?void 0:t.leadIdHash,n=i.map((e=>null==e?void 0:e.leadIdHash)).orElse(void 0),r=null!=n?n:s,o=null==t?void 0:t.contactIdHash,a=i.map((e=>null==e?void 0:e.contactIdHash)).orElse(void 0),l=null!=a?a:o;if(void 0!==r||void 0!==l){const t={};void 0!==r&&(t.leadIdHash=r),void 0!==l&&(t.contactIdHash=l),e.salesforce=t}}return this.isNewSession||(this.integrationDataStorage.getMetadata("firmographic").flatMap((e=>j.ofNullable(null==e?void 0:e.domain))).ifPresent((t=>{e.firmographic={domain:t}})),this.integrationDataStorage.getMetadata("6sense").flatMap((e=>j.ofNullable(null==e?void 0:e.domain))).ifPresent((t=>{e["6sense"]={domain:t}})),this.integrationDataStorage.getMetadata("demandbase").flatMap((e=>j.ofNullable(null==e?void 0:e.website))).ifPresent((t=>{e.demandbase={website:t}}))),dr(this.customer)&&ur(this.environment).ifPresent((t=>{e.hubspot={cookie:t}})),e}handleResponse(e,t){var i,s,n,r,o,a,l,c,d,u,h,g,p;const m=t,{integrationData:v}=m,b=__objRest(m,["integrationData"]);this.integrationDataStorage.setDataAndMetadata("marketo",v.marketo,void 0===e.marketo?void 0:{}),this.integrationDataStorage.setDataAndMetadata("salesforce",v.salesforce,null!=(i=e.salesforce)?i:void 0),this.integrationDataStorage.setDataAndMetadata("hubspot",v.hubspot,void 0===e.hubspot?void 0:{}),(null==(s=e.firmographic)?void 0:s.domain)!==(null==(n=v.firmographic)?void 0:n.domain)&&((null==(r=v.firmographic)?void 0:r.domain)===$l.DOMAIN_NOT_AVAILABLE?this.integrationDataStorage.setDataAndMetadata("firmographic",{},{domain:$l.DOMAIN_NOT_AVAILABLE}):this.integrationDataStorage.setDataAndMetadata("firmographic",v.firmographic,{domain:null==(o=v.firmographic)?void 0:o.domain})),(null==(a=e["6sense"])?void 0:a.domain)!==(null==(l=v["6sense"])?void 0:l.domain)&&((null==(c=v["6sense"])?void 0:c.domain)===$l.DOMAIN_NOT_AVAILABLE?this.integrationDataStorage.setDataAndMetadata("6sense",{},{domain:$l.DOMAIN_NOT_AVAILABLE}):this.integrationDataStorage.setDataAndMetadata("6sense",v["6sense"],{domain:null==(d=v["6sense"])?void 0:d.domain})),(null==(u=e.demandbase)?void 0:u.website)!==(null==(h=v.demandbase)?void 0:h.website)&&((null==(g=v.demandbase)?void 0:g.website)===$l.DOMAIN_NOT_AVAILABLE?this.integrationDataStorage.setDataAndMetadata("demandbase",{},{website:$l.DOMAIN_NOT_AVAILABLE}):this.integrationDataStorage.setDataAndMetadata("demandbase",v.demandbase,{website:null==(p=v.demandbase)?void 0:p.website})),this.serverContextStorage.set(b)}}class Vl{constructor(e,t,i){__publicField(this,"environment"),__publicField(this,"storageKeyPrefix"),__publicField(this,"eventProcessor"),__publicField(this,"isObserving",!1),__publicField(this,"pollTimer"),this.environment=e,this.storageKeyPrefix=t,this.eventProcessor=i}static get POLLING_INTERVAL_MS(){return 1e3}start(){this.isObserving||(Mo.debug("StorageQueueConsumer starting to poll"),this.isObserving=!0,this.poll())}stop(){Mo.debug("StorageQueueConsumer stopping polling"),this.environment.getWindow().clearTimeout(this.pollTimer),this.isObserving=!1}handlePageUnload(){this.poll(),this.stop()}readSessionStorage(e){try{return this.environment.readSessionStorage(e)}catch(t){const i=new Ei(`StorageQueueConsumer: Storage failed for getItem("${e}")`,t);throw Mo.warn(i,227),i}}writeSessionStorage(e,t){try{this.environment.writeSessionStorage(e,t)}catch(i){const t=new Ei(`StorageQueueConsumer: Storage failed for setItem("${e}")`,i);throw Mo.warn(t,228),t}}removeSessionStorage(e){try{this.environment.deleteSessionStorage(e)}catch(t){const i=new Ei(`StorageQueueConsumer: Storage failed for removeItem("${e}")`,t);throw Mo.warn(i,229),i}}poll(){const e=`${this.storageKeyPrefix}lastWrittenId`,t=`${this.storageKeyPrefix}lastReadId`;let i=j.empty(),s=0;try{i=this.readSessionStorage(e).map((e=>Number.parseInt(e,10))),s=this.readSessionStorage(t).map((e=>Number.parseInt(e,10))).orElse(0)}catch(n){return void this.stop()}if(i.isPresent()&&s<i.get()){const e=s+1,i=`${this.storageKeyPrefix}event-${e}`;let o=j.empty();try{o=this.readSessionStorage(i)}catch(n){return void this.stop()}try{this.writeSessionStorage(t,e.toString()),this.removeSessionStorage(i)}catch(n){return void this.stop()}if(o.isPresent()){let e;try{e=JSON.parse(o.get())}catch(r){const e=new Ei(`StorageQueueConsumer: Event in queue is not valid JSON: "${o.get()}"`,r);Mo.error(e,230)}if(void 0!==e)try{Mo.debug(`StorageQueueConsumer processing event ${Le(e)}`),this.eventProcessor(e)}catch(r){const e=new Ei("StorageQueueConsumer: Could not process event",r);Mo.error(e,231)}this.poll()}else Mo.error(new Error(`StorageQueueConsumer: event-${e} not found in session storage`),232),this.pollAfterDelay()}else this.pollAfterDelay()}pollAfterDelay(){this.environment.getWindow().clearTimeout(this.pollTimer),this.pollTimer=this.environment.getWindow().setTimeout((()=>{this.poll()}),Vl.POLLING_INTERVAL_MS)}}const Wl=3e3,zl=1;class jl extends Rt{constructor(e,t,i,s,n,r,o,a,l){super(),__publicField(this,"environment"),__publicField(this,"browserEventLogger"),__publicField(this,"customer"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"activityStorage"),__publicField(this,"user"),__publicField(this,"statusModel"),__publicField(this,"taskManager"),__publicField(this,"taskStartTime"),__publicField(this,"storageQueueConsumer"),this.environment=N(e),this.browserEventLogger=N(t),this.customer=N(i),this.customerStorage=N(s),this.attributeStorage=N(n),this.activityStorage=N(r),this.user=N(o),this.statusModel=N(a),this.taskManager=N(l),L(this.customer.getIntegrationShopify().isPresent())}inTransaction(){return!0}isUnloading(){return!1}getStorageQueueConsumer(){return j.ofNullable(this.storageQueueConsumer)}run(){void 0===this.taskStartTime&&(this.taskStartTime=this.environment.getNowDate(),Mo.debug(`ShopifyStorageQueueTask start time: ${this.taskStartTime.getTime()}`));const e=this.environment.getNowDate().getTime()-this.taskStartTime.getTime(),t=this.waitForCurrencyRate(e);return t.isPresent()?(this.storageQueueConsumer=new Vl(this.environment,`${Se}${this.customer.getId()}_`,(e=>{kr(e,t.get(),this.customer,this.environment,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.statusModel,this.taskManager,this.browserEventLogger)})),this.storageQueueConsumer.start(),Nt.SUCCEEDED):Nt.TRY_LATER}waitForCurrencyRate(e){const t=this.environment.getWindow();if(void 0===t.Shopify)Mo.debug(`window.Shopify not defined after ${e} ms`);else{if(!hn(t.Shopify))return Mo.error("window.Shopify is not an object",245),j.of(zl);if(void 0===t.Shopify.currency)Mo.debug(`window.Shopify.currency not defined after ${e} ms`);else{if(!hn(t.Shopify.currency))return Mo.error("window.Shopify.currency is not an object",246),j.of(zl);if(void 0!==t.Shopify.currency.rate)return b(t.Shopify.currency.rate)?(Mo.debug(`window.Shopify.currency defined after ${e} ms (${t.Shopify.currency.rate})`),j.of(Number(t.Shopify.currency.rate))):(Mo.error("window.Shopify.currency.rate is not a non-empty string",247),j.of(zl));Mo.debug(`window.Shopify.currency.rate not defined after ${e} ms`)}}return e>=Wl?(Mo.error(`Timed out waiting for window.Shopify.currency (waited ${e}ms)`,248),j.of(zl)):j.empty()}}class Bl extends Bo{constructor(){super(...arguments),__publicField(this,"hasRun",!1),__publicField(this,"isRestartingStatus",!1),__publicField(this,"taskManager"),__publicField(this,"consentManagerApis"),__publicField(this,"environment"),__publicField(this,"override"),__publicField(this,"customer"),__publicField(this,"conditionEvaluationRuntime"),__publicField(this,"elementObserver"),__publicField(this,"pageContext"),__publicField(this,"customerStorage"),__publicField(this,"attributeStorage"),__publicField(this,"integrationDataStorage"),__publicField(this,"serverContextStorage"),__publicField(this,"intellimizeApi"),__publicField(this,"user"),__publicField(this,"browserStorage"),__publicField(this,"localStorage"),__publicField(this,"statusModel"),__publicField(this,"attributeData"),__publicField(this,"activityStorage"),__publicField(this,"internalApi"),__publicField(this,"hider"),__publicField(this,"browserEventLogger"),__publicField(this,"shopifyStorageQueueTask",j.empty()),__publicField(this,"extensionManager"),__publicField(this,"storageRestrictionProxy")}run(){return __async(this,null,(function*(){if(Mo.info("Starting client"),Mo.time("Client run time"),this.hasRun)return void Mo.error("Tried to run client again",143);Mo.time("new Environment");const e=Ua(window);this.environment=new Ta(window,e),Mo.configureEnvironment(this.environment),Mo.timeEnd("new Environment");if(void 0!==this.environment.getWindow().ipgvidtfr)return void Mo.error("Intellimize duplicate code detected on page.",144);this.environment.getWindow().ipgvidtfr=this.environment.getPageviewId(),this.extensionManager=Aa(),this.hider=new ua(this.environment,this.extensionManager),this.hider.startTimedRegionHider();const t=ii(this.environment);if(!t.isPresent())return void ua.revealAll(this.environment.getWindow().document);const i=ri(t.get());if(!i.isPresent())return void ua.revealAll(this.environment.getWindow().document);if(this.customer=i.get(),Mo.configureCustomer(this.customer),Mo.time("load json"),this.taskManager=new ll(this,this.environment),this.consentManagerApis=new ia(this.environment,this.customer,this.taskManager),this.override=new $a(this.environment,this.customer,this.consentManagerApis),this.override.shouldCheckOptOut().orElse(!0)&&this.consentManagerApis.checkAndProcessOptOutFromQueryParam(),!this.override.shouldRun().orElse(!0))return Mo.info("Client not running"),this.environment.getWindow().inrsgew=!0,void ua.revealAll(this.environment.getWindow().document);this.override.shouldHide().orElse(!0)||ua.revealAll(this.environment.getWindow().document),this.localStorage=new Qa(this.environment),this.intellimizeApi=new Pa(this.environment),this.intellimizeApi.initialize(),Mo.time("Setup storage"),this.override.isStorageAndOptimizationRestricted()?(this.storageRestrictionProxy=new Ya,this.browserStorage=this.storageRestrictionProxy):this.browserStorage=yield oi(this.localStorage,this.environment,this.taskManager,this.customer,this.override),this.user=new al(this.browserStorage,this.environment,this.customer),Mo.configureUser(this.user),this.statusModel=this.override.showStatusModule().orElse(!1)?new za(this.environment,this.customer,this.override,this.browserStorage):new ja,this.customerStorage=new Ga(this.customer,this.environment,this.browserStorage,this.override);const s=this.customerStorage.getSessionId(),n=this.customerStorage.updateAndGetSessionId(),r=!s.map((e=>e===n)).orElse(!1);if(this.customer.isCampaignControl()||this.customerStorage.setAndGetCustomerControl(this.customer),this.attributeStorage=new Ba(this.customer,this.environment,this.browserStorage,this.user),this.activityStorage=new Gr(this.customer,this.browserStorage,this.environment,this.override),Zt(this.environment,this.attributeStorage,this.customer),this.integrationDataStorage=new Ha(this.customer,this.browserStorage),ar(this.customer)&&lr(this.environment,this.integrationDataStorage,this.customer),this.serverContextStorage=new qa(this.customer,this.browserStorage,this.override),Mo.timeEnd("Setup storage"),this.environment.addUnloadHandler((()=>{try{this.handlePageUnload()}catch(e){const t=new Ei("Exception in ClientV5 unload handler",e);Mo.error(t,240)}})),this.attributeData=new No(this.environment,void 0,this.attributeStorage,this.override,this.customerStorage,this.integrationDataStorage,this.user),this.conditionEvaluationRuntime=new Go(this.attributeData,this.activityStorage),this.customer.getLimitedModeCondition().isPresent()){const e=this.customer.getLimitedModeCondition().get();let t=!1;try{t=e.evaluate(this.conditionEvaluationRuntime)}catch(o){t=!1;const e=new Ei('"Limited mode" condition evaluation failed',o);Mo.error(e,261)}if(Mo.debug(`check "limited mode" => ${t}`),t)return this.environment.getWindow().inrsgew=!0,void ua.revealAll(this.environment.getWindow().document)}this.override.isStorageAndOptimizationRestricted()||($l.shouldWaitForServerContext(this.customer,this.environment,this.integrationDataStorage,this.serverContextStorage,r)?yield this.reinitializeServerContextStorage(r):this.reinitializeServerContextStorage(r)),this.postServerContextWork(),this.hasRun=!0,Mo.timeEnd("Client run time"),this.taskManager.runNow()}))}reinitializeServerContextStorage(e=!0){return __async(this,null,(function*(){return new Promise((t=>{const i=new $l(this.environment,this.customer,this.user,this.integrationDataStorage,this.serverContextStorage,e);i.onDone(t),this.taskManager.addTask(i)}))}))}unrestrictProxyAndReinitializeStorage(){return __async(this,null,(function*(){var e;if(!0===(null==(e=this.storageRestrictionProxy)?void 0:e.isRestricted())&&!this.override.isStorageAndOptimizationRestricted()){const e=yield oi(this.localStorage,this.environment,this.taskManager,this.customer,this.override);this.storageRestrictionProxy.unrestrictProxyAndReplaceBrowserStorage(e),this.user.reinitializeUser(),Mo.configureUser(this.user),this.customer.isCampaignControl()||this.customerStorage.setAndGetCustomerControl(this.customer),yield this.reinitializeServerContextStorage(),this.serverContextStorage.get().ifPresent((e=>{Mo.configureServerContext(e),this.attributeData.setServerContext(e),this.browserEventLogger.setServerContext(e),this.internalApi.setServerContext(e)})).ifAbsent((()=>{Mo.error("Expected ServerContext is missing",146)}))}}))}reRun(e){Mo.info("Client.reRun()"),this.isRestartingStatus=!0,this.handlePageUnload(),this.environment.getWindow().setTimeout((()=>{this.taskManager.stop(),this.taskManager.onDone((()=>__async(this,null,(function*(){this.statusModel.reinitializeExperienceVariations(),this.environment.reinitialize(),this.taskManager.addTask(new Cl(this.elementObserver)),void 0!==this.storageRestrictionProxy&&(yield this.unrestrictProxyAndReinitializeStorage()),this.customerStorage.updateAndGetSessionId(),Zt(this.environment,this.attributeStorage,this.customer),ar(this.customer)&&lr(this.environment,this.integrationDataStorage,this.customer),this.attributeData.reinitialize(),this.internalApi.reinitialize(),e.forEach((e=>{Dt.executeFunction(e,150)})),this.activityStorage.add(Gr.buildPageviewActivity(this.customer,this.environment,this.user,this.attributeStorage,this.customerStorage)),ei(this.customer,this.statusModel,this.environment.getWindow().document),this.pageContext.reinitialize(),this.scheduleTasks(this.browserEventLogger),this.isRestartingStatus=!1}))))}),0)}isRestarting(){return Mo.debug(`Client.isRestarting() => ${this.isRestartingStatus}`),this.isRestartingStatus}postServerContextWork(){Mo.time("postServerContextWork");const e=this.serverContextStorage.get();if(!e.isPresent())return void Mo.error("Expected ServerContext is missing",146);const t=e.get();Mo.debug(`postServerContextWork() serverContext: ${Le(t)}`),Mo.configureServerContext(t),this.attributeData.setServerContext(t),this.elementObserver=this.override.useElementObserver().orElse(!0)?new va:new ra,this.taskManager.addTask(new Cl(this.elementObserver)),this.pageContext=new Va(this.environment,this.browserStorage,this.extensionManager,this.customer,this.conditionEvaluationRuntime,this.attributeData,this.override,this.taskManager,this.hider,this.elementObserver,this.statusModel),this.browserEventLogger=new jo(this.customer,this.environment,this.user,this.attributeStorage,this.pageContext,t,this.customerStorage,void 0,this.override);const i=new eo(this.environment,this.extensionManager,this.browserEventLogger,this.taskManager,this.consentManagerApis,this.customerStorage,this.attributeStorage,this.activityStorage,this.integrationDataStorage,this.customer,this.user,this.override,this.statusModel,this.intellimizeApi.extractCallQueue(),this.intellimizeApi.extractReadyCallbackQueue(),(e=>{this.addVariationRecordedCallbackTuple(e)}));i.initialize(),this.internalApi=new Ma(this.customer,this.user,this.environment,this.extensionManager,t,this.taskManager,this.browserStorage,this.customerStorage,i,this.attributeData,this.elementObserver),this.internalApi.initialize(),this.activityStorage.add(Gr.buildPageviewActivity(this.customer,this.environment,this.user,this.attributeStorage,this.customerStorage)),this.override.isStorageAndOptimizationRestricted()||ei(this.customer,this.statusModel,this.environment.getWindow().document),this.pageContext.reinitialize(),this.scheduleTasks(this.browserEventLogger),this.override.injectVisualEditor().orElse(!1)&&this.environment.injectScript("https://intellimizeditor.com/common.js"),Mo.timeEnd("postServerContextWork")}scheduleTasks(e){if(Mo.debug("scheduleTasks()"),this.override.shouldRunTasks().orElse(!0)){const t=new Rl(this.environment,this.browserStorage,this.extensionManager,this.browserEventLogger,this.customer,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.override,this.pageContext,this.attributeData,this.statusModel,this.conditionEvaluationRuntime,this.hider,this.getVariationRecordedCallbackTuples.bind(this),this.elementObserver);t.onPolicyReady((t=>{void 0!==e&&e.setPolicy(t),Mo.debug("scheduleTasks() onPolicyReady()"),this.override.sendPageviewBrowserEvents().orElse(!0)&&this.taskManager.addTask(new kl(this.environment,this.browserEventLogger));const i=new Sl(this.environment,this.browserEventLogger,this.customer,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.override,this.pageContext,this.statusModel),s=new cl(this.environment,this.browserEventLogger,this.integrationDataStorage);if(this.taskManager.addTask(s),this.taskManager.addTask(i),this.customer.getIntegrationShopify().isPresent()){Mo.debug("Shopify Pixel enabled for customer");const e=new jl(this.environment,this.browserEventLogger,this.customer,this.customerStorage,this.attributeStorage,this.activityStorage,this.user,this.statusModel,this.taskManager);this.shopifyStorageQueueTask=j.of(e),this.taskManager.addTask(e)}if(gr(this.customer)){if(this.environment.getHostingPageUrl().getQueryParam(Ne,b).isPresent()){const e=new Il(this.environment,this.customer,"/marketo-id-associator",(()=>pr(this.environment)));this.taskManager.addTask(e)}const e=new Al(this.environment,this.browserEventLogger,this.customer,this.user,this.override,this.activityStorage,this.attributeStorage,this.customerStorage,this.statusModel);this.taskManager.addTask(e)}if(dr(this.customer)){if(this.environment.getHostingPageUrl().getQueryParam(Oe,b).isPresent()){const e=new Il(this.environment,this.customer,"/hubspot-id-associator",(()=>ur(this.environment)));this.taskManager.addTask(e)}const e=new Tl(this.environment,this.browserEventLogger,this.customer,this.user,this.override,this.activityStorage,this.attributeStorage,this.customerStorage,this.statusModel);this.taskManager.addTask(e)}})),this.taskManager.addTask(t)}else this.override.sendPageviewBrowserEvents().orElse(!0)&&this.taskManager.addTask(new kl(this.environment,this.browserEventLogger))}handlePageUnload(){Mo.debug("(Prepare for Shutdown): handlePageUnload() giving the client one more chance to run tasks..."),this.shopifyStorageQueueTask.ifPresent((e=>{e.getStorageQueueConsumer().ifPresent((e=>{e.handlePageUnload()}))}));this.taskManager.findTasks((e=>!e.isDone())).length>0&&(this.taskManager.addTask(new Fl),this.taskManager.runNow())}}function Gl(e,t){return i=>{Mo.time("frameTick",["raf"]);const s=null!=t?t:i;i-s<e&&window.requestAnimationFrame(Gl(e,s)),Mo.timeEnd("frameTick",["raf"])}}function Hl(){new MutationObserver((e=>{na.time(`mutations: ${e.length}`),e.forEach((e=>{na.debug(`Doc mutation: ${$e(e)}`)})),na.timeEnd(`mutations: ${e.length}`)})).observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,characterData:!0,attributeOldValue:!0,characterDataOldValue:!0})}(()=>{__async(this,null,(function*(){try{Mo.time("client startup time"),Mo.getLevel()<=i.LogLevel.DEBUG&&(window.requestAnimationFrame(Gl(2e3)),Hl());const e=new Bl;yield e.run(),Mo.timeEnd("client startup time")}catch(e){ua.revealAll(document);const t=fi(e);Mo.error(t,142)}}))})()}();

