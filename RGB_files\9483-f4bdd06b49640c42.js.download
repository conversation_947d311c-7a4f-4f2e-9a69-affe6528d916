!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="3c60eed8-c26f-4d0e-bb37-1cd1b7c565ca",e._sentryDebugIdIdentifier="sentry-dbid-3c60eed8-c26f-4d0e-bb37-1cd1b7c565ca")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9483],{61864:(e,n,t)=>{t.d(n,{t:()=>b});var l=t(54568),a=t(85906),i=t(7620),o=t(5467),s=t(62942),c=t(62904),r=t(5474),d=t(26268),u=t(85610),f=t.n(u);let h={pill:(e,n,t,l,a)=>f()("\n      xs:text-xs\n      md:text-sm\n      lg:text-base\n      text-start\n\n      ".concat(l&&"flex flex-col","\n      ").concat(t,"\n      ").concat(n&&e,"\n      ").concat(a&&"block py-2 lg:text-xl font-semibold","\n    ")),pillIconClass:e=>f()("\n      ".concat(e,"\n    ")),productCardsV3Pills:f()("\n    flex\n    justify-evenly\n    relative\n    transition-all\n    w-auto\n    gap-2\n  "),tabsDropdownContainer:"bg-white w-full flex justify-center my-8",buttonAndImageContainer:(e,n)=>f()("\n      w-full      \n      ".concat(e?"hidden lg:inline":"","\n      ").concat(n?"lg:flex":"flex-col","\n      gap-4\n    ")),buttonsContainer:(e,n,t)=>f()(n?"\n    justify-start\n    flex\n    flex-wrap\n    gap-0\n    w-full\n    border-b-2 border-b-gainsboro\n    ".concat(t&&"flex-col border-none max-w-190px lg:sticky top-[140px] lg:h-[450px]","\n    "):"\n    overflow-hidden\n    flex\n    flex-wrap\n    justify-center\n    w-full\n    gap-y-5\n\n    ".concat(n&&0!=e?"lg:my-16 my-8":n?"lg:mb-16 mb-8":"","\n    ").concat(n&&e>4?"gap-x-5":"","\n    ").concat(n?"":"gap-x-16 lg:my-16 my-8","\n  ")),singleTabButtonContainer:(e,n)=>f()("\n      w-full\n      flex\n      justify-center\n      ".concat(e&&"lg:justify-start text-xl font-semibold","\n      ").concat(e&&n&&"mt-4 border-t border-dark-tan pt-4 max-w-190px","\n    ")),tabContent:(e,n,t,l)=>f()("\n      relative\n      ".concat(e,"\n      ").concat(n?t:"","\n      ").concat(l?"[&_img]:box-border [&_img]:border-[1px] [&_img]:border-tan":"","\n    ")),tabPanel:e=>f()("\n      transition-all\n      left-0\n      ".concat(e?"opacity-100 visible":"invisible opacity-0","\n      w-full \n      ").concat(e?"transition duration-300 delay-100 opacity-100 visible z-10":"absolute transition duration-300 opacity-0","\n    ")),tabs:e=>f()("\n      ".concat(e,"\n    ")),wrapper:(e,n)=>f()("\n      flex\n      relative\n      w-full\n      flex-col\n\n      ".concat(n&&"border-b-2 border-b-gainsboro","\n      ").concat(e,"\n    "))},b=e=>{var n,t,u;let{activePillClass:f="",className:b="",isInlineTabButtons:w=!1,nextBtns:p,pillClass:x="",pillIconClass:m="",previousBtns:g,tabContentClass:v="",activeTabContentClass:y="",tabsContainerClass:j,tabs:C,wrapperClass:N="",isPricingTabs:E=!1,isNewPricingTabs:S=!1,sidebarTitle:k="",children:I}=e,[A,L]=(0,i.useState)(-1),[_,z]=(0,i.useState)(0),D=(0,d.rd)(),R=(0,s.useSearchParams)(),P=(0,d.a8)(),U=e=>{let n=new URL(window.location.href);n.hash=C[e].title.toLowerCase().replace(/\s+/g,"-"),window.history.pushState(null,"",n.toString())},V=(0,i.useCallback)(()=>{if(P.includes("/legal")||P.includes("/news")){let e=window.location.hash.replace("#",""),n=C.findIndex(n=>n.title.toLowerCase().replace(/\s+/g,"-")===e);-1!==n&&L(n)}},[P,C]);(0,i.useEffect)(()=>{V();let e=()=>{window.location.hash&&window.location.reload()};return window.addEventListener("hashchange",V),window.addEventListener("popstate",e),()=>{window.removeEventListener("hashchange",V),window.addEventListener("popstate",e)}},[C,V]);let T=e=>{L(e),U(e),window.intellimize&&"function"==typeof window.intellimize.activate&&window.intellimize.activate()};(0,i.useEffect)(()=>{{let e=new URL(window.location.href),n=e.hash.replace("#","");if(n&&"undefined"!==n){let t=C.findIndex(e=>e.tabAnchor===n);-1!==t?L(t):(C[0].tabAnchor&&(e.hash=C[0].tabAnchor),C[0].tabAnchor&&window.history.pushState({},"",e.toString()))}else C[0].tabAnchor&&(e.hash=C[0].tabAnchor,window.history.pushState({},"",e.toString())),L(0)}},[C]);let B=(0,i.useCallback)(()=>{let e=P.split("#")[1];if(e&&"undefined"!==e){let n=C.findIndex(n=>n.title.toLowerCase().replace(/\s+/g,"-")===e);n>=-1?L(n):L(0)}},[P,C]);(0,i.useEffect)(()=>{("/legal"===P||"/news"===P)&&B()},[B,P]);let{innerWidth:K}=(0,o.l)(),{isBelowLg:H,isBelowSm:O}=(0,a.d)(),q=(0,i.useRef)([]),F=(0,i.useRef)([]),G=(0,i.useRef)(null),J=e=>{let{key:n,currentTarget:t}=e,l=t.previousElementSibling,a=null==l?void 0:l.previousElementSibling,i=t.nextElementSibling,o=null==i?void 0:i.nextElementSibling;["ArrowLeft","ArrowUp"].includes(n)&&a?a.focus():["ArrowRight","ArrowDown"].includes(n)&&o&&o.focus()},M=(0,i.useCallback)(()=>A<C.length-1&&L(A+1),[A,C.length]),Q=(0,i.useCallback)(()=>A>0&&L(A-1),[A]);(0,i.useEffect)(()=>(null==p||p.forEach(e=>{null==e||e.addEventListener("click",M)}),null==g||g.forEach(e=>{null==e||e.addEventListener("click",Q)}),()=>{null==p||p.forEach(e=>{null==e||e.removeEventListener("click",M)}),null==g||g.forEach(e=>{null==e||e.removeEventListener("click",Q)})}),[M,Q,p,g]),(0,i.useEffect)(()=>{var e;z((null==(e=q.current[0])?void 0:e.offsetHeight)||0)},[K]);let W=null==C?void 0:C.map(e=>{let{title:n}=e;return{label:n,value:n}}),X=C.filter(e=>"string"==typeof e.tabAnchor).map(e=>{let{tabAnchor:n}=e;return{anchor:n}}),Y=e=>{L(e),T(e)};return(0,l.jsx)("section",{"data-section":"tabs","data-pricingtabs":E?"true":void 0,children:(0,l.jsx)("div",{className:h.tabs(b),children:(0,l.jsxs)("div",{className:h.wrapper(N,!1),children:[w&&(0,l.jsx)(()=>(0,l.jsx)("div",{className:h.productCardsV3Pills,ref:G,children:null==C?void 0:C.map((e,n)=>{let{title:t,icon:a,subtitle:i}=e,o=A===n;return(0,l.jsxs)("button",{"aria-label":", ".concat(n+1," of ").concat(C.length," tab items"),"aria-hidden":H&&!o,"aria-expanded":o,className:h.pill(f,o,x,i,S),onClick:()=>Y(n),onKeyDown:e=>H&&!o&&J(e),ref:e=>{q.current[n]=e},tabIndex:H&&!o?-1:0,"data-testid":"tab-".concat(t),children:[a&&(0,l.jsx)(r._,{"aria-hidden":!0,className:h.pillIconClass(m),src:a,width:20,height:20,alt:t}),t]},n)})}),{}),(0,l.jsxs)("div",{className:h.buttonAndImageContainer(E,S),children:[!w&&(O?(0,l.jsx)(()=>(0,l.jsx)("div",{className:h.tabsDropdownContainer,children:(0,l.jsx)(c.m,{label:"Section",options:C.map((e,n)=>({label:e.title,value:String(n)})),onChange:e=>L(Number(e)),selectedValue:String(A),variant:"tertiary",className:"[&_button]:flex [&_button]:items-center",nonTransparentOptions:!0})}),{}):(0,l.jsx)(()=>(0,l.jsxs)("div",{className:null!=j?j:h.buttonsContainer(C.length,E,S),children:[S&&!!k&&C.length&&(0,l.jsx)("h5",{className:"uppercase text-deep-blue",children:k}),null==C?void 0:C.map((e,n)=>{let{content:t,icon:a,title:i,subtitle:o,tabAnchor:s,hasSeparator:c}=e,d=A===n;return t&&(0,l.jsx)("div",{children:(0,l.jsx)("div",{className:h.singleTabButtonContainer(S,!!c),children:(0,l.jsxs)("button",{"aria-label":", ".concat(n+1," of ").concat(C.length," tab items"),"aria-expanded":d,className:h.pill(f,d,x,o,S),onClick:()=>{if(Y(n),s)if(E){let e=new URL(window.location.href);e.hash=s,window.history.pushState({},"",e.toString()),window.intellimize&&"function"==typeof window.intellimize.activate&&window.intellimize.activate()}else D.replace("".concat(P,"?").concat(null==R?void 0:R.toString(),"#").concat(s),{scroll:!1})},onKeyDown:J,ref:e=>{q.current[n]=e},"data-pricingtabs":E?"true":void 0,"data-tabanchor":s?"".concat(s):void 0,"data-testid":"tab-".concat(i),children:[a&&(0,l.jsx)(r._,{"aria-hidden":!0,className:h.pillIconClass(m),src:a,width:20,height:20,alt:i}),(0,l.jsx)("span",{className:"transition-colors duration-100 ease-in-out hover:text-dp-purple",children:i}),o&&!S&&(0,l.jsxs)("span",{className:"font-light",children:[" ",o]})]})})},n)}),I]}),{})),(0,l.jsx)("div",{className:"relative w-full",children:null==C?void 0:C.map((e,n)=>{let{content:t,showBorder:a}=e,i=A===n;return(0,l.jsx)("div",{className:h.tabPanel(i),children:(0,l.jsx)("div",{className:h.tabContent(v,i,y,a),children:t})},n)})})]}),E&&(0,l.jsxs)("div",{className:"flex w-full flex-col items-center gap-6 lg:hidden",children:[(0,l.jsx)(c.m,{size:"small",className:"inline w-full lg:hidden",onChange:e=>{let n=C.findIndex(n=>n.title===e);L(n),U(n),window.intellimize&&"function"==typeof window.intellimize.activate&&window.intellimize.activate()},selectedValue:null!=(u=null==(n=C[A])?void 0:n.title)?u:"",variant:"tertiary",label:null==(t=W[X.findIndex(e=>{var n;return e.anchor===(null==(n=C[A])?void 0:n.tabAnchor)})])?void 0:t.label,options:W,tabAnchors:X,disableRouter:!0}),null==C?void 0:C.map((e,n)=>{var t;let{content:a}=e,i=(null==(t=C[A])?void 0:t.title)===C[n].title;return i?(0,l.jsx)("div",{className:"".concat(E&&"inline w-full lg:hidden"," ").concat(!i&&"hidden"),children:(0,l.jsx)("div",{className:h.tabPanel(i),ref:e=>{F.current[n]=e},style:{top:_},children:(0,l.jsx)("div",{className:h.tabContent(v,i,y),children:a})})},n):null})]})]})})})}}}]);