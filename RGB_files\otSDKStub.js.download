var OneTrustStub=(t=>{function e(t,r,s,u){return new(s=s||Promise)(function(i,e){function n(t){try{o(u.next(t))}catch(t){e(t)}}function a(t){try{o(u.throw(t))}catch(t){e(t)}}function o(t){var e;t.done?i(t.value):((e=t.value)instanceof s?e:new s(function(t){t(e)})).then(n,a)}o((u=u.apply(t,r||[])).next())})}function u(n,a){var o,r,s,u={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},l=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return l.next=t(0),l.throw=t(1),l.return=t(2),"function"==typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l;function t(i){return function(t){var e=[i,t];if(o)throw new TypeError("Generator is already executing.");for(;u=l&&e[l=0]?0:u;)try{if(o=1,r&&(s=2&e[0]?r.return:e[0]?r.throw||((s=r.return)&&s.call(r),0):r.next)&&!(s=s.call(r,e[1])).done)return s;switch(r=0,(e=s?[2&e[0],s.value]:e)[0]){case 0:case 1:s=e;break;case 4:return u.label++,{value:e[1],done:!1};case 5:u.label++,r=e[1],e=[0];continue;case 7:e=u.ops.pop(),u.trys.pop();continue;default:if(!(s=0<(s=u.trys).length&&s[s.length-1])&&(6===e[0]||2===e[0])){u=0;continue}if(3===e[0]&&(!s||e[1]>s[0]&&e[1]<s[3]))u.label=e[1];else if(6===e[0]&&u.label<s[1])u.label=s[1],s=e;else{if(!(s&&u.label<s[2])){s[2]&&u.ops.pop(),u.trys.pop();continue}u.label=s[2],u.ops.push(e)}}e=a.call(n,u)}catch(t){e=[6,t],r=0}finally{o=s=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}}}var i,a,o,s,n,c=new function(){this.optanonCookieName="OptanonConsent",this.optanonHtmlGroupData=[],this.optanonHostData=[],this.genVendorsData=[],this.vendorsServiceData=[],this.IABCookieValue="",this.oneTrustIABCookieName="eupubconsent",this.oneTrustIsIABCrossConsentEnableParam="isIABGlobal",this.isStubReady=!0,this.geolocationCookiesParam="geolocation",this.EUCOUNTRIES=["BE","BG","CZ","DK","DE","EE","IE","GR","ES","FR","IT","CY","LV","LT","LU","HU","MT","NL","AT","PL","PT","RO","SI","SK","FI","SE","GB","HR","LI","NO","IS"],this.stubFileName="otSDKStub",this.DATAFILEATTRIBUTE="data-domain-script",this.bannerScriptName="otBannerSdk.js",this.domPurifyScriptName="otDomPurify.js",this.mobileOnlineURL=[],this.isMigratedURL=!1,this.migratedCCTID="[[OldCCTID]]",this.migratedDomainId="[[NewDomainId]]",this.userLocation={country:"",state:"",stateName:""}},r=((y=i=i||{})[y.Days=1]="Days",y[y.Weeks=7]="Weeks",y[y.Months=30]="Months",y[y.Years=365]="Years",(y=n=n||{}).GDPR="GDPR",y.CCPA="CCPA",y.IAB2="IAB2",y.IAB2V2="IAB2V2",y.GENERIC="GENERIC",y.LGPD="LGPD",y.GENERIC_PROMPT="GENERIC_PROMPT",y.CPRA="CPRA",y.CDPA="CDPA",y.DELAWARE="DELAWARE",y.IOWA="IOWA",y.NEBRASKA="NEBRASKA",y.USNATIONAL="USNATIONAL",y.CUSTOM="CUSTOM",y.FLORIDA="FLORIDA",y.COLORADO="COLORADO",y.CONNECTICUT="CTDPA",y.MONTANA="MONTANA",y.TEXAS="TEXAS",y.OREGON="OREGON",y.TENNESSEE="TENNESSEE",y.NEWJERSEY="NEWJERSEY",y.NEWHAMPSHIRE="NEWHAMPSHIRE",y.UCPA="UCPA",y.VIRGINIA="VIRGINIA",n.CPRA,n.CDPA,n.COLORADO,n.OREGON,n.CONNECTICUT,n.FLORIDA,n.MONTANA,n.TEXAS,n.DELAWARE,n.IOWA,n.NEBRASKA,n.TENNESSEE,n.NEWJERSEY,n.NEWHAMPSHIRE,n.UCPA,(y=b=b||{}).Name="OTGPPConsent",y[y.ChunkSize=4e3]="ChunkSize",y.ChunkCountParam="GPPCookiesCount",y.gppSid="gppSid",(n=a=a||{}).CPRA="usca",n.CCPA="usca",n.CDPA="usva",n.OREGON="usor",n.USNATIONAL="usnat",n.COLORADO="usco",n.FLORIDA="usfl",n.CTDPA="usct",n.MONTANA="usmt",n.TEXAS="ustx",n.DELAWARE="usde",n.IOWA="usia",n.NEBRASKA="usne",n.TENNESSEE="ustn",n.NEWJERSEY="usnj",n.NEWHAMPSHIRE="usnh",n.UCPA="usut",n.VIRGINIA="usva",n.IAB2V2="tcfeuv2",(y=o=o||{})[y.CPRA=8]="CPRA",y[y.CCPA=8]="CCPA",y[y.CDPA=9]="CDPA",y[y.OREGON=15]="OREGON",y[y.USNATIONAL=7]="USNATIONAL",y[y.COLORADO=10]="COLORADO",y[y.FLORIDA=13]="FLORIDA",y[y.MONTANA=14]="MONTANA",y[y.TEXAS=16]="TEXAS",y[y.DELAWARE=17]="DELAWARE",y[y.IOWA=18]="IOWA",y[y.NEBRASKA=19]="NEBRASKA",y[y.NEWHAMPSHIRE=20]="NEWHAMPSHIRE",y[y.NEWJERSEY=21]="NEWJERSEY",y[y.TENNESSEE=22]="TENNESSEE",y[y.UCPA=11]="UCPA",y[y.VIRGINIA=9]="VIRGINIA",y[y.CTDPA=12]="CTDPA",y[y.IAB2V2=2]="IAB2V2","geo"),l="otpreview",p=(b.Name,"PRODUCTION"),d=(0,i.Days,i.Weeks,i.Months,i.Years,h.prototype.camelize=function(t){return(t=t.replace("--","")).split("-").map(function(t,e){var i=t?t[0].toUpperCase()+t.slice(1):"";return 0===e?t:i}).join("")},h.prototype.strToObj=function(t){for(var e={},i=t.split(";").map(function(t){return t.trim()}),n=0,a=void 0;n<i.length;++n)if(/:/.test(i[n])){if(!(a=i[n].split(/:(.+)/))[1])return null;e[this.camelize(a[0])]=a[1].trim()}return e},h);function h(){var t=this;this.implementThePolyfill=function(){var a=t,o=Element.prototype.setAttribute;return Element.prototype.setAttribute=function(t,e){if("style"!==t.toLowerCase()&&o.apply(this,[t,e]),"style"!==t.toLowerCase()||e||this.removeAttribute("style"),"style"===t.toLowerCase()&&e){this.removeAttribute("style");var i,n=a.strToObj(e);for(i in n)this.style[i]=n[i]}},!0}}function m(t,e,i){void 0===i&&(i=!1);function n(t){return t?(";"!==(t=t.trim()).charAt(t.length-1)&&(t+=";"),t.trim()):null}var o=n(t.getAttribute("style")),r=n(e),e="",e=i&&o?(()=>{for(var t=o.split(";").concat(r.split(";")).filter(function(t){return 0!==t.length}),e="",i="",n=t.length-1;0<=n;n--){var a=t[n].substring(0,t[n].indexOf(":")+1).trim();e.indexOf(a)<0&&(e+=a,i+=t[n]+";")}return i})():r;t.setAttribute("style",e)}(n=s=s||{}).ping="ping",n.addEventListener="addEventListener",n.removeEventListener="removeEventListener",n.hasSection="hasSection",n.getSection="getSection",n.getField="getField",n.getGPPData="getGPPData";var g=new function(){var r=this;this.LOCATOR_NAME="__gppLocator",this.win=window,this.customInit="CUSTOMINIT",this.init=function(){r.win.__gpp&&"function"==typeof r.win.__gpp||(r.win.__gpp=r.executeGppApi,window.addEventListener("message",r.messageHandler,!1),r.addFrame(r.LOCATOR_NAME))},this.removeGppApi=function(){delete r.win.__gpp;var t=document.querySelectorAll("iframe[name="+r.LOCATOR_NAME+"]")[0];t&&t.parentElement.removeChild(t)},this.executeGppApi=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var i=null==(i=r.win)?void 0:i.__gpp;if(i.queue=i.queue||[],i.events=i.events||[],!t.length||1===t.length&&"queue"===t[0])return i.queue;if(1===t.length&&"events"===t[0])return i.events;var n=t[0],a=1<t.length?t[1]:null,o=2<t.length?t[2]:null;switch(n){case s.ping:return r.getPingRequest(a);case s.addEventListener:return r.addEventListener(a,o);case s.removeEventListener:return r.removeEventListener(o);default:return void r.addToQueue(n,a,o)}},this.getPingRequest=function(t){var i,n,e={gppVersion:1.1,cmpStatus:"stub",cmpDisplayStatus:"hidden",signalStatus:"not ready",supportedAPIs:(i=[],n={},Object.keys(o).forEach(function(t){var e={},t=(e[t]=o[t],Object.assign(e,n));n=t}),Object.keys(a).map(function(t){return{name:t,value:a[t]}}).forEach(function(t){t=n[t.name]+":"+t.value;i.push(t)}),i.filter(function(t,e){return i.indexOf(t)===e})),currentAPI:"",cmpId:Number.parseInt("28"),sectionList:[],applicableSections:[0],gppString:"",parsedSections:{}};return t&&t(e,!0),e},this.addFrame=function(t){var e,i=r.win.document,n=Boolean(r.win.frames[t]);return n||(i.body?((e=i.createElement("iframe")).style.cssText="display:none",e.name=t,e.setAttribute("title","GPP Locator"),i.body.appendChild(e)):setTimeout(function(){r.addFrame(t)},5)),!n},this.addEventListener=function(t,e){var i,n=r.win.__gpp;return n.events=n.events||[],null!=(i=n)&&i.lastId||(n.lastId=0),n.lastId++,n.events.push({id:n.lastId,callback:t,parameter:e}),{eventName:"listenerRegistered",listenerId:n.lastId,data:!0,pingData:r.getPingRequest()}},this.removeEventListener=function(e){var i=!1,t=r.win.__gpp;return t.events=t.events||[],t.events=t.events.filter(function(t){return t.id.toString()!==e.toString()||!(i=!0)}),{eventName:"listenerRemoved",listenerId:e,data:i,pingData:r.getPingRequest()}},this.addToQueue=function(t,e,i){var n=r.win.__gpp;n.queue=n.queue||[],n.queue.push([t,e,i])},this.messageHandler=function(i){var e,n,a="string"==typeof i.data;try{e=a?JSON.parse(i.data):i.data}catch(t){e=null}e&&e.__gppCall&&(n=e.__gppCall,(0,r.win.__gpp)(n.command,function(t,e){t={__gppReturn:{returnValue:t,success:e,callId:n.callId}};i&&i.source&&i.source.postMessage&&i.source.postMessage(a?JSON.stringify(t):t,i.origin||"*")},n.parameter))},this.customInit||this.init()},f=(A.initCSPTrustedType=function(t){var n=new URL(t,location.origin);window.DOMPurify&&window.trustedTypes&&window.trustedTypes.createPolicy&&(window.OtTrustedType.TrustedTypePolicy=window.trustedTypes.createPolicy("ot-trusted-type-policy",{createHTML:function(t){return window.DOMPurify.sanitize(t)},createScript:function(t){return window.DOMPurify.sanitize(t)},createScriptURL:function(t){var e,i=[document.location.hostname,n.hostname];try{e=new URL(t,location.origin)}catch(t){return"about:blank#error"}return e.hostname&&!i.includes(e.hostname)?"about:blank#blocked":e.href}}))},A.isCspTrustedType=function(){var t;return(null==(t=window.OtTrustedType)?void 0:t.isCspTrustedTypeEnabled)&&(null==(t=window.OtTrustedType)?void 0:t.TrustedTypePolicy)},A.createScriptURL=function(t){return A.isCspTrustedType()?window.OtTrustedType.TrustedTypePolicy.createScriptURL(t):t},A.checkAndAssignCspTrustedTypeEnabled=function(t){t=null==(t=t.TenantFeatures)?void 0:t.CookieV2CSPTrustedType;return window.OtTrustedType={isCspTrustedTypeEnabled:t},t},A);function A(){}v.prototype.initConsentSDK=function(){this.initCustomEventPolyfill(),this.ensureHtmlGroupDataInitialised(),this.setStubScriptElement(),this.setOTDataLayer(),this.getParam(),this.fetchBannerSDKDependency(),this.captureNonce()},v.prototype.captureNonce=function(){this.nonce=c.stubScriptElement.nonce||c.stubScriptElement.getAttribute("nonce")||null},v.prototype.fetchBannerSDKDependency=function(){this.setDomainDataFileURL(),this.crossOrigin=c.stubScriptElement.getAttribute("crossorigin")||null,this.previewMode="true"===c.stubScriptElement.getAttribute("data-preview-mode"),this.otFetch(c.bannerDataParentURL,this.getLocation.bind(this))},v.prototype.setDomainIfBulkDomainEnabled=function(t){var e=t&&t.TenantFeatures,i=window.location.hostname,n=t.Domain,a=t.BulkDomainCheckUrl;e&&e.CookieV2BulkDomainManagement&&i!==n&&t.ScriptType===p&&((e=window.sessionStorage)&&e.getItem("bulkDomainMgmtEnabled")?this.handleBulkDomainMgmt({isValid:"true"===window.sessionStorage.getItem("bulkDomainMgmtEnabled")},t):(n={location:c.storageBaseURL.replace(/^https?:\/\//,""),domainId:this.domainId,url:i},this.otFetch(a,this.handleBulkDomainMgmt,!1,n,t)))},v.prototype.getLocation=function(t){if(this.setDomainIfBulkDomainEnabled(t),this.updateVersion(t),(t.TenantFeatures&&t.TenantFeatures.CookieV2CSP||t.CookieV2CSPEnabled)&&this.nonce&&(this.setAttributePolyfillIsActive=!0,(new d).implementThePolyfill()),f.checkAndAssignCspTrustedTypeEnabled(t)&&f.initCSPTrustedType(c.storageBaseURL),!t.RuleSet[0].Type)return this.iabTypeAdded=!1,window.__tcfapi=this.executeTcfApi,this.intializeIabStub(),this.addBannerSDKScript(t);var e,i=window;i.OneTrust&&i.OneTrust.geolocationResponse?(i=i.OneTrust.geolocationResponse,this.setGeoLocation(i.countryCode,i.stateCode,i.stateName),this.addBannerSDKScript(t)):(i=this.readCookieParam(c.optanonCookieName,c.geolocationCookiesParam))||t.SkipGeolocation?(e=i.split(";")[0],i=i.split(";")[1],this.setGeoLocation(e,i),this.addBannerSDKScript(t)):this.getGeoLocation(t)},v.prototype.handleBulkDomainMgmt=function(t,e){window.sessionStorage&&window.sessionStorage.setItem("bulkDomainMgmtEnabled",JSON.stringify(t.isValid)),t.isValid&&(e.Domain=window.location.hostname)},v.prototype.getGeolocationURL=function(t){t.TenantFeatures;var e=""+c.stubScriptElement.getAttribute("src").split(c.stubFileName)[0]+t.Version;return new RegExp("^file://","i").test(e)&&t.MobileSDK?(e="/"+t.GeolocationUrl.replace(/^(http|https):\/\//,"").split("/").slice(1).join("/")+".js",c.storageBaseURL+e):t.GeolocationUrl},v.prototype.geoLocationJsonCallback=function(t,e){e&&this.setGeoLocation(e.country,e.state,e.stateName),this.addBannerSDKScript(t)},v.prototype.getGeoLocation=function(t){var e=this.getGeolocationURL(t);this.otFetch(e,this.geoLocationJsonCallback.bind(this,t),!0)},v.prototype.setOTDataLayer=function(){var t="data-dLayer-ignore",e=c.stubScriptElement.hasAttribute(t),t=c.stubScriptElement.getAttribute(t);this.otDataLayer={ignore:e&&"true"===t||e&&""===t,name:c.stubScriptElement.getAttribute("data-dLayer-name")||"dataLayer"}},v.prototype.setGeoLocation=function(t,e,i){c.userLocation={country:t,state:e=void 0===e?"":e,stateName:i=void 0===i?"":i}},v.prototype.otFetch=function(t,i,e,n,a){void 0===e&&(e=!1),void 0===n&&(n=null);var o=window.sessionStorage&&window.sessionStorage.getItem("otPreviewData");if(new RegExp("^file://","i").test(t))this.otFetchOfflineFile(t,i);else if(0<=t.indexOf("/consent/")&&this.previewMode&&o){o=JSON.parse(o).domainJson;i(o)}else{c.mobileOnlineURL.push(t);var r=new XMLHttpRequest;if(r.onload=function(t){var e;this&&this.responseText?e=this.responseText:t&&t.target&&(e=t.target.responseText),a?i(JSON.parse(e),a):i(JSON.parse(e))},r.onerror=function(){i()},r.open("GET",t),r.withCredentials=!1,e&&r.setRequestHeader("accept","application/json"),n)for(var s in n)r.setRequestHeader(s,n[s]);r.send()}},v.prototype.otFetchOfflineFile=function(t,e){var i=(t=t.replace(".json",".js")).split("/"),n=i[i.length-1].split(".js")[0];this.jsonp(t,function(){e(window[n])})},v.prototype.jsonp=function(t,e,i,n){void 0===i&&(i=!1),void 0===n&&(n="");var a=document.createElement("script"),o=f.createScriptURL(t);a.setAttribute("src",o),this.nonce&&a.setAttribute("nonce",this.nonce),a.async=!0,a.type="text/javascript",i&&n&&(a.integrity=n),this.crossOrigin&&a.setAttribute("crossorigin",this.crossOrigin),document.getElementsByTagName("head")[0].appendChild(a),new RegExp("^file://","i").test(t)||c.mobileOnlineURL.push(t),e&&(a.onload=a.onerror=function(){e()})},v.prototype.getRegionSet=function(t){var e,i,n,a=c.userLocation,o=t.RuleSet.filter(function(t){return!0===t.Default});if(!a.country&&!a.state)return o&&0<o.length?o[0]:null;for(var r=a.state.toLowerCase(),s=a.country.toLowerCase(),u=0;u<t.RuleSet.length;u++)if(!0===t.RuleSet[u].Global)n=t.RuleSet[u];else{var l=t.RuleSet[u].States;if(l[s]&&0<=l[s].indexOf(r)){i=t.RuleSet[u];break}0<=t.RuleSet[u].Countries.indexOf(s)&&(e=t.RuleSet[u])}return i||e||n},v.prototype.ensureHtmlGroupDataInitialised=function(){this.initializeIABData(),this.initializeGroupData(),this.initializeHostData(),this.initializeGenVenData()},v.prototype.initializeGroupData=function(){var t=this.readCookieParam(c.optanonCookieName,"groups");t&&(c.optanonHtmlGroupData=this.deserialiseStringToArray(t))},v.prototype.initializeHostData=function(){var t=this.readCookieParam(c.optanonCookieName,"hosts");t&&(c.optanonHostData=this.deserialiseStringToArray(t))},v.prototype.initializeGenVenData=function(){var t=this.readCookieParam(c.optanonCookieName,"genVendors");t&&(c.genVendorsData=this.deserialiseStringToArray(t))},v.prototype.initializeIABData=function(){this.validateIABGDPRApplied(),this.validateIABGlobalScope()},v.prototype.validateIABGlobalScope=function(){var t=this.readCookieParam(c.optanonCookieName,c.oneTrustIsIABCrossConsentEnableParam);t?"true"===t?(c.hasIABGlobalScope=!0,c.isStubReady=!1):(c.hasIABGlobalScope=!1,c.IABCookieValue=this.getCookie(c.oneTrustIABCookieName)):c.isStubReady=!1},v.prototype.validateIABGDPRApplied=function(){var t=this.readCookieParam(c.optanonCookieName,c.geolocationCookiesParam).split(";")[0];t?this.isBoolean(t)?c.oneTrustIABgdprAppliesGlobally="true"===t:c.oneTrustIABgdprAppliesGlobally=0<=c.EUCOUNTRIES.indexOf(t):c.isStubReady=!1},v.prototype.isBoolean=function(t){return"true"===t||"false"===t},v.prototype.readCookieParam=function(t,e){var i,n,a,o,t=this.getCookie(t);if(t){for(n={},a=t.split("&"),i=0;i<a.length;i+=1)o=a[i].split("="),n[decodeURIComponent(o[0])]=decodeURIComponent(o[1]).replace(/\+/g," ");return e&&n[e]?n[e]:e&&!n[e]?"":n}return""},v.prototype.getCookie=function(t){if(this.isAmp){var e=JSON.parse(window.localStorage.getItem(this.domainId))||{};if(e)return e[t]||null}for(var i,n=t+"=",a=document.cookie.split(";"),o=0;o<a.length;o+=1){for(i=a[o];" "==i.charAt(0);)i=i.substring(1,i.length);if(0==i.indexOf(n))return i.substring(n.length,i.length)}return null},v.prototype.updateGtmMacros=function(){for(var t=[],e=c.optanonHtmlGroupData.length,i=0;i<e;i++)this.endsWith(c.optanonHtmlGroupData[i],":1")&&t.push(c.optanonHtmlGroupData[i].replace(":1",""));for(e=c.optanonHostData.length,i=0;i<e;i++)this.endsWith(c.optanonHostData[i],":1")&&t.push(c.optanonHostData[i].replace(":1",""));for(e=c.genVendorsData.length,i=0;i<e;i++)this.endsWith(c.genVendorsData[i],":1")&&t.push(c.genVendorsData[i].replace(":1",""));for(e=c.vendorsServiceData.length,i=0;i<e;i++)this.endsWith(c.vendorsServiceData[i],":1")&&t.push(c.vendorsServiceData[i].replace(":1",""));var n,a=","+this.serialiseArrayToString(t)+",",o=(window.OnetrustActiveGroups=a,window.OptanonActiveGroups=a,window),r=(this.otDataLayer.ignore||void 0===o[this.otDataLayer.name]?this.otDataLayer.ignore||(o[this.otDataLayer.name]=[{event:"OneTrustLoaded",OnetrustActiveGroups:a},{event:"OptanonLoaded",OptanonActiveGroups:a}]):o[this.otDataLayer.name].constructor===Array&&(o[this.otDataLayer.name].push({event:"OneTrustLoaded",OnetrustActiveGroups:a}),o[this.otDataLayer.name].push({event:"OptanonLoaded",OptanonActiveGroups:a})),new CustomEvent("consent.onetrust",{detail:t}));!this.otDataLayer.ignore&&t.length&&(o[this.otDataLayer.name].constructor===Array&&o[this.otDataLayer.name].push({event:"OneTrustGroupsUpdated",OnetrustActiveGroups:a}),n=new CustomEvent("OneTrustGroupsUpdated",{detail:t})),setTimeout(function(){t.length&&window.dispatchEvent(r),n&&window.dispatchEvent(n)})},v.prototype.deserialiseStringToArray=function(t){return t?t.split(","):[]},v.prototype.endsWith=function(t,e){return-1!==t.indexOf(e,t.length-e.length)},v.prototype.serialiseArrayToString=function(t){return t.toString()},v.prototype.getStubQueryParam=function(t,e){return!t||(t=t.split("?")).length<2?null:new URLSearchParams(t[1]).get(e)},v.prototype.setStubScriptElement=function(){c.stubScriptElement=document.querySelector("script[src*='"+c.stubFileName+"']");var t=c.stubScriptElement&&c.stubScriptElement.getAttribute("src"),t=t&&this.getStubQueryParam(t,"did");c.stubScriptElement&&c.stubScriptElement.hasAttribute(c.DATAFILEATTRIBUTE)?this.domainId=c.stubScriptElement.getAttribute(c.DATAFILEATTRIBUTE).trim():t?this.domainId=t:c.stubScriptElement||(c.stubScriptElement=document.querySelector("script[src*='"+c.migratedCCTID+"']"),c.stubScriptElement&&(c.isMigratedURL=!0,this.domainId=c.migratedDomainId.trim()))},v.prototype.setDomainDataFileURL=function(){var t=c.stubScriptElement.getAttribute("src"),e=-1<t.indexOf("/consent");t&&(c.isMigratedURL?c.storageBaseURL=t.split("/consent/"+c.migratedCCTID)[0]:c.storageBaseURL=(e?t.split("/consent"):t.split("/scripttemplates/"+c.stubFileName))[0]),this.storageBaseURL=c.storageBaseURL,this.isPreview&&-1===this.domainId.indexOf("test")?this.domainId=this.domainId+"-test":this.isPreview=!1,c.bannerBaseDataURL=c.storageBaseURL&&c.storageBaseURL+"/consent/"+this.domainId,c.bannerDataParentURL=c.bannerBaseDataURL+"/"+this.domainId+".json"},v.prototype.initCustomEventPolyfill=function(){if("function"==typeof window.CustomEvent)return!1;function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var i=document.createEvent("CustomEvent");return i.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),i}t.prototype=window.Event.prototype,window.CustomEvent=t},v.prototype.removeTcf=function(){delete window.__tcfapi;var t=document.querySelectorAll("iframe[name='__tcfapiLocator']")[0];t&&t.parentElement.removeChild(t)},v.prototype.getParamForIE=function(){return{get:function(t){t=new RegExp("[?&]"+t+"=([^&#]*)").exec(window.location.search);return null===t?null:decodeURI(t[1])||""}}},v.prototype.getParam=function(){window.document.documentMode||!window.URLSearchParams?this.urlParams=this.getParamForIE():this.urlParams=new URLSearchParams(window.location.search);var t="true"===this.urlParams.get("otreset"),e="true"===this.urlParams.get("otpreview"),i=(this.geoFromUrl=(this.urlParams.get("otgeo")||"").toLowerCase(),this.readCookieParam(l,"expiry")),n=this.readCookieParam(l,r);this.isReset=t||i&&new Date(i)<new Date,this.isPreview=!this.isReset&&(e||i&&new Date(i)>new Date),this.setGeoParam(this.geoFromUrl||n)},v.prototype.setGeoParam=function(t){var e;t&&((e=window).OneTrust||(e.OneTrust={}),t=t.split(","),e.OneTrust.geolocationResponse={countryCode:t[0],stateCode:t[1]})},v.prototype.updateVersion=function(t){"debug"!==this.buildType&&"cybuild"!==this.buildType||(t.Version="202507.1.0")};var y=v;function v(){var s=this;this.iabType=null,this.iabTypeAdded=!0,this.crossOrigin=null,this.isAmp=!1,this.domainId="",this.isReset=!1,this.isPreview=!1,this.geoFromUrl="",this.nonce="",this.setAttributePolyfillIsActive=!1,this.storageBaseURL="",this.charset=null,this.buildType="undefined",this.addBannerSDKScript=function(r){return e(s,void 0,void 0,function(){var e,i,n,a,o;return u(this,function(t){switch(t.label){case 0:return((e=this.getRegionSet(r)).GCEnable||(this.updateGtmMacros(),this.gtmUpdated=!0),this.iabTypeAdded&&("IAB2"!==e.Type&&"IAB2V2"!==e.Type||(this.iabType=e.Type,this.intializeIabStub()),"IAB2"!==e.Type)&&"IAB2V2"!==e.Type&&this.removeTcf(),e.IsGPPEnabled?g.init():g.removeGppApi(),i=c.stubScriptElement.cloneNode(!0),n="",n=r.UseSDKRefactor?(c.isMigratedURL&&(i.src=c.storageBaseURL+"/scripttemplates/new/scripttemplates/"+c.stubFileName+".js"),c.storageBaseURL+"/scripttemplates/new/scripttemplates/"+r.Version+"/"+c.bannerScriptName):"5.11.0"===r.Version?(c.isMigratedURL&&(i.src=c.storageBaseURL+"/scripttemplates/old/scripttemplates/"+c.stubFileName+".js"),c.storageBaseURL+"/scripttemplates/old/scripttemplates/5.11.0/"+c.bannerScriptName):(c.isMigratedURL&&(i.src=c.storageBaseURL+"/scripttemplates/"+c.stubFileName+".js"),c.storageBaseURL+"/scripttemplates/"+r.Version+"/"+c.bannerScriptName),["charset","data-language","data-document-language","data-domain-script","crossorigin","data-ignore-ga"].forEach(function(t){c.stubScriptElement.getAttribute(t)&&i.setAttribute(t,c.stubScriptElement.getAttribute(t))}),this.charset=c.stubScriptElement.getAttribute("charset"),this.isAmp=!!c.stubScriptElement.getAttribute("amp"),c.stubScriptElement.getAttribute("integrity"))?(o=r.CDNLocation+"/scripttemplates/"+r.Version+"/sri-hashes.json",[4,this.fetchSriHash(o)]):[3,2];case 1:return a=t.sent(),[3,3];case 2:a=null,t.label=3;case 3:return window.otStubData={bannerBaseDataURL:c.bannerBaseDataURL,crossOrigin:this.crossOrigin,domainData:r,domainId:this.domainId,geoFromUrl:this.geoFromUrl,isAmp:this.isAmp,isPreview:this.isPreview,isReset:this.isReset,mobileOnlineURL:c.mobileOnlineURL,nonce:this.nonce,otDataLayer:this.otDataLayer,regionRule:e,setAttributePolyfillIsActive:this.setAttributePolyfillIsActive,storageBaseURL:this.storageBaseURL,stubElement:i,urlParams:this.urlParams,userLocation:c.userLocation,gtmUpdated:this.gtmUpdated,previewMode:this.previewMode,charset:this.charset,stubUrl:c.stubScriptElement.getAttribute("src"),sriHash:a},this.jsonp(n,null,!0,null==(o=a)?void 0:o["otBannerSdk.js"]),[2]}})})},this.fetchSriHash=function(i){return e(s,void 0,void 0,function(){var e;return u(this,function(t){switch(t.label){case 0:return t.trys.push([0,4,,5]),[4,fetch(i)];case 1:return(e=t.sent()).ok?[4,e.json()]:[3,3];case 2:return[2,t.sent()];case 3:return[3,5];case 4:return e=t.sent(),console.error("Error fetching SRI hash:",e),[3,5];case 5:return[2,null]}})})},this.intializeIabStub=function(){var t=window;s.iabTypeAdded?(void 0===t.__tcfapi&&(window.__tcfapi=s.executeTcfApi),s.addIabFrame()):s.addBackwardIabFrame(),t.receiveOTMessage=s.receiveIabMessage,(t.attachEvent||window.addEventListener)("message",t.receiveOTMessage,!1)},this.addIabFrame=function(){var t=window,e="__tcfapiLocator";!t.frames[e]&&(t.document.body?s.addLocator(e,"CMP"):setTimeout(s.addIabFrame,5))},this.addBackwardIabFrame=function(){var t=window,e="__tcfapiLocator";!t.frames[e]&&(t.document.body?s.addLocator(e,"TCF"):setTimeout(s.addIabFrame,5))},this.addLocator=function(t,e){var i=window,n=i.document.createElement("iframe");m(n,"display: none;",!0),n.name=t,n.setAttribute("title",e+" Locator"),i.document.body.appendChild(n)},this.receiveIabMessage=function(i){var n,a,t,o="string"==typeof i.data,e={};try{e=o?JSON.parse(i.data):i.data}catch(t){}e.__cmpCall&&"IAB2"===s.iabType&&console.log("Expecting IAB TCF v2.0 vendor iFrame call; Received IAB TCF v1.1"),e.__tcfapiCall&&"IAB2"===s.iabType&&(n=e.__tcfapiCall.callId,a=e.__tcfapiCall.command,t=e.__tcfapiCall.parameter,e=e.__tcfapiCall.version,s.executeTcfApi(a,t,function(t,e){t={__tcfapiReturn:{returnValue:t,success:e,callId:n,command:a}};i&&i.source&&i.source.postMessage&&i.source.postMessage(o?JSON.stringify(t):t,"*")},e))},this.executeTcfApi=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(s.iabType="IAB2",!t.length)return window.__tcfapi.a||[];var i=t[0],n=t[1],a=t[2],o=t[3];"function"==typeof a&&i&&("ping"===i?s.getPingRequest(a):s.addToQueue(i,n,a,o))},this.addToQueue=function(t,e,i,n){var a=window,o="__tcfapi";a[o].a=a[o].a||[],a[o].a.push([t,e,i,n])},this.getPingRequest=function(t,e){var i,n;t&&(n=!(i={}),"IAB2"!==s.iabType&&"IAB2V2"!==s.iabType||(i={gdprApplies:c.oneTrustIABgdprAppliesGlobally,cmpLoaded:!1,cmpStatus:"stub",displayStatus:"stub",apiVersion:"2.0",cmpVersion:void 0,cmpId:void 0,gvlVersion:void 0,tcfPolicyVersion:void 0},n=!0),t(i,n))},this.getConsentDataRequest=function(t){t&&c.IABCookieValue&&t({gdprApplies:c.oneTrustIABgdprAppliesGlobally,hasGlobalScope:c.hasIABGlobalScope,consentData:c.IABCookieValue},!0)},this.initConsentSDK()}var b=new y;return t.OtSDKStub=y,t.otSdkStub=b,Object.defineProperty(t,"__esModule",{value:!0}),t})({});
