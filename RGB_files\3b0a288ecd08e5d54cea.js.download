(()=>{"use strict";var e,t,n={},r={};function i(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return n[e](o,o.exports,i),o.exports}i.m=n,i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,n)=>(i.f[n](e,t),t)),[])),i.u=e=>e+"/431110629a9fe8297174.js",i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="Destination:",i.l=(n,r,o,s)=>{if(e[n])e[n].push(r);else{var a,l;if(void 0!==o)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==n||d.getAttribute("data-webpack")==t+o){a=d;break}}a||(l=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,i.nc&&a.setAttribute("nonce",i.nc),a.setAttribute("data-webpack",t+o),a.src=n),e[n]=[r];var p=(t,r)=>{a.onerror=a.onload=null,clearTimeout(f);var i=e[n];if(delete e[n],a.parentNode&&a.parentNode.removeChild(a),i&&i.forEach((e=>e(r))),t)return t(r)},f=setTimeout(p.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=p.bind(null,a.onerror),a.onload=p.bind(null,a.onload),l&&document.head.appendChild(a)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;i.g.importScripts&&(e=i.g.location+"");var t=i.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var r=n.length-1;r>-1&&!e;)e=n[r--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=e+"../"})(),(()=>{var e={238:0};i.f.j=(t,n)=>{var r=i.o(e,t)?e[t]:void 0;if(0!==r)if(r)n.push(r[2]);else{var o=new Promise(((n,i)=>r=e[t]=[n,i]));n.push(r[2]=o);var s=i.p+i.u(t),a=new Error;i.l(s,(n=>{if(i.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var o=n&&("load"===n.type?"missing":n.type),s=n&&n.target&&n.target.src;a.message="Loading chunk "+t+" failed.\n("+o+": "+s+")",a.name="ChunkLoadError",a.type=o,a.request=s,r[1](a)}}),"chunk-"+t,t)}};var t=(t,n)=>{var r,o,[s,a,l]=n,c=0;if(s.some((t=>0!==e[t]))){for(r in a)i.o(a,r)&&(i.m[r]=a[r]);if(l)l(i)}for(t&&t(n);c<s.length;c++)o=s[c],i.o(e,o)&&e[o]&&e[o][0](),e[o]=0},n=self.webpackChunkDestination=self.webpackChunkDestination||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var o={};function s(){return(new Date).getTime()}i.d(o,{default:()=>a});const a=function(e){const t=async t=>(await i.e(845).then(i.bind(i,3962))).generatePlugins(e,t,t.subscriptions||[]);return t.pluginName=e.name,t}({name:"Amplitude (Actions)",mode:"device",actions:{sessionId:{title:"Session Plugin",description:"Generates a Session ID and attaches it to every Amplitude browser based event.",platform:"web",hidden:!0,defaultSubscription:'type = "track" or type = "identify" or type = "group" or type = "page" or type = "alias"',fields:{sessionLength:{label:"Session Length",type:"number",required:!1,description:"Time in milliseconds to be used before considering a session stale."}},lifecycleHook:"enrichment",perform:(e,{context:t,payload:n,analytics:r})=>{const i={get:e=>{const t=window.localStorage.getItem(e);return null===t?null:parseInt(t,10)},set:(e,t)=>window.localStorage.setItem(e,t.toString())},o=s(),a=r.storage?r.storage:i,l=a.get("analytics_session_id");let c=l;!function(e,t,n=18e5){if(null===e||null===t)return!0;const r=t;return s()-r>=n}(l,a.get("analytics_session_id.last_access"),n.sessionLength)||(c=o),a.set("analytics_session_id",c),a.set("analytics_session_id.last_access",o),(!1!==t.event.integrations?.All||t.event.integrations["Actions Amplitude"])&&(t.updateEvent("integrations.Actions Amplitude",{}),t.updateEvent("integrations.Actions Amplitude.session_id",c))}}},initialize:async()=>({})});window["amplitude-pluginsDestination"]=o.default})();
//# sourceMappingURL=3b0a288ecd08e5d54cea.js.map