!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1e63a37c-4df3-42b5-b3b5-8874167a8d52",e._sentryDebugIdIdentifier="sentry-dbid-1e63a37c-4df3-42b5-b3b5-8874167a8d52")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8599],{1e3:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/UnorderedList","definitions":{"UnorderedList":{"type":"object","properties":{"nodeType":{"type":"string","enum":["unordered-list"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"$ref":"#/definitions/ListItem"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItem":{"type":"object","properties":{"nodeType":{"type":"string","enum":["list-item"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"$ref":"#/definitions/ListItemBlock"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItemBlock":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/ListItemBlockEnum"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItemBlockEnum":{"enum":["blockquote","embedded-asset-block","embedded-entry-block","embedded-resource-block","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","hr","ordered-list","paragraph","unordered-list"],"type":"string"},"Block":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/BLOCKS"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"BLOCKS":{"description":"Map of all Contentful block types. Blocks contain inline or block nodes.","type":"string","enum":["document","paragraph","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","ordered-list","unordered-list","list-item","hr","blockquote","embedded-entry-block","embedded-asset-block","embedded-resource-block","table","table-row","table-cell","table-header-cell"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},4963:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)},a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaWithNodeType=t.helpers=t.EMPTY_DOCUMENT=t.MARKS=t.INLINES=t.BLOCKS=void 0;var d=n(84463);Object.defineProperty(t,"BLOCKS",{enumerable:!0,get:function(){return d.BLOCKS}});var s=n(8257);Object.defineProperty(t,"INLINES",{enumerable:!0,get:function(){return s.INLINES}});var p=n(12237);Object.defineProperty(t,"MARKS",{enumerable:!0,get:function(){return p.MARKS}}),o(n(27358),t),o(n(13270),t),o(n(79718),t);var l=n(15491);Object.defineProperty(t,"EMPTY_DOCUMENT",{enumerable:!0,get:function(){return l.EMPTY_DOCUMENT}}),t.helpers=a(n(20370));var y=n(62196);Object.defineProperty(t,"getSchemaWithNodeType",{enumerable:!0,get:function(){return y.getSchemaWithNodeType}})},5243:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/ListItem","definitions":{"ListItem":{"type":"object","properties":{"nodeType":{"type":"string","enum":["list-item"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"$ref":"#/definitions/ListItemBlock"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItemBlock":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/ListItemBlockEnum"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItemBlockEnum":{"enum":["blockquote","embedded-asset-block","embedded-entry-block","embedded-resource-block","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","hr","ordered-list","paragraph","unordered-list"],"type":"string"},"Block":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/BLOCKS"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"BLOCKS":{"description":"Map of all Contentful block types. Blocks contain inline or block nodes.","type":"string","enum":["document","paragraph","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","ordered-list","unordered-list","list-item","hr","blockquote","embedded-entry-block","embedded-asset-block","embedded-resource-block","table","table-row","table-cell","table-header-cell"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},5274:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/TableHeaderCell","definitions":{"TableHeaderCell":{"type":"object","properties":{"nodeType":{"type":"string","enum":["table-header-cell"]},"data":{"type":"object","properties":{"colspan":{"type":"number"},"rowspan":{"type":"number"}},"additionalProperties":false},"content":{"minItems":1,"type":"array","items":{"$ref":"#/definitions/Paragraph"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Paragraph":{"type":"object","properties":{"nodeType":{"type":"string","enum":["paragraph"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},8257:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.INLINES=void 0,function(e){e.ASSET_HYPERLINK="asset-hyperlink",e.EMBEDDED_ENTRY="embedded-entry-inline",e.EMBEDDED_RESOURCE="embedded-resource-inline",e.ENTRY_HYPERLINK="entry-hyperlink",e.HYPERLINK="hyperlink",e.RESOURCE_HYPERLINK="resource-hyperlink"}(n||(t.INLINES=n={}))},8960:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/TableCell","definitions":{"TableCell":{"type":"object","properties":{"nodeType":{"enum":["table-cell","table-header-cell"],"type":"string"},"data":{"type":"object","properties":{"colspan":{"type":"number"},"rowspan":{"type":"number"}},"additionalProperties":false},"content":{"minItems":1,"type":"array","items":{"$ref":"#/definitions/Paragraph"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Paragraph":{"type":"object","properties":{"nodeType":{"type":"string","enum":["paragraph"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},9104:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/EntryLinkInline","definitions":{"EntryLinkInline":{"type":"object","properties":{"nodeType":{"type":"string","enum":["embedded-entry-inline"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/Link<\\"Entry\\">"}},"additionalProperties":false,"required":["target"]},"content":{"maxItems":0,"type":"array","items":{"$ref":"#/definitions/Text"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Link<\\"Entry\\">":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["Link"]},"linkType":{"type":"string","enum":["Entry"]},"id":{"type":"string"}},"additionalProperties":false,"required":["id","linkType","type"]}},"additionalProperties":false,"required":["sys"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},9642:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Document","definitions":{"Document":{"type":"object","properties":{"nodeType":{"type":"string","enum":["document"]},"content":{"type":"array","items":{"$ref":"#/definitions/TopLevelBlock"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"TopLevelBlock":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/TopLevelBlockEnum"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"TopLevelBlockEnum":{"enum":["blockquote","embedded-asset-block","embedded-entry-block","embedded-resource-block","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","hr","ordered-list","paragraph","table","unordered-list"],"type":"string"},"Block":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/BLOCKS"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"BLOCKS":{"description":"Map of all Contentful block types. Blocks contain inline or block nodes.","type":"string","enum":["document","paragraph","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","ordered-list","unordered-list","list-item","hr","blockquote","embedded-entry-block","embedded-asset-block","embedded-resource-block","table","table-row","table-cell","table-header-cell"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},9662:(e,t,n)=>{var r,i,o,a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self?self:{},d={},s={};Object.defineProperty(s,"__esModule",{value:!0}),s.BLOCKS=void 0,function(e){e.DOCUMENT="document",e.PARAGRAPH="paragraph",e.HEADING_1="heading-1",e.HEADING_2="heading-2",e.HEADING_3="heading-3",e.HEADING_4="heading-4",e.HEADING_5="heading-5",e.HEADING_6="heading-6",e.OL_LIST="ordered-list",e.UL_LIST="unordered-list",e.LIST_ITEM="list-item",e.HR="hr",e.QUOTE="blockquote",e.EMBEDDED_ENTRY="embedded-entry-block",e.EMBEDDED_ASSET="embedded-asset-block",e.EMBEDDED_RESOURCE="embedded-resource-block",e.TABLE="table",e.TABLE_ROW="table-row",e.TABLE_CELL="table-cell",e.TABLE_HEADER_CELL="table-header-cell"}(r||(s.BLOCKS=r={}));var p={};Object.defineProperty(p,"__esModule",{value:!0}),p.INLINES=void 0,function(e){e.ASSET_HYPERLINK="asset-hyperlink",e.EMBEDDED_ENTRY="embedded-entry-inline",e.EMBEDDED_RESOURCE="embedded-resource-inline",e.ENTRY_HYPERLINK="entry-hyperlink",e.HYPERLINK="hyperlink",e.RESOURCE_HYPERLINK="resource-hyperlink"}(i||(p.INLINES=i={}));var l={};Object.defineProperty(l,"__esModule",{value:!0}),l.MARKS=void 0,function(e){e.BOLD="bold",e.ITALIC="italic",e.UNDERLINE="underline",e.CODE="code",e.SUPERSCRIPT="superscript",e.SUBSCRIPT="subscript",e.STRIKETHROUGH="strikethrough"}(o||(l.MARKS=o={}));var y={};!function(e){var t,n=a&&a.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(e,"__esModule",{value:!0}),e.V1_MARKS=e.V1_NODE_TYPES=e.TEXT_CONTAINERS=e.HEADINGS=e.CONTAINERS=e.VOID_BLOCKS=e.TABLE_BLOCKS=e.LIST_ITEM_BLOCKS=e.TOP_LEVEL_BLOCKS=void 0,e.TOP_LEVEL_BLOCKS=[s.BLOCKS.PARAGRAPH,s.BLOCKS.HEADING_1,s.BLOCKS.HEADING_2,s.BLOCKS.HEADING_3,s.BLOCKS.HEADING_4,s.BLOCKS.HEADING_5,s.BLOCKS.HEADING_6,s.BLOCKS.OL_LIST,s.BLOCKS.UL_LIST,s.BLOCKS.HR,s.BLOCKS.QUOTE,s.BLOCKS.EMBEDDED_ENTRY,s.BLOCKS.EMBEDDED_ASSET,s.BLOCKS.EMBEDDED_RESOURCE,s.BLOCKS.TABLE],e.LIST_ITEM_BLOCKS=[s.BLOCKS.PARAGRAPH,s.BLOCKS.HEADING_1,s.BLOCKS.HEADING_2,s.BLOCKS.HEADING_3,s.BLOCKS.HEADING_4,s.BLOCKS.HEADING_5,s.BLOCKS.HEADING_6,s.BLOCKS.OL_LIST,s.BLOCKS.UL_LIST,s.BLOCKS.HR,s.BLOCKS.QUOTE,s.BLOCKS.EMBEDDED_ENTRY,s.BLOCKS.EMBEDDED_ASSET,s.BLOCKS.EMBEDDED_RESOURCE],e.TABLE_BLOCKS=[s.BLOCKS.TABLE,s.BLOCKS.TABLE_ROW,s.BLOCKS.TABLE_CELL,s.BLOCKS.TABLE_HEADER_CELL],e.VOID_BLOCKS=[s.BLOCKS.HR,s.BLOCKS.EMBEDDED_ENTRY,s.BLOCKS.EMBEDDED_ASSET,s.BLOCKS.EMBEDDED_RESOURCE],(t={})[s.BLOCKS.OL_LIST]=[s.BLOCKS.LIST_ITEM],t[s.BLOCKS.UL_LIST]=[s.BLOCKS.LIST_ITEM],t[s.BLOCKS.LIST_ITEM]=e.LIST_ITEM_BLOCKS,t[s.BLOCKS.QUOTE]=[s.BLOCKS.PARAGRAPH],t[s.BLOCKS.TABLE]=[s.BLOCKS.TABLE_ROW],t[s.BLOCKS.TABLE_ROW]=[s.BLOCKS.TABLE_CELL,s.BLOCKS.TABLE_HEADER_CELL],t[s.BLOCKS.TABLE_CELL]=[s.BLOCKS.PARAGRAPH,s.BLOCKS.UL_LIST,s.BLOCKS.OL_LIST],t[s.BLOCKS.TABLE_HEADER_CELL]=[s.BLOCKS.PARAGRAPH],e.CONTAINERS=t,e.HEADINGS=[s.BLOCKS.HEADING_1,s.BLOCKS.HEADING_2,s.BLOCKS.HEADING_3,s.BLOCKS.HEADING_4,s.BLOCKS.HEADING_5,s.BLOCKS.HEADING_6],e.TEXT_CONTAINERS=n([s.BLOCKS.PARAGRAPH],e.HEADINGS,!0),e.V1_NODE_TYPES=[s.BLOCKS.DOCUMENT,s.BLOCKS.PARAGRAPH,s.BLOCKS.HEADING_1,s.BLOCKS.HEADING_2,s.BLOCKS.HEADING_3,s.BLOCKS.HEADING_4,s.BLOCKS.HEADING_5,s.BLOCKS.HEADING_6,s.BLOCKS.OL_LIST,s.BLOCKS.UL_LIST,s.BLOCKS.LIST_ITEM,s.BLOCKS.HR,s.BLOCKS.QUOTE,s.BLOCKS.EMBEDDED_ENTRY,s.BLOCKS.EMBEDDED_ASSET,p.INLINES.HYPERLINK,p.INLINES.ENTRY_HYPERLINK,p.INLINES.ASSET_HYPERLINK,p.INLINES.EMBEDDED_ENTRY,"text"],e.V1_MARKS=[l.MARKS.BOLD,l.MARKS.CODE,l.MARKS.ITALIC,l.MARKS.UNDERLINE]}(y);var f={};Object.defineProperty(f,"__esModule",{value:!0});var c={};Object.defineProperty(c,"__esModule",{value:!0});var u={};Object.defineProperty(u,"__esModule",{value:!0}),u.EMPTY_DOCUMENT=void 0,u.EMPTY_DOCUMENT={nodeType:s.BLOCKS.DOCUMENT,data:{},content:[{nodeType:s.BLOCKS.PARAGRAPH,data:{},content:[{nodeType:"text",value:"",marks:[],data:{}}]}]};var E={};function L(e,t){for(var n=0,r=Object.keys(e);n<r.length;n++)if(t===e[r[n]])return!0;return!1}Object.defineProperty(E,"__esModule",{value:!0}),E.isInline=function(e){return L(p.INLINES,e.nodeType)},E.isBlock=function(e){return L(s.BLOCKS,e.nodeType)},E.isText=function(e){return"text"===e.nodeType};var T={};Object.defineProperty(T,"__esModule",{value:!0}),T.getSchemaWithNodeType=function(e){try{throw Error('Could not dynamically require "'+"./generated/".concat(e,".json")+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}catch(t){throw Error('Schema for nodeType "'.concat(e,'" was not found.'))}},!function(e){var t=a&&a.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),n=a&&a.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=a&&a.__exportStar||function(e,n){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(n,r)||t(n,e,r)},i=a&&a.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var i in e)"default"!==i&&Object.prototype.hasOwnProperty.call(e,i)&&t(r,e,i);return n(r,e),r};Object.defineProperty(e,"__esModule",{value:!0}),e.getSchemaWithNodeType=e.helpers=e.EMPTY_DOCUMENT=e.MARKS=e.INLINES=e.BLOCKS=void 0,Object.defineProperty(e,"BLOCKS",{enumerable:!0,get:function(){return s.BLOCKS}}),Object.defineProperty(e,"INLINES",{enumerable:!0,get:function(){return p.INLINES}}),Object.defineProperty(e,"MARKS",{enumerable:!0,get:function(){return l.MARKS}}),r(y,e),r(f,e),r(c,e),Object.defineProperty(e,"EMPTY_DOCUMENT",{enumerable:!0,get:function(){return u.EMPTY_DOCUMENT}}),e.helpers=i(E),Object.defineProperty(e,"getSchemaWithNodeType",{enumerable:!0,get:function(){return T.getSchemaWithNodeType}})}(d),t.l=function e(t,n){return(void 0===n&&(n=" "),t&&t.content&&Array.isArray(t.content))?t.content.reduce(function(r,i,o){if(d.helpers.isText(i))a=i.value;else if((d.helpers.isBlock(i)||d.helpers.isInline(i))&&!(a=e(i,n)).length)return r;var a,s=t.content[o+1];return r+a+(s&&d.helpers.isBlock(s)?n:"")},""):""}},12237:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.MARKS=void 0,function(e){e.BOLD="bold",e.ITALIC="italic",e.UNDERLINE="underline",e.CODE="code",e.SUPERSCRIPT="superscript",e.SUBSCRIPT="subscript",e.STRIKETHROUGH="strikethrough"}(n||(t.MARKS=n={}))},13270:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},13812:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/ResourceHyperlink","definitions":{"ResourceHyperlink":{"type":"object","properties":{"nodeType":{"type":"string","enum":["resource-hyperlink"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/ResourceLink"}},"additionalProperties":false,"required":["target"]},"content":{"type":"array","items":{"$ref":"#/definitions/Text"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ResourceLink":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["ResourceLink"]},"linkType":{"type":"string","enum":["Contentful:Entry"]},"urn":{"type":"string"}},"additionalProperties":false,"required":["linkType","type","urn"]}},"additionalProperties":false,"required":["sys"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},15491:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EMPTY_DOCUMENT=void 0;var r=n(84463);t.EMPTY_DOCUMENT={nodeType:r.BLOCKS.DOCUMENT,data:{},content:[{nodeType:r.BLOCKS.PARAGRAPH,data:{},content:[{nodeType:"text",value:"",marks:[],data:{}}]}]}},19175:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/OrderedList","definitions":{"OrderedList":{"type":"object","properties":{"nodeType":{"type":"string","enum":["ordered-list"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"$ref":"#/definitions/ListItem"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItem":{"type":"object","properties":{"nodeType":{"type":"string","enum":["list-item"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"$ref":"#/definitions/ListItemBlock"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItemBlock":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/ListItemBlockEnum"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"ListItemBlockEnum":{"enum":["blockquote","embedded-asset-block","embedded-entry-block","embedded-resource-block","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","hr","ordered-list","paragraph","unordered-list"],"type":"string"},"Block":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/BLOCKS"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Block"},{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"BLOCKS":{"description":"Map of all Contentful block types. Blocks contain inline or block nodes.","type":"string","enum":["document","paragraph","heading-1","heading-2","heading-3","heading-4","heading-5","heading-6","ordered-list","unordered-list","list-item","hr","blockquote","embedded-entry-block","embedded-asset-block","embedded-resource-block","table","table-row","table-cell","table-header-cell"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},20370:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isInline=function(e){return o(i.INLINES,e.nodeType)},t.isBlock=function(e){return o(r.BLOCKS,e.nodeType)},t.isText=function(e){return"text"===e.nodeType};var r=n(84463),i=n(8257);function o(e,t){for(var n=0,r=Object.keys(e);n<r.length;n++)if(t===e[r[n]])return!0;return!1}},23113:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Hr","definitions":{"Hr":{"type":"object","properties":{"nodeType":{"type":"string","enum":["hr"]},"data":{"maxItems":0,"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},23881:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Hyperlink","definitions":{"Hyperlink":{"type":"object","properties":{"nodeType":{"type":"string","enum":["hyperlink"]},"data":{"type":"object","properties":{"uri":{"type":"string"}},"additionalProperties":false,"required":["uri"]},"content":{"type":"array","items":{"$ref":"#/definitions/Text"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},26e3:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Text","definitions":{"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},27358:function(e,t,n){var r,i=this&&this.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.V1_MARKS=t.V1_NODE_TYPES=t.TEXT_CONTAINERS=t.HEADINGS=t.CONTAINERS=t.VOID_BLOCKS=t.TABLE_BLOCKS=t.LIST_ITEM_BLOCKS=t.TOP_LEVEL_BLOCKS=void 0;var o=n(84463),a=n(8257),d=n(12237);t.TOP_LEVEL_BLOCKS=[o.BLOCKS.PARAGRAPH,o.BLOCKS.HEADING_1,o.BLOCKS.HEADING_2,o.BLOCKS.HEADING_3,o.BLOCKS.HEADING_4,o.BLOCKS.HEADING_5,o.BLOCKS.HEADING_6,o.BLOCKS.OL_LIST,o.BLOCKS.UL_LIST,o.BLOCKS.HR,o.BLOCKS.QUOTE,o.BLOCKS.EMBEDDED_ENTRY,o.BLOCKS.EMBEDDED_ASSET,o.BLOCKS.EMBEDDED_RESOURCE,o.BLOCKS.TABLE],t.LIST_ITEM_BLOCKS=[o.BLOCKS.PARAGRAPH,o.BLOCKS.HEADING_1,o.BLOCKS.HEADING_2,o.BLOCKS.HEADING_3,o.BLOCKS.HEADING_4,o.BLOCKS.HEADING_5,o.BLOCKS.HEADING_6,o.BLOCKS.OL_LIST,o.BLOCKS.UL_LIST,o.BLOCKS.HR,o.BLOCKS.QUOTE,o.BLOCKS.EMBEDDED_ENTRY,o.BLOCKS.EMBEDDED_ASSET,o.BLOCKS.EMBEDDED_RESOURCE],t.TABLE_BLOCKS=[o.BLOCKS.TABLE,o.BLOCKS.TABLE_ROW,o.BLOCKS.TABLE_CELL,o.BLOCKS.TABLE_HEADER_CELL],t.VOID_BLOCKS=[o.BLOCKS.HR,o.BLOCKS.EMBEDDED_ENTRY,o.BLOCKS.EMBEDDED_ASSET,o.BLOCKS.EMBEDDED_RESOURCE],(r={})[o.BLOCKS.OL_LIST]=[o.BLOCKS.LIST_ITEM],r[o.BLOCKS.UL_LIST]=[o.BLOCKS.LIST_ITEM],r[o.BLOCKS.LIST_ITEM]=t.LIST_ITEM_BLOCKS,r[o.BLOCKS.QUOTE]=[o.BLOCKS.PARAGRAPH],r[o.BLOCKS.TABLE]=[o.BLOCKS.TABLE_ROW],r[o.BLOCKS.TABLE_ROW]=[o.BLOCKS.TABLE_CELL,o.BLOCKS.TABLE_HEADER_CELL],r[o.BLOCKS.TABLE_CELL]=[o.BLOCKS.PARAGRAPH,o.BLOCKS.UL_LIST,o.BLOCKS.OL_LIST],r[o.BLOCKS.TABLE_HEADER_CELL]=[o.BLOCKS.PARAGRAPH],t.CONTAINERS=r,t.HEADINGS=[o.BLOCKS.HEADING_1,o.BLOCKS.HEADING_2,o.BLOCKS.HEADING_3,o.BLOCKS.HEADING_4,o.BLOCKS.HEADING_5,o.BLOCKS.HEADING_6],t.TEXT_CONTAINERS=i([o.BLOCKS.PARAGRAPH],t.HEADINGS,!0),t.V1_NODE_TYPES=[o.BLOCKS.DOCUMENT,o.BLOCKS.PARAGRAPH,o.BLOCKS.HEADING_1,o.BLOCKS.HEADING_2,o.BLOCKS.HEADING_3,o.BLOCKS.HEADING_4,o.BLOCKS.HEADING_5,o.BLOCKS.HEADING_6,o.BLOCKS.OL_LIST,o.BLOCKS.UL_LIST,o.BLOCKS.LIST_ITEM,o.BLOCKS.HR,o.BLOCKS.QUOTE,o.BLOCKS.EMBEDDED_ENTRY,o.BLOCKS.EMBEDDED_ASSET,a.INLINES.HYPERLINK,a.INLINES.ENTRY_HYPERLINK,a.INLINES.ASSET_HYPERLINK,a.INLINES.EMBEDDED_ENTRY,"text"],t.V1_MARKS=[d.MARKS.BOLD,d.MARKS.CODE,d.MARKS.ITALIC,d.MARKS.UNDERLINE]},33883:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Heading5","definitions":{"Heading5":{"type":"object","properties":{"nodeType":{"type":"string","enum":["heading-5"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},34367:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Heading1","definitions":{"Heading1":{"type":"object","properties":{"nodeType":{"type":"string","enum":["heading-1"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},39968:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Quote","definitions":{"Quote":{"type":"object","properties":{"nodeType":{"type":"string","enum":["blockquote"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"$ref":"#/definitions/Paragraph"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Paragraph":{"type":"object","properties":{"nodeType":{"type":"string","enum":["paragraph"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},40692:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/EntryHyperlink","definitions":{"EntryHyperlink":{"type":"object","properties":{"nodeType":{"type":"string","enum":["entry-hyperlink"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/Link<\\"Entry\\">"}},"additionalProperties":false,"required":["target"]},"content":{"type":"array","items":{"$ref":"#/definitions/Text"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Link<\\"Entry\\">":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["Link"]},"linkType":{"type":"string","enum":["Entry"]},"id":{"type":"string"}},"additionalProperties":false,"required":["id","linkType","type"]}},"additionalProperties":false,"required":["sys"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},41540:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/AssetLinkBlock","definitions":{"AssetLinkBlock":{"type":"object","properties":{"nodeType":{"type":"string","enum":["embedded-asset-block"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/Link<\\"Asset\\">"}},"additionalProperties":false,"required":["target"]},"content":{"maxItems":0,"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Link<\\"Asset\\">":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["Link"]},"linkType":{"type":"string","enum":["Asset"]},"id":{"type":"string"}},"additionalProperties":false,"required":["id","linkType","type"]}},"additionalProperties":false,"required":["sys"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},41933:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Heading3","definitions":{"Heading3":{"type":"object","properties":{"nodeType":{"type":"string","enum":["heading-3"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},45514:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Heading6","definitions":{"Heading6":{"type":"object","properties":{"nodeType":{"type":"string","enum":["heading-6"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},46605:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Paragraph","definitions":{"Paragraph":{"type":"object","properties":{"nodeType":{"type":"string","enum":["paragraph"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},53536:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/AssetHyperlink","definitions":{"AssetHyperlink":{"type":"object","properties":{"nodeType":{"type":"string","enum":["asset-hyperlink"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/Link<\\"Asset\\">"}},"additionalProperties":false,"required":["target"]},"content":{"type":"array","items":{"$ref":"#/definitions/Text"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Link<\\"Asset\\">":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["Link"]},"linkType":{"type":"string","enum":["Asset"]},"id":{"type":"string"}},"additionalProperties":false,"required":["id","linkType","type"]}},"additionalProperties":false,"required":["sys"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},62196:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaWithNodeType=function(e){try{return n(91593)("./".concat(e,".json"))}catch(t){throw Error('Schema for nodeType "'.concat(e,'" was not found.'))}}},66762:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/ResourceLinkBlock","definitions":{"ResourceLinkBlock":{"type":"object","properties":{"nodeType":{"type":"string","enum":["embedded-resource-block"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/ResourceLink"}},"additionalProperties":false,"required":["target"]},"content":{"maxItems":0,"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ResourceLink":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["ResourceLink"]},"linkType":{"type":"string","enum":["Contentful:Entry"]},"urn":{"type":"string"}},"additionalProperties":false,"required":["linkType","type","urn"]}},"additionalProperties":false,"required":["sys"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},68908:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/EntryLinkBlock","definitions":{"EntryLinkBlock":{"type":"object","properties":{"nodeType":{"type":"string","enum":["embedded-entry-block"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/Link<\\"Entry\\">"}},"additionalProperties":false,"required":["target"]},"content":{"maxItems":0,"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Link<\\"Entry\\">":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["Link"]},"linkType":{"type":"string","enum":["Entry"]},"id":{"type":"string"}},"additionalProperties":false,"required":["id","linkType","type"]}},"additionalProperties":false,"required":["sys"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},69230:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Heading2","definitions":{"Heading2":{"type":"object","properties":{"nodeType":{"type":"string","enum":["heading-2"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},69708:(e,t,n)=>{var r,i,o,a,d,s=n(7620),p=function(){return(p=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self?self:{},y={},f={};Object.defineProperty(f,"__esModule",{value:!0}),f.BLOCKS=void 0,function(e){e.DOCUMENT="document",e.PARAGRAPH="paragraph",e.HEADING_1="heading-1",e.HEADING_2="heading-2",e.HEADING_3="heading-3",e.HEADING_4="heading-4",e.HEADING_5="heading-5",e.HEADING_6="heading-6",e.OL_LIST="ordered-list",e.UL_LIST="unordered-list",e.LIST_ITEM="list-item",e.HR="hr",e.QUOTE="blockquote",e.EMBEDDED_ENTRY="embedded-entry-block",e.EMBEDDED_ASSET="embedded-asset-block",e.EMBEDDED_RESOURCE="embedded-resource-block",e.TABLE="table",e.TABLE_ROW="table-row",e.TABLE_CELL="table-cell",e.TABLE_HEADER_CELL="table-header-cell"}(r||(f.BLOCKS=r={}));var c={};Object.defineProperty(c,"__esModule",{value:!0}),c.INLINES=void 0,function(e){e.ASSET_HYPERLINK="asset-hyperlink",e.EMBEDDED_ENTRY="embedded-entry-inline",e.EMBEDDED_RESOURCE="embedded-resource-inline",e.ENTRY_HYPERLINK="entry-hyperlink",e.HYPERLINK="hyperlink",e.RESOURCE_HYPERLINK="resource-hyperlink"}(i||(c.INLINES=i={}));var u={};Object.defineProperty(u,"__esModule",{value:!0}),u.MARKS=void 0,function(e){e.BOLD="bold",e.ITALIC="italic",e.UNDERLINE="underline",e.CODE="code",e.SUPERSCRIPT="superscript",e.SUBSCRIPT="subscript",e.STRIKETHROUGH="strikethrough"}(o||(u.MARKS=o={}));var E={};!function(e){var t,n=l&&l.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(e,"__esModule",{value:!0}),e.V1_MARKS=e.V1_NODE_TYPES=e.TEXT_CONTAINERS=e.HEADINGS=e.CONTAINERS=e.VOID_BLOCKS=e.TABLE_BLOCKS=e.LIST_ITEM_BLOCKS=e.TOP_LEVEL_BLOCKS=void 0,e.TOP_LEVEL_BLOCKS=[f.BLOCKS.PARAGRAPH,f.BLOCKS.HEADING_1,f.BLOCKS.HEADING_2,f.BLOCKS.HEADING_3,f.BLOCKS.HEADING_4,f.BLOCKS.HEADING_5,f.BLOCKS.HEADING_6,f.BLOCKS.OL_LIST,f.BLOCKS.UL_LIST,f.BLOCKS.HR,f.BLOCKS.QUOTE,f.BLOCKS.EMBEDDED_ENTRY,f.BLOCKS.EMBEDDED_ASSET,f.BLOCKS.EMBEDDED_RESOURCE,f.BLOCKS.TABLE],e.LIST_ITEM_BLOCKS=[f.BLOCKS.PARAGRAPH,f.BLOCKS.HEADING_1,f.BLOCKS.HEADING_2,f.BLOCKS.HEADING_3,f.BLOCKS.HEADING_4,f.BLOCKS.HEADING_5,f.BLOCKS.HEADING_6,f.BLOCKS.OL_LIST,f.BLOCKS.UL_LIST,f.BLOCKS.HR,f.BLOCKS.QUOTE,f.BLOCKS.EMBEDDED_ENTRY,f.BLOCKS.EMBEDDED_ASSET,f.BLOCKS.EMBEDDED_RESOURCE],e.TABLE_BLOCKS=[f.BLOCKS.TABLE,f.BLOCKS.TABLE_ROW,f.BLOCKS.TABLE_CELL,f.BLOCKS.TABLE_HEADER_CELL],e.VOID_BLOCKS=[f.BLOCKS.HR,f.BLOCKS.EMBEDDED_ENTRY,f.BLOCKS.EMBEDDED_ASSET,f.BLOCKS.EMBEDDED_RESOURCE],(t={})[f.BLOCKS.OL_LIST]=[f.BLOCKS.LIST_ITEM],t[f.BLOCKS.UL_LIST]=[f.BLOCKS.LIST_ITEM],t[f.BLOCKS.LIST_ITEM]=e.LIST_ITEM_BLOCKS,t[f.BLOCKS.QUOTE]=[f.BLOCKS.PARAGRAPH],t[f.BLOCKS.TABLE]=[f.BLOCKS.TABLE_ROW],t[f.BLOCKS.TABLE_ROW]=[f.BLOCKS.TABLE_CELL,f.BLOCKS.TABLE_HEADER_CELL],t[f.BLOCKS.TABLE_CELL]=[f.BLOCKS.PARAGRAPH,f.BLOCKS.UL_LIST,f.BLOCKS.OL_LIST],t[f.BLOCKS.TABLE_HEADER_CELL]=[f.BLOCKS.PARAGRAPH],e.CONTAINERS=t,e.HEADINGS=[f.BLOCKS.HEADING_1,f.BLOCKS.HEADING_2,f.BLOCKS.HEADING_3,f.BLOCKS.HEADING_4,f.BLOCKS.HEADING_5,f.BLOCKS.HEADING_6],e.TEXT_CONTAINERS=n([f.BLOCKS.PARAGRAPH],e.HEADINGS,!0),e.V1_NODE_TYPES=[f.BLOCKS.DOCUMENT,f.BLOCKS.PARAGRAPH,f.BLOCKS.HEADING_1,f.BLOCKS.HEADING_2,f.BLOCKS.HEADING_3,f.BLOCKS.HEADING_4,f.BLOCKS.HEADING_5,f.BLOCKS.HEADING_6,f.BLOCKS.OL_LIST,f.BLOCKS.UL_LIST,f.BLOCKS.LIST_ITEM,f.BLOCKS.HR,f.BLOCKS.QUOTE,f.BLOCKS.EMBEDDED_ENTRY,f.BLOCKS.EMBEDDED_ASSET,c.INLINES.HYPERLINK,c.INLINES.ENTRY_HYPERLINK,c.INLINES.ASSET_HYPERLINK,c.INLINES.EMBEDDED_ENTRY,"text"],e.V1_MARKS=[u.MARKS.BOLD,u.MARKS.CODE,u.MARKS.ITALIC,u.MARKS.UNDERLINE]}(E);var L={};Object.defineProperty(L,"__esModule",{value:!0});var T={};Object.defineProperty(T,"__esModule",{value:!0});var S={};Object.defineProperty(S,"__esModule",{value:!0}),S.EMPTY_DOCUMENT=void 0,S.EMPTY_DOCUMENT={nodeType:f.BLOCKS.DOCUMENT,data:{},content:[{nodeType:f.BLOCKS.PARAGRAPH,data:{},content:[{nodeType:"text",value:"",marks:[],data:{}}]}]};var O={};function m(e,t){for(var n=0,r=Object.keys(e);n<r.length;n++)if(t===e[r[n]])return!0;return!1}Object.defineProperty(O,"__esModule",{value:!0}),O.isInline=function(e){return m(c.INLINES,e.nodeType)},O.isBlock=function(e){return m(f.BLOCKS,e.nodeType)},O.isText=function(e){return"text"===e.nodeType};var I={};Object.defineProperty(I,"__esModule",{value:!0}),I.getSchemaWithNodeType=function(e){try{throw Error('Could not dynamically require "'+"./generated/".concat(e,".json")+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}catch(t){throw Error('Schema for nodeType "'.concat(e,'" was not found.'))}},!function(e){var t=l&&l.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),n=l&&l.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=l&&l.__exportStar||function(e,n){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(n,r)||t(n,e,r)},i=l&&l.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var i in e)"default"!==i&&Object.prototype.hasOwnProperty.call(e,i)&&t(r,e,i);return n(r,e),r};Object.defineProperty(e,"__esModule",{value:!0}),e.getSchemaWithNodeType=e.helpers=e.EMPTY_DOCUMENT=e.MARKS=e.INLINES=e.BLOCKS=void 0,Object.defineProperty(e,"BLOCKS",{enumerable:!0,get:function(){return f.BLOCKS}}),Object.defineProperty(e,"INLINES",{enumerable:!0,get:function(){return c.INLINES}}),Object.defineProperty(e,"MARKS",{enumerable:!0,get:function(){return u.MARKS}}),r(E,e),r(L,e),r(T,e),Object.defineProperty(e,"EMPTY_DOCUMENT",{enumerable:!0,get:function(){return S.EMPTY_DOCUMENT}}),e.helpers=i(O),Object.defineProperty(e,"getSchemaWithNodeType",{enumerable:!0,get:function(){return I.getSchemaWithNodeType}})}(y);var b=((a={})[y.BLOCKS.DOCUMENT]=function(e,t){return t},a[y.BLOCKS.PARAGRAPH]=function(e,t){return s.createElement("p",null,t)},a[y.BLOCKS.HEADING_1]=function(e,t){return s.createElement("h1",null,t)},a[y.BLOCKS.HEADING_2]=function(e,t){return s.createElement("h2",null,t)},a[y.BLOCKS.HEADING_3]=function(e,t){return s.createElement("h3",null,t)},a[y.BLOCKS.HEADING_4]=function(e,t){return s.createElement("h4",null,t)},a[y.BLOCKS.HEADING_5]=function(e,t){return s.createElement("h5",null,t)},a[y.BLOCKS.HEADING_6]=function(e,t){return s.createElement("h6",null,t)},a[y.BLOCKS.EMBEDDED_ENTRY]=function(e,t){return s.createElement("div",null,t)},a[y.BLOCKS.EMBEDDED_RESOURCE]=function(e,t){return s.createElement("div",null,t)},a[y.BLOCKS.UL_LIST]=function(e,t){return s.createElement("ul",null,t)},a[y.BLOCKS.OL_LIST]=function(e,t){return s.createElement("ol",null,t)},a[y.BLOCKS.LIST_ITEM]=function(e,t){return s.createElement("li",null,t)},a[y.BLOCKS.QUOTE]=function(e,t){return s.createElement("blockquote",null,t)},a[y.BLOCKS.HR]=function(){return s.createElement("hr",null)},a[y.BLOCKS.TABLE]=function(e,t){return s.createElement("table",null,s.createElement("tbody",null,t))},a[y.BLOCKS.TABLE_ROW]=function(e,t){return s.createElement("tr",null,t)},a[y.BLOCKS.TABLE_HEADER_CELL]=function(e,t){return s.createElement("th",null,t)},a[y.BLOCKS.TABLE_CELL]=function(e,t){return s.createElement("td",null,t)},a[y.INLINES.ASSET_HYPERLINK]=function(e){return B(y.INLINES.ASSET_HYPERLINK,e)},a[y.INLINES.ENTRY_HYPERLINK]=function(e){return B(y.INLINES.ENTRY_HYPERLINK,e)},a[y.INLINES.RESOURCE_HYPERLINK]=function(e){return _(y.INLINES.RESOURCE_HYPERLINK,e)},a[y.INLINES.EMBEDDED_ENTRY]=function(e){return B(y.INLINES.EMBEDDED_ENTRY,e)},a[y.INLINES.EMBEDDED_RESOURCE]=function(e,t){return _(y.INLINES.EMBEDDED_RESOURCE,e)},a[y.INLINES.HYPERLINK]=function(e,t){return s.createElement("a",{href:e.data.uri},t)},a),N=((d={})[y.MARKS.BOLD]=function(e){return s.createElement("b",null,e)},d[y.MARKS.ITALIC]=function(e){return s.createElement("i",null,e)},d[y.MARKS.UNDERLINE]=function(e){return s.createElement("u",null,e)},d[y.MARKS.CODE]=function(e){return s.createElement("code",null,e)},d[y.MARKS.SUPERSCRIPT]=function(e){return s.createElement("sup",null,e)},d[y.MARKS.SUBSCRIPT]=function(e){return s.createElement("sub",null,e)},d[y.MARKS.STRIKETHROUGH]=function(e){return s.createElement("s",null,e)},d);function B(e,t){return s.createElement("span",{key:t.data.target.sys.id},"type: ",t.nodeType," id: ",t.data.target.sys.id)}function _(e,t){return s.createElement("span",{key:t.data.target.sys.urn},"type: ",t.nodeType," urn: ",t.data.target.sys.urn)}t.i=function(e,t){return(void 0===t&&(t={}),e)?function e(t,n){var r=n.renderNode,i=n.renderMark,o=n.renderText,a=n.preserveWhitespace;if(y.helpers.isText(t)){var d=o?o(t.value):t.value;if(a&&!o){var p=(d=d.replace(/ {2,}/g,function(e){return"\xa0".repeat(e.length)})).split("\n"),l=[];p.forEach(function(e,t){l.push(e),t!==p.length-1&&l.push(s.createElement("br",null))}),d=l}return t.marks.reduce(function(e,t){return i[t.type]?i[t.type](e):e},d)}var f,c=(f=t.content,f.map(function(t,r){var i;return i=e(t,n),s.isValidElement(i)&&null===i.key?s.cloneElement(i,{key:r}):i}));return t.nodeType&&r[t.nodeType]?r[t.nodeType](t,c):s.createElement(s.Fragment,null,c)}(e,{renderNode:p(p({},b),t.renderNode),renderMark:p(p({},N),t.renderMark),renderText:t.renderText,preserveWhitespace:t.preserveWhitespace}):null}},72095:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Table","definitions":{"Table":{"type":"object","properties":{"nodeType":{"type":"string","enum":["table"]},"data":{"type":"object","properties":{}},"content":{"minItems":1,"type":"array","items":{"$ref":"#/definitions/TableRow"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"TableRow":{"type":"object","properties":{"nodeType":{"type":"string","enum":["table-row"]},"data":{"type":"object","properties":{}},"content":{"minItems":1,"type":"array","items":{"$ref":"#/definitions/TableCell"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"TableCell":{"type":"object","properties":{"nodeType":{"enum":["table-cell","table-header-cell"],"type":"string"},"data":{"type":"object","properties":{"colspan":{"type":"number"},"rowspan":{"type":"number"}},"additionalProperties":false},"content":{"minItems":1,"type":"array","items":{"$ref":"#/definitions/Paragraph"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Paragraph":{"type":"object","properties":{"nodeType":{"type":"string","enum":["paragraph"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},76212:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/Heading4","definitions":{"Heading4":{"type":"object","properties":{"nodeType":{"type":"string","enum":["heading-4"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},79718:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},84463:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.BLOCKS=void 0,function(e){e.DOCUMENT="document",e.PARAGRAPH="paragraph",e.HEADING_1="heading-1",e.HEADING_2="heading-2",e.HEADING_3="heading-3",e.HEADING_4="heading-4",e.HEADING_5="heading-5",e.HEADING_6="heading-6",e.OL_LIST="ordered-list",e.UL_LIST="unordered-list",e.LIST_ITEM="list-item",e.HR="hr",e.QUOTE="blockquote",e.EMBEDDED_ENTRY="embedded-entry-block",e.EMBEDDED_ASSET="embedded-asset-block",e.EMBEDDED_RESOURCE="embedded-resource-block",e.TABLE="table",e.TABLE_ROW="table-row",e.TABLE_CELL="table-cell",e.TABLE_HEADER_CELL="table-header-cell"}(n||(t.BLOCKS=n={}))},90778:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/TableRow","definitions":{"TableRow":{"type":"object","properties":{"nodeType":{"type":"string","enum":["table-row"]},"data":{"type":"object","properties":{}},"content":{"minItems":1,"type":"array","items":{"$ref":"#/definitions/TableCell"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"TableCell":{"type":"object","properties":{"nodeType":{"enum":["table-cell","table-header-cell"],"type":"string"},"data":{"type":"object","properties":{"colspan":{"type":"number"},"rowspan":{"type":"number"}},"additionalProperties":false},"content":{"minItems":1,"type":"array","items":{"$ref":"#/definitions/Paragraph"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Paragraph":{"type":"object","properties":{"nodeType":{"type":"string","enum":["paragraph"]},"data":{"type":"object","properties":{}},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}}},"additionalProperties":false,"required":["content","data","nodeType"]},"Inline":{"type":"object","properties":{"nodeType":{"$ref":"#/definitions/INLINES"},"content":{"type":"array","items":{"anyOf":[{"$ref":"#/definitions/Inline"},{"$ref":"#/definitions/Text"}]}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["content","data","nodeType"]},"INLINES":{"description":"Map of all Contentful inline types. Inline contain inline or text nodes.","type":"string","enum":["asset-hyperlink","embedded-entry-inline","embedded-resource-inline","entry-hyperlink","hyperlink","resource-hyperlink"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')},93546:e=>{e.exports=JSON.parse('{"$ref":"#/definitions/ResourceLinkInline","definitions":{"ResourceLinkInline":{"type":"object","properties":{"nodeType":{"type":"string","enum":["embedded-resource-inline"]},"data":{"type":"object","properties":{"target":{"$ref":"#/definitions/ResourceLink"}},"additionalProperties":false,"required":["target"]},"content":{"maxItems":0,"type":"array","items":{"$ref":"#/definitions/Text"}}},"additionalProperties":false,"required":["content","data","nodeType"]},"ResourceLink":{"type":"object","properties":{"sys":{"type":"object","properties":{"type":{"type":"string","enum":["ResourceLink"]},"linkType":{"type":"string","enum":["Contentful:Entry"]},"urn":{"type":"string"}},"additionalProperties":false,"required":["linkType","type","urn"]}},"additionalProperties":false,"required":["sys"]},"Text":{"type":"object","properties":{"nodeType":{"type":"string","enum":["text"]},"value":{"type":"string"},"marks":{"type":"array","items":{"$ref":"#/definitions/Mark"}},"data":{"$ref":"#/definitions/NodeData"}},"additionalProperties":false,"required":["data","marks","nodeType","value"]},"Mark":{"type":"object","properties":{"type":{"type":"string"}},"additionalProperties":false,"required":["type"]},"NodeData":{"additionalProperties":true,"type":"object"}},"$schema":"http://json-schema.org/draft-07/schema#"}')}}]);