# PowerShell script to remove problematic srcset attributes from HTML file
$htmlFile = "index.html"
$content = Get-Content $htmlFile -Raw

# Remove srcset attributes that contain _next/image URLs
$content = $content -replace 'srcset="[^"]*_next/image[^"]*"', ''

# Remove any double spaces that might have been created
$content = $content -replace '  +', ' '

# Write the cleaned content back to the file
Set-Content $htmlFile $content -NoNewline

Write-Host "Fixed srcset attributes in $htmlFile"
