!function o(u,a,f){function c(n,t){if(!a[n]){if(!u[n]){var r="function"==typeof require&&require;if(!t&&r)return r(n,!0);if(s)return s(n,!0);var e=new Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e}var i=a[n]={exports:{}};u[n][0].call(i.exports,function(t){return c(u[n][1][t]||t)},i,i.exports,o,u,a,f)}return a[n].exports}for(var s="function"==typeof require&&require,t=0;t<f.length;t++)c(f[t]);return c}({1:[function(t,n,r){},{}],2:[function(m,r,e){(function(p){(function(){var t,n;t=this,n=function(){var t=t||function(s,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==p&&p.crypto&&(n=p.crypto),!n&&"function"==typeof m)try{n=m("crypto")}catch(t){}var e=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},r=Object.create||function(){function r(){}return function(t){var n;return r.prototype=t,n=new r,r.prototype=null,n}}(),i={},o=i.lib={},u=o.Base={extend:function(t){var n=r(this);return t&&n.mixIn(t),n.hasOwnProperty("init")&&this.init!==n.init||(n.init=function(){n.$super.init.apply(this,arguments)}),(n.init.prototype=n).$super=this,n},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var n in t)t.hasOwnProperty(n)&&(this[n]=t[n]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},l=o.WordArray=u.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=null!=n?n:4*t.length},toString:function(t){return(t||f).stringify(this)},concat:function(t){var n=this.words,r=t.words,e=this.sigBytes,i=t.sigBytes;if(this.clamp(),e%4)for(var o=0;o<i;o++){var u=r[o>>>2]>>>24-o%4*8&255;n[e+o>>>2]|=u<<24-(e+o)%4*8}else for(var a=0;a<i;a+=4)n[e+a>>>2]=r[a>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=s.ceil(n/4)},clone:function(){var t=u.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var n=[],r=0;r<t;r+=4)n.push(e());return new l.init(n,t)}}),a=i.enc={},f=a.Hex={stringify:function(t){for(var n=t.words,r=t.sigBytes,e=[],i=0;i<r;i++){var o=n[i>>>2]>>>24-i%4*8&255;e.push((o>>>4).toString(16)),e.push((15&o).toString(16))}return e.join("")},parse:function(t){for(var n=t.length,r=[],e=0;e<n;e+=2)r[e>>>3]|=parseInt(t.substr(e,2),16)<<24-e%8*4;return new l.init(r,n/2)}},c=a.Latin1={stringify:function(t){for(var n=t.words,r=t.sigBytes,e=[],i=0;i<r;i++){var o=n[i>>>2]>>>24-i%4*8&255;e.push(String.fromCharCode(o))}return e.join("")},parse:function(t){for(var n=t.length,r=[],e=0;e<n;e++)r[e>>>2]|=(255&t.charCodeAt(e))<<24-e%4*8;return new l.init(r,n)}},h=a.Utf8={stringify:function(t){try{return decodeURIComponent(escape(c.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return c.parse(unescape(encodeURIComponent(t)))}},v=o.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=h.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(t){var n,r=this._data,e=r.words,i=r.sigBytes,o=this.blockSize,u=i/(4*o),a=(u=t?s.ceil(u):s.max((0|u)-this._minBufferSize,0))*o,f=s.min(4*a,i);if(a){for(var c=0;c<a;c+=o)this._doProcessBlock(e,c);n=e.splice(0,a),r.sigBytes-=f}return new l.init(n,f)},clone:function(){var t=u.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),d=(o.Hasher=v.extend({cfg:u.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){v.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(r){return function(t,n){return new r.init(n).finalize(t)}},_createHmacHelper:function(r){return function(t,n){return new d.HMAC.init(r,n).finalize(t)}}}),i.algo={});return i}(Math);return t},"object"==typeof e?r.exports=e=n():"function"==typeof define&&define.amd?define([],n):t.CryptoJS=n()}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{crypto:1}],3:[function(t,n,r){var e,i;e=this,i=function(t){return t.enc.Hex},"object"==typeof r?n.exports=r=i(t("./core")):"function"==typeof define&&define.amd?define(["./core"],i):i(e.CryptoJS)},{"./core":2}],4:[function(t,n,r){var e,i;e=this,i=function(f){return function(i){var t=f,n=t.lib,r=n.WordArray,e=n.Hasher,o=t.algo,u=[],E=[];!function(){function t(t){for(var n=i.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(t){return 4294967296*(t-(0|t))|0}for(var r=2,e=0;e<64;)t(r)&&(e<8&&(u[e]=n(i.pow(r,.5))),E[e]=n(i.pow(r,1/3)),e++),r++}();var T=[],a=o.SHA256=e.extend({_doReset:function(){this._hash=new r.init(u.slice(0))},_doProcessBlock:function(t,n){for(var r=this._hash.words,e=r[0],i=r[1],o=r[2],u=r[3],a=r[4],f=r[5],c=r[6],s=r[7],l=0;l<64;l++){if(l<16)T[l]=0|t[n+l];else{var h=T[l-15],v=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,d=T[l-2],p=(d<<15|d>>>17)^(d<<13|d>>>19)^d>>>10;T[l]=v+T[l-7]+p+T[l-16]}var m=e&i^e&o^i&o,w=(e<<30|e>>>2)^(e<<19|e>>>13)^(e<<10|e>>>22),y=s+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&f^~a&c)+E[l]+T[l];s=c,c=f,f=a,a=u+y|0,u=o,o=i,i=e,e=y+(w+m)|0}r[0]=r[0]+e|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+u|0,r[4]=r[4]+a|0,r[5]=r[5]+f|0,r[6]=r[6]+c|0,r[7]=r[7]+s|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,e=8*t.sigBytes;return n[e>>>5]|=128<<24-e%32,n[14+(e+64>>>9<<4)]=i.floor(r/4294967296),n[15+(e+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t}});t.SHA256=e._createHelper(a),t.HmacSHA256=e._createHmacHelper(a)}(Math),f.SHA256},"object"==typeof r?n.exports=r=i(t("./core")):"function"==typeof define&&define.amd?define(["./core"],i):i(e.CryptoJS)},{"./core":2}],5:[function(t,n,r){var e,i,o=n.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function f(n){if(e===setTimeout)return setTimeout(n,0);if((e===u||!e)&&setTimeout)return e=setTimeout,setTimeout(n,0);try{return e(n,0)}catch(t){try{return e.call(null,n,0)}catch(t){return e.call(this,n,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:u}catch(t){e=u}try{i="function"==typeof clearTimeout?clearTimeout:a}catch(t){i=a}}();var c,s=[],l=!1,h=-1;function v(){l&&c&&(l=!1,c.length?s=c.concat(s):h=-1,s.length&&d())}function d(){if(!l){var t=f(v);l=!0;for(var n=s.length;n;){for(c=s,s=[];++h<n;)c&&c[h].run();h=-1,n=s.length}c=null,l=!1,function(n){if(i===clearTimeout)return clearTimeout(n);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(n);try{i(n)}catch(t){try{return i.call(null,n)}catch(t){return i.call(this,n)}}}(t)}}function p(t,n){this.fun=t,this.array=n}function m(){}o.nextTick=function(t){var n=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)n[r-1]=arguments[r];s.push(new p(t,n)),1!==s.length||l||f(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=m,o.addListener=m,o.once=m,o.off=m,o.removeListener=m,o.removeAllListeners=m,o.emit=m,o.prependListener=m,o.prependOnceListener=m,o.listeners=function(t){return[]},o.binding=function(t){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],6:[function(t,n,r){(function(p,m){(function(){"use strict";function t(n){var r=this.constructor;return this.then(function(t){return r.resolve(n()).then(function(){return t})},function(t){return r.resolve(n()).then(function(){return r.reject(t)})})}function n(r){return new this(function(e,t){if(!r||void 0===r.length)return t(new TypeError(typeof r+" "+r+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var i=Array.prototype.slice.call(r);if(0===i.length)return e([]);var o=i.length;function u(n,t){if(t&&("object"==typeof t||"function"==typeof t)){var r=t.then;if("function"==typeof r)return void r.call(t,function(t){u(n,t)},function(t){i[n]={status:"rejected",reason:t},0==--o&&e(i)})}i[n]={status:"fulfilled",value:t},0==--o&&e(i)}for(var n=0;n<i.length;n++)u(n,i[n])})}function a(t,n){this.name="AggregateError",this.errors=t,this.message=n||""}function r(o){var u=this;return new u(function(t,n){if(!o||void 0===o.length)return n(new TypeError("Promise.any accepts an array"));var r=Array.prototype.slice.call(o);if(0===r.length)return n();for(var e=[],i=0;i<r.length;i++)try{u.resolve(r[i]).then(t).catch(function(t){e.push(t),e.length===r.length&&n(new a(e,"All promises were rejected"))})}catch(t){n(t)}})}a.prototype=Error.prototype;var e=setTimeout;function f(t){return Boolean(t&&void 0!==t.length)}function i(){}function o(t){if(!(this instanceof o))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],v(t,this)}function u(r,e){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,o._immediateFn(function(){var t=1===r._state?e.onFulfilled:e.onRejected;if(null!==t){var n;try{n=t(r._value)}catch(t){return void s(e.promise,t)}c(e.promise,n)}else(1===r._state?c:s)(e.promise,r._value)})):r._deferreds.push(e)}function c(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var r=t.then;if(t instanceof o)return n._state=3,n._value=t,void l(n);if("function"==typeof r)return void v((e=r,i=t,function(){e.apply(i,arguments)}),n)}n._state=1,n._value=t,l(n)}catch(t){s(n,t)}var e,i}function s(t,n){t._state=2,t._value=n,l(t)}function l(t){2===t._state&&0===t._deferreds.length&&o._immediateFn(function(){t._handled||o._unhandledRejectionFn(t._value)});for(var n=0,r=t._deferreds.length;n<r;n++)u(t,t._deferreds[n]);t._deferreds=null}function h(t,n,r){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.promise=r}function v(t,n){var r=!1;try{t(function(t){r||(r=!0,c(n,t))},function(t){r||(r=!0,s(n,t))})}catch(t){if(r)return;r=!0,s(n,t)}}o.prototype.catch=function(t){return this.then(null,t)},o.prototype.then=function(t,n){var r=new this.constructor(i);return u(this,new h(t,n,r)),r},o.prototype.finally=t,o.all=function(n){return new o(function(e,i){if(!f(n))return i(new TypeError("Promise.all accepts an array"));var o=Array.prototype.slice.call(n);if(0===o.length)return e([]);var u=o.length;function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var r=t.then;if("function"==typeof r)return void r.call(t,function(t){a(n,t)},i)}o[n]=t,0==--u&&e(o)}catch(t){i(t)}}for(var t=0;t<o.length;t++)a(t,o[t])})},o.any=r,o.allSettled=n,o.resolve=function(n){return n&&"object"==typeof n&&n.constructor===o?n:new o(function(t){t(n)})},o.reject=function(r){return new o(function(t,n){n(r)})},o.race=function(i){return new o(function(t,n){if(!f(i))return n(new TypeError("Promise.race accepts an array"));for(var r=0,e=i.length;r<e;r++)o.resolve(i[r]).then(t,n)})},o._immediateFn="function"==typeof m&&function(t){m(t)}||function(t){e(t,0)},o._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)};var d=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==p)return p;throw new Error("unable to locate global object")}();"function"!=typeof d.Promise?d.Promise=o:(d.Promise.prototype.finally||(d.Promise.prototype.finally=t),d.Promise.allSettled||(d.Promise.allSettled=n),d.Promise.any||(d.Promise.any=r))}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},t("timers").setImmediate)},{timers:7}],7:[function(f,t,c){(function(r,a){(function(){var e=f("process/browser.js").nextTick,t=Function.prototype.apply,i=Array.prototype.slice,o={},u=0;function n(t,n){this._id=t,this._clearFn=n}c.setTimeout=function(){return new n(t.call(setTimeout,window,arguments),clearTimeout)},c.setInterval=function(){return new n(t.call(setInterval,window,arguments),clearInterval)},c.clearTimeout=c.clearInterval=function(t){t.close()},n.prototype.unref=n.prototype.ref=function(){},n.prototype.close=function(){this._clearFn.call(window,this._id)},c.enroll=function(t,n){clearTimeout(t._idleTimeoutId),t._idleTimeout=n},c.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},c._unrefActive=c.active=function(t){clearTimeout(t._idleTimeoutId);var n=t._idleTimeout;0<=n&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},n))},c.setImmediate="function"==typeof r?r:function(t){var n=u++,r=!(arguments.length<2)&&i.call(arguments,1);return o[n]=!0,e(function(){o[n]&&(r?t.apply(null,r):t.call(null),c.clearImmediate(n))}),n},c.clearImmediate="function"==typeof a?a:function(t){delete o[t]}}).call(this)}).call(this,f("timers").setImmediate,f("timers").clearImmediate)},{"process/browser.js":5,timers:7}],8:[function(t,n,r){"use strict";function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(){N=function(){return u};var s,u={},t=Object.prototype,l=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",r=n.asyncIterator||"@@asyncIterator",e=n.toStringTag||"@@toStringTag";function h(t,n,r,e){return Object.defineProperty(t,n,{value:r,enumerable:!e,configurable:!e,writable:!e})}try{h({},"")}catch(s){h=function(t,n,r){return t[n]=r}}function a(t,n,r,e){var o,u,a,f,i=n&&n.prototype instanceof p?n:p,c=Object.create(i.prototype);return h(c,"_invoke",(o=t,u=r,a=new A(e||[]),f=1,function(t,n){if(3===f)throw Error("Generator is already running");if(4===f){if("throw"===t)throw n;return{value:s,done:!0}}for(a.method=t,a.arg=n;;){var r=a.delegate;if(r){var e=b(r,a);if(e){if(e===d)continue;return e}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===f)throw f=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);f=3;var i=v(o,u,a);if("normal"===i.type){if(f=a.done?4:2,i.arg===d)continue;return{value:i.arg,done:a.done}}"throw"===i.type&&(f=4,a.method="throw",a.arg=i.arg)}}),!0),c}function v(t,n,r){try{return{type:"normal",arg:t.call(n,r)}}catch(t){return{type:"throw",arg:t}}}u.wrap=a;var d={};function p(){}function o(){}function f(){}var c={};h(c,i,function(){return this});var m=Object.getPrototypeOf,w=m&&m(m(S([])));w&&w!==t&&l.call(w,i)&&(c=w);var y=f.prototype=p.prototype=Object.create(c);function E(t){["next","throw","return"].forEach(function(n){h(t,n,function(t){return this._invoke(n,t)})})}function T(f,c){var n;h(this,"_invoke",function(r,e){function t(){return new c(function(t,n){!function n(t,r,e,i){var o=v(f[t],f,r);if("throw"!==o.type){var u=o.arg,a=u.value;return a&&"object"==I(a)&&l.call(a,"__await")?c.resolve(a.__await).then(function(t){n("next",t,e,i)},function(t){n("throw",t,e,i)}):c.resolve(a).then(function(t){u.value=t,e(u)},function(t){return n("throw",t,e,i)})}i(o.arg)}(r,e,t,n)})}return n=n?n.then(t,t):t()},!0)}function b(t,n){var r=n.method,e=t.i[r];if(e===s)return n.delegate=null,"throw"===r&&t.i.return&&(n.method="return",n.arg=s,b(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=v(e,t.i,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,d;var o=i.arg;return o?o.done?(n[t.r]=o.value,n.next=t.n,"return"!==n.method&&(n.method="next",n.arg=s),n.delegate=null,d):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}function g(t){this.tryEntries.push(t)}function _(t){var n=t[4]||{};n.type="normal",n.arg=s,t[4]=n}function A(t){this.tryEntries=[[-1]],t.forEach(g,this),this.reset(!0)}function S(n){if(null!=n){var t=n[i];if(t)return t.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var r=-1,e=function t(){for(;++r<n.length;)if(l.call(n,r))return t.value=n[r],t.done=!1,t;return t.value=s,t.done=!0,t};return e.next=e}}throw new TypeError(I(n)+" is not iterable")}return h(y,"constructor",o.prototype=f),h(f,"constructor",o),o.displayName=h(f,e,"GeneratorFunction"),u.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===o||"GeneratorFunction"===(n.displayName||n.name))},u.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,h(t,e,"GeneratorFunction")),t.prototype=Object.create(y),t},u.awrap=function(t){return{__await:t}},E(T.prototype),h(T.prototype,r,function(){return this}),u.AsyncIterator=T,u.async=function(t,n,r,e,i){void 0===i&&(i=Promise);var o=new T(a(t,n,r,e),i);return u.isGeneratorFunction(n)?o:o.next().then(function(t){return t.done?t.value:o.next()})},E(y),h(y,e,"Generator"),h(y,i,function(){return this}),h(y,"toString",function(){return"[object Generator]"}),u.keys=function(t){var n=Object(t),r=[];for(var e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},u.values=S,A.prototype={constructor:A,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&l.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function t(t){o.type="throw",o.arg=n,r.next=t}for(var e=r.tryEntries.length-1;0<=e;--e){var i=this.tryEntries[e],o=i[4],u=this.prev,a=i[1],f=i[2];if(-1===i[0])return t("end"),!1;if(!a&&!f)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=u){if(u<a)return this.method="next",this.arg=s,t(a),!0;if(u<f)return t(f),!1}}},abrupt:function(t,n){for(var r=this.tryEntries.length-1;0<=r;--r){var e=this.tryEntries[r];if(-1<e[0]&&e[0]<=this.prev&&this.prev<e[2]){var i=e;break}}i&&("break"===t||"continue"===t)&&i[0]<=n&&n<=i[2]&&(i=null);var o=i?i[4]:{};return o.type=t,o.arg=n,i?(this.method="next",this.next=i[2],d):this.complete(o)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),d},finish:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r[2]===t)return this.complete(r[4],r[3]),_(r),d}},catch:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r[0]===t){var e=r[4];if("throw"===e.type){var i=e.arg;_(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={i:S(t),r:n,n:r},"next"===this.method&&(this.arg=s),d}},u}function f(t,n,r,e,i,o,u){try{var a=t[o](u),f=a.value}catch(t){return void r(t)}a.done?n(f):Promise.resolve(f).then(e,i)}var c=t("../utils/html"),s=t("../constants"),l=t("../utils/reduceDefered.js").reduceDefered,a=t("../validators/identityHasher").normalizeEmail,h=t("../validators/identityHasher").isValidEmail,o=t("../validators/identityHasher").hashIfSet,u=t("../cookie").getFirstCookie,v=t("../utils/performanceLogger"),d=/[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+/g;function e(){var a;return a=N().mark(function t(n){var r,e,i,o,u,a,f,c;return N().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:for(r=document.body.querySelectorAll("*"),e=[],i=0;i<r.length;i++)(o=r[i])&&e.push(o);return v.stop(n),t.next=6,l(e,100,n)(p,{});case 6:u=t.sent,v.start(n),a=[],f=N().mark(function t(n){return N().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:u.hasOwnProperty(n)&&u[n].forEach(function(t){a.push({email:n,element:t})});case 1:case"end":return t.stop()}},t)}),t.t0=N().keys(u);case 11:if((t.t1=t.t0()).done){t.next=16;break}return c=t.t1.value,t.delegateYield(f(c),"t2",14);case 14:t.next=11;break;case 16:return t.abrupt("return",w(a));case 17:case"end":return t.stop()}},t)}),(e=function(){var t=this,u=arguments;return new Promise(function(n,r){var e=a.apply(t,u);function i(t){f(e,n,r,i,o,"next",t)}function o(t){f(e,n,r,i,o,"throw",t)}i(void 0)})}).apply(this,arguments)}function p(t,n){if(c.isScript(n)||c.isAnyNonTextSVG(n))return t;var r=function(t){var n=t.match(d);if(!n)return[];for(var r=[],e=0;e<n.length;e++)h(n[e])&&r.push(n[e]);return r}(c.getBestAvailableText(n));if(0==r.length)return t;for(var e=0;e<r.length;e++){var i=a(r[e]);if(m(i))return t;var o=t[i];if(!o)return t[i]=[n],t;for(var u=0;u<o.length;u++)o[u].contains(n)&&o.splice(u,1);return o.push(n),t[i]=o,t}}function m(t){for(var n,r,e,i=(n=t.split("@"),r=n[0],e=n[1],[r,e]),o=i[0],u=i[1],a=window.location.hostname,f=!1,c=0;c<s.AAM.t.length;c++)if(s.AAM.t[c]===o){f=!0;break}return f||-1!==a.indexOf(u)||-1!==u.indexOf(a)}function w(t){if(0===t.length)return[];var o=[],u=[],a=[];return t.forEach(function(t){var n=t.element,r=c.isInput(n);if(r&&"email"===n.type)o.unshift(t);else if(r)o.push(t);else{var e=function(t){for(;t&&1===t.nodeType;){if(y(t))return t;t=t.parentElement||t.parentNode}return null}(n),i=n.parentNode.querySelector(s.AAM.o);e||i?u.push(t):a.push(t)}}),o.concat(u,a)}function y(r){return["class","id","aria-label","name","title"].some(function(t){var n=r.getAttribute(t);return n&&-1!==n.indexOf("email")})}n.exports={u:function(t){return e.apply(this,arguments)},l:function(t){var n=[],r=function(){var t=u(s.EMAIL_COOKIE_NAME);if(!t)return"";var n=t.split(s.AAM.h);return 2==n.length?n[1]:""}();if(!r)return t;for(var e=r.split(s.AAM.p),i=0;i<e.length;i++)-1===t.indexOf(e[i])&&n.push(e[i]);return t.concat(n)},m:function(t){for(var n=[],r=0;r<t.length;r++){var e=t[r],i=o(e.email,"email");n.push(i)}return n}}},{"../constants":11,"../cookie":12,"../utils/html":29,"../utils/performanceLogger":30,"../utils/reduceDefered.js":31,"../validators/identityHasher":34}],9:[function(t,n,r){"use strict";function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(){N=function(){return u};var s,u={},t=Object.prototype,l=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",r=n.asyncIterator||"@@asyncIterator",e=n.toStringTag||"@@toStringTag";function h(t,n,r,e){return Object.defineProperty(t,n,{value:r,enumerable:!e,configurable:!e,writable:!e})}try{h({},"")}catch(s){h=function(t,n,r){return t[n]=r}}function a(t,n,r,e){var o,u,a,f,i=n&&n.prototype instanceof p?n:p,c=Object.create(i.prototype);return h(c,"_invoke",(o=t,u=r,a=new A(e||[]),f=1,function(t,n){if(3===f)throw Error("Generator is already running");if(4===f){if("throw"===t)throw n;return{value:s,done:!0}}for(a.method=t,a.arg=n;;){var r=a.delegate;if(r){var e=b(r,a);if(e){if(e===d)continue;return e}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===f)throw f=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);f=3;var i=v(o,u,a);if("normal"===i.type){if(f=a.done?4:2,i.arg===d)continue;return{value:i.arg,done:a.done}}"throw"===i.type&&(f=4,a.method="throw",a.arg=i.arg)}}),!0),c}function v(t,n,r){try{return{type:"normal",arg:t.call(n,r)}}catch(t){return{type:"throw",arg:t}}}u.wrap=a;var d={};function p(){}function o(){}function f(){}var c={};h(c,i,function(){return this});var m=Object.getPrototypeOf,w=m&&m(m(S([])));w&&w!==t&&l.call(w,i)&&(c=w);var y=f.prototype=p.prototype=Object.create(c);function E(t){["next","throw","return"].forEach(function(n){h(t,n,function(t){return this._invoke(n,t)})})}function T(f,c){var n;h(this,"_invoke",function(r,e){function t(){return new c(function(t,n){!function n(t,r,e,i){var o=v(f[t],f,r);if("throw"!==o.type){var u=o.arg,a=u.value;return a&&"object"==I(a)&&l.call(a,"__await")?c.resolve(a.__await).then(function(t){n("next",t,e,i)},function(t){n("throw",t,e,i)}):c.resolve(a).then(function(t){u.value=t,e(u)},function(t){return n("throw",t,e,i)})}i(o.arg)}(r,e,t,n)})}return n=n?n.then(t,t):t()},!0)}function b(t,n){var r=n.method,e=t.i[r];if(e===s)return n.delegate=null,"throw"===r&&t.i.return&&(n.method="return",n.arg=s,b(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=v(e,t.i,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,d;var o=i.arg;return o?o.done?(n[t.r]=o.value,n.next=t.n,"return"!==n.method&&(n.method="next",n.arg=s),n.delegate=null,d):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}function g(t){this.tryEntries.push(t)}function _(t){var n=t[4]||{};n.type="normal",n.arg=s,t[4]=n}function A(t){this.tryEntries=[[-1]],t.forEach(g,this),this.reset(!0)}function S(n){if(null!=n){var t=n[i];if(t)return t.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var r=-1,e=function t(){for(;++r<n.length;)if(l.call(n,r))return t.value=n[r],t.done=!1,t;return t.value=s,t.done=!0,t};return e.next=e}}throw new TypeError(I(n)+" is not iterable")}return h(y,"constructor",o.prototype=f),h(f,"constructor",o),o.displayName=h(f,e,"GeneratorFunction"),u.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===o||"GeneratorFunction"===(n.displayName||n.name))},u.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,h(t,e,"GeneratorFunction")),t.prototype=Object.create(y),t},u.awrap=function(t){return{__await:t}},E(T.prototype),h(T.prototype,r,function(){return this}),u.AsyncIterator=T,u.async=function(t,n,r,e,i){void 0===i&&(i=Promise);var o=new T(a(t,n,r,e),i);return u.isGeneratorFunction(n)?o:o.next().then(function(t){return t.done?t.value:o.next()})},E(y),h(y,e,"Generator"),h(y,i,function(){return this}),h(y,"toString",function(){return"[object Generator]"}),u.keys=function(t){var n=Object(t),r=[];for(var e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},u.values=S,A.prototype={constructor:A,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&l.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function t(t){o.type="throw",o.arg=n,r.next=t}for(var e=r.tryEntries.length-1;0<=e;--e){var i=this.tryEntries[e],o=i[4],u=this.prev,a=i[1],f=i[2];if(-1===i[0])return t("end"),!1;if(!a&&!f)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=u){if(u<a)return this.method="next",this.arg=s,t(a),!0;if(u<f)return t(f),!1}}},abrupt:function(t,n){for(var r=this.tryEntries.length-1;0<=r;--r){var e=this.tryEntries[r];if(-1<e[0]&&e[0]<=this.prev&&this.prev<e[2]){var i=e;break}}i&&("break"===t||"continue"===t)&&i[0]<=n&&n<=i[2]&&(i=null);var o=i?i[4]:{};return o.type=t,o.arg=n,i?(this.method="next",this.next=i[2],d):this.complete(o)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),d},finish:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r[2]===t)return this.complete(r[4],r[3]),_(r),d}},catch:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r[0]===t){var e=r[4];if("throw"===e.type){var i=e.arg;_(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={i:S(t),r:n,n:r},"next"===this.method&&(this.arg=s),d}},u}function f(t,n,r,e,i,o,u){try{var a=t[o](u),f=a.value}catch(t){return void r(t)}a.done?n(f):Promise.resolve(f).then(e,i)}var e,i=t("../constants").AAM,o=t("../cookie"),u=t("../utils/performanceLogger"),c=t("./email"),a=t("../utils/adaptiveInterval").setAdaptiveInterval,s="aam-em";function l(){return h.apply(this,arguments)}function h(){var a;return a=N().mark(function t(){var n,r,e;return N().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return u.reset(s),t.next=3,c.u(s);case 3:n=t.sent,0!==(r=c.m(n)).length&&(e=c.l(r),rdt.config.auto_em=e.slice(0,i.T).join(i.p),!1!==rdt.enableFirstPartyCookies&&o.setEmailCookie()),u.stop(s);case 7:case"end":return t.stop()}},t)}),(h=function(){var t=this,u=arguments;return new Promise(function(n,r){var e=a.apply(t,u);function i(t){f(e,n,r,i,o,"next",t)}function o(t){f(e,n,r,i,o,"throw",t)}i(void 0)})}).apply(this,arguments)}u.register(s),n.exports={initAAM:function(){e&&e(),e=a(l,s,{totalTimePercentage:i.g,cpuTimePercentage:i._},i.A,i.S,0,!0)},disableAAM:function(){e&&e(),e=void 0}}},{"../constants":11,"../cookie":12,"../utils/adaptiveInterval":27,"../utils/performanceLogger":30,"./email":8}],10:[function(t,n,r){"use strict";var e,i=t("./constants"),o=t("./helper"),u=new XMLHttpRequest,a=window.rdt;if(a&&(e=a.pixelId+"_telemetry"),u.onreadystatechange=function(){u.readyState==XMLHttpRequest.DONE&&u.status,u.readyState==XMLHttpRequest.DONE&&200!=u.status&&o.sendErrorReport(["CCS error: "+JSON.stringify({status:u.status,response:u.responseText})])},u.open("GET",i.EVENT_CONFIG.CONVERSIONS_CONFIG_PIXEL_URL+"/"+e),"JSON"in window)u.send();else{var f=document.createElement("script");f.async=!0,f.src="https://www.redditstatic.com/ads/json3.min.js",f.onload=function(){u.send()};var c=document.getElementsByTagName("script")[0];c.parentNode.insertBefore(f,c)}},{"./constants":11,"./helper":16}],11:[function(t,n,r){"use strict";var e="rdt_cid",i="PageVisit",o="ViewContent",u="AddToCart",a="AddToWishlist",f="Purchase",c="Lead",s="SignUp",l="Custom",h="itemCount",v="value",d="valueDecimal",p="currency",m="transactionId",w="products",y="conversionId",E="customEventName";n.exports={CLICK_ID_NAME:e,PIXEL_ENDPOINT:"https://alb.reddit.com/rp.gif",ADS_UI_ENDPOINT:"https://ads.reddit.com",ADS_UI_STATIC_BASE:"https://www.redditstatic.com/campaign-management/",UNIX_EPOCH:"Thu, 01 Jan 1970 00:00:00 GMT",COOKIE_EXPIRATION_DAYS:90,CLICK_ID_COOKIE_NAME:"_"+e,EMAIL_COOKIE_NAME:"_rdt_em",UUID_COOKIE_NAME_V2:"_rdt_uuid",METADATA_PREFIX:"m.",CUSTOM_EVENT_NAME_LIMIT:128,CONVERSION_EVENTS:{PAGEVISIT:i,VIEWCONTENT:o,SEARCH:"Search",ADDTOCART:u,ADDTOWISHLIST:a,PURCHASE:f,LEAD:c,SIGNUP:s,CUSTOM:l},CONVERSION_EVENTS_LIST:[i,o,"Search",u,a,f,c,s,l],INTEGRATION_PROVIDERS:{REDDIT:"reddit",GTM:"gtm",NOSCRIPT:"noscript"},PIXEL_METHODS:{INIT:"init",ENABLE_FIRST_PARTY_COOKIES:"enableFirstPartyCookies",TRACK:"track",DISABLE_FIRST_PARTY_COOKIES:"disableFirstPartyCookies"},EVENT_METADATA:{ITEM_COUNT:h,VALUE:v,VALUE_DECIMAL:d,CURRENCY:p,TRANSACTION_ID:m,CUSTOM_EVENT_NAME:E,PRODUCTS:w,CONVERSION_ID:y},EVENT_METADATA_LIST:[h,v,d,p,m,E,w,y],REVENUE_METADATA_LIST:[h,v,d,p,m],DEFAULT_CURRENCY:"USD",REVENUE_EVENTS_LIST:[u,a,f,c,s,l],EVENT_CONFIG:{PIXEL_URL_DEFAULT:"https://www.redditstatic.com/ads/LATEST/pixel.js",CONVERSIONS_CONFIG_PIXEL_URL:"https://www.redditstatic.com/ads/conversions-config/v1/pixel/config",CONVERSIONS_CONFIG_ERROR_URL:"https://conversions-config.reddit.com/v1/pixel/error",EVENT_CONFIGS_URL:"https://pixel-config.reddit.com/pixels"},EVENT_SETUP:{PAGE_LISTENERS_UPDATE_TIMEOUT:5e3,URL_FETCH_TIMEOUT:50,EVENT_CONFIG:{TRIGGER_TYPE:{CLICK:"CLICK",URL:"URL"},TRIGGER_SELECTOR_TYPE:{CLICK_CSS:"CLICK_CSS",CLICK_TEXT:"CLICK_TEXT",URL_CONTAINS:"URL_CONTAINS",URL_EXACT:"URL_EXACT"},METADATA_EXTRACTOR_TYPE:{FIXED:"FIXED",SELECTOR:"SELECTOR"},METADATA_SELECTOR_TYPE:{CSS:"METADATA_CSS",TEXT:"METADATA_TEXT"}}},AAM:{_:.05,g:.25,A:500,S:1e4,t:["support","help","contact","info","admin","sales","billing","careers","webmaster","helpdesk","inquiry","marketing","pr","feedback"],o:'[class*="email"], [id*="email"], [aria-label*="email"], [name*="email"], [title*="email"]',h:":",T:5,p:","}}},{}],12:[function(t,n,r){"use strict";var c=t("./constants"),a=t("./strings"),e=t("./uuid"),i=function(t){return(new Date).getTime()+"."+t};r.addTimestampToCookie=i,r.extractUuidFromCookieV2=function(t){var n=t.split(".");return n.length<2?t:n[1]},r.createCookieV2=function(){return i(e())},r.getCookieV2=function(){for(var t=document.cookie.split(";"),n="",r=1/0,e=0;e<t.length;e++){var i=t[e].split("=");if(1<i.length)if(a.trim(i[0])===c.UUID_COOKIE_NAME_V2){var o=i[1].split(".");if(1<o.length){var u=parseInt(o[0],10);!isNaN(u)&&u<r&&(r=u,n=i[1])}}}return n};var s=function(t){for(var n=document.cookie.split(";"),r=0;r<n.length;r++){var e=n[r].split("=");if(1<e.length){var i=a.trim(e[0]),o=e[1];if(i===t&&o.length)return o}}return null};r.getFirstCookie=s,r.setClickIdCookie=function(){if(rdt.clickId)h(c.CLICK_ID_COOKIE_NAME,rdt.clickId,c.COOKIE_EXPIRATION_DAYS);else{var t=s(c.CLICK_ID_COOKIE_NAME);t&&(rdt.clickIdFromCookie=!0,rdt.clickId=t)}},r.setEmailCookie=function(){var t,n,r,e="config"in rdt?rdt.config.em:null,i="config"in rdt?rdt.config.auto_em:null,o=s(c.EMAIL_COOKIE_NAME),u=!!e,a=!!i;if(!!o){var f=o.split(c.AAM.h);1==f.length?t=o:2==f.length&&(t=f[0],n=f[1])}("config"in rdt==!1&&(rdt.config={}),t&&!u&&(rdt.emailFromCookie=!0,rdt.config.em=t),n&&!a&&(rdt.autoEmailsFromCookie=!0,rdt.config.auto_em=n),rdt.config.em||rdt.config.auto_em)&&(rdt.config.em&&(r=rdt.config.em),rdt.config.auto_em&&(r=(r||"")+c.AAM.h+rdt.config.auto_em),h(c.EMAIL_COOKIE_NAME,r,c.COOKIE_EXPIRATION_DAYS))};var f=function(t){return""+Number(t)===t};r.stringIsInteger=f;var l=function(t,n,r,e){var i=t+"="+n+";domain=."+r+";expires="+e+";path=/;samesite=strict";return"https:"===window.location.protocol&&(i+=";secure"),document.cookie=i,0<=document.cookie.indexOf(t+"="+n)};r.setAndTestCookie=l;var h=function(t,n,r){var e=new Date,i=24*r*60*60;e.setTime(e.getTime()+1e3*i);var o=e.toUTCString(),u=window.location.hostname;u="."==u.slice(-1)?u.slice(0,-1):u,v(t,u);var a=u.split(".");if(1==a.length||f(a[a.length-1]))return l(t,n,u,o),u;for(u=a.pop(),u=a.pop()+"."+u;!l(t,n,u,o)&&a.length;)u=a.pop()+"."+u;return u};r.setCookie=h;var v=function(t,n){for(var r=(n=n||window.location.hostname).split(".");l(t,"",r.join("."),c.UNIX_EPOCH)&&r.length;)r.shift();return r.join(".")};r.deleteCookie=v},{"./constants":11,"./strings":26,"./uuid":32}],13:[function(t,n,r){"use strict";r.screenSize=function(){var t=1*window.screen.width,n=1*window.screen.height;return{sh:Math.max(t,n),sw:Math.min(t,n)}}},{}],14:[function(t,n,r){"use strict";var i=t("./constants");function e(t){if(t.origin===i.ADS_UI_ENDPOINT&&"MOUNT_EST"===t.data&&t.source===window.opener){try{o()}catch(t){console.error("Failed to inject EST script. Error: ",t)}removeEventListener("message",e)}}function o(){fetch(i.ADS_UI_ENDPOINT+"/public/estManifest").then(function(t){return t.json()}).then(function(t){var n=t.assets;fetch(i.ADS_UI_STATIC_BASE+n["estStyles.css"]).then(function(t){t.text().then(function(t){var n=document.createElement("style");n.innerText=t,document.head.append(n)})});var r=document.createElement("script");r.src=i.ADS_UI_STATIC_BASE+n["estStyles.js"];var e=document.createElement("script");e.src=i.ADS_UI_STATIC_BASE+n["eventSetupTool.js"],document.body.append(r,e)})}function u(){return-1<window.location.search.indexOf("reddit_open_est=true")}r.isOpenedViaAdsUI=u,r.initEventSetupListener=function(){u()&&!window.opener&&"append"in document.body?o():null!==window.opener&&void 0!==window.opener&&"postMessage"in window.opener!=!1&&"append"in document.body!=!1&&(window.opener.postMessage("PIXEL_READY",i.ADS_UI_ENDPOINT),window.addEventListener("message",e))}},{"./constants":11}],15:[function(t,n,r){"use strict";var i=t("./constants"),o=t("./helper"),u=t("./pageEventsListeners"),a=t("./automaticAdvancedMatching/index");n.exports={getPixelConfig:function(t){var n="".concat(i.EVENT_CONFIG.EVENT_CONFIGS_URL,"/").concat(t,"/config"),r=new XMLHttpRequest;r.open("GET",n);var e=function(){o.sendErrorReport(["Pixel Config error: "+JSON.stringify({status:r.status,response:r.responseText})])};r.onreadystatechange=function(){var t;r.readyState===XMLHttpRequest.DONE&&(200===r.status?""!==r.response&&("pixelEventConfigs"in(t=JSON.parse(r.response))&&u.initEventsListeners(t.pixelEventConfigs),"aam"in t&&("aam"in rdt==0&&(rdt.aam={}),rdt.aam.email=t.aam.email,rdt.aam.email?a.initAAM():a.disableAAM())):e())},r.ontimeout=e,r.send()}}},{"./automaticAdvancedMatching/index":9,"./constants":11,"./helper":16,"./pageEventsListeners":22}],16:[function(t,n,r){"use strict";var i=t("./constants");n.exports={sendErrorReport:function(t){var n=i.EVENT_CONFIG.CONVERSIONS_CONFIG_ERROR_URL,r=new XMLHttpRequest;r.open("PUT",n);var e={messages:t,timestamp:(new Date).getTime()};r.send(JSON.stringify(e))}}},{"./constants":11}],17:[function(t,n,r){"use strict";var i,o=t("./utils"),u=t("../utils/html"),a=t("../constants").EVENT_SETUP.EVENT_CONFIG.TRIGGER_SELECTOR_TYPE;function f(){i=document.body.querySelectorAll("*")}function c(r,e){var t=s(e);function n(t){var n=o.configToRdtEvent(r,e);window.rdt(n.method,n.eventName,n.metadataArgs)}return null!==t&&t.addEventListener("click",n,!0),{config:r,trigger:e,listener:n,target:t}}function s(t){var n=null;if(t.triggerSelectorType===a.CLICK_CSS&&(n=document.querySelector(t.triggerSelectorValue)),t.triggerSelectorType===a.CLICK_TEXT)for(var r=0;r<i.length;r++){var e=i[r];if(u.getRenderedText(e)===t.triggerSelectorValue){n=e;break}}return n}window.rdt.updatePageElementsList=f,window.rdt.queryClickTriggerTarget=s,n.exports={initClickListeners:function(t){f();for(var n=[],r=0;r<t.length;r++)for(var e=t[r],i=e.triggers,o=0;o<i.length;o++){var u=i[o];n.push(c(e,u))}return n},updateClickListeners:function(t){f();for(var n=0;n<t.length;n++){var r=t[n],e=null!==r.target,i=document.body.contains(r.target);if(!e||!i){var o=c(r.config,r.trigger);t[n]=o}}}}},{"../constants":11,"../utils/html":29,"./utils":20}],18:[function(t,n,r){"use strict";var s=t("./utils"),f=t("./urlMatchers.js"),c=t("../constants").EVENT_SETUP.EVENT_CONFIG.TRIGGER_SELECTOR_TYPE,a=location.hostname,v=location.pathname,d=location.search,p=location.hostname,m=location.pathname,w=location.search;function i(t,n,r,e){var i=t.triggers[0],o=l(i,n,r,e),u=o.hasMatched,a=o.matches,f=o.matcher;if(u){var c=s.configToRdtEvent(t,i);window.rdt(c.method,c.eventName,c.metadataArgs)}return{config:t,hasMatched:u,matcher:f,matches:a}}function e(){p=a,m=v,w=d,a=location.hostname,v=location.pathname,d=location.search}function o(t){var n,r,e=t.config.triggers[0],i=t.matcher;if("EXACT"===i.type&&(n=f.matchExact(v,d,i)),"KEYWORDS"===i.type){var o=f.matchKeywords(a,v,d,i);n=null!==o,r=o||void 0}if(function(t,n,r){if(!n)return!1;if(!t.hasMatched)return!0;if("KEYWORDS"===t.matcher.type){if(v!==m)return!0;for(var e=v+d,i=p+m+w,o=0;o<r.length;o++){var u=r[o],a=t.matches[o];if(u.length!==a.length)return!0;for(var f=0;f<u;f++){var c=u[f],s=a[f];if(c!==s)return!0;var l=e.substring(0,c),h=i.substring(0,s);if(l!==h)return!0}}}return!1}(t,n,r)){var u=s.configToRdtEvent(t.config,e);window.rdt(u.method,u.eventName,u.metadataArgs)}return{config:t.config,hasMatched:n,matcher:i,matches:r}}function l(t,n,r,e){var i,o,u;if(t.triggerSelectorType===c.URL_EXACT&&(o=f.generateExactMatcher(t),i=f.matchExact(r,e,o)),t.triggerSelectorType===c.URL_CONTAINS){o=f.generateKeywordsMatcher(t);var a=f.matchKeywords(n,r,e,o);i=null!==a,u=a||void 0}return{hasMatched:i,matches:u,matcher:o}}window.rdt.matchURLTrigger=l,window.rdt.matchURLTriggerVersion="2",n.exports={initURLListeners:function(t){for(var n=[],r=0;r<t.length;r++){var e=t[r];n.push(i(e,a,v,d))}return n},updateURLListeners:function(t){if(v!==location.pathname||d!==location.search){e();for(var n=0;n<t.length;n++)t[n]=o(t[n])}},updateUrl:e}},{"../constants":11,"./urlMatchers.js":19,"./utils":20}],19:[function(t,n,r){"use strict";function o(t){var n=[];return 0<t.length&&(n=t.split(/[?&]/).filter(function(t){return""!==t})),n}n.exports={matchExact:function(t,n,r){if(t!==r.path)return!1;var e=o(n);return r.params.every(function(t){return-1!==e.indexOf(t)})},matchKeywords:function(t,n,r,e){for(var i=t+n+r,o=[],u=0;u<e.keywords.length;u++){for(var a=e.keywords[u],f=[],c=i.indexOf(a);-1!==c;c=i.indexOf(a,c+1))f.push(c);if(0===f.length)return null;o.push(f)}return o},generateExactMatcher:function(t){var n,r,e=(n=t.triggerSelectorValue,(r=document.createElement("a")).href=n,{hash:r.hash,host:r.host,hostname:r.hostname,href:r.href,origin:r.origin,pathname:r.pathname,port:r.port,protocol:r.protocol,search:r.search}),i=o(e.search);return{type:"EXACT",path:e.pathname,params:i}},generateKeywordsMatcher:function(t){return{type:"KEYWORDS",keywords:JSON.parse(t.triggerSelectorValue)}}}},{}],20:[function(t,n,r){"use strict";var o=t("../constants"),e=t("../utils/html"),i=o.EVENT_SETUP.EVENT_CONFIG.METADATA_EXTRACTOR_TYPE,u=o.EVENT_SETUP.EVENT_CONFIG.METADATA_SELECTOR_TYPE,a=o.EVENT_SETUP.EVENT_CONFIG.TRIGGER_TYPE;function f(t,n){if(t)for(var r=0;r<t.length;r++)if(t[r].metadataType===n)return t[r]}function c(t){if(t){if(t.metadataExtractorType===i.FIXED)return t.metadataFixedValue;if(t.metadataExtractorType===i.SELECTOR&&t.metadataSelectorType===u.CSS){var n=document.querySelector(t.metadataSelectorValue);if(n)return e.getRenderedText(n)}}}function s(t){return t&&"string"==typeof t?t.replace(/[^0-9\.]/gi,""):t}n.exports={configToRdtEvent:function(t,n){var r=f(t.metadata,"ITEM_COUNT"),e=f(t.metadata,"VALUE"),i=f(t.metadata,"CURRENCY");return{metadataArgs:function(t){for(var n in t)t.hasOwnProperty(n)&&void 0===t[n]&&delete t[n];return t}({itemCount:s(c(r)),value:s(c(e)),currency:c(i),triggerId:n.id,customEventName:t.eventConfigName}),method:o.PIXEL_METHODS.TRACK,eventName:t.trackingType}},isClickEvent:function(t){return 0!==t.triggers.length&&t.triggers[0].triggerType===a.CLICK},isURLEvent:function(t){return 0!==t.triggers.length&&t.triggers[0].triggerType===a.URL},isKeywordInUrl:function(t,n){var r,e=(r=t,JSON.parse(r)),i=n.toLowerCase();return e.every(function(t){return i.includes(t)})}}},{"../constants":11,"../utils/html":29}],21:[function(t,n,r){"use strict";var e=window.console||{},i={},o={error:"Reddit Pixel Error",warn:"Reddit Pixel Warning",info:"Reddit Pixel Info"},u=function(n){return function(t){n in e&&n in o&&e[n](o[n]+":"+t)}};i.error=u("error"),i.warn=u("warn"),i.info=u("info"),n.exports=i},{}],22:[function(t,n,r){"use strict";var e=t("./constants"),o=t("./listeners/utils"),i=t("./listeners/click"),u=t("./listeners/url"),a=i.initClickListeners,f=i.updateClickListeners,c=u.initURLListeners,s=u.updateURLListeners,l=e.EVENT_SETUP;n.exports={initEventsListeners:function(t){var n=t.filter(o.isClickEvent),r=t.filter(o.isURLEvent);if(0!==n.length){var e=a(n);setInterval(f,l.PAGE_LISTENERS_UPDATE_TIMEOUT,e)}if(0!==r.length){var i=c(r);setInterval(s,l.URL_FETCH_TIMEOUT,i)}}}},{"./constants":11,"./listeners/click":17,"./listeners/url":18,"./listeners/utils":20}],23:[function(t,n,r){"use strict";function e(t){"postMessage"in window&&postMessage(t,"*")}r.sendInit=function(t,n){e({type:"Init",config:t,args:n})},r.sendEventData=function(t,n){e({type:"EventData",eventData:t,args:n})},r.sendError=function(t,n){e({type:"Error",exceptionName:t,args:n,timestamp:(new Date).valueOf()})}},{}],24:[function(t,n,r){"use strict";function g(t){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}t("promise-polyfill/lib/polyfill");var _=Array.prototype.slice,A=t("./cookie"),S=t("./device"),I=t("./validators/identityHasher"),N=t("./logger"),C=t("./pixelHelperConnect"),x=t("./eventSetupConnect"),O=t("./validators/integrations"),L=t("./validators/event"),R=t("./constants"),M=t("./eventsConfigsLoader"),P=t("./automaticAdvancedMatching/index"),j=t("./queryString").getQueryParameter,D=t("./queryString").QueryBuilder,U=window.rdt;U||(N.error("Pixel was not initialized.Please ensure you have included the correct pixel script in your head tag"),C.sendError("PixelWasNotInitialised"));var k=function(){if(!1!==U.enableFirstPartyCookies){var t,n,r,e=A.getCookieV2()||A.createCookieV2();t=R.UUID_COOKIE_NAME_V2,n=e,r=R.COOKIE_EXPIRATION_DAYS,!1!==U.enableFirstPartyCookies&&A.setCookie(t,n,r),U.uuid=A.extractUuidFromCookieV2(e),A.setClickIdCookie(),A.setEmailCookie(),U.enableFirstPartyCookies=!0}},G=function(){A.deleteCookie(R.UUID_COOKIE_NAME_V2),A.deleteCookie(R.CLICK_ID_COOKIE_NAME),A.deleteCookie(R.EMAIL_COOKIE_NAME),U.enableFirstPartyCookies=!1,delete U.uuid,U.emailFromCookie&&delete U.config.em,U.autoEmailsFromCookie&&delete U.config.auto_em,U.clickIdFromCookie&&delete U.clickId};function e(t){var n,r,e,i,o,u=_.call(arguments),a=R.PIXEL_METHODS;switch(t){case a.INIT:U.pixelId=u[1];var f="object"===g(u[2])&&u[2]||{},c=S.screenSize();U.config={aaid:I.hashIfSet(f.aaid,"aaid"),em:I.hashIfSet(f.email,"email"),pn:I.hashIfSetNormalizeFirst(f.phoneNumber,"phoneNumber"),external_id:I.hashIfSet(f.externalId,"externalId"),idfa:I.hashIfSet(f.idfa,"idfa"),integration:O.getIntegrationProvider(f.integration),partner:f.partner||"",partner_version:f.partner_version||"",opt_out:f.optOut?1:0,sh:c.sh,sw:c.sw,v:"rdt_646b36ad"},i=f,o=L.normalizeDataProcessingOptions(i.dpm,i.dpcc,i.dprc),U.config.dpm=o.dpm,U.config.dpcc=o.dpcc,U.config.dprc=o.dprc,U.useDecimalCurrencyValues=!("useDecimalCurrencyValues"in f)||!!f.useDecimalCurrencyValues,"aam"in U==!1&&(U.aam={}),"aam"in f&&(U.aam.email=!!f.aam.email),(e=j(window.location.search,R.CLICK_ID_NAME))&&(U.clickId=e),f.disableFirstPartyCookies?G():k(),U.aam.email?P.initAAM():P.disableAAM(),C.sendInit(U.config,u);var s=localStorage.getItem("rdt_pixel_id");if(s&&(U.pixelId=s),!U.isESListenerMounted){U.isESListenerMounted=!0,x.initEventSetupListener();try{M.getPixelConfig(U.pixelId)}catch(t){N.error("Pixel Config error: ".concat(t))}}return;case a.ENABLE_FIRST_PARTY_COOKIES:return U.enableFirstPartyCookies=!0,void k();case a.DISABLE_FIRST_PARTY_COOKIES:return void G();case a.TRACK:var l=u[1],h=u[2]||{};if("email"in h){var v=I.hashIfSet(h.email,"email");delete h.email,v&&(U.config.em=v,A.setEmailCookie())}if("phoneNumber"in h){var d=I.hashIfSetNormalizeFirst(h.phoneNumber,"phoneNumber");delete h.phoneNumber,d&&(U.config.pn=d)}if("externalId"in h){var p=I.hashIfSet(h.externalId,"externalId");delete h.externalId,p&&(U.config.external_id=p)}if("idfa"in h){var m=I.hashIfSet(h.idfa,"idfa");delete h.idfa,m&&(U.config.idfa=m)}if("aaid"in h){var w=I.hashIfSet(h.aaid,"aaid");delete h.aaid,w&&(U.config.aaid=w)}var y="";"triggerId"in h&&(y=h.triggerId,delete h.triggerId);var E="";"partner_version"in h&&(E=h.partner_version,delete h.partner_version);var T=L.validateMetadata(l,h),b=(new D).addParams({id:U.pixelId,event:l}).addParams(T);return y&&b.addParams({trigger_id:y}),U.clickId&&U.clickId.length&&b.addParams({click_id:U.clickId}),U.uuid&&U.uuid.length&&b.addParams({uuid:U.uuid}),b.addParams(U.config),E&&b.addParams({partner_version:E}),n=R.PIXEL_ENDPOINT,r=b.toQueryString(),(new Image).src=n+r,void C.sendEventData(b.params,u)}}for(U.sendEvent=e;U.callQueue.length;)e.apply(U,U.callQueue.shift());t("./bootLoader")},{"./automaticAdvancedMatching/index":9,"./bootLoader":10,"./constants":11,"./cookie":12,"./device":13,"./eventSetupConnect":14,"./eventsConfigsLoader":15,"./logger":21,"./pixelHelperConnect":23,"./queryString":25,"./validators/event":33,"./validators/identityHasher":34,"./validators/integrations":35,"promise-polyfill/lib/polyfill":6}],25:[function(t,n,r){"use strict";var e=t("../lib/constants"),i=t("./logger"),o=function(){this.params={},this.params.ts=(new Date).valueOf()};o.prototype.addParams=function(t){for(var n in t)this.isMetadata(n)?this.params[e.METADATA_PREFIX+n]=t[n]:this.params[n]=t[n];return this},o.prototype.isMetadata=function(t){for(var n=0;n<e.EVENT_METADATA_LIST.length;n++)if(t===e.EVENT_METADATA_LIST[n])return!0;return!1},o.prototype.toQueryString=function(){var t=[];for(var n in this.params){var r=this.params[n],e="undefined";try{e=encodeURIComponent(r)}catch(t){i.warn("unsupported value type for "+n)}t.push(encodeURIComponent(n)+"="+e)}return"?"+t.join("&")},r.QueryBuilder=o,r.getQueryParameter=function(t,n){if(!t)return"";for(var r=t.slice(1).split("&"),e=0;e<r.length;e++){var i=r[e].split("=");if(i[0]===n)return i[1]}return""}},{"../lib/constants":11,"./logger":21}],26:[function(t,n,r){"use strict";var e=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;r.trim=function(t){return t.replace(e,"")}},{}],27:[function(t,n,r){"use strict";function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(){N=function(){return u};var s,u={},t=Object.prototype,l=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",r=n.asyncIterator||"@@asyncIterator",e=n.toStringTag||"@@toStringTag";function h(t,n,r,e){return Object.defineProperty(t,n,{value:r,enumerable:!e,configurable:!e,writable:!e})}try{h({},"")}catch(s){h=function(t,n,r){return t[n]=r}}function a(t,n,r,e){var o,u,a,f,i=n&&n.prototype instanceof p?n:p,c=Object.create(i.prototype);return h(c,"_invoke",(o=t,u=r,a=new A(e||[]),f=1,function(t,n){if(3===f)throw Error("Generator is already running");if(4===f){if("throw"===t)throw n;return{value:s,done:!0}}for(a.method=t,a.arg=n;;){var r=a.delegate;if(r){var e=b(r,a);if(e){if(e===d)continue;return e}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===f)throw f=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);f=3;var i=v(o,u,a);if("normal"===i.type){if(f=a.done?4:2,i.arg===d)continue;return{value:i.arg,done:a.done}}"throw"===i.type&&(f=4,a.method="throw",a.arg=i.arg)}}),!0),c}function v(t,n,r){try{return{type:"normal",arg:t.call(n,r)}}catch(t){return{type:"throw",arg:t}}}u.wrap=a;var d={};function p(){}function o(){}function f(){}var c={};h(c,i,function(){return this});var m=Object.getPrototypeOf,w=m&&m(m(S([])));w&&w!==t&&l.call(w,i)&&(c=w);var y=f.prototype=p.prototype=Object.create(c);function E(t){["next","throw","return"].forEach(function(n){h(t,n,function(t){return this._invoke(n,t)})})}function T(f,c){var n;h(this,"_invoke",function(r,e){function t(){return new c(function(t,n){!function n(t,r,e,i){var o=v(f[t],f,r);if("throw"!==o.type){var u=o.arg,a=u.value;return a&&"object"==I(a)&&l.call(a,"__await")?c.resolve(a.__await).then(function(t){n("next",t,e,i)},function(t){n("throw",t,e,i)}):c.resolve(a).then(function(t){u.value=t,e(u)},function(t){return n("throw",t,e,i)})}i(o.arg)}(r,e,t,n)})}return n=n?n.then(t,t):t()},!0)}function b(t,n){var r=n.method,e=t.i[r];if(e===s)return n.delegate=null,"throw"===r&&t.i.return&&(n.method="return",n.arg=s,b(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=v(e,t.i,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,d;var o=i.arg;return o?o.done?(n[t.r]=o.value,n.next=t.n,"return"!==n.method&&(n.method="next",n.arg=s),n.delegate=null,d):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}function g(t){this.tryEntries.push(t)}function _(t){var n=t[4]||{};n.type="normal",n.arg=s,t[4]=n}function A(t){this.tryEntries=[[-1]],t.forEach(g,this),this.reset(!0)}function S(n){if(null!=n){var t=n[i];if(t)return t.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var r=-1,e=function t(){for(;++r<n.length;)if(l.call(n,r))return t.value=n[r],t.done=!1,t;return t.value=s,t.done=!0,t};return e.next=e}}throw new TypeError(I(n)+" is not iterable")}return h(y,"constructor",o.prototype=f),h(f,"constructor",o),o.displayName=h(f,e,"GeneratorFunction"),u.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===o||"GeneratorFunction"===(n.displayName||n.name))},u.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,h(t,e,"GeneratorFunction")),t.prototype=Object.create(y),t},u.awrap=function(t){return{__await:t}},E(T.prototype),h(T.prototype,r,function(){return this}),u.AsyncIterator=T,u.async=function(t,n,r,e,i){void 0===i&&(i=Promise);var o=new T(a(t,n,r,e),i);return u.isGeneratorFunction(n)?o:o.next().then(function(t){return t.done?t.value:o.next()})},E(y),h(y,e,"Generator"),h(y,i,function(){return this}),h(y,"toString",function(){return"[object Generator]"}),u.keys=function(t){var n=Object(t),r=[];for(var e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},u.values=S,A.prototype={constructor:A,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&l.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function t(t){o.type="throw",o.arg=n,r.next=t}for(var e=r.tryEntries.length-1;0<=e;--e){var i=this.tryEntries[e],o=i[4],u=this.prev,a=i[1],f=i[2];if(-1===i[0])return t("end"),!1;if(!a&&!f)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=u){if(u<a)return this.method="next",this.arg=s,t(a),!0;if(u<f)return t(f),!1}}},abrupt:function(t,n){for(var r=this.tryEntries.length-1;0<=r;--r){var e=this.tryEntries[r];if(-1<e[0]&&e[0]<=this.prev&&this.prev<e[2]){var i=e;break}}i&&("break"===t||"continue"===t)&&i[0]<=n&&n<=i[2]&&(i=null);var o=i?i[4]:{};return o.type=t,o.arg=n,i?(this.method="next",this.next=i[2],d):this.complete(o)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),d},finish:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r[2]===t)return this.complete(r[4],r[3]),_(r),d}},catch:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r[0]===t){var e=r[4];if("throw"===e.type){var i=e.arg;_(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={i:S(t),r:n,n:r},"next"===this.method&&(this.arg=s),d}},u}function _(t,n,r,e,i,o,u){try{var a=t[o](u),f=a.value}catch(t){return void r(t)}a.done?n(f):Promise.resolve(f).then(e,i)}var A=t("./performanceLogger");n.exports={setAdaptiveInterval:function(d,p){var m=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},w=3<arguments.length&&void 0!==arguments[3]?arguments[3]:0,y=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1/0,E=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0,T=6<arguments.length&&void 0!==arguments[6]&&arguments[6],b=void 0,g=!0;return function n(){var t,r,e,i,o,u,a,f,c,s,l=A.getReport(p),h=(r=l,e=w,i=y,o=(t=m).cpuTimePercentage?r.cpuTime/t.cpuTimePercentage:0,u=t.totalTimePercentage?r.totalTime/t.totalTimePercentage:0,a=Math.max(o,u),f=e,c=i,Math.min(c,Math.max(f,a))),v=g?E:Math.max(h-l.totalTime,0);g=!1,b=setTimeout((s=N().mark(function t(){return N().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(T){t.next=4;break}d(),t.next=6;break;case 4:return t.next=6,d();case 6:n();case 7:case"end":return t.stop()}},t)}),function(){var t=this,u=arguments;return new Promise(function(n,r){var e=s.apply(t,u);function i(t){_(e,n,r,i,o,"next",t)}function o(t){_(e,n,r,i,o,"throw",t)}i(void 0)})}),v)}(),function(){clearTimeout(b)}}}},{"./performanceLogger":30}],28:[function(t,n,r){"use strict";var e=performance&&"now"in performance;n.exports=function(){return e?performance.now():Date.now()}},{}],29:[function(t,n,r){"use strict";var e=["altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","animation","audio","canvas","circle","clipPath","color-profile","cursor","defs","discard","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","handler","hkern","iframe","image","line","linearGradient","listener","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","prefetch","radialGradient","rect","script","set","solidColor","stop","style","svg","switch","symbol","tbreak","textPath","tref","unknown","use","video","view","vkern"];function i(t){return t&&t.tagName?t.tagName.toLowerCase():""}function o(t){return"input"===i(t)}function u(t){return"textarea"===i(t)}function a(t){return"select"===i(t)}n.exports={getRenderedText:function(t){return t?o(t)||u(t)?t.value:a(t)?-1===t.selectedIndex?"":t.options[t.selectedIndex].text||"":t.textContent||"":""},getBestAvailableText:function(t){return t?o(t)||u(t)?t.value:a(t)?-1===t.selectedIndex?"":t.options[t.selectedIndex].text||"":t.innerText||t.textContent||"":""},isInput:o,isTextArea:u,isSelect:a,isScript:function(t){return"script"===i(t)},isAnyNonTextSVG:function(t){var n=i(t);return-1!==e.indexOf(n)}}},{}],30:[function(t,n,r){"use strict";var e=t("./getTimeMs"),i={};function o(t){var n=i[t];if(n){var r=e();void 0===n.startTimestamp&&(n.startTimestamp=r),void 0===n.subtaskStartTimestamp&&(n.subtaskStartTimestamp=r)}}function u(t){var n=e(),r=i[t];r&&(void 0!==r.subtaskStartTimestamp&&(r.cpuTime+=n-r.subtaskStartTimestamp,r.subtaskStartTimestamp=void 0),void 0!==r.startTimestamp&&(r.lastStopTimestamp=n))}n.exports={register:function(t){i[t]={cpuTime:0,startTimestamp:void 0,lastStopTimestamp:void 0,subtaskStartTimestamp:void 0}},reset:function(t){var n=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],r=i[t];r&&(r.cpuTime=0,r.startTimestamp=void 0,r.lastStopTimestamp=void 0,r.subtaskStartTimestamp=void 0,n&&o(t))},start:o,stop:u,measuredCallback:function(n,r){return function(){o(n);var t=r();return u(n),t}},getReport:function(t){var n,r=i[t];if(r)return{totalTime:(n=r,n.startTimestamp?n.lastStopTimestamp?n.lastStopTimestamp-n.startTimestamp:e()-n.startTimestamp:0),cpuTime:r.cpuTime}}}},{"./getTimeMs":28}],31:[function(t,n,r){"use strict";var l=t("./performanceLogger");n.exports={reduceDefered:function(f){var c=1<arguments.length&&void 0!==arguments[1]?arguments[1]:16,s=2<arguments.length?arguments[2]:void 0;return function(o,t){var u=t,a=0;return 1===arguments.length&&(u=f[0],a=1),new Promise(function(n){var r,e=a;function t(){for(var t=e+c;e<t;e++)if(u=o(u,f[e],e,f),e+1===f.length)return n(u),void clearInterval(r)}if(void 0===s)r=setInterval(t);else{var i=l.measuredCallback(s,t);r=setInterval(i)}})}}}},{"./performanceLogger":30}],32:[function(t,n,r){"use strict";n.exports=function(){return self&&self.crypto&&"function"==typeof self.crypto.randomUUID?self.crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var n=16*Math.random()|0;return("x"===t?n:3&n|8).toString(16)})}},{}],33:[function(t,n,r){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var f=t("../constants"),c=t("./identityHasher");r.validateMetadata=function(t,n){for(var r={},e=0;e<f.REVENUE_METADATA_LIST.length;e++){var i=f.REVENUE_METADATA_LIST[e];i!==f.EVENT_METADATA.VALUE_DECIMAL&&o(i,r,n)}return o(f.EVENT_METADATA.CUSTOM_EVENT_NAME,r,n),o(f.EVENT_METADATA.CONVERSION_ID,r,n),u(f.EVENT_METADATA.PRODUCTS,r,n),a(r)};var o=function(t,n,r){t in r&&(n[t]=r[t])},u=function(t,n,r){t in r&&(i(t)(r[t])&&(n[t]=r[t]))},a=function(t){if(f.EVENT_METADATA.VALUE in t){var n=t[f.EVENT_METADATA.VALUE],r=rdt.useDecimalCurrencyValues?f.EVENT_METADATA.VALUE_DECIMAL:f.EVENT_METADATA.VALUE;rdt.useDecimalCurrencyValues&&delete t[f.EVENT_METADATA.VALUE],t[r]=n}f.EVENT_METADATA.TRANSACTION_ID in t&&(t[f.EVENT_METADATA.TRANSACTION_ID]=c.hashIfSet(t[f.EVENT_METADATA.TRANSACTION_ID],f.EVENT_METADATA.TRANSACTION_ID)),f.EVENT_METADATA.CONVERSION_ID in t&&(t[f.EVENT_METADATA.CONVERSION_ID]=c.hashIfSet(t[f.EVENT_METADATA.CONVERSION_ID],f.EVENT_METADATA.CONVERSION_ID));var e=f.EVENT_METADATA.CUSTOM_EVENT_NAME;t[e]=l(t[e]);var i=f.EVENT_METADATA.PRODUCTS;t[i]=s(t[i]);for(var o={},u=0;u<f.EVENT_METADATA_LIST.length;u++){var a=f.EVENT_METADATA_LIST[u];o[a]=a in t?t[a]:""}return o},s=function(t){if(!t)return"";if(v(t))try{t=JSON.parse(t)}catch(t){return""}p(t)||(t=[t]);for(var n=[],r=0;r<t.length;r++){var e=t[r],i={};for(var o in e.id&&(h(e.id)?i.id=e.id.toString():v(e.id)&&(i.id=e.id)),e.name&&v(e.name)&&(i.name=e.name),e.category&&v(e.category)&&(i.category=e.category),i){n.push(i);break}}return 0!==n.length?JSON.stringify(n):""},l=function(t){if(!t)return"";for(var n=0,r=0,e=t.length;n<e&&r<f.CUSTOM_EVENT_NAME_LIMIT;){var i=t.charCodeAt(n);if(r+=1,n+=1,55296<=i&&i<=56319&&n<e)56320==(64512&t.charCodeAt(n))&&(n+=1)}return t.slice(0,n)};r.normalizeCustomEventName=l;r.normalizeDataProcessingOptions=function(t,n,r){return v(t)?d(t)&&(t=t.split(",")):p(t)||(t=""),{dpm:t,dpcc:n=v(n)?n:"",dprc:r=v(r)?r:""}};var i=function(t){return{products:w}[t]},h=function(t){return"string"==typeof t&&(t=parseFloat(t)),"number"==typeof t&&!isNaN(t)&&isFinite(t)},v=function(t){return"string"==typeof t},d=function(t){return-1!==t.indexOf(",")},p=function(t){return"[object Array]"===Object.prototype.toString.call(t)},m=function(t){return"object"===e(t)&&null!==t};r.isObject=m;var w=function(t){return m(t)||v(t)};r.isObjectOrString=w},{"../constants":11,"./identityHasher":34}],34:[function(t,n,r){"use strict";function s(t,n){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,n){if(t){if("string"==typeof t)return f(t,n);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,n):void 0}}(t))||n&&t&&"number"==typeof t.length){r&&(t=r);var e=0,i=function(){};return{s:i,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,u=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return u=t.done,t},e:function(t){a=!0,o=t},f:function(){try{u||null==r.return||r.return()}finally{if(a)throw o}}}}function f(t,n){(null==n||n>t.length)&&(n=t.length);for(var r=0,e=Array(n);r<n;r++)e[r]=t[r];return e}var e=t("crypto-js/sha256"),i=t("crypto-js/enc-hex"),o="0000000000000000000000000000000000000000000000000000000000000000",l="0000000000000000000000000000000000000000000000000000000000000001",h=r.PLACEHOLDERS={aaid:"<AAID-HERE>",email:"<EMAIL-HERE>",phoneNumber:"<PHONE-NUMBER-HERE>",externalId:"<EXTERNAL-ID-HERE>",idfa:"<IDFA-HERE>"},v=["ext","x","anexo","#","poste","int"],u=function(t,n){return t.split(n).join("")},a=function(t,n){return t.length==n&&!isNaN(Number("0x"+t))},c=function(t){var n=u(t,"-");return a(n,32)&&"00000000000000000000000000000000"!==n},d=r.normalizeEmail=function(t){if(t===h.email)return l;var n=t.split("@");if(2!=n.length)return"";var r=n[0];return n[0]=u(r,".").split("+")[0],n.join("@").toLowerCase()};window.redditNormalizeEmail=d;var p=function(t){return t},m=r.normalizePhoneNumber=function(t){if(t===h.phoneNumber)return l;var n="";(t=t.trim()).startsWith("+")&&(n="+",t=t.substring(1)),t=t.toLowerCase();for(var r=0,e=v;r<e.length;r++){var i=e[r],o=t.indexOf(i);if(-1!==o){t=t.substring(0,o);break}}var u,a=n,f=s(t);try{for(f.s();!(u=f.n()).done;){var c=u.value;"0"<=c&&c<="9"&&(a+=c)}}catch(t){f.e(t)}finally{f.f()}return a},w=r.isValidAaid=function(t){return t===h.aaid||c(t)},y=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,E=/^(?:\+)?\d{4,18}$/,T=r.isValidEmail=function(t){return t===h.email||y.test(t)},b=r.isValidPhoneNumber=function(t){return t===l||E.test(t)},g=r.isValidHash=function(t){return a(t,64)},_=r.isValidIdfa=function(t){return t===h.idfa||c(t)},A=r.isValidNoOp=function(t){return!0},S=["aaid","email","phoneNumber","externalId","idfa","transactionId","conversionId"],I=function(t){for(var n=0;n<S.length;n++)if(t===S[n])return!0;return!1},N={aaid:function(t){return t===h.aaid?l:t.toLowerCase()},email:d,phoneNumber:m,externalId:function(t){return t===h.externalId?l:p(t)},idfa:function(t){return t===h.idfa?l:t.toUpperCase()},transactionId:p,conversionId:p},C={aaid:w,email:T,phoneNumber:b,externalId:A,idfa:_,transactionId:A,conversionId:A};r.hashIfSet=function(t,n){if(!t||"string"!=typeof t||!I(n))return"";if(g(t))return t.toLowerCase();if(!(0,C[n])(t))return"email"===n?o:"";var r=(0,N[n])(t);return r===l?r:i.stringify(e(r))},r.hashIfSetNormalizeFirst=function(t,n){if(!t||"string"!=typeof t||!I(n))return"";if(g(t))return t.toLowerCase();var r=(0,N[n])(t);return(0,C[n])(r)?r===l?l:i.stringify(e(r)):"email"===n?o:"phoneNumber"===n?"0000000000000000000000000000000000000000000000000000000000000002":""}},{"crypto-js/enc-hex":3,"crypto-js/sha256":4}],35:[function(t,n,r){"use strict";var e=t("../constants");r.getIntegrationProvider=function(t){switch(t){case e.INTEGRATION_PROVIDERS.GTM:return e.INTEGRATION_PROVIDERS.GTM;default:return e.INTEGRATION_PROVIDERS.REDDIT}}},{"../constants":11}]},{},[24]);
