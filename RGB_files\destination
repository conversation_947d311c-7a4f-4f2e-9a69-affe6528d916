
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__cid"}],
  "tags":[{"function":"__ccd_ads_first","priority":100,"vtp_instanceDestinationId":["macro",1],"tag_id":2},{"function":"__rep","once_per_event":true,"vtp_containerId":["macro",1],"tag_id":1},{"function":"__ccd_ads_last","vtp_instanceDestinationId":["macro",1],"tag_id":3}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"}],
  "rules":[[["if",0],["add",1,0,2]]]
},
"runtime":[ [50,"__ccd_ads_first",[46,"a"],[50,"e",[46,"f"],[2,[15,"c"],"B",[7,[15,"f"]]],[2,[15,"d"],"A",[7,[15,"f"]]]],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_webPrivacyTasks"]],[52,"d",[15,"__module_taskConversionAutoDataAnalysis"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"f"],["e",[15,"f"]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ads_last",[46,"a"],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_metadataSchema"]],[52,"d",[15,"__module_gtagSchema"]],[52,"e",[15,"__module_adwordsHitType"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"f"],[52,"g",[2,[15,"f"],"getMetadata",[7,[17,[15,"c"],"AF"]]]],[22,[1,[20,[15,"g"],[17,[15,"e"],"B"]],[28,[2,[15,"f"],"getHitData",[7,[17,[15,"d"],"JS"]]]]],[46,[53,[2,[15,"f"],"abort",[7]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__cid",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"containerId"]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",34],[52,"f",40],[52,"g",42],[52,"h",43],[52,"i",44],[52,"j",45],[52,"k",46],[52,"l",47],[52,"m",56],[52,"n",113],[52,"o",129],[52,"p",142],[52,"q",156],[52,"r",168],[52,"s",174],[52,"t",178],[52,"u",188],[52,"v",212],[36,[8,"DP",[15,"r"],"W",[15,"b"],"X",[15,"c"],"Y",[15,"d"],"Z",[15,"e"],"AF",[15,"f"],"AH",[15,"g"],"AI",[15,"h"],"AJ",[15,"i"],"AK",[15,"j"],"AL",[15,"k"],"AM",[15,"l"],"EA",[15,"u"],"AR",[15,"m"],"DT",[15,"s"],"DW",[15,"t"],"BX",[15,"n"],"CK",[15,"o"],"CX",[15,"p"],"EQ",[15,"v"],"DH",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_adwordsHitType",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","conversion"],[52,"c","ga_conversion"],[52,"d","page_view"],[52,"e","remarketing"],[52,"f","user_data_lead"],[52,"g","user_data_web"],[36,[8,"B",[15,"b"],"D",[15,"c"],"F",[15,"d"],"G",[15,"e"],"H",[15,"f"],"I",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","allow_interest_groups"],[52,"z","auid"],[52,"aA","aw_remarketing"],[52,"aB","aw_remarketing_only"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_cookie_prefix"],[52,"aK","conversion_id"],[52,"aL","conversion_linker"],[52,"aM","conversion_api"],[52,"aN","cookie_deprecation"],[52,"aO","cookie_expires"],[52,"aP","cookie_prefix"],[52,"aQ","cookie_update"],[52,"aR","country"],[52,"aS","currency"],[52,"aT","customer_buyer_stage"],[52,"aU","customer_lifetime_value"],[52,"aV","customer_loyalty"],[52,"aW","customer_ltv_bucket"],[52,"aX","debug_mode"],[52,"aY","developer_id"],[52,"aZ","shipping"],[52,"bA","engagement_time_msec"],[52,"bB","estimated_delivery_date"],[52,"bC","event_developer_id_string"],[52,"bD","event"],[52,"bE","event_timeout"],[52,"bF","first_party_collection"],[52,"bG","match_id"],[52,"bH","gdpr_applies"],[52,"bI","google_analysis_params"],[52,"bJ","_google_ng"],[52,"bK","gpp_sid"],[52,"bL","gpp_string"],[52,"bM","gsa_experiment_id"],[52,"bN","gtag_event_feature_usage"],[52,"bO","iframe_state"],[52,"bP","ignore_referrer"],[52,"bQ","is_passthrough"],[52,"bR","_lps"],[52,"bS","language"],[52,"bT","merchant_feed_label"],[52,"bU","merchant_feed_language"],[52,"bV","merchant_id"],[52,"bW","new_customer"],[52,"bX","page_hostname"],[52,"bY","page_path"],[52,"bZ","page_referrer"],[52,"cA","page_title"],[52,"cB","_platinum_request_status"],[52,"cC","quantity"],[52,"cD","restricted_data_processing"],[52,"cE","screen_resolution"],[52,"cF","send_page_view"],[52,"cG","server_container_url"],[52,"cH","session_duration"],[52,"cI","session_engaged_time"],[52,"cJ","session_id"],[52,"cK","_shared_user_id"],[52,"cL","delivery_postal_code"],[52,"cM","topmost_url"],[52,"cN","transaction_id"],[52,"cO","transport_url"],[52,"cP","update"],[52,"cQ","_user_agent_architecture"],[52,"cR","_user_agent_bitness"],[52,"cS","_user_agent_full_version_list"],[52,"cT","_user_agent_mobile"],[52,"cU","_user_agent_model"],[52,"cV","_user_agent_platform"],[52,"cW","_user_agent_platform_version"],[52,"cX","_user_agent_wow64"],[52,"cY","user_data"],[52,"cZ","user_data_auto_latency"],[52,"dA","user_data_auto_meta"],[52,"dB","user_data_auto_multi"],[52,"dC","user_data_auto_selectors"],[52,"dD","user_data_auto_status"],[52,"dE","user_data_mode"],[52,"dF","user_id"],[52,"dG","user_properties"],[52,"dH","us_privacy_string"],[52,"dI","value"],[52,"dJ","_fpm_parameters"],[52,"dK","_host_name"],[52,"dL","_in_page_command"],[52,"dM","non_personalized_ads"],[52,"dN","conversion_label"],[52,"dO","page_location"],[52,"dP","global_developer_id_string"],[52,"dQ","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BK",[15,"y"],"BP",[15,"z"],"BR",[15,"aA"],"BS",[15,"aB"],"BT",[15,"aC"],"BU",[15,"aD"],"BV",[15,"aE"],"BW",[15,"aF"],"BX",[15,"aG"],"BY",[15,"aH"],"CG",[15,"aI"],"CL",[15,"aJ"],"CM",[15,"aK"],"JS",[15,"dN"],"CN",[15,"aL"],"CP",[15,"aM"],"CQ",[15,"aN"],"CS",[15,"aO"],"CW",[15,"aP"],"CX",[15,"aQ"],"CY",[15,"aR"],"CZ",[15,"aS"],"DA",[15,"aT"],"DB",[15,"aU"],"DC",[15,"aV"],"DD",[15,"aW"],"DH",[15,"aX"],"DI",[15,"aY"],"DU",[15,"aZ"],"DW",[15,"bA"],"EA",[15,"bB"],"EE",[15,"bC"],"EG",[15,"bD"],"EI",[15,"bE"],"EN",[15,"bF"],"EY",[15,"bG"],"FI",[15,"bH"],"JU",[15,"dP"],"FM",[15,"bI"],"FN",[15,"bJ"],"FQ",[15,"bK"],"FR",[15,"bL"],"FT",[15,"bM"],"FU",[15,"bN"],"FW",[15,"bO"],"FX",[15,"bP"],"GC",[15,"bQ"],"GD",[15,"bR"],"GE",[15,"bS"],"GL",[15,"bT"],"GM",[15,"bU"],"GN",[15,"bV"],"GR",[15,"bW"],"GU",[15,"bX"],"JT",[15,"dO"],"GV",[15,"bY"],"GW",[15,"bZ"],"GX",[15,"cA"],"HF",[15,"cB"],"HH",[15,"cC"],"HL",[15,"cD"],"HP",[15,"cE"],"HS",[15,"cF"],"HU",[15,"cG"],"HV",[15,"cH"],"HX",[15,"cI"],"HY",[15,"cJ"],"IA",[15,"cK"],"IB",[15,"cL"],"JV",[15,"dQ"],"IG",[15,"cM"],"IJ",[15,"cN"],"IK",[15,"cO"],"IM",[15,"cP"],"IP",[15,"cQ"],"IQ",[15,"cR"],"IR",[15,"cS"],"IS",[15,"cT"],"IT",[15,"cU"],"IU",[15,"cV"],"IV",[15,"cW"],"IW",[15,"cX"],"IX",[15,"cY"],"IY",[15,"cZ"],"IZ",[15,"dA"],"JA",[15,"dB"],"JB",[15,"dC"],"JC",[15,"dD"],"JD",[15,"dE"],"JF",[15,"dF"],"JG",[15,"dG"],"JI",[15,"dH"],"JJ",[15,"dI"],"JL",[15,"dJ"],"JM",[15,"dK"],"JN",[15,"dL"],"JQ",[15,"dM"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"H",[15,"d"],"I",[15,"e"],"J",[15,"f"],"K",[15,"g"],"P",[15,"h"],"U",[15,"i"],"V",[15,"j"],"AD",[15,"k"],"AF",[15,"l"],"AG",[15,"m"],"AJ",[15,"n"],"AL",[15,"o"],"AN",[15,"p"],"AO",[15,"q"],"AQ",[15,"r"],"AR",[15,"s"],"AS",[15,"t"],"AT",[15,"u"],"AW",[15,"v"],"AX",[15,"w"],"AY",[15,"x"],"AZ",[15,"y"],"BA",[15,"z"],"BC",[15,"aA"],"BD",[15,"aB"],"BI",[15,"aC"],"BL",[15,"aD"],"BM",[15,"aE"],"BO",[15,"aF"],"BT",[15,"aG"],"BV",[15,"aH"],"BY",[15,"aI"],"BZ",[15,"aJ"],"CA",[15,"aK"],"CB",[15,"aL"],"CC",[15,"aM"],"CD",[15,"aN"],"CE",[15,"aO"],"CF",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_webPrivacyTasks",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"d",[46,"f"],[52,"g",["b"]],[65,"h",[7,[17,[15,"c"],"JI"],[17,[15,"c"],"FI"],[17,[15,"c"],"JV"]],[46,[53,[2,[15,"f"],"setHitData",[7,[15,"h"],[16,[15,"g"],[15,"h"]]]]]]]],[50,"e",[46,"f"],[52,"g",["b"]],[22,[16,[15,"g"],[17,[15,"c"],"FR"]],[46,[53,[2,[15,"f"],"setHitData",[7,[17,[15,"c"],"FR"],[16,[15,"g"],[17,[15,"c"],"FR"]]]]]]],[22,[16,[15,"g"],[17,[15,"c"],"FQ"]],[46,[53,[2,[15,"f"],"setHitData",[7,[17,[15,"c"],"FQ"],[16,[15,"g"],[17,[15,"c"],"FQ"]]]]]]]],[52,"b",["require","internal.getPrivacyStrings"]],[52,"c",[15,"__module_gtagSchema"]],[36,[8,"B",[15,"e"],"A",[15,"d"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_taskConversionAutoDataAnalysis",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"p",[46,"q"],[22,[28,[1,["e",[17,[15,"f"],"EA"]],[20,[2,[15,"q"],"getMetadata",[7,[17,[15,"j"],"AF"]]],[17,[15,"b"],"B"]]]],[46,[53,[36]]]],[52,"r",[7]],[65,"s",[15,"o"],[46,[53,[52,"t",["c",[17,[15,"s"],"modelKey"]]],[52,"u",["g",[15,"t"]]],[22,[28,[30,[20,[15,"u"],"string"],[20,[15,"u"],"number"]]],[46,[6]]],[22,[28,["k",[17,[15,"s"],"regexp"],[2,["i",[15,"t"]],"replace",[7,["d","\\s","g"],""]]]],[46,[53,[6]]]],[2,[15,"r"],"push",[7,[17,[15,"s"],"googleAnalysisKey"]]]]]],[22,[28,[17,[15,"r"],"length"]],[46,[36]]],[2,[15,"q"],"mergeHitDataForKey",[7,[17,[15,"h"],"FM"],[8,"cad",[2,[15,"r"],"join",[7,"."]]]]]],[52,"b",[15,"__module_adwordsHitType"]],[52,"c",["require","copyFromDataLayer"]],[52,"d",["require","internal.createRegex"]],[52,"e",["require","internal.isFeatureEnabled"]],[52,"f",[15,"__module_featureFlags"]],[52,"g",["require","getType"]],[52,"h",[15,"__module_gtagSchema"]],[52,"i",["require","makeString"]],[52,"j",[15,"__module_metadataSchema"]],[52,"k",["require","internal.testRegex"]],[52,"l",["d","^(?:[1-9][\\d.,]*)|(?:0?[.,]\\d+)$"]],[52,"m",["d","^[A-Za-z]{3}$"]],[52,"n",["d","^.+$"]],[52,"o",[7,[8,"modelKey","ecommerce.value","googleAnalysisKey",1,"regexp",[15,"l"]],[8,"modelKey","ecommerce.currency","googleAnalysisKey",31,"regexp",[15,"m"]],[8,"modelKey","ecommerce.currencyCode","googleAnalysisKey",33,"regexp",[15,"m"]],[8,"modelKey","ecommerce.purchase.value","googleAnalysisKey",2,"regexp",[15,"l"]],[8,"modelKey","ecommerce.purchase.currency","googleAnalysisKey",39,"regexp",[15,"m"]],[8,"modelKey","ecommerce.purchase.transaction_id","googleAnalysisKey",68,"regexp",[15,"n"]],[8,"modelKey","ecommerce.purchase.actionField.revenue","googleAnalysisKey",3,"regexp",[15,"l"]],[8,"modelKey","ecommerce.purchase.actionField.currency","googleAnalysisKey",41,"regexp",[15,"m"]],[8,"modelKey","ecommerce.purchase.actionField.id","googleAnalysisKey",62,"regexp",[15,"n"]],[8,"modelKey","ecommerce.cart.currencyCode","googleAnalysisKey",45,"regexp",[15,"m"]],[8,"modelKey","ecommerce.transaction_id","googleAnalysisKey",61,"regexp",[15,"n"]],[8,"modelKey","common_model.order.id","googleAnalysisKey",69,"regexp",[15,"n"]],[8,"modelKey","common_model.order.total_amounts.revenue","googleAnalysisKey",10,"regexp",[15,"l"]],[8,"modelKey","common.currency","googleAnalysisKey",42,"regexp",[15,"m"]],[8,"modelKey","orderConversions.currency","googleAnalysisKey",43,"regexp",[15,"m"]],[8,"modelKey","eventModel.value","googleAnalysisKey",4,"regexp",[15,"l"]],[8,"modelKey","eventModel.currency","googleAnalysisKey",34,"regexp",[15,"m"]],[8,"modelKey","eventModel.transaction_id","googleAnalysisKey",64,"regexp",[15,"n"]],[8,"modelKey","context.localization.currency_code","googleAnalysisKey",35,"regexp",[15,"m"]],[8,"modelKey","leadsHookData.googleConversion.value","googleAnalysisKey",15,"regexp",[15,"l"]],[8,"modelKey","leadsHookData.googleConversion.currency","googleAnalysisKey",44,"regexp",[15,"m"]],[8,"modelKey","orderData.attributes.order_number","googleAnalysisKey",74,"regexp",[15,"n"]],[8,"modelKey","order.id","googleAnalysisKey",75,"regexp",[15,"n"]],[8,"modelKey","transaction.id","googleAnalysisKey",76,"regexp",[15,"n"]],[8,"modelKey","transactionTotal","googleAnalysisKey",5,"regexp",[15,"l"]],[8,"modelKey","value","googleAnalysisKey",6,"regexp",[15,"l"]],[8,"modelKey","totalValue","googleAnalysisKey",7,"regexp",[15,"l"]],[8,"modelKey","ecomm_totalvalue","googleAnalysisKey",8,"regexp",[15,"l"]],[8,"modelKey","price","googleAnalysisKey",9,"regexp",[15,"l"]],[8,"modelKey","conversionValue","googleAnalysisKey",11,"regexp",[15,"l"]],[8,"modelKey","ihAmount","googleAnalysisKey",12,"regexp",[15,"l"]],[8,"modelKey","wp_conversion_value","googleAnalysisKey",13,"regexp",[15,"l"]],[8,"modelKey","revenue","googleAnalysisKey",14,"regexp",[15,"l"]],[8,"modelKey","currency","googleAnalysisKey",32,"regexp",[15,"m"]],[8,"modelKey","transactionCurrency","googleAnalysisKey",36,"regexp",[15,"m"]],[8,"modelKey","currencyCode","googleAnalysisKey",37,"regexp",[15,"m"]],[8,"modelKey","ihCurrency","googleAnalysisKey",38,"regexp",[15,"m"]],[8,"modelKey","CurrCode","googleAnalysisKey",40,"regexp",[15,"m"]],[8,"modelKey","transactionId","googleAnalysisKey",63,"regexp",[15,"n"]],[8,"modelKey","transaction_id","googleAnalysisKey",65,"regexp",[15,"n"]],[8,"modelKey","order_id","googleAnalysisKey",66,"regexp",[15,"n"]],[8,"modelKey","orderId","googleAnalysisKey",67,"regexp",[15,"n"]],[8,"modelKey","ihConfirmID","googleAnalysisKey",70,"regexp",[15,"n"]],[8,"modelKey","wp_order_id","googleAnalysisKey",71,"regexp",[15,"n"]],[8,"modelKey","orderID","googleAnalysisKey",72,"regexp",[15,"n"]],[8,"modelKey","id","googleAnalysisKey",73,"regexp",[15,"n"]]]],[36,[8,"A",[15,"p"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__ccd_ads_first":{"2":true,"5":true}
,
"__ccd_ads_last":{"2":true,"5":true}
,
"__cid":{"2":true,"3":true,"5":true}
,
"__e":{"2":true,"5":true}


}
,"blob":{"1":"1","10":"AW-10793199209","14":"57l0","15":"0","16":"ChEI8O38wwYQzZn8jJmzlf7qARInADH3+ykDxk4K+ylclkEOTrcLImgRuDz1WzD4KGYBXgLlU22gHCBwGgL7nw==","17":"c","19":"dataLayer","20":"","21":"www.googletagmanager.com","22":"eyIwIjoiUEsiLCIxIjoiUEstUEIiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20ucGsiLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"PK","31":"PK-PB","32":true,"34":"AW-10793199209","35":"AW","36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BHzT6/oair5aOoHdYjEmfzUicVThxHYEl7UgRE/YE2GI/lntBc4CILotyJom55+T7o8VJA67XSfq6dT5RPph5sE=\",\"version\":0},\"id\":\"f0c5b86a-0c49-4602-bc59-93c0ebdc0e5b\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BCccnnob7RTpZnXozKqbUvXw1mtdRlZZJG0WsMn7JboEACHSKM/s+Rta9TyXuMxFmxHM5sBeIfrK96fRPJOe3aA=\",\"version\":0},\"id\":\"856269be-3e07-4432-be53-43bb4fef6799\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BICV1Oo3rVMnzzZPvqF2lPJ7rSGKM63Wquezi8KacvOrbKJczfVxaewJtKDiFC0wtd/usVpi7GfBNgXOUA4f3do=\",\"version\":0},\"id\":\"7bc98b84-7f5e-4a08-b561-134399ffd635\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BBOfSFHCVnwpMDMK/4YvG1aU6mpHrLqapgzYTpW0m4L18YROpTgYE67uzFFI3/+Del+5jK6w4oR7Ga6dcGGkH44=\",\"version\":0},\"id\":\"67490360-94dd-46de-96a7-3e1880072ea3\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BBWqoKhV6upED2PWudvnYDnXtdJ+ZVW64FLcSHvapcIQs1LeJzomcNf1bzhQn4If7R1jM/XNbrieqkGH2OX7gL8=\",\"version\":0},\"id\":\"1e127111-8a8e-47b5-a85d-c432304f7b5c\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211~104921715~104921717~104952209~104952211","5":"AW-10793199209","7":true,"8":"res_ts:1,srv_cl:785771831,ds:live,cv:1","9":"AW-10793199209"}
,"permissions":{
"__ccd_ads_first":{"read_data_layer":{"allowedKeys":"any"}}
,
"__ccd_ads_last":{}
,
"__cid":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}


}



,"security_groups":{
"google":[
"__ccd_ads_first"
,
"__ccd_ads_last"
,
"__cid"
,
"__e"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=ca(this),fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ha={},la={},ma=function(a,b,c){if(!c||a!=null){var d=la[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},na=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ha?g=ha:g=ea;for(var h=0;h<d.length-1;h++){var m=d[h];if(!(m in g))break a;g=g[m]}var n=d[d.length-1],p=fa&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ha,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(la[n]===void 0){var r=
Math.random()*1E9>>>0;la[n]=fa?ea.Symbol(n):"$jscp$"+r+"$"+n}ba(g,la[n],{configurable:!0,writable:!0,value:q})}}};na("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var oa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},pa;if(fa&&typeof Object.setPrototypeOf=="function")pa=Object.setPrototypeOf;else{var qa;a:{var ra={a:!0},ta={};try{ta.__proto__=ra;qa=ta.a;break a}catch(a){}qa=!1}pa=qa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ua=pa,va=function(a,b){a.prototype=oa(b.prototype);a.prototype.constructor=a;if(ua)ua(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Dq=b.prototype},l=function(a){var b=typeof ha.Symbol!="undefined"&&ha.Symbol.iterator&&a[ha.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},xa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ya=function(a){return a instanceof Array?a:xa(l(a))},Aa=function(a){return za(a,a)},za=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ba=fa&&typeof ma(Object,"assign")=="function"?ma(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||Ba},"es6");
var Ca=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Da=this||self,Ea=function(a,b){function c(){}c.prototype=b.prototype;a.Dq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Dr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Fa=function(a,b){this.type=a;this.data=b};var Ga=function(){this.map={};this.C={}};Ga.prototype.get=function(a){return this.map["dust."+a]};Ga.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ga.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ga.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ha=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ga.prototype.Aa=function(){return Ha(this,1)};Ga.prototype.Ac=function(){return Ha(this,2)};Ga.prototype.ac=function(){return Ha(this,3)};var Ia=function(){};Ia.prototype.reset=function(){};var Ja=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.Cb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ga};Ja.prototype.add=function(a,b){Ka(this,a,b,!1)};Ja.prototype.nh=function(a,b){Ka(this,a,b,!0)};var Ka=function(a,b,c,d){if(!a.Cb)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ja.prototype;k.set=function(a,b){this.Cb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.tb=function(){var a=new Ja(this.P,this);this.C&&a.Nb(this.C);a.Vc(this.H);a.Od(this.N);return a};k.Fd=function(){return this.P};k.Nb=function(a){this.C=a};k.Zl=function(){return this.C};k.Vc=function(a){this.H=a};k.aj=function(){return this.H};k.Wa=function(){this.Cb=!0};k.Od=function(a){this.N=a};k.ub=function(){return this.N};var La=function(a,b){this.fa=a;this.parent=b;this.P=this.H=void 0;this.Cb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.nh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.Cb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.Cb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.tb=function(){var a=new La(this.fa,this);this.H&&a.Nb(this.H);a.Vc(this.N);a.Od(this.P);return a};k.Fd=function(){return this.fa};k.Nb=function(a){this.H=a};k.Zl=function(){return this.H};k.Vc=function(a){this.N=a};k.aj=function(){return this.N};k.Wa=function(){this.Cb=!0};k.Od=function(a){this.P=a};k.ub=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.lm=a;this.Rl=c===void 0?!1:c;this.debugInfo=[];this.C=b};va(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=[],Qa={};function Ra(a){return Pa[a]===void 0?!1:Pa[a]};var Sa=new Map;function Ta(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ua(a,e.value),c instanceof Fa);e=d.next());return c}
function Ua(a,b){try{if(Ra(16)){var c=b[0],d=b.slice(1),e=String(c),f=Sa.has(e)?Sa.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=l(b),h=g.next().value,m=xa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(ya(m)))}catch(q){var p=a.Zl();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var Xa=function(){this.H=new Ia;this.C=Ra(16)?new La(this.H):new Ja(this.H)};k=Xa.prototype;k.Fd=function(){return this.H};k.Nb=function(a){this.C.Nb(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Bj([a].concat(ya(Ca.apply(1,arguments))))};k.Bj=function(){for(var a,b=l(Ca.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ua(this.C,c.value);return a};
k.eo=function(a){var b=Ca.apply(1,arguments),c=this.C.tb();c.Od(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ua(c,f.value);return d};k.Wa=function(){this.C.Wa()};var Ya=function(){this.Ea=!1;this.aa=new Ga};k=Ya.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Cb=function(){return this.Ea};function Za(){for(var a=$a,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function ab(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var $a,bb;function cb(a){$a=$a||ab();bb=bb||Za();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push($a[m],$a[n],$a[p],$a[q])}return b.join("")}
function db(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=bb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}$a=$a||ab();bb=bb||Za();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var eb={};function fb(a,b){eb[a]=eb[a]||[];eb[a][b]=!0}function gb(){eb.GTAG_EVENT_FEATURE_CHANNEL=hb}function ib(a){var b=eb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return cb(c.join("")).replace(/\.+$/,"")}function jb(){for(var a=[],b=eb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function kb(){}function lb(a){return typeof a==="function"}function mb(a){return typeof a==="string"}function nb(a){return typeof a==="number"&&!isNaN(a)}function ob(a){return Array.isArray(a)?a:[a]}function pb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function qb(a,b){if(!nb(a)||!nb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function rb(a,b){for(var c=new sb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function tb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function ub(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function vb(a){return Math.round(Number(a))||0}function wb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function xb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function yb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function zb(){return new Date(Date.now())}function Ab(){return zb().getTime()}var sb=function(){this.prefix="gtm.";this.values={}};sb.prototype.set=function(a,b){this.values[this.prefix+a]=b};sb.prototype.get=function(a){return this.values[this.prefix+a]};sb.prototype.contains=function(a){return this.get(a)!==void 0};
function Bb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Cb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Eb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Fb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Gb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Hb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Ib(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Jb=/^\w{1,9}$/;function Kb(a,b){a=a||{};b=b||",";var c=[];tb(a,function(d,e){Jb.test(d)&&e&&c.push(d)});return c.join(b)}function Lb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Mb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Nb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Ob(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Pb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ya(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Qb=globalThis.trustedTypes,Rb;function Sb(){var a=null;if(!Qb)return a;try{var b=function(c){return c};a=Qb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Tb(){Rb===void 0&&(Rb=Sb());return Rb};var Vb=function(a){this.C=a};Vb.prototype.toString=function(){return this.C+""};function Wb(a){var b=a,c=Tb(),d=c?c.createScriptURL(b):b;return new Vb(d)}function Xb(a){if(a instanceof Vb)return a.C;throw Error("");};var Yb=Aa([""]),Zb=za(["\x00"],["\\0"]),$b=za(["\n"],["\\n"]),bc=za(["\x00"],["\\u0000"]);function cc(a){return a.toString().indexOf("`")===-1}cc(function(a){return a(Yb)})||cc(function(a){return a(Zb)})||cc(function(a){return a($b)})||cc(function(a){return a(bc)});var dc=function(a){this.C=a};dc.prototype.toString=function(){return this.C};var ec=function(a){this.Sp=a};function fc(a){return new ec(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var hc=[fc("data"),fc("http"),fc("https"),fc("mailto"),fc("ftp"),new ec(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function ic(a){var b;b=b===void 0?hc:b;if(a instanceof dc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof ec&&d.Sp(a))return new dc(a)}}var jc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function kc(a){var b;if(a instanceof dc)if(a instanceof dc)b=a.C;else throw Error("");else b=jc.test(a)?a:void 0;return b};function lc(a,b){var c=kc(b);c!==void 0&&(a.action=c)};function mc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var nc=function(a){this.C=a};nc.prototype.toString=function(){return this.C+""};var pc=function(){this.C=oc[0].toLowerCase()};pc.prototype.toString=function(){return this.C};function qc(a,b){var c=[new pc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof pc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var rc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function sc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,tc=window.history,A=document,uc=navigator;function vc(){var a;try{a=uc.serviceWorker}catch(b){return}return a}var wc=A.currentScript,xc=wc&&wc.src;function yc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function zc(a){return(uc.userAgent||"").indexOf(a)!==-1}function Ac(){return zc("Firefox")||zc("FxiOS")}function Bc(){return(zc("GSA")||zc("GoogleApp"))&&(zc("iPhone")||zc("iPad"))}function Cc(){return zc("Edg/")||zc("EdgA/")||zc("EdgiOS/")}
var Dc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Ec={onload:1,src:1,width:1,height:1,style:1};function Fc(a,b,c){b&&tb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Gc(a,b,c,d,e){var f=A.createElement("script");Fc(f,d,Dc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Wb(sc(a));f.src=Xb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function Hc(){if(xc){var a=xc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Ic(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);Fc(g,c,Ec);d&&tb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Jc(a,b,c,d){return Kc(a,b,c,d)}function Lc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Mc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Nc(a){x.setTimeout(a,0)}function Oc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Pc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Qc(a){var b=A.createElement("div"),c=b,d,e=sc("A<div>"+a+"</div>"),f=Tb(),g=f?f.createHTML(e):e;d=new nc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof nc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Rc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Sc(a,b,c){var d;try{d=uc.sendBeacon&&uc.sendBeacon(a)}catch(e){fb("TAGGING",15)}d?b==null||b():Kc(a,b,c)}function Tc(a,b){try{return uc.sendBeacon(a,b)}catch(c){fb("TAGGING",15)}return!1}var Uc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Vc(a,b,c,d,e){if(Wc()){var f=ma(Object,"assign").call(Object,{},Uc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ch)return e==null||e(),
!1;if(b){var h=Tc(a,b);h?d==null||d():e==null||e();return h}Xc(a,d,e);return!0}function Wc(){return typeof x.fetch==="function"}function Yc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Zc(){var a=x.performance;if(a&&lb(a.now))return a.now()}
function $c(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function ad(){return x.performance||void 0}function bd(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Kc=function(a,b,c,d){var e=new Image(1,1);Fc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Xc=Sc;function cd(a,b){return this.evaluate(a)&&this.evaluate(b)}function dd(a,b){return this.evaluate(a)===this.evaluate(b)}function ed(a,b){return this.evaluate(a)||this.evaluate(b)}function fd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function gd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function hd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ya&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var id=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,jd=function(a){if(a==null)return String(a);var b=id.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},kd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},ld=function(a){if(!a||jd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!kd(a,"constructor")&&!kd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
kd(a,b)},md=function(a,b){var c=b||(jd(a)=="array"?[]:{}),d;for(d in a)if(kd(a,d)){var e=a[d];jd(e)=="array"?(jd(c[d])!="array"&&(c[d]=[]),c[d]=md(e,c[d])):ld(e)?(ld(c[d])||(c[d]={}),c[d]=md(e,c[d])):c[d]=e}return c};function nd(a){if(a==void 0||Array.isArray(a)||ld(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function od(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var pd=function(a){a=a===void 0?[]:a;this.aa=new Ga;this.values=[];this.Ea=!1;for(var b in a)a.hasOwnProperty(b)&&(od(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=pd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof pd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ea)if(a==="length"){if(!od(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else od(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():od(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.Aa=function(){for(var a=this.aa.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Ac=function(){for(var a=this.aa.Ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){od(a)?delete this.values[Number(a)]:this.Ea||this.aa.remove(a)};k.pop=function(){return this.values.pop()};
k.push=function(){var a=Ca.apply(0,arguments);return Ra(17)&&arguments.length===1?this.values.push(arguments[0]):this.values.push.apply(this.values,ya(a))};k.shift=function(){return this.values.shift()};k.splice=function(a,b){var c=Ca.apply(2,arguments);return b===void 0&&c.length===0?new pd(this.values.splice(a)):new pd(this.values.splice.apply(this.values,[a,b||0].concat(ya(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ya(Ca.apply(0,arguments)))};
k.has=function(a){return od(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Wa=function(){this.Ea=!0;Object.freeze(this.values)};k.Cb=function(){return this.Ea};function qd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var rd=function(a,b){this.functionName=a;this.Sc=b;this.aa=new Ga;this.Ea=!1};k=rd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new pd(this.Aa())};k.invoke=function(a){var b=Ca.apply(1,arguments);return Ra(18)?this.Sc.apply(new sd(this,a),b):this.Sc.call.apply(this.Sc,[new sd(this,a)].concat(ya(b)))};k.apply=function(a,b){return this.Sc.apply(new sd(this,a),b)};
k.Lb=function(a){var b=Ca.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ya(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Cb=function(){return this.Ea};var td=function(a,b){rd.call(this,a,b)};
va(td,rd);var ud=function(a,b){rd.call(this,a,b)};va(ud,rd);var sd=function(a,b){this.Sc=a;this.K=b};sd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ua(b,a):a};sd.prototype.getName=function(){return this.Sc.getName()};sd.prototype.Fd=function(){return this.K.Fd()};var vd=function(){this.map=new Map};vd.prototype.set=function(a,b){this.map.set(a,b)};vd.prototype.get=function(a){return this.map.get(a)};var wd=function(){this.keys=[];this.values=[]};wd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};wd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function xd(){try{return Map?new vd:new wd}catch(a){return new wd}};var yd=function(a){if(a instanceof yd)return a;if(nd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};yd.prototype.getValue=function(){return this.value};yd.prototype.toString=function(){return String(this.value)};var Ad=function(a){this.promise=a;this.Ea=!1;this.aa=new Ga;this.aa.set("then",zd(this));this.aa.set("catch",zd(this,!0));this.aa.set("finally",zd(this,!1,!0))};k=Ad.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};
var zd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new td("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof td||(d=void 0);e instanceof td||(e=void 0);var f=this.K.tb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new yd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Ad(h)})};Ad.prototype.Wa=function(){this.Ea=!0};Ad.prototype.Cb=function(){return this.Ea};function Bd(a,b,c){var d=xd(),e=function(g,h){for(var m=g.Aa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof pd){var m=[];d.set(g,m);for(var n=g.Aa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof Ad)return g.promise.then(function(u){return Bd(u,b,1)},function(u){return Promise.reject(Bd(u,b,1))});if(g instanceof Ya){var q={};d.set(g,q);e(g,q);return q}if(g instanceof td){var r=function(){for(var u=
[],v=0;v<arguments.length;v++)u[v]=Cd(arguments[v],b,c);var w=new Ja(b?b.Fd():new Ia);b&&w.Od(b.ub());return f(Ra(16)?g.apply(w,u):g.invoke.apply(g,[w].concat(ya(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof yd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Cd(a,b,c){var d=xd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||ub(g)){var m=new pd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(ld(g)){var p=new Ya;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new td("",function(){for(var u=Ca.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Bd(this.evaluate(u[w]),b,c);return f(this.K.aj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new yd(g)};return f(a)};var Dd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof pd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new pd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new pd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new pd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ya(Ca.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=qd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new pd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=qd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ya(Ca.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ya(Ca.apply(1,arguments)))}};var Ed={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Fd=new Fa("break"),Gd=new Fa("continue");function Hd(a,b){return this.evaluate(a)+this.evaluate(b)}function Id(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Jd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof pd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Bd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Ed.hasOwnProperty(e)){var m=2;m=1;var n=Bd(f,void 0,m);return Cd(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof pd){if(d.has(e)){var p=d.get(String(e));if(p instanceof td){var q=qd(f);return Ra(16)?p.apply(this.K,q):p.invoke.apply(p,[this.K].concat(ya(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));
}if(Dd.supportedMethods.indexOf(e)>=0){var r=qd(f);return Dd[e].call.apply(Dd[e],[d,this.K].concat(ya(r)))}}if(d instanceof td||d instanceof Ya||d instanceof Ad){if(d.has(e)){var t=d.get(e);if(t instanceof td){var u=qd(f);return Ra(16)?t.apply(this.K,u):t.invoke.apply(t,[this.K].concat(ya(u)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof td?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof yd&&e==="toString")return d.toString();
throw Oa(Error("TypeError: Object has no '"+e+"' property."));}function Kd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Ld(){var a=Ca.apply(0,arguments),b=this.K.tb(),c=Ta(b,a);if(c instanceof Fa)return c}function Md(){return Fd}
function Nd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Fa)return d}}function Od(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.nh(c,d)}}}function Pd(){return Gd}function Qd(a,b){return new Fa(a,this.evaluate(b))}
function Rd(a,b){var c=Ca.apply(2,arguments),d;if(Ra(17)){for(var e=[],f=this.evaluate(b),g=0;g<f.length;g++)e.push(f[g]);d=new pd(e)}else{d=new pd;for(var h=this.evaluate(b),m=0;m<h.length;m++)d.push(h[m])}var n=[51,a,d].concat(ya(c));this.K.add(a,this.evaluate(n))}function Td(a,b){return this.evaluate(a)/this.evaluate(b)}function Ud(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof yd,f=d instanceof yd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}
function Vd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Wd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ta(f,d);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}}}function Xd(a,b,c){if(typeof b==="string")return Wd(a,function(){return b.length},function(f){return f},c);if(b instanceof Ya||b instanceof Ad||b instanceof pd||b instanceof td){var d=b.Aa(),e=d.length;return Wd(a,function(){return e},function(f){return d[f]},c)}}
function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){g.set(d,h);return g},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){var m=g.tb();m.nh(d,h);return m},e,f)}function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){var m=g.tb();m.add(d,h);return m},e,f)}
function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){g.set(d,h);return g},e,f)}function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){var m=g.tb();m.nh(d,h);return m},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){var m=g.tb();m.add(d,h);return m},e,f)}
function be(a,b,c){if(typeof b==="string")return Wd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof pd)return Wd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function ee(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof pd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.tb();for(e(g,m);Ua(m,b);){var n=Ta(m,h);if(n instanceof Fa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.tb();e(m,p);Ua(p,c);m=p}}
function fe(a,b){var c=Ca.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof pd))throw Error("Error: non-List value given for Fn argument names.");return new td(a,function(){return function(){var f=Ca.apply(0,arguments),g=d.tb();g.ub()===void 0&&g.Od(this.K.ub());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new pd(h));var r=Ta(g,c);if(r instanceof Fa)return r.type===
"return"?r.data:r}}())}function ge(a){var b=this.evaluate(a),c=this.K;if(he&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ie(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ya||d instanceof Ad||d instanceof pd||d instanceof td)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:od(e)&&(c=d[e]);else if(d instanceof yd)return;return c}function je(a,b){return this.evaluate(a)>this.evaluate(b)}function ke(a,b){return this.evaluate(a)>=this.evaluate(b)}
function le(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof yd&&(c=c.getValue());d instanceof yd&&(d=d.getValue());return c===d}function me(a,b){return!le.call(this,a,b)}function ne(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ta(this.K,d);if(e instanceof Fa)return e}var he=!1;
function oe(a,b){return this.evaluate(a)<this.evaluate(b)}function pe(a,b){return this.evaluate(a)<=this.evaluate(b)}function qe(){if(Ra(17)){for(var a=[],b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return new pd(a)}for(var d=new pd,e=0;e<arguments.length;e++){var f=this.evaluate(arguments[e]);d.push(f)}return d}function re(){for(var a=new Ya,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}
function se(a,b){return this.evaluate(a)%this.evaluate(b)}function te(a,b){return this.evaluate(a)*this.evaluate(b)}function ue(a){return-this.evaluate(a)}function ve(a){return!this.evaluate(a)}function we(a,b){return!Ud.call(this,a,b)}function xe(){return null}function ye(a,b){return this.evaluate(a)||this.evaluate(b)}function ze(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Ae(a){return this.evaluate(a)}function Be(){return Ca.apply(0,arguments)}
function Ce(a){return new Fa("return",this.evaluate(a))}function De(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof td||d instanceof pd||d instanceof Ya)&&d.set(String(e),f);return f}function Ee(a,b){return this.evaluate(a)-this.evaluate(b)}
function Fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Fa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Fa&&(g.type==="return"||g.type==="continue")))return g}
function Ge(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function He(a){var b=this.evaluate(a);return b instanceof td?"function":typeof b}function Ie(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Je(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ta(this.K,e);if(f instanceof Fa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ta(this.K,e);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ke(a){return~Number(this.evaluate(a))}function Le(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ne(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Pe(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Qe(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Re(){}
function Se(a,b,c){try{var d=this.evaluate(b);if(d instanceof Fa)return d}catch(h){if(!(h instanceof Na&&h.Rl))throw h;var e=this.K.tb();a!==""&&(h instanceof Na&&(h=h.lm),e.add(a,new yd(h)));var f=this.evaluate(c),g=Ta(e,f);if(g instanceof Fa)return g}}function Te(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.Rl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Fa)return e;if(c)throw c;if(d instanceof Fa)return d};var Ve=function(){this.C=new Xa;Ue(this)};Ve.prototype.execute=function(a){return this.C.Bj(a)};var Ue=function(a){var b=function(c,d){var e=new ud(String(c),d);e.Wa();var f=String(c);a.C.C.set(f,e);Sa.set(f,e)};b("map",re);b("and",cd);b("contains",fd);b("equals",dd);b("or",ed);b("startsWith",gd);b("variable",hd)};Ve.prototype.Nb=function(a){this.C.Nb(a)};var Xe=function(){this.H=!1;this.C=new Xa;We(this);this.H=!0};Xe.prototype.execute=function(a){return Ze(this.C.Bj(a))};var $e=function(a,b,c){return Ze(a.C.eo(b,c))};Xe.prototype.Wa=function(){this.C.Wa()};
var We=function(a){var b=function(c,d){var e=String(c),f=new ud(e,d);f.Wa();a.C.C.set(e,f);Sa.set(e,f)};b(0,Hd);b(1,Id);b(2,Jd);b(3,Kd);b(56,Oe);b(57,Le);b(58,Ke);b(59,Qe);b(60,Me);b(61,Ne);b(62,Pe);b(53,Ld);b(4,Md);b(5,Nd);b(68,Se);b(52,Od);b(6,Pd);b(49,Qd);b(7,qe);b(8,re);b(9,Nd);b(50,Rd);b(10,Td);b(12,Ud);b(13,Vd);b(67,Te);b(51,fe);b(47,Yd);b(54,Zd);b(55,$d);b(63,ee);b(64,ae);b(65,ce);b(66,de);b(15,ge);b(16,ie);b(17,ie);b(18,je);b(19,ke);b(20,le);b(21,me);b(22,ne);b(23,oe);b(24,pe);b(25,se);b(26,
te);b(27,ue);b(28,ve);b(29,we);b(45,xe);b(30,ye);b(32,ze);b(33,ze);b(34,Ae);b(35,Ae);b(46,Be);b(36,Ce);b(43,De);b(37,Ee);b(38,Fe);b(39,Ge);b(40,He);b(44,Re);b(41,Ie);b(42,Je)};Xe.prototype.Fd=function(){return this.C.Fd()};Xe.prototype.Nb=function(a){this.C.Nb(a)};Xe.prototype.Vc=function(a){this.C.Vc(a)};
function Ze(a){if(a instanceof Fa||a instanceof td||a instanceof pd||a instanceof Ya||a instanceof Ad||a instanceof yd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var af=function(a){this.message=a};function bf(a){a.Kr=!0;return a};var cf=bf(function(a){return typeof a==="string"});function df(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new af("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function ef(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var ff=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function gf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+df(e)+c}a<<=2;d||(a|=32);return c=""+df(a|b)+c}
function hf(a,b){var c;var d=a.Uc,e=a.Ah;d===void 0?c="":(e||(e=0),c=""+gf(1,1)+df(d<<2|e));var f=a.Ql,g=a.No,h="4"+c+(f?""+gf(2,1)+df(f):"")+(g?""+gf(12,1)+df(g):""),m,n=a.Cj;m=n&&ff.test(n)?""+gf(3,2)+n:"";var p,q=a.yj;p=q?""+gf(4,1)+df(q):"";var r;var t=a.ctid;if(t&&b){var u=gf(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+df(1+y.length)+(a.dm||0)+y}}else r="";var z=a.Cq,C=a.we,D=a.Pa,G=a.Or,I=h+m+p+r+(z?""+gf(6,1)+df(z):"")+(C?""+gf(7,3)+df(C.length)+
C:"")+(D?""+gf(8,3)+df(D.length)+D:"")+(G?""+gf(9,3)+df(G.length)+G:""),M;var T=a.Sl;T=T===void 0?{}:T;for(var da=[],N=l(Object.keys(T)),W=N.next();!W.done;W=N.next()){var ia=W.value;da[Number(ia)]=T[ia]}if(da.length){var ka=gf(10,3),Y;if(da.length===0)Y=df(0);else{for(var X=[],ja=0,wa=!1,sa=0;sa<da.length;sa++){wa=!0;var Va=sa%6;da[sa]&&(ja|=1<<Va);Va===5&&(X.push(df(ja)),ja=0,wa=!1)}wa&&X.push(df(ja));Y=X.join("")}var Wa=Y;M=""+ka+df(Wa.length)+Wa}else M="";var Db=a.om,Ub=a.sq;return I+M+(Db?""+
gf(11,3)+df(Db.length)+Db:"")+(Ub?""+gf(13,3)+df(Ub.length)+Ub:"")};var jf=function(){function a(b){return{toString:function(){return b}}}return{Qm:a("consent"),Qj:a("convert_case_to"),Rj:a("convert_false_to"),Sj:a("convert_null_to"),Tj:a("convert_true_to"),Uj:a("convert_undefined_to"),Qq:a("debug_mode_metadata"),Ua:a("function"),Ai:a("instance_name"),io:a("live_only"),jo:a("malware_disabled"),METADATA:a("metadata"),mo:a("original_activity_id"),lr:a("original_vendor_template_id"),kr:a("once_on_load"),lo:a("once_per_event"),pl:a("once_per_load"),nr:a("priority_override"),
ur:a("respected_consent_types"),Al:a("setup_tags"),kh:a("tag_id"),Il:a("teardown_tags")}}();var Ff;var Gf=[],Hf=[],If=[],Jf=[],Kf=[],Lf,Mf,Nf;function Of(a){Nf=Nf||a}
function Pf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Gf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Jf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)If.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Qf(p[r])}Hf.push(p)}}
function Qf(a){}var Rf,Sf=[],Tf=[];function Uf(a,b){var c={};c[jf.Ua]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Vf(a,b,c){try{return Mf(Wf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Wf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Xf(a[e],b,c));return d},Xf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Xf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Gf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[jf.Ai]);try{var m=Wf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Yf(m,{event:b,index:f,type:2,
name:h});Rf&&(d=Rf.Oo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Xf(a[n],b,c)]=Xf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Xf(a[q],b,c);Nf&&(p=p||Nf.Pp(r));d.push(r)}return Nf&&p?Nf.To(d):d.join("");case "escape":d=Xf(a[1],b,c);if(Nf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Nf.Qp(a))return Nf.hq(d);d=String(d);for(var t=2;t<a.length;t++)qf[a[t]]&&(d=qf[a[t]](d));return d;
case "tag":var u=a[1];if(!Jf[u])throw Error("Unable to resolve tag reference "+u+".");return{Wl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[jf.Ua]=a[1];var w=Vf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Yf=function(a,b){var c=a[jf.Ua],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Lf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Sf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Gb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Gf[q];break;case 1:r=Jf[q];break;default:n="";break a}var t=r&&r[jf.Ai];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Tf.indexOf(c)===-1){Tf.push(c);
var y=Ab();u=e(g);var z=Ab()-y,C=Ab();v=Ff(c,h,b);w=z-(Ab()-C)}else if(e&&(u=e(g)),!e||f)v=Ff(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),nd(u)?(Array.isArray(u)?Array.isArray(v):ld(u)?ld(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Zf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};va(Zf,Error);Zf.prototype.getMessage=function(){return this.message};function $f(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)$f(a[c],b[c])}};function ag(){return function(a,b){var c;var d=bg;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function bg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)nb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function cg(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=dg(a),f=0;f<Hf.length;f++){var g=Hf[f],h=eg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Jf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function eg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function dg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Vf(If[c],a));return b[c]}};function fg(a,b){b[jf.Qj]&&typeof a==="string"&&(a=b[jf.Qj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(jf.Sj)&&a===null&&(a=b[jf.Sj]);b.hasOwnProperty(jf.Uj)&&a===void 0&&(a=b[jf.Uj]);b.hasOwnProperty(jf.Tj)&&a===!0&&(a=b[jf.Tj]);b.hasOwnProperty(jf.Rj)&&a===!1&&(a=b[jf.Rj]);return a};var gg=function(){this.C={}},ig=function(a,b){var c=hg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ya(Ca.apply(0,arguments)))})};function jg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Zf(c,d,g);}}
function kg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ya(Ca.apply(1,arguments))));jg(e,b,d,g);jg(f,b,d,g)}}}};var og=function(){var a=data.permissions||{},b=lg.ctid,c=this;this.H={};this.C=new gg;var d={},e={},f=kg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ya(Ca.apply(1,arguments)))):{}});tb(a,function(g,h){function m(p){var q=Ca.apply(1,arguments);if(!n[p])throw mg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ya(q)))}var n={};tb(h,function(p,q){var r=ng(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Ol&&!e[p]&&(e[p]=r.Ol)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw mg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ya(t.slice(1))))}})},pg=function(a){return hg.H[a]||function(){}};
function ng(a,b){var c=Uf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=mg;try{return Yf(c)}catch(d){return{assert:function(e){throw new Zf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Zf(a,{},"Permission "+a+" is unknown.");}}}}function mg(a,b,c){return new Zf(a,b,c)};var qg=!1;var rg={};rg.Hm=wb('');rg.cp=wb('');function wg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var xg=[];function yg(a){switch(a){case 1:return 0;case 216:return 15;case 222:return 18;case 38:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 16;case 75:return 3;case 103:return 13;case 197:return 14;case 114:return 11;case 116:return 4;case 221:return 17;case 135:return 8;case 136:return 5}}function zg(a,b){xg[a]=b;var c=yg(a);c!==void 0&&(Pa[c]=b)}function B(a){zg(a,!0)}B(39);
B(34);B(35);B(36);B(56);
B(145);B(153);
B(144);
B(120);B(5);B(111);
B(139);B(87);B(92);B(159);
B(132);B(20);B(72);
B(113);B(154);B(116);zg(23,!1),B(24);Qa[1]=wg('1',6E4);Qa[3]=wg('10',1);Qa[2]=wg('',50);B(29);
Ag(26,25);
B(37);B(9);
B(91);B(123);B(158);B(71);
B(136);B(127);
B(27);B(69);
B(135);B(95);B(38);
B(103);B(112);B(63);B(152);
B(101);
B(122);B(121);
B(134);
B(22);

B(19);
B(90);
B(114);B(59);

B(175);
B(185);
B(186);B(192);
B(200);B(202);
B(210);
B(213);
function E(a){return!!xg[a]}
function Ag(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};var Cg={},Dg=(Cg.uaa=!0,Cg.uab=!0,Cg.uafvl=!0,Cg.uamb=!0,Cg.uam=!0,Cg.uap=!0,Cg.uapv=!0,Cg.uaw=!0,Cg);
var Lg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Jg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Kg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Gb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Kg=/^[a-z$_][\w-$]*$/i,Jg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Mg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Ng(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Og(a,b){return String(a).split(",").indexOf(String(b))>=0}var Pg=new sb;function Qg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Pg.get(e);f||(f=new RegExp(b,d),Pg.set(e,f));return f.test(a)}catch(g){return!1}}function Rg(a,b){return String(a).indexOf(String(b))>=0}
function Sg(a,b){return String(a)===String(b)}function Tg(a,b){return Number(a)>=Number(b)}function Ug(a,b){return Number(a)<=Number(b)}function Vg(a,b){return Number(a)>Number(b)}function Wg(a,b){return Number(a)<Number(b)}function Xg(a,b){return Gb(String(a),String(b))};var dh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,eh={Fn:"function",PixieMap:"Object",List:"Array"};
function fh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=dh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof td?n="Fn":m instanceof pd?n="List":m instanceof Ya?n="PixieMap":m instanceof Ad?n="PixiePromise":m instanceof yd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((eh[n]||n)+", which does not match required type ")+
((eh[h]||h)+"."));}}}function F(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof td?d.push("function"):g instanceof pd?d.push("Array"):g instanceof Ya?d.push("Object"):g instanceof Ad?d.push("Promise"):g instanceof yd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function gh(a){return a instanceof Ya}function hh(a){return gh(a)||a===null||ih(a)}
function jh(a){return a instanceof td}function kh(a){return jh(a)||a===null||ih(a)}function lh(a){return a instanceof pd}function mh(a){return a instanceof yd}function nh(a){return typeof a==="string"}function oh(a){return nh(a)||a===null||ih(a)}function ph(a){return typeof a==="boolean"}function qh(a){return ph(a)||ih(a)}function rh(a){return ph(a)||a===null||ih(a)}function sh(a){return typeof a==="number"}function ih(a){return a===void 0};function th(a){return""+a}
function uh(a,b){var c=[];return c};function vh(a,b){var c=new td(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Wa();return c}
function wh(a,b){var c=new Ya,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];lb(e)?c.set(d,vh(a+"_"+d,e)):ld(e)?c.set(d,wh(a+"_"+d,e)):(nb(e)||mb(e)||typeof e==="boolean")&&c.set(d,e)}c.Wa();return c};function xh(a,b){if(!nh(a))throw F(this.getName(),["string"],arguments);if(!oh(b))throw F(this.getName(),["string","undefined"],arguments);var c={},d=new Ya;return d=wh("AssertApiSubject",
c)};function yh(a,b){if(!oh(b))throw F(this.getName(),["string","undefined"],arguments);if(a instanceof Ad)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ya;return d=wh("AssertThatSubject",c)};function zh(a){return function(){for(var b=Ca.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Bd(b[e],d));return Cd(a.apply(null,c))}}function Ah(){for(var a=Math,b=Bh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=zh(a[e].bind(a)))}return c};function Ch(a){return a!=null&&Gb(a,"__cvt_")};function Dh(a){var b;return b};function Eh(a){var b;return b};function Fh(a){try{return encodeURI(a)}catch(b){}};function Gh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Lh(a){if(!oh(a))throw F(this.getName(),["string|undefined"],arguments);};function Mh(a,b){if(!sh(a)||!sh(b))throw F(this.getName(),["number","number"],arguments);return qb(a,b)};function Nh(){return(new Date).getTime()};function Oh(a){if(a===null)return"null";if(a instanceof pd)return"array";if(a instanceof td)return"function";if(a instanceof yd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Ph(a){function b(c){return function(d){try{return c(d)}catch(e){(qg||rg.Hm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Cd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Bd(c))}),publicName:"JSON"}};function Qh(a){return vb(Bd(a,this.K))};function Rh(a){return Number(Bd(a,this.K))};function Sh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Th(a,b,c){var d=null,e=!1;return e?d:null};var Bh="floor ceil round max min abs pow sqrt".split(" ");function Uh(){var a={};return{qp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Dm:function(b,c){a[b]=c},reset:function(){a={}}}}function Vh(a,b){return function(){return td.prototype.invoke.apply(a,[b].concat(ya(Ca.apply(0,arguments))))}}
function Wh(a,b){if(!nh(a))throw F(this.getName(),["string","any"],arguments);}
function Xh(a,b){if(!nh(a)||!gh(b))throw F(this.getName(),["string","PixieMap"],arguments);};var Yh={};
Yh.keys=function(a){return new pd};
Yh.values=function(a){return new pd};
Yh.entries=function(a){return new pd};
Yh.freeze=function(a){return a};Yh.delete=function(a,b){return!1};function H(a,b){var c=Ca.apply(2,arguments),d=a.K.ub();if(!d)throw Error("Missing program state.");if(d.oq){try{d.Pl.apply(null,[b].concat(ya(c)))}catch(e){throw fb("TAGGING",21),e;}return}d.Pl.apply(null,[b].concat(ya(c)))};var $h=function(){this.H={};this.C={};this.N=!0;};$h.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};$h.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
$h.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:lb(b)?vh(a,b):wh(a,b)};function ai(a,b){var c=void 0;return c};function bi(){var a={};return a};var J={m:{Ka:"ad_personalization",U:"ad_storage",V:"ad_user_data",ja:"analytics_storage",hc:"region",da:"consent_updated",rg:"wait_for_update",Zm:"app_remove",bn:"app_store_refund",dn:"app_store_subscription_cancel",fn:"app_store_subscription_convert",gn:"app_store_subscription_renew",hn:"consent_update",Yj:"add_payment_info",Zj:"add_shipping_info",Sd:"add_to_cart",Td:"remove_from_cart",bk:"view_cart",Xc:"begin_checkout",Ud:"select_item",kc:"view_item_list",Gc:"select_promotion",mc:"view_promotion",
lb:"purchase",Vd:"refund",yb:"view_item",dk:"add_to_wishlist",jn:"exception",kn:"first_open",ln:"first_visit",qa:"gtag.config",Eb:"gtag.get",mn:"in_app_purchase",Yc:"page_view",nn:"screen_view",on:"session_start",pn:"source_update",qn:"timing_complete",rn:"track_social",Wd:"user_engagement",sn:"user_id_update",Le:"gclid_link_decoration_source",Me:"gclid_storage_source",nc:"gclgb",mb:"gclid",ek:"gclid_len",Xd:"gclgs",Yd:"gcllp",Zd:"gclst",ya:"ads_data_redaction",Ne:"gad_source",Oe:"gad_source_src",
Zc:"gclid_url",fk:"gclsrc",Pe:"gbraid",ae:"wbraid",Ga:"allow_ad_personalization_signals",yg:"allow_custom_scripts",Qe:"allow_direct_google_requests",zg:"allow_display_features",Ag:"allow_enhanced_conversions",Ob:"allow_google_signals",nb:"allow_interest_groups",tn:"app_id",un:"app_installer_id",vn:"app_name",wn:"app_version",Pb:"auid",xn:"auto_detection_enabled",bd:"aw_remarketing",Ph:"aw_remarketing_only",Bg:"discount",Cg:"aw_feed_country",Dg:"aw_feed_language",sa:"items",Eg:"aw_merchant_id",gk:"aw_basket_type",
Re:"campaign_content",Se:"campaign_id",Te:"campaign_medium",Ue:"campaign_name",Ve:"campaign",We:"campaign_source",Xe:"campaign_term",Qb:"client_id",hk:"rnd",Qh:"consent_update_type",yn:"content_group",zn:"content_type",Rb:"conversion_cookie_prefix",Ye:"conversion_id",Ra:"conversion_linker",Rh:"conversion_linker_disabled",dd:"conversion_api",Fg:"cookie_deprecation",ob:"cookie_domain",pb:"cookie_expires",zb:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",cb:"cookie_prefix",Hc:"cookie_update",fd:"country",
Sa:"currency",Sh:"customer_buyer_stage",Ze:"customer_lifetime_value",Th:"customer_loyalty",Uh:"customer_ltv_bucket",af:"custom_map",Vh:"gcldc",gd:"dclid",ik:"debug_mode",oa:"developer_id",An:"disable_merchant_reported_purchases",hd:"dc_custom_params",Bn:"dc_natural_search",jk:"dynamic_event_settings",kk:"affiliation",Gg:"checkout_option",Wh:"checkout_step",lk:"coupon",bf:"item_list_name",Xh:"list_name",Cn:"promotions",be:"shipping",Yh:"tax",Hg:"engagement_time_msec",Ig:"enhanced_client_id",Zh:"enhanced_conversions",
mk:"enhanced_conversions_automatic_settings",cf:"estimated_delivery_date",ai:"euid_logged_in_state",df:"event_callback",Dn:"event_category",Tb:"event_developer_id_string",En:"event_label",jd:"event",Jg:"event_settings",Kg:"event_timeout",Gn:"description",Hn:"fatal",In:"experiments",bi:"firebase_id",ce:"first_party_collection",Lg:"_x_20",qc:"_x_19",nk:"fledge_drop_reason",pk:"fledge",qk:"flight_error_code",rk:"flight_error_message",sk:"fl_activity_category",tk:"fl_activity_group",di:"fl_advertiser_id",
uk:"fl_ar_dedupe",ef:"match_id",vk:"fl_random_number",wk:"tran",xk:"u",Mg:"gac_gclid",de:"gac_wbraid",yk:"gac_wbraid_multiple_conversions",zk:"ga_restrict_domain",ei:"ga_temp_client_id",Jn:"ga_temp_ecid",kd:"gdpr_applies",Ak:"geo_granularity",Ic:"value_callback",rc:"value_key",sc:"google_analysis_params",ee:"_google_ng",fe:"google_signals",Bk:"google_tld",ff:"gpp_sid",hf:"gpp_string",Ng:"groups",Ck:"gsa_experiment_id",jf:"gtag_event_feature_usage",Dk:"gtm_up",Jc:"iframe_state",kf:"ignore_referrer",
fi:"internal_traffic_results",Ek:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Og:"is_passthrough",ld:"_lps",Ab:"language",Pg:"legacy_developer_id_string",Ta:"linker",he:"accept_incoming",uc:"decorate_forms",la:"domains",Mc:"url_position",md:"merchant_feed_label",nd:"merchant_feed_language",od:"merchant_id",Fk:"method",Kn:"name",Gk:"navigation_type",lf:"new_customer",Qg:"non_interaction",Ln:"optimize_id",Hk:"page_hostname",nf:"page_path",Ya:"page_referrer",Fb:"page_title",Ik:"passengers",
Jk:"phone_conversion_callback",Mn:"phone_conversion_country_code",Kk:"phone_conversion_css_class",Nn:"phone_conversion_ids",Lk:"phone_conversion_number",Mk:"phone_conversion_options",On:"_platinum_request_status",Pn:"_protected_audience_enabled",ie:"quantity",Rg:"redact_device_info",gi:"referral_exclusion_definition",Tq:"_request_start_time",Vb:"restricted_data_processing",Qn:"retoken",Rn:"sample_rate",hi:"screen_name",Nc:"screen_resolution",Nk:"_script_source",Sn:"search_term",qb:"send_page_view",
pd:"send_to",rd:"server_container_url",pf:"session_duration",Sg:"session_engaged",ii:"session_engaged_time",Wb:"session_id",Tg:"session_number",qf:"_shared_user_id",je:"delivery_postal_code",Uq:"_tag_firing_delay",Vq:"_tag_firing_time",Wq:"temporary_client_id",ji:"_timezone",ki:"topmost_url",Tn:"tracking_id",li:"traffic_type",La:"transaction_id",vc:"transport_url",Ok:"trip_type",ud:"update",Gb:"url_passthrough",Pk:"uptgs",rf:"_user_agent_architecture",tf:"_user_agent_bitness",uf:"_user_agent_full_version_list",
vf:"_user_agent_mobile",wf:"_user_agent_model",xf:"_user_agent_platform",yf:"_user_agent_platform_version",zf:"_user_agent_wow64",eb:"user_data",mi:"user_data_auto_latency",ni:"user_data_auto_meta",oi:"user_data_auto_multi",ri:"user_data_auto_selectors",si:"user_data_auto_status",wc:"user_data_mode",Ug:"user_data_settings",Ma:"user_id",Xb:"user_properties",Qk:"_user_region",Af:"us_privacy_string",za:"value",Rk:"wbraid_multiple_conversions",wd:"_fpm_parameters",yi:"_host_name",al:"_in_page_command",
bl:"_ip_override",kl:"_is_passthrough_cid",xc:"non_personalized_ads",Ji:"_sst_parameters",oc:"conversion_label",Ca:"page_location",Ub:"global_developer_id_string",sd:"tc_privacy_string"}};var ci={},di=(ci[J.m.da]="gcu",ci[J.m.nc]="gclgb",ci[J.m.mb]="gclaw",ci[J.m.ek]="gclid_len",ci[J.m.Xd]="gclgs",ci[J.m.Yd]="gcllp",ci[J.m.Zd]="gclst",ci[J.m.Pb]="auid",ci[J.m.Bg]="dscnt",ci[J.m.Cg]="fcntr",ci[J.m.Dg]="flng",ci[J.m.Eg]="mid",ci[J.m.gk]="bttype",ci[J.m.Qb]="gacid",ci[J.m.oc]="label",ci[J.m.dd]="capi",ci[J.m.Fg]="pscdl",ci[J.m.Sa]="currency_code",ci[J.m.Sh]="clobs",ci[J.m.Ze]="vdltv",ci[J.m.Th]="clolo",ci[J.m.Uh]="clolb",ci[J.m.ik]="_dbg",ci[J.m.cf]="oedeld",ci[J.m.Tb]="edid",ci[J.m.nk]=
"fdr",ci[J.m.pk]="fledge",ci[J.m.Mg]="gac",ci[J.m.de]="gacgb",ci[J.m.yk]="gacmcov",ci[J.m.kd]="gdpr",ci[J.m.Ub]="gdid",ci[J.m.ee]="_ng",ci[J.m.ff]="gpp_sid",ci[J.m.hf]="gpp",ci[J.m.Ck]="gsaexp",ci[J.m.jf]="_tu",ci[J.m.Jc]="frm",ci[J.m.Og]="gtm_up",ci[J.m.ld]="lps",ci[J.m.Pg]="did",ci[J.m.md]="fcntr",ci[J.m.nd]="flng",ci[J.m.od]="mid",ci[J.m.lf]=void 0,ci[J.m.Fb]="tiba",ci[J.m.Vb]="rdp",ci[J.m.Wb]="ecsid",ci[J.m.qf]="ga_uid",ci[J.m.je]="delopc",ci[J.m.sd]="gdpr_consent",ci[J.m.La]="oid",ci[J.m.Pk]=
"uptgs",ci[J.m.rf]="uaa",ci[J.m.tf]="uab",ci[J.m.uf]="uafvl",ci[J.m.vf]="uamb",ci[J.m.wf]="uam",ci[J.m.xf]="uap",ci[J.m.yf]="uapv",ci[J.m.zf]="uaw",ci[J.m.mi]="ec_lat",ci[J.m.ni]="ec_meta",ci[J.m.oi]="ec_m",ci[J.m.ri]="ec_sel",ci[J.m.si]="ec_s",ci[J.m.wc]="ec_mode",ci[J.m.Ma]="userId",ci[J.m.Af]="us_privacy",ci[J.m.za]="value",ci[J.m.Rk]="mcov",ci[J.m.yi]="hn",ci[J.m.al]="gtm_ee",ci[J.m.xc]="npa",ci[J.m.Ye]=null,ci[J.m.Nc]=null,ci[J.m.Ab]=null,ci[J.m.sa]=null,ci[J.m.Ca]=null,ci[J.m.Ya]=null,ci[J.m.ki]=
null,ci[J.m.wd]=null,ci[J.m.Le]=null,ci[J.m.Me]=null,ci[J.m.sc]=null,ci);function ei(a,b){if(a){var c=a.split("x");c.length===2&&(fi(b,"u_w",c[0]),fi(b,"u_h",c[1]))}}
function gi(a){var b=hi;b=b===void 0?ii:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(ji(q.value)),r.push(ji(q.quantity)),r.push(ji(q.item_id)),r.push(ji(q.start_date)),r.push(ji(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ii(a){return ki(a.item_id,a.id,a.item_name)}function ki(){for(var a=l(Ca.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function li(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function fi(a,b,c){c===void 0||c===null||c===""&&!Dg[b]||(a[b]=c)}function ji(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var mi={},ni=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=qb(0,1)===0,b=qb(0,1)===0,c++,c>30)return;return a},pi={uq:oi};function qi(a,b){var c=mi[b],d=c.studyId,e=c.experimentId,f=c.probability;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;mi[b].active||(mi[b].probability>.5?ri(a,e):f<=0||f>1||pi.uq(a,b))}}
function oi(a,b){var c=mi[b],d=c.controlId2;if(!(qb(0,9999)<c.probability*(c.controlId2&&c.probability<=.25?4:2)*1E4))return a;si(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.probability<=.25?d:void 0,experimentCallback:function(){}});return a}function ri(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function si(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=ni()?0:1;e&&(g|=(ni()?0:1)<<1);g===0?(ri(a,c),f()):g===1?ri(a,d):g===2&&ri(a,e)}};var K={J:{Kj:"call_conversion",W:"conversion",Un:"floodlight",Cf:"ga_conversion",Fi:"landing_page",Ha:"page_view",na:"remarketing",jb:"user_data_lead",Na:"user_data_web"}};
var ti={},ui=Object.freeze((ti[J.m.Le]=1,ti[J.m.Me]=1,ti[J.m.Ga]=1,ti[J.m.Qe]=1,ti[J.m.Ag]=1,ti[J.m.nb]=1,ti[J.m.bd]=1,ti[J.m.Ph]=1,ti[J.m.Bg]=1,ti[J.m.Cg]=1,ti[J.m.Dg]=1,ti[J.m.sa]=1,ti[J.m.Eg]=1,ti[J.m.Rb]=1,ti[J.m.Ra]=1,ti[J.m.ob]=1,ti[J.m.pb]=1,ti[J.m.zb]=1,ti[J.m.cb]=1,ti[J.m.Sa]=1,ti[J.m.Sh]=1,ti[J.m.Ze]=1,ti[J.m.Th]=1,ti[J.m.Uh]=1,ti[J.m.oa]=1,ti[J.m.An]=1,ti[J.m.Zh]=1,ti[J.m.cf]=1,ti[J.m.bi]=1,ti[J.m.ce]=1,ti[J.m.sc]=1,ti[J.m.Kc]=1,ti[J.m.Lc]=1,ti[J.m.Ab]=1,ti[J.m.md]=1,ti[J.m.nd]=1,ti[J.m.od]=
1,ti[J.m.lf]=1,ti[J.m.Ca]=1,ti[J.m.Ya]=1,ti[J.m.Jk]=1,ti[J.m.Kk]=1,ti[J.m.Lk]=1,ti[J.m.Mk]=1,ti[J.m.Vb]=1,ti[J.m.qb]=1,ti[J.m.pd]=1,ti[J.m.rd]=1,ti[J.m.je]=1,ti[J.m.La]=1,ti[J.m.vc]=1,ti[J.m.ud]=1,ti[J.m.Gb]=1,ti[J.m.eb]=1,ti[J.m.Ma]=1,ti[J.m.za]=1,ti));function vi(a){return wi?A.querySelectorAll(a):null}
function xi(a,b){if(!wi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var yi=!1;
if(A.querySelectorAll)try{var zi=A.querySelectorAll(":root");zi&&zi.length==1&&zi[0]==A.documentElement&&(yi=!0)}catch(a){}var wi=yi;var Ai="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Bi="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Ci(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Di(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Di(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Ei(a){if(E(178)&&a){Ci(Ai,a);for(var b=ob(a.address),c=0;c<b.length;c++){var d=b[c];d&&Ci(Bi,d)}var e=a.home_address;e&&Ci(Bi,e)}};function Fi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function Gi(){this.blockSize=-1};function Hi(a,b){this.blockSize=-1;this.blockSize=64;this.N=Da.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.fa=a;this.R=b;this.ma=Da.Int32Array?new Int32Array(64):Array(64);Ii===void 0&&(Da.Int32Array?Ii=new Int32Array(Ji):Ii=Ji);this.reset()}Ea(Hi,Gi);for(var Ki=[],Li=0;Li<63;Li++)Ki[Li]=0;var Mi=[].concat(128,Ki);
Hi.prototype.reset=function(){this.P=this.H=0;var a;if(Da.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Ni=function(a){for(var b=a.N,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Ii[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Hi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ni(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Ni(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Hi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Mi,56-this.H):this.update(Mi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Ni(this);for(var d=0,e=0;e<this.fa;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Ji=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Ii;function Oi(){Hi.call(this,8,Pi)}Ea(Oi,Hi);var Pi=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Qi=/^[0-9A-Fa-f]{64}$/;function Ri(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Si(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Qi.test(a))return Promise.resolve(a);try{var d=Ri(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Ti(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ti(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ui=[],Vi;function Wi(a){Vi?Vi(a):Ui.push(a)}function Xi(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Wi(a),b):c}function Yi(a,b){if(!E(190))return b;var c=Zi(a,"");return c!==b?(Wi(a),b):c}function Zi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function $i(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Wi(a),b)}function aj(){Vi=bj;for(var a=l(Ui),b=a.next();!b.done;b=a.next())Vi(b.value);Ui.length=0};var cj={Nm:'5000',Om:'5000',Xm:'512',Ym:'1000',Yn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',Zn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',vo:Yi(44,'101509157~103116026~103200004~103233427~104684208~104684211~104921715~104921717~104952209~104952211')},dj={Do:Number(cj.Nm)||-1,Eo:Number(cj.Om)||-1,Zo:Number(cj.Xm)||0,bp:Number(cj.Ym)||0,vp:cj.Yn.split("~"),
wp:cj.Zn.split("~"),Mq:cj.vo};ma(Object,"assign").call(Object,{},dj);function L(a){fb("GTM",a)};
var ij=function(a,b){var c=["tv.1"],d=ej(a);if(d)return c.push(d),{Za:!1,Dj:c.join("~"),ng:{}};var e={},f=0;var g=fj(a,function(p,q,r){var t=p.value,u;if(r){var v=q+"__"+f++;u="${userData."+v+"|sha256}";e[v]=t}else u=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).Za;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{Za:g,Dj:h,ng:m,ap:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?gj():hj()}:{Za:g,Dj:h,ng:m}},kj=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=jj(a);return fj(b,function(){}).Za},fj=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=lj[g.name];if(h){var m=mj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{Za:d,ej:c}},mj=function(a){var b=nj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(oj.test(e)||
Qi.test(e))}return d},nj=function(a){return pj.indexOf(a)!==-1},hj=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BHzT6/oair5aOoHdYjEmfzUicVThxHYEl7UgRE/YE2GI/lntBc4CILotyJom55+T7o8VJA67XSfq6dT5RPph5sE\x3d\x22,\x22version\x22:0},\x22id\x22:\x22f0c5b86a-0c49-4602-bc59-93c0ebdc0e5b\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BCccnnob7RTpZnXozKqbUvXw1mtdRlZZJG0WsMn7JboEACHSKM/s+Rta9TyXuMxFmxHM5sBeIfrK96fRPJOe3aA\x3d\x22,\x22version\x22:0},\x22id\x22:\x22856269be-3e07-4432-be53-43bb4fef6799\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BICV1Oo3rVMnzzZPvqF2lPJ7rSGKM63Wquezi8KacvOrbKJczfVxaewJtKDiFC0wtd/usVpi7GfBNgXOUA4f3do\x3d\x22,\x22version\x22:0},\x22id\x22:\x227bc98b84-7f5e-4a08-b561-134399ffd635\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BBOfSFHCVnwpMDMK/4YvG1aU6mpHrLqapgzYTpW0m4L18YROpTgYE67uzFFI3/+Del+5jK6w4oR7Ga6dcGGkH44\x3d\x22,\x22version\x22:0},\x22id\x22:\x2267490360-94dd-46de-96a7-3e1880072ea3\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BBWqoKhV6upED2PWudvnYDnXtdJ+ZVW64FLcSHvapcIQs1LeJzomcNf1bzhQn4If7R1jM/XNbrieqkGH2OX7gL8\x3d\x22,\x22version\x22:0},\x22id\x22:\x221e127111-8a8e-47b5-a85d-c432304f7b5c\x22}]}'},sj=function(a){if(x.Promise){var b=void 0;return b}},xj=function(a,b,c,d,e){if(x.Promise)try{var f=jj(a),g=tj(f,e).then(uj);return g}catch(p){}},zj=function(a){try{return uj(yj(jj(a)))}catch(b){}},rj=function(a,b){var c=void 0;return c},uj=function(a){var b=a.Tc,c=a.time,d=["tv.1"],e=ej(b);if(e)return d.push(e),{Kb:encodeURIComponent(d.join("~")),ej:!1,Za:!1,time:c,dj:!0};var f=b.filter(function(n){return!mj(n)}),g=fj(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.ej,m=g.Za;return{Kb:encodeURIComponent(d.join("~")),ej:h,Za:m,time:c,dj:!1}},ej=function(a){if(a.length===1&&a[0].name==="error_code")return lj.error_code+
"."+a[0].value},wj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(lj[d.name]&&d.value)return!0}return!1},jj=function(a){function b(r,t,u,v){var w=Aj(r);w!==""&&(Qi.test(w)?h.push({name:t,value:w,index:v}):h.push({name:t,value:u(w),index:v}))}function c(r,t){var u=r;if(mb(u)||Array.isArray(u)){u=ob(r);for(var v=0;v<u.length;++v){var w=Aj(u[v]),y=Qi.test(w);t&&!y&&L(89);!t&&y&&L(88)}}}function d(r,t){var u=r[t];c(u,!1);var v=
Bj[t];r[v]&&(r[t]&&L(90),u=r[v],c(u,!0));return u}function e(r,t,u){for(var v=ob(d(r,t)),w=0;w<v.length;++w)b(v[w],t,u)}function f(r,t,u,v){var w=d(r,t);b(w,t,u,v)}function g(r){return function(t){L(64);return r(t)}}var h=[];if(x.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",Cj);e(a,"phone_number",Dj);e(a,"first_name",g(Ej));e(a,"last_name",g(Ej));var m=a.home_address||{};e(m,"street",g(Fj));e(m,"city",g(Fj));e(m,"postal_code",g(Gj));e(m,"region",
g(Fj));e(m,"country",g(Gj));for(var n=ob(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",Ej,p);f(q,"last_name",Ej,p);f(q,"street",Fj,p);f(q,"city",Fj,p);f(q,"postal_code",Gj,p);f(q,"region",Fj,p);f(q,"country",Gj,p)}return h},Hj=function(a){var b=a?jj(a):[];return uj({Tc:b})},Ij=function(a){return a&&a!=null&&Object.keys(a).length>0&&x.Promise?jj(a).some(function(b){return b.value&&nj(b.name)&&!Qi.test(b.value)}):!1},Aj=function(a){return a==null?"":mb(a)?yb(String(a)):"e0"},Gj=function(a){return a.replace(Jj,
"")},Ej=function(a){return Fj(a.replace(/\s/g,""))},Fj=function(a){return yb(a.replace(Kj,"").toLowerCase())},Dj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Lj.test(a)?a:"e0"},Cj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Mj.test(c))return c}return"e0"},yj=function(a){var b=Zc();try{a.forEach(function(e){if(e.value&&nj(e.name)){var f;var g=e.value,h=x;if(g===""||
g==="e0"||Qi.test(g))f=g;else try{var m=new Oi;m.update(Ri(g));f=Ti(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Tc:a};if(b!==void 0){var d=Zc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Tc:[]}}},tj=function(a,b){if(!a.some(function(d){return d.value&&nj(d.name)}))return Promise.resolve({Tc:a});if(!x.Promise)return Promise.resolve({Tc:[]});var c=b?Zc():void 0;return Promise.all(a.map(function(d){return d.value&&nj(d.name)?Si(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Tc:a};if(c!==void 0){var e=Zc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Tc:[]}})},Kj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Mj=/^\S+@\S+\.\S+$/,Lj=/^\+\d{10,15}$/,Jj=/[.~]/g,oj=/^[0-9A-Za-z_-]{43}$/,Nj={},lj=(Nj.email="em",Nj.phone_number="pn",Nj.first_name="fn",Nj.last_name="ln",Nj.street="sa",Nj.city="ct",Nj.region="rg",Nj.country="co",Nj.postal_code="pc",Nj.error_code="ec",Nj),Oj={},Bj=(Oj.email="sha256_email_address",Oj.phone_number="sha256_phone_number",
Oj.first_name="sha256_first_name",Oj.last_name="sha256_last_name",Oj.street="sha256_street",Oj);var pj=Object.freeze(["email","phone_number","first_name","last_name","street"]);
var Pj={},Qj=(Pj[J.m.nb]=1,Pj[J.m.rd]=2,Pj[J.m.vc]=2,Pj[J.m.ya]=3,Pj[J.m.Ze]=4,Pj[J.m.yg]=5,Pj[J.m.Hc]=6,Pj[J.m.cb]=6,Pj[J.m.ob]=6,Pj[J.m.ed]=6,Pj[J.m.Sb]=6,Pj[J.m.zb]=6,Pj[J.m.pb]=7,Pj[J.m.Vb]=9,Pj[J.m.zg]=10,Pj[J.m.Ob]=11,Pj),Rj={},Sj=(Rj.unknown=13,Rj.standard=14,Rj.unique=15,Rj.per_session=16,Rj.transactions=17,Rj.items_sold=18,Rj);var hb=[];function Tj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Qj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Qj[f],h=b;h=h===void 0?!1:h;fb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(hb[g]=!0)}}};var Uj=function(){this.C=new Set;this.H=new Set},Wj=function(a){var b=Vj.R;a=a===void 0?[]:a;var c=[].concat(ya(b.C)).concat([].concat(ya(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Xj=function(){var a=[].concat(ya(Vj.R.C));a.sort(function(b,c){return b-c});return a},Yj=function(){var a=Vj.R,b=dj.Mq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Zj={},ak=Yi(14,"57l0"),bk=$i(15,Number("0")),ck=Yi(19,"dataLayer");Yi(20,"");Yi(16,"ChEI8O38wwYQzZn8jJmzlf7qARInADH3+ykDxk4K+ylclkEOTrcLImgRuDz1WzD4KGYBXgLlU22gHCBwGgL7nw\x3d\x3d");var dk={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},ek={__paused:1,__tg:1},fk;for(fk in dk)dk.hasOwnProperty(fk)&&(ek[fk]=1);var gk=Xi(11,wb("")),hk=!1;
function ik(){var a=!1;a=!0;return a}var jk=E(218)?Xi(45,ik()):ik(),kk,lk=!1;kk=lk;Zj.wg=Yi(3,"www.googletagmanager.com");var mk=""+Zj.wg+(jk?"/gtag/js":"/gtm.js"),nk=null,ok=null,pk={},qk={};Zj.Rm=Xi(2,wb(""));var rk="";
Zj.Ki=rk;var Vj=new function(){this.R=new Uj;this.C=this.N=!1;this.H=0;this.Da=this.Va=this.rb=this.P="";this.fa=this.ma=!1};function sk(){var a;a=a===void 0?[]:a;return Wj(a).join("~")}function tk(){var a=Vj.P.length;return Vj.P[a-1]==="/"?Vj.P.substring(0,a-1):Vj.P}function uk(){return Vj.C?E(84)?Vj.H===0:Vj.H!==1:!1}function vk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var wk=new sb,xk={},yk={},Bk={name:ck,set:function(a,b){md(Ib(a,b),xk);zk()},get:function(a){return Ak(a,2)},reset:function(){wk=new sb;xk={};zk()}};function Ak(a,b){return b!=2?wk.get(a):Ck(a)}function Ck(a,b){var c=a.split(".");b=b||[];for(var d=xk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function Dk(a,b){yk.hasOwnProperty(a)||(wk.set(a,b),md(Ib(a,b),xk),zk())}
function Ek(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Ak(c,1);if(Array.isArray(d)||ld(d))d=md(d,null);yk[c]=d}}function zk(a){tb(yk,function(b,c){wk.set(b,c);md(Ib(b),xk);md(Ib(b,c),xk);a&&delete yk[b]})}function Fk(a,b){var c,d=(b===void 0?2:b)!==1?Ck(a):wk.get(a);jd(d)==="array"||jd(d)==="object"?c=md(d,null):c=d;return c};
var Hk=function(a){for(var b=[],c=Object.keys(Gk),d=0;d<c.length;d++){var e=c[d],f=Gk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},Ik=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},Jk=function(a,b,c){if(a!==void 0)return Array.isArray(a)?a.map(function(){return{mode:"m",location:b,selector:c}}):{mode:"m",location:b,selector:c}},Kk=function(a,b,c,d,e){if(!c)return!1;for(var f=String(c.value),g,h=void 0,m=f.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(D){return D.trim()}).filter(function(D){return D&&
!Gb(D,"#")&&!Gb(D,".")}),n=0;n<m.length;n++){var p=m[n];if(Gb(p,"dataLayer."))g=Ak(p.substring(10)),h=Jk(g,"d",p);else{var q=p.split(".");g=x[q.shift()];for(var r=0;r<q.length;r++)g=g&&g[q[r]];h=Jk(g,"j",p)}if(g!==void 0)break}if(g===void 0&&wi)try{var t=vi(f);if(t&&t.length>0){g=[];for(var u=0;u<t.length&&u<(b==="email"||b==="phone_number"?5:1);u++)g.push(Pc(t[u])||yb(t[u].value));g=g.length===1?g[0]:g;h=Jk(g,"c",f)}}catch(D){L(149)}if(E(60)){for(var v,w,y=0;y<m.length;y++){var z=m[y];v=Ak(z);if(v!==
void 0){w=Jk(v,"d",z);break}}var C=g!==void 0;e[b]=Ik(v!==void 0,C);C||(g=v,h=w)}return g?(a[b]=g,d&&h&&(d[b]=h),!0):!1},Lk=function(a,b,c){b=b===void 0?{}:b;c=c===void 0?!1:c;if(a){var d={},e=!1,f={};e=Kk(d,"email",a.email,f,b)||e;e=Kk(d,"phone_number",a.phone,f,b)||e;d.address=[];for(var g=a.name_and_address||[],h=0;h<g.length;h++){var m={},n={};e=Kk(m,"first_name",g[h].first_name,n,b)||e;e=Kk(m,"last_name",g[h].last_name,n,b)||e;e=Kk(m,"street",g[h].street,n,b)||e;e=Kk(m,"city",g[h].city,n,b)||
e;e=Kk(m,"region",g[h].region,n,b)||e;e=Kk(m,"country",g[h].country,n,b)||e;e=Kk(m,"postal_code",g[h].postal_code,n,b)||e;d.address.push(m);c&&(m._tag_metadata=n)}c&&(d._tag_metadata=f);return e?d:void 0}},Mk=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&ld(b))return b;var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=x.enhanced_conversion_data;d&&fb("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return Lk(a[J.m.mk])}},Nk=function(a){return ld(a)?
!!a.enable_code:!1},Gk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Ok=function(){return uc.userAgent.toLowerCase().indexOf("firefox")!==-1},Pk=function(a){var b=a&&a[J.m.mk];return b&&!!b[J.m.xn]};var Qk=/:[0-9]+$/,Rk=/^\d+\.fls\.doubleclick\.net$/;function Sk(a,b,c,d){var e=Tk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Tk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=xa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Uk(a){try{return decodeURIComponent(a)}catch(b){}}function Vk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Wk(a.protocol)||Wk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Qk,"").toLowerCase());return Xk(a,b,c,d,e)}
function Xk(a,b,c,d,e){var f,g=Wk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Yk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Qk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||fb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Sk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Wk(a){return a?a.replace(":","").toLowerCase():""}function Yk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Zk={},$k=0;
function al(a){var b=Zk[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||fb("TAGGING",1),d="/"+d);var e=c.hostname.replace(Qk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};$k<5&&(Zk[a]=b,$k++)}return b}function bl(a,b,c){var d=al(a);return Nb(b,d,c)}
function cl(a){var b=al(x.location.href),c=Vk(b,"host",!1);if(c&&c.match(Rk)){var d=Vk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var dl={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},el=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function fl(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return al(""+c+b).href}}function gl(a,b){if(uk()||Vj.N)return fl(a,b)}
function hl(){return!!Zj.Ki&&Zj.Ki.split("@@").join("")!=="SGTM_TOKEN"}function il(a){for(var b=l([J.m.rd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function jl(a,b,c){c=c===void 0?"":c;if(!uk())return a;var d=b?dl[a]||"":"";d==="/gs"&&(c="");return""+tk()+d+c}function kl(a){if(!uk())return a;for(var b=l(el),c=b.next();!c.done;c=b.next())if(Gb(a,""+tk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function ll(a){var b=String(a[jf.Ua]||"").replace(/_/g,"");return Gb(b,"cvt")?"cvt":b}var ml=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var nl={qq:$i(27,Number("0.005000")),Xo:$i(42,Number("0.010000"))},ol=Math.random(),pl=ml||ol<Number(nl.qq),ql=ml||ol>=1-Number(nl.Xo);var rl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},sl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var tl,ul;a:{for(var vl=["CLOSURE_FLAGS"],wl=Da,xl=0;xl<vl.length;xl++)if(wl=wl[vl[xl]],wl==null){ul=null;break a}ul=wl}var yl=ul&&ul[610401301];tl=yl!=null?yl:!1;function zl(){var a=Da.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var Al,Bl=Da.navigator;Al=Bl?Bl.userAgentData||null:null;function Cl(a){if(!tl||!Al)return!1;for(var b=0;b<Al.brands.length;b++){var c=Al.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Dl(a){return zl().indexOf(a)!=-1};function El(){return tl?!!Al&&Al.brands.length>0:!1}function Fl(){return El()?!1:Dl("Opera")}function Gl(){return Dl("Firefox")||Dl("FxiOS")}function Hl(){return El()?Cl("Chromium"):(Dl("Chrome")||Dl("CriOS"))&&!(El()?0:Dl("Edge"))||Dl("Silk")};var Il=function(a){Il[" "](a);return a};Il[" "]=function(){};var Jl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Kl(){return tl?!!Al&&!!Al.platform:!1}function Ll(){return Dl("iPhone")&&!Dl("iPod")&&!Dl("iPad")}function Ml(){Ll()||Dl("iPad")||Dl("iPod")};Fl();El()||Dl("Trident")||Dl("MSIE");Dl("Edge");!Dl("Gecko")||zl().toLowerCase().indexOf("webkit")!=-1&&!Dl("Edge")||Dl("Trident")||Dl("MSIE")||Dl("Edge");zl().toLowerCase().indexOf("webkit")!=-1&&!Dl("Edge")&&Dl("Mobile");Kl()||Dl("Macintosh");Kl()||Dl("Windows");(Kl()?Al.platform==="Linux":Dl("Linux"))||Kl()||Dl("CrOS");Kl()||Dl("Android");Ll();Dl("iPad");Dl("iPod");Ml();zl().toLowerCase().indexOf("kaios");var Nl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Il(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Ol=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Pl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Ql=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Nl(b.top)?1:2},Rl=function(a){a=a===void 0?document:a;return a.createElement("img")},Sl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Nl(a)&&(b=a);return b};function Tl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Ul(){return Tl("join-ad-interest-group")&&lb(uc.joinAdInterestGroup)}
function Vl(a,b,c){var d=Qa[3]===void 0?1:Qa[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Qa[2]===void 0?50:Qa[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&Ab()-q<(Qa[1]===void 0?6E4:Qa[1])?(fb("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Wl(f[0]);else{if(n)return fb("TAGGING",10),!1}else f.length>=d?Wl(f[0]):n&&Wl(m[0]);Ic(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:Ab()});return!0}function Wl(a){try{a.parentNode.removeChild(a)}catch(b){}};function Xl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Yl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Gl();Ll()||Dl("iPod");Dl("iPad");!Dl("Android")||Hl()||Gl()||Fl()||Dl("Silk");Hl();!Dl("Safari")||Hl()||(El()?0:Dl("Coast"))||Fl()||(El()?0:Dl("Edge"))||(El()?Cl("Microsoft Edge"):Dl("Edg/"))||(El()?Cl("Opera"):Dl("OPR"))||Gl()||Dl("Silk")||Dl("Android")||Ml();var Zl={},$l=null,am=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!$l){$l={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Zl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];$l[q]===void 0&&($l[q]=p)}}}for(var r=Zl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
z=b[v+1],C=b[v+2],D=r[y>>2],G=r[(y&3)<<4|z>>4],I=r[(z&15)<<2|C>>6],M=r[C&63];t[w++]=""+D+G+I+M}var T=0,da=u;switch(b.length-v){case 2:T=b[v+1],da=r[(T&15)<<2]||u;case 1:var N=b[v];t[w]=""+r[N>>2]+r[(N&3)<<4|T>>4]+da+u}return t.join("")};var bm=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},cm=/#|$/,dm=function(a,b){var c=a.search(cm),d=bm(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Jl(a.slice(d,e!==-1?e:0))},em=/[?&]($|#)/,fm=function(a,b,c){for(var d,e=a.search(cm),f=0,g,h=[];(g=bm(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(em,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function gm(a,b,c,d,e,f,g){var h=dm(c,"fmt");if(d){var m=dm(c,"random"),n=dm(c,"label")||"";if(!m)return!1;var p=am(Jl(n)+":"+Jl(m));if(!Xl(a,p,d))return!1}h&&Number(h)!==4&&(c=fm(c,"rfmt",h));var q=fm(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||hm(g);Gc(q,function(){g==null||im(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||im(g);e==null||e()},f,r||void 0);return!0};var jm={},km=(jm[1]={},jm[2]={},jm[3]={},jm[4]={},jm);function lm(a,b,c){var d=mm(b,c);if(d){var e=km[b][d];e||(e=km[b][d]=[]);e.push(ma(Object,"assign").call(Object,{},a))}}function nm(a,b){var c=mm(a,b);if(c){var d=km[a][c];d&&(km[a][c]=d.filter(function(e){return!e.zm}))}}function om(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function mm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function pm(a){var b=Ca.apply(1,arguments);ql&&(lm(a,2,b[0]),lm(a,3,b[0]));Sc.apply(null,ya(b))}function qm(a){var b=Ca.apply(1,arguments);ql&&lm(a,2,b[0]);return Tc.apply(null,ya(b))}function rm(a){var b=Ca.apply(1,arguments);ql&&lm(a,3,b[0]);Jc.apply(null,ya(b))}
function sm(a){var b=Ca.apply(1,arguments),c=b[0];ql&&(lm(a,2,c),lm(a,3,c));return Vc.apply(null,ya(b))}function tm(a){var b=Ca.apply(1,arguments);ql&&lm(a,1,b[0]);Gc.apply(null,ya(b))}function um(a){var b=Ca.apply(1,arguments);b[0]&&ql&&lm(a,4,b[0]);Ic.apply(null,ya(b))}function vm(a){var b=Ca.apply(1,arguments);ql&&lm(a,1,b[2]);return gm.apply(null,ya(b))}function wm(a){var b=Ca.apply(1,arguments);ql&&lm(a,4,b[0]);Vl.apply(null,ya(b))};var xm=/gtag[.\/]js/,ym=/gtm[.\/]js/,zm=!1;function Am(a){if(zm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(xm.test(c))return"3";if(ym.test(c))return"2"}return"0"};function Bm(a,b,c){var d=Cm(),e=Dm().container[a];e&&e.state!==3||(Dm().container[a]={state:1,context:b,parent:d},Em({ctid:a,isDestination:!1},c))}function Em(a,b){var c=Dm();c.pending||(c.pending=[]);pb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Fm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Gm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Fm()};function Dm(){var a=yc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Gm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Fm());return c};var Hm={},lg={ctid:Yi(5,"AW-10793199209"),canonicalContainerId:Yi(6,""),rm:Yi(10,"AW-10793199209"),sm:Yi(9,"AW-10793199209")};Hm.qe=Xi(7,wb("true"));function Im(){return Hm.qe&&Jm().some(function(a){return a===lg.ctid})}function Km(){return lg.canonicalContainerId||"_"+lg.ctid}function Lm(){return lg.rm?lg.rm.split("|"):[lg.ctid]}
function Jm(){return lg.sm?lg.sm.split("|").filter(function(a){return a.indexOf("GTM-")!==0}):[]}function Mm(){var a=Nm(Cm()),b=a&&a.parent;if(b)return Nm(b)}function Om(){var a=Nm(Cm());if(a){for(;a.parent;){var b=Nm(a.parent);if(!b)break;a=b}return a}}function Nm(a){var b=Dm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Pm(){var a=Dm();if(a.pending){for(var b,c=[],d=!1,e=Lm(),f=Jm(),g={},h=0;h<a.pending.length;g={kg:void 0},h++)g.kg=a.pending[h],pb(g.kg.target.isDestination?f:e,function(m){return function(n){return n===m.kg.target.ctid}}(g))?d||(b=g.kg.onLoad,d=!0):c.push(g.kg);a.pending=c;if(b)try{b(Km())}catch(m){}}}
function Qm(){for(var a=lg.ctid,b=Lm(),c=Jm(),d=function(n,p){var q={canonicalContainerId:lg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};wc&&(q.scriptElement=wc);xc&&(q.scriptSource=xc);if(Mm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Vj.C,y=al(v),z=w?y.pathname:""+y.hostname+y.pathname,C=A.scripts,D="",G=0;G<C.length;++G){var I=C[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(z)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}D=String(G)}}if(D){t=D;break b}}t=void 0}var M=t;if(M){zm=!0;r=M;break a}}var T=[].slice.call(A.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=Am(q)}var da=p?e.destination:e.container,N=da[n];N?(p&&N.state===0&&L(93),ma(Object,"assign").call(Object,N,q)):da[n]=q},e=Dm(),f=l(b),
g=f.next();!g.done;g=f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Km()]={};Pm()}function Rm(){var a=Km();return!!Dm().canonical[a]}function Sm(a){return!!Dm().container[a]}function Tm(a){var b=Dm().destination[a];return!!b&&!!b.state}function Cm(){return{ctid:lg.ctid,isDestination:Hm.qe}}function Um(){var a=Dm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Vm(){var a={};tb(Dm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Wm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Xm(){for(var a=Dm(),b=l(Lm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Ym={Ia:{me:0,pe:1,Gi:2}};Ym.Ia[Ym.Ia.me]="FULL_TRANSMISSION";Ym.Ia[Ym.Ia.pe]="LIMITED_TRANSMISSION";Ym.Ia[Ym.Ia.Gi]="NO_TRANSMISSION";var Zm={X:{Hb:0,Fa:1,Fc:2,Oc:3}};Zm.X[Zm.X.Hb]="NO_QUEUE";Zm.X[Zm.X.Fa]="ADS";Zm.X[Zm.X.Fc]="ANALYTICS";Zm.X[Zm.X.Oc]="MONITORING";function $m(){var a=yc("google_tag_data",{});return a.ics=a.ics||new an}var an=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
an.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;fb("TAGGING",19);b==null?fb("TAGGING",18):bn(this,a,b==="granted",c,d,e,f,g)};an.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)bn(this,a[d],void 0,void 0,"","",b,c)};
var bn=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&mb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(fb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=an.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())cn(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())cn(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&mb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Sc:b})};var cn=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.tm=!0)}};an.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.tm){d.tm=!1;try{d.Sc({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var dn=!1,en=!1,fn={},gn={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(fn.ad_storage=1,fn.analytics_storage=1,fn.ad_user_data=1,fn.ad_personalization=1,fn),usedContainerScopedDefaults:!1};function hn(a){var b=$m();b.accessedAny=!0;return(mb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,gn)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function jn(a){var b=$m();b.accessedAny=!0;return b.getConsentState(a,gn)}function kn(a){var b=$m();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function ln(){if(!Ra(7))return!1;var a=$m();a.accessedAny=!0;if(a.active)return!0;if(!gn.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(gn.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(gn.containerScopedDefaults[c.value]!==1)return!0;return!1}function mn(a,b){$m().addListener(a,b)}
function nn(a,b){$m().notifyListeners(a,b)}function on(a,b){function c(){for(var e=0;e<b.length;e++)if(!kn(b[e]))return!0;return!1}if(c()){var d=!1;mn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function pn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];hn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=mb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),mn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var qn={},rn=(qn[Zm.X.Hb]=Ym.Ia.me,qn[Zm.X.Fa]=Ym.Ia.me,qn[Zm.X.Fc]=Ym.Ia.me,qn[Zm.X.Oc]=Ym.Ia.me,qn),sn=function(a,b){this.C=a;this.consentTypes=b};sn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return hn(a)});case 1:return this.consentTypes.some(function(a){return hn(a)});default:mc(this.C,"consentsRequired had an unknown type")}};
var tn={},un=(tn[Zm.X.Hb]=new sn(0,[]),tn[Zm.X.Fa]=new sn(0,["ad_storage"]),tn[Zm.X.Fc]=new sn(0,["analytics_storage"]),tn[Zm.X.Oc]=new sn(1,["ad_storage","analytics_storage"]),tn);var wn=function(a){var b=this;this.type=a;this.C=[];mn(un[a].consentTypes,function(){vn(b)||b.flush()})};wn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var vn=function(a){return rn[a.type]===Ym.Ia.Gi&&!un[a.type].isConsentGranted()},xn=function(a,b){vn(a)?a.C.push(b):b()},yn=new Map;function zn(a){yn.has(a)||yn.set(a,new wn(a));return yn.get(a)};var An={Z:{Mm:"aw_user_data_cache",Lh:"cookie_deprecation_label",xg:"diagnostics_page_id",Vn:"fl_user_data_cache",Xn:"ga4_user_data_cache",Df:"ip_geo_data_cache",Bi:"ip_geo_fetch_in_progress",ol:"nb_data",ql:"page_experiment_ids",Nf:"pt_data",rl:"pt_listener_set",zl:"service_worker_endpoint",Bl:"shared_user_id",Cl:"shared_user_id_requested",jh:"shared_user_id_source"}};var Bn=function(a){return bf(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(An.Z);
function Cn(a,b){b=b===void 0?!1:b;if(Bn(a)){var c,d,e=(d=(c=yc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function Dn(a,b){var c=Cn(a,!0);c&&c.set(b)}function En(a){var b;return(b=Cn(a))==null?void 0:b.get()}function Fn(a){var b={},c=Cn(a);if(!c){c=Cn(a,!0);if(!c)return;c.set(b)}return c.get()}function Gn(a,b){if(typeof b==="function"){var c;return(c=Cn(a,!0))==null?void 0:c.subscribe(b)}}function Hn(a,b){var c=Cn(a);return c?c.unsubscribe(b):!1};var In="https://"+Yi(21,"www.googletagmanager.com"),Jn="/td?id="+lg.ctid,Kn={},Ln=(Kn.tdp=1,Kn.exp=1,Kn.pid=1,Kn.dl=1,Kn.seq=1,Kn.t=1,Kn.v=1,Kn),Mn=["mcc"],Nn={},On={},Pn=!1;function Qn(a,b,c){On[a]=b;(c===void 0||c)&&Rn(a)}function Rn(a,b){Nn[a]!==void 0&&(b===void 0||!b)||Gb(lg.ctid,"GTM-")&&a==="mcc"||(Nn[a]=!0)}
function Sn(a){a=a===void 0?!1:a;var b=Object.keys(Nn).filter(function(c){return Nn[c]===!0&&On[c]!==void 0&&(a||!Mn.includes(c))}).map(function(c){var d=On[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+jl(In)+Jn+(""+b+"&z=0")}function Tn(){Object.keys(Nn).forEach(function(a){Ln[a]||(Nn[a]=!1)})}
function Un(a){a=a===void 0?!1:a;if(Vj.fa&&ql&&lg.ctid){var b=zn(Zm.X.Oc);if(vn(b))Pn||(Pn=!0,xn(b,Un));else{var c=Sn(a),d={destinationId:lg.ctid,endpoint:61};a?sm(d,c,void 0,{Ch:!0},void 0,function(){rm(d,c+"&img=1")}):rm(d,c);Tn();Pn=!1}}}function Vn(){Object.keys(Nn).filter(function(a){return Nn[a]&&!Ln[a]}).length>0&&Un(!0)}var Wn;function Xn(){if(En(An.Z.xg)===void 0){var a=function(){Dn(An.Z.xg,qb());Wn=0};a();x.setInterval(a,864E5)}else Gn(An.Z.xg,function(){Wn=0});Wn=0}
function Yn(){Xn();Qn("v","3");Qn("t","t");Qn("pid",function(){return String(En(An.Z.xg))});Qn("seq",function(){return String(++Wn)});Qn("exp",sk());Lc(x,"pagehide",Vn)};var Zn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],$n=[J.m.rd,J.m.vc,J.m.ce,J.m.Qb,J.m.Wb,J.m.Ma,J.m.Ta,J.m.cb,J.m.ob,J.m.Sb],ao=!1,bo=!1,co={},eo={};function fo(){!bo&&ao&&(Zn.some(function(a){return gn.containerScopedDefaults[a]!==1})||go("mbc"));bo=!0}function go(a){ql&&(Qn(a,"1"),Un())}function ho(a,b){if(!co[b]&&(co[b]=!0,eo[b]))for(var c=l($n),d=c.next();!d.done;d=c.next())if(O(a,d.value)){go("erc");break}};function io(a){fb("HEALTH",a)};var jo={pp:Yi(22,"eyIwIjoiUEsiLCIxIjoiUEstUEIiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20ucGsiLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ")},ko={},lo=!1;function mo(){function a(){c!==void 0&&Hn(An.Z.Df,c);try{var e=En(An.Z.Df);ko=JSON.parse(e)}catch(f){L(123),io(2),ko={}}lo=!0;b()}var b=no,c=void 0,d=En(An.Z.Df);d?a(d):(c=Gn(An.Z.Df,a),oo())}
function oo(){function a(c){Dn(An.Z.Df,c||"{}");Dn(An.Z.Bi,!1)}if(!En(An.Z.Bi)){Dn(An.Z.Bi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function po(){var a=jo.pp;try{return JSON.parse(db(a))}catch(b){return L(123),io(2),{}}}function qo(){return ko["0"]||""}function ro(){return ko["1"]||""}function so(){var a=!1;return a}function to(){return ko["6"]!==!1}function uo(){var a="";return a}
function vo(){var a=!1;a=!!ko["5"];return a}function wo(){var a="";return a};var xo={},yo=Object.freeze((xo[J.m.Ga]=1,xo[J.m.zg]=1,xo[J.m.Ag]=1,xo[J.m.Ob]=1,xo[J.m.sa]=1,xo[J.m.ob]=1,xo[J.m.pb]=1,xo[J.m.zb]=1,xo[J.m.ed]=1,xo[J.m.Sb]=1,xo[J.m.cb]=1,xo[J.m.Hc]=1,xo[J.m.af]=1,xo[J.m.oa]=1,xo[J.m.jk]=1,xo[J.m.df]=1,xo[J.m.Jg]=1,xo[J.m.Kg]=1,xo[J.m.ce]=1,xo[J.m.zk]=1,xo[J.m.sc]=1,xo[J.m.fe]=1,xo[J.m.Bk]=1,xo[J.m.Ng]=1,xo[J.m.fi]=1,xo[J.m.Kc]=1,xo[J.m.Lc]=1,xo[J.m.Ta]=1,xo[J.m.gi]=1,xo[J.m.Vb]=1,xo[J.m.qb]=1,xo[J.m.pd]=1,xo[J.m.rd]=1,xo[J.m.pf]=1,xo[J.m.ii]=1,xo[J.m.je]=1,xo[J.m.vc]=
1,xo[J.m.ud]=1,xo[J.m.Ug]=1,xo[J.m.Xb]=1,xo[J.m.wd]=1,xo[J.m.Ji]=1,xo));Object.freeze([J.m.Ca,J.m.Ya,J.m.Fb,J.m.Ab,J.m.hi,J.m.Ma,J.m.bi,J.m.yn]);
var zo={},Ao=Object.freeze((zo[J.m.Zm]=1,zo[J.m.bn]=1,zo[J.m.dn]=1,zo[J.m.fn]=1,zo[J.m.gn]=1,zo[J.m.kn]=1,zo[J.m.ln]=1,zo[J.m.mn]=1,zo[J.m.on]=1,zo[J.m.Wd]=1,zo)),Bo={},Co=Object.freeze((Bo[J.m.Yj]=1,Bo[J.m.Zj]=1,Bo[J.m.Sd]=1,Bo[J.m.Td]=1,Bo[J.m.bk]=1,Bo[J.m.Xc]=1,Bo[J.m.Ud]=1,Bo[J.m.kc]=1,Bo[J.m.Gc]=1,Bo[J.m.mc]=1,Bo[J.m.lb]=1,Bo[J.m.Vd]=1,Bo[J.m.yb]=1,Bo[J.m.dk]=1,Bo)),Do=Object.freeze([J.m.Ga,J.m.Qe,J.m.Ob,J.m.Hc,J.m.ce,J.m.kf,J.m.qb,J.m.ud]),Eo=Object.freeze([].concat(ya(Do))),Fo=Object.freeze([J.m.pb,
J.m.Kg,J.m.pf,J.m.ii,J.m.Hg]),Go=Object.freeze([].concat(ya(Fo))),Ho={},Io=(Ho[J.m.U]="1",Ho[J.m.ja]="2",Ho[J.m.V]="3",Ho[J.m.Ka]="4",Ho),Jo={},Ko=Object.freeze((Jo.search="s",Jo.youtube="y",Jo.playstore="p",Jo.shopping="h",Jo.ads="a",Jo.maps="m",Jo));function Lo(a){return typeof a!=="object"||a===null?{}:a}function Mo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function No(a){if(a!==void 0&&a!==null)return Mo(a)}function Oo(a){return typeof a==="number"?a:No(a)};function Po(a){return a&&a.indexOf("pending:")===0?Qo(a.substr(8)):!1}function Qo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Ab();return b<c+3E5&&b>c-9E5};var Ro=!1,So=!1,To=!1,Uo=0,Vo=!1,Wo=[];function Xo(a){if(Uo===0)Vo&&Wo&&(Wo.length>=100&&Wo.shift(),Wo.push(a));else if(Yo()){var b=Yi(41,'google.tagmanager.ta.prodqueue'),c=yc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Zo(){$o();Mc(A,"TAProdDebugSignal",Zo)}function $o(){if(!So){So=!0;ap();var a=Wo;Wo=void 0;a==null||a.forEach(function(b){Xo(b)})}}
function ap(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Qo(a)?Uo=1:!Po(a)||Ro||To?Uo=2:(To=!0,Lc(A,"TAProdDebugSignal",Zo,!1),x.setTimeout(function(){$o();Ro=!0},200))}function Yo(){if(!Vo)return!1;switch(Uo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var bp=!1;function cp(a,b){var c=Lm(),d=Jm();if(Yo()){var e=dp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Xo(e)}}
function ep(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Oa;e=a.isBatched;var f;if(f=Yo()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=dp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Xo(h)}}function fp(a){Yo()&&ep(a())}
function dp(a,b){b=b===void 0?{}:b;b.groupId=gp;var c,d=b,e={publicId:hp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=bp?"OGT":"GTM";c.key.targetRef=ip;return c}var hp="",ip={ctid:"",isDestination:!1},gp;
function jp(a){var b=lg.ctid,c=Im();Uo=0;Vo=!0;ap();gp=a;hp=b;bp=jk;ip={ctid:b,isDestination:c}};var kp=[J.m.U,J.m.ja,J.m.V,J.m.Ka],lp,mp;function np(a){var b=a[J.m.hc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)tb(a,function(d){return function(e,f){if(e!==J.m.hc){var g=Mo(f),h=b[d.cg],m=qo(),n=ro();en=!0;dn&&fb("TAGGING",20);$m().declare(e,g,h,m,n)}}}(c))}
function op(a){fo();!mp&&lp&&go("crc");mp=!0;var b=a[J.m.rg];b&&L(41);var c=a[J.m.hc];c?L(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)tb(a,function(e){return function(f,g){if(f!==J.m.hc&&f!==J.m.rg){var h=No(g),m=c[e.dg],n=Number(b),p=qo(),q=ro();n=n===void 0?0:n;dn=!0;en&&fb("TAGGING",20);$m().default(f,h,m,p,q,n,gn)}}}(d))}
function pp(a){gn.usedContainerScopedDefaults=!0;var b=a[J.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ro())&&!c.includes(qo()))return}tb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}gn.usedContainerScopedDefaults=!0;gn.containerScopedDefaults[d]=e==="granted"?3:2})}
function qp(a,b){fo();lp=!0;tb(a,function(c,d){var e=Mo(d);dn=!0;en&&fb("TAGGING",20);$m().update(c,e,gn)});nn(b.eventId,b.priorityId)}function rp(a){a.hasOwnProperty("all")&&(gn.selectedAllCorePlatformServices=!0,tb(Ko,function(b){gn.corePlatformServices[b]=a.all==="granted";gn.usedCorePlatformServices=!0}));tb(a,function(b,c){b!=="all"&&(gn.corePlatformServices[b]=c==="granted",gn.usedCorePlatformServices=!0)})}function P(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return hn(b)})}
function sp(a,b){mn(a,b)}function tp(a,b){pn(a,b)}function up(a,b){on(a,b)}function vp(){var a=[J.m.U,J.m.Ka,J.m.V];$m().waitForUpdate(a,500,gn)}function wp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;$m().clearTimeout(d,void 0,gn)}nn()}function xp(){if(!kk)for(var a=to()?vk(Vj.Va):vk(Vj.rb),b=0;b<kp.length;b++){var c=kp[b],d=c,e=a[c]?"granted":"denied";$m().implicit(d,e)}};var yp=!1,zp=[];function Ap(){if(!yp){yp=!0;for(var a=zp.length-1;a>=0;a--)zp[a]();zp=[]}};var Bp=x.google_tag_manager=x.google_tag_manager||{};function Cp(a,b){return Bp[a]=Bp[a]||b()}function Dp(){var a=lg.ctid,b=Ep;Bp[a]=Bp[a]||b}function Fp(){var a=Bp.sequence||1;Bp.sequence=a+1;return a}x.google_tag_data=x.google_tag_data||{};function Gp(){if(Bp.pscdl!==void 0)En(An.Z.Lh)===void 0&&Dn(An.Z.Lh,Bp.pscdl);else{var a=function(c){Bp.pscdl=c;Dn(An.Z.Lh,c)},b=function(){a("error")};try{uc.cookieDeprecationLabel?(a("pending"),uc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Hp=0;function Ip(a){ql&&a===void 0&&Hp===0&&(Qn("mcc","1"),Hp=1)};var Jp={Bf:{Sm:"cd",Tm:"ce",Um:"cf",Vm:"cpf",Wm:"cu"}};var Kp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Lp=/\s/;
function Mp(a,b){if(mb(a)){a=yb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Kp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Lp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Np(a,b){for(var c={},d=0;d<a.length;++d){var e=Mp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Op[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Pp={},Op=(Pp[0]=0,Pp[1]=1,Pp[2]=2,Pp[3]=0,Pp[4]=1,Pp[5]=0,Pp[6]=0,Pp[7]=0,Pp);var Qp=Number('')||500,Rp={},Sp={},Tp={initialized:11,complete:12,interactive:13},Up={},Vp=Object.freeze((Up[J.m.qb]=!0,Up)),Wp=void 0;function Xp(a,b){if(b.length&&ql){var c;(c=Rp)[a]!=null||(c[a]=[]);Sp[a]!=null||(Sp[a]=[]);var d=b.filter(function(e){return!Sp[a].includes(e)});Rp[a].push.apply(Rp[a],ya(d));Sp[a].push.apply(Sp[a],ya(d));!Wp&&d.length>0&&(Rn("tdc",!0),Wp=x.setTimeout(function(){Un();Rp={};Wp=void 0},Qp))}}
function Yp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Zp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;jd(t)==="object"?u=t[r]:jd(t)==="array"&&(u=t[r]);return u===void 0?Vp[r]:u},f=Yp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=jd(m)==="object"||jd(m)==="array",q=jd(n)==="object"||jd(n)==="array";if(p&&q)Zp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function $p(){Qn("tdc",function(){Wp&&(x.clearTimeout(Wp),Wp=void 0);var a=[],b;for(b in Rp)Rp.hasOwnProperty(b)&&a.push(b+"*"+Rp[b].join("."));return a.length?a.join("!"):void 0},!1)};var aq=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},bq=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},O=function(a,b,c,d){for(var e=l(bq(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},cq=function(a){for(var b={},c=bq(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
aq.prototype.getMergedValues=function(a,b,c){function d(n){ld(n)&&tb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=bq(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var dq=function(a){for(var b=[J.m.Ve,J.m.Re,J.m.Se,J.m.Te,J.m.Ue,J.m.We,J.m.Xe],c=bq(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},eq=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.fa={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},fq=function(a,
b){a.H=b;return a},gq=function(a,b){a.R=b;return a},hq=function(a,b){a.C=b;return a},iq=function(a,b){a.N=b;return a},jq=function(a,b){a.fa=b;return a},kq=function(a,b){a.P=b;return a},lq=function(a,b){a.eventMetadata=b||{};return a},mq=function(a,b){a.onSuccess=b;return a},nq=function(a,b){a.onFailure=b;return a},oq=function(a,b){a.isGtmEvent=b;return a},pq=function(a){return new aq(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Q={A:{Hj:"accept_by_default",qg:"add_tag_timing",Hh:"allow_ad_personalization",Jj:"batch_on_navigation",Lj:"client_id_source",He:"consent_event_id",Ie:"consent_priority_id",Pq:"consent_state",da:"consent_updated",Wc:"conversion_linker_enabled",xa:"cookie_options",tg:"create_dc_join",ug:"create_fpm_geo_join",vg:"create_fpm_signals_join",Rd:"create_google_join",Ke:"em_event",Sq:"endpoint_for_debug",Xj:"enhanced_client_id_source",Oh:"enhanced_match_result",ke:"euid_mode_enabled",fb:"event_start_timestamp_ms",
Vk:"event_usage",Wg:"extra_tag_experiment_ids",Zq:"add_parameter",wi:"attribution_reporting_experiment",xi:"counting_method",Xg:"send_as_iframe",ar:"parameter_order",Yg:"parsed_target",Wn:"ga4_collection_subdomain",Yk:"gbraid_cookie_marked",ia:"hit_type",xd:"hit_type_override",bo:"is_config_command",Ef:"is_consent_update",Ff:"is_conversion",fl:"is_ecommerce",yd:"is_external_event",Ci:"is_fallback_aw_conversion_ping_allowed",Gf:"is_first_visit",il:"is_first_visit_conversion",Zg:"is_fl_fallback_conversion_flow_allowed",
Hf:"is_fpm_encryption",ah:"is_fpm_split",ne:"is_gcp_conversion",jl:"is_google_signals_allowed",zd:"is_merchant_center",bh:"is_new_to_site",eh:"is_server_side_destination",oe:"is_session_start",ml:"is_session_start_conversion",hr:"is_sgtm_ga_ads_conversion_study_control_group",ir:"is_sgtm_prehit",nl:"is_sgtm_service_worker",Di:"is_split_conversion",co:"is_syn",If:"join_id",Ei:"join_elapsed",Jf:"join_timer_sec",se:"tunnel_updated",mr:"prehit_for_retry",qr:"promises",rr:"record_aw_latency",yc:"redact_ads_data",
te:"redact_click_ids",oo:"remarketing_only",xl:"send_ccm_parallel_ping",ih:"send_fledge_experiment",vr:"send_ccm_parallel_test_ping",Of:"send_to_destinations",Ii:"send_to_targets",yl:"send_user_data_hit",hb:"source_canonical_id",Ba:"speculative",Dl:"speculative_in_message",El:"suppress_script_load",Fl:"syn_or_mod",Jl:"transient_ecsid",Pf:"transmission_type",ib:"user_data",yr:"user_data_from_automatic",zr:"user_data_from_automatic_getter",ve:"user_data_from_code",mh:"user_data_from_manual",Ll:"user_data_mode",
Qf:"user_id_updated"}};var qq={Lm:Number("5"),Qr:Number("")},rq=[],sq=!1;function tq(a){rq.push(a)}var uq="?id="+lg.ctid,vq=void 0,wq={},xq=void 0,yq=new function(){var a=5;qq.Lm>0&&(a=qq.Lm);this.H=a;this.C=0;this.N=[]},zq=1E3;
function Aq(a,b){var c=vq;if(c===void 0)if(b)c=Fp();else return"";for(var d=[jl("https://www.googletagmanager.com"),"/a",uq],e=l(rq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Qd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Bq(){if(Vj.fa&&(xq&&(x.clearTimeout(xq),xq=void 0),vq!==void 0&&Cq)){var a=zn(Zm.X.Oc);if(vn(a))sq||(sq=!0,xn(a,Bq));else{var b;if(!(b=wq[vq])){var c=yq;b=c.C<c.H?!1:Ab()-c.N[c.C%c.H]<1E3}if(b||zq--<=0)L(1),wq[vq]=!0;else{var d=yq,e=d.C++%d.H;d.N[e]=Ab();var f=Aq(!0);rm({destinationId:lg.ctid,endpoint:56,eventId:vq},f);sq=Cq=!1}}}}function Dq(){if(pl&&Vj.fa){var a=Aq(!0,!0);rm({destinationId:lg.ctid,endpoint:56,eventId:vq},a)}}var Cq=!1;
function Eq(a){wq[a]||(a!==vq&&(Bq(),vq=a),Cq=!0,xq||(xq=x.setTimeout(Bq,500)),Aq().length>=2022&&Bq())}var Fq=qb();function Gq(){Fq=qb()}function Hq(){return[["v","3"],["t","t"],["pid",String(Fq)]]};var Iq={};function Jq(a,b,c){pl&&a!==void 0&&(Iq[a]=Iq[a]||[],Iq[a].push(c+b),Eq(a))}function Kq(a){var b=a.eventId,c=a.Qd,d=[],e=Iq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Iq[b];return d};function Lq(a,b,c,d){var e=Mp(a,!0);e&&Mq.register(e,b,c,d)}function Nq(a,b,c,d){var e=Mp(c,d.isGtmEvent);e&&(hk&&(d.deferrable=!0),Mq.push("event",[b,a],e,d))}function Oq(a,b,c,d){var e=Mp(c,d.isGtmEvent);e&&Mq.push("get",[a,b],e,d)}function Pq(a){var b=Mp(a,!0),c;b?c=Qq(Mq,b).C:c={};return c}function Rq(a,b){var c=Mp(a,!0);c&&Tq(Mq,c,b)}
var Uq=function(){this.R={};this.C={};this.H={};this.fa=null;this.P={};this.N=!1;this.status=1},Vq=function(a,b,c,d){this.H=Ab();this.C=b;this.args=c;this.messageContext=d;this.type=a},Wq=function(){this.destinations={};this.C={};this.commands=[]},Qq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Uq},Xq=function(a,b,c,d){if(d.C){var e=Qq(a,d.C),f=e.fa;if(f){var g=md(c,null),h=md(e.R[d.C.id],null),m=md(e.P,null),n=md(e.C,null),p=md(a.C,null),q={};if(pl)try{q=
md(xk,null)}catch(w){L(72)}var r=d.C.prefix,t=function(w){Jq(d.messageContext.eventId,r,w)},u=pq(oq(nq(mq(lq(jq(iq(kq(hq(gq(fq(new eq(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Jq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(ql&&w==="config"){var z,C=(z=Mp(y))==null?void 0:z.ids;if(!(C&&C.length>1)){var D,G=yc("google_tag_data",{});G.td||(G.td={});D=G.td;var I=md(u.P);md(u.C,I);var M=[],T;for(T in D)D.hasOwnProperty(T)&&Zp(D[T],I).length&&M.push(T);M.length&&(Xp(y,M),fb("TAGGING",Tp[A.readyState]||14));D[y]=I}}f(d.C.id,b,d.H,u)}catch(da){Jq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():xn(e.ma,v)}}};
Wq.prototype.register=function(a,b,c,d){var e=Qq(this,a);e.status!==3&&(e.fa=b,e.status=3,e.ma=zn(c),Tq(this,a,d||{}),this.flush())};
Wq.prototype.push=function(a,b,c,d){c!==void 0&&(Qq(this,c).status===1&&(Qq(this,c).status=2,this.push("require",[{}],c,{})),Qq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[Q.A.Of]||(d.eventMetadata[Q.A.Of]=[c.destinationId]),d.eventMetadata[Q.A.Ii]||(d.eventMetadata[Q.A.Ii]=[c.id]));this.commands.push(new Vq(a,c,b,d));d.deferrable||this.flush()};
Wq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,rh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Qq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Qq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];tb(h,function(t,u){md(Ib(t,u),b.C)});Tj(h,!0);break;case "config":var m=Qq(this,g);
e.Qc={};tb(f.args[0],function(t){return function(u,v){md(Ib(u,v),t.Qc)}}(e));var n=!!e.Qc[J.m.ud];delete e.Qc[J.m.ud];var p=g.destinationId===g.id;Tj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Xq(this,J.m.qa,e.Qc,f);m.N=!0;p?md(e.Qc,m.P):(md(e.Qc,m.R[g.id]),L(70));d=!0;break;case "event":e.rh={};tb(f.args[0],function(t){return function(u,v){md(Ib(u,v),t.rh)}}(e));Tj(e.rh);Xq(this,f.args[1],e.rh,f);break;case "get":var q={},r=(q[J.m.rc]=f.args[0],q[J.m.Ic]=f.args[1],q);Xq(this,J.m.Eb,r,f)}this.commands.shift();
Yq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Yq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Qq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Tq=function(a,b,c){var d=md(c,null);md(Qq(a,b).C,d);Qq(a,b).C=d},Mq=new Wq;function Zq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function $q(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function ar(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Rl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=rc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}$q(e,"load",f);$q(e,"error",f)};Zq(e,"load",f);Zq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function br(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Ol(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});cr(c,b)}
function cr(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else ar(c,a,b===void 0?!1:b,d===void 0?!1:d)};var dr=function(){this.fa=this.fa;this.P=this.P};dr.prototype.fa=!1;dr.prototype.dispose=function(){this.fa||(this.fa=!0,this.N())};dr.prototype[ha.Symbol.dispose]=function(){this.dispose()};dr.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};dr.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function er(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var fr=function(a,b){b=b===void 0?{}:b;dr.call(this);this.C=null;this.ma={};this.rb=0;this.R=null;this.H=a;var c;this.Va=(c=b.timeoutMs)!=null?c:500;var d;this.Da=(d=b.Fr)!=null?d:!1};va(fr,dr);fr.prototype.N=function(){this.ma={};this.R&&($q(this.H,"message",this.R),delete this.R);delete this.ma;delete this.H;delete this.C;dr.prototype.N.call(this)};var hr=function(a){return typeof a.H.__tcfapi==="function"||gr(a)!=null};
fr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Da},d=sl(function(){return a(c)}),e=0;this.Va!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Va));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=er(c),c.internalBlockOnErrors=b.Da,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{ir(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};fr.prototype.removeEventListener=function(a){a&&a.listenerId&&ir(this,"removeEventListener",null,a.listenerId)};
var kr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=jr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&jr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?jr(a.purpose.legitimateInterests,
b)&&jr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},jr=function(a,b){return!(!a||!a[b])},ir=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(gr(a)){lr(a);var g=++a.rb;a.ma[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},gr=function(a){if(a.C)return a.C;a.C=Pl(a.H,"__tcfapiLocator");return a.C},lr=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Zq(a.H,"message",b)}},mr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=er(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(br({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var nr={1:0,3:0,4:0,7:3,9:3,10:3};function or(){return Cp("tcf",function(){return{}})}var pr=function(){return new fr(x,{timeoutMs:-1})};
function qr(){var a=or(),b=pr();hr(b)&&!rr()&&!sr()&&L(124);if(!a.active&&hr(b)){rr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,$m().active=!0,a.tcString="tcunavailable");vp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)tr(a),wp([J.m.U,J.m.Ka,J.m.V]),$m().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,sr()&&(a.active=!0),!ur(c)||rr()||sr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in nr)nr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(ur(c)){var g={},h;for(h in nr)if(nr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={op:!0};p=p===void 0?{}:p;m=mr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.op)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?kr(n,"1",0):!0:!1;g["1"]=m}else g[h]=kr(c,h,nr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(wp([J.m.U,J.m.Ka,J.m.V]),$m().active=!0):(r[J.m.Ka]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":wp([J.m.V]),qp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:vr()||""}))}}else wp([J.m.U,J.m.Ka,J.m.V])})}catch(c){tr(a),wp([J.m.U,J.m.Ka,J.m.V]),$m().active=!0}}}
function tr(a){a.type="e";a.tcString="tcunavailable"}function ur(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function rr(){return x.gtag_enable_tcf_support===!0}function sr(){return or().enableAdvertiserConsentMode===!0}function vr(){var a=or();if(a.active)return a.tcString}function wr(){var a=or();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function xr(a){if(!nr.hasOwnProperty(String(a)))return!0;var b=or();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var yr=[J.m.U,J.m.ja,J.m.V,J.m.Ka],zr={},Ar=(zr[J.m.U]=1,zr[J.m.ja]=2,zr);function Br(a){if(a===void 0)return 0;switch(O(a,J.m.Ga)){case void 0:return 1;case !1:return 3;default:return 2}}function Cr(){return(E(183)?dj.vp:dj.wp).indexOf(ro())!==-1&&uc.globalPrivacyControl===!0}function Dr(a){if(Cr())return!1;var b=Br(a);if(b===3)return!1;switch(jn(J.m.Ka)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Er(){return ln()||!hn(J.m.U)||!hn(J.m.ja)}function Fr(){var a={},b;for(b in Ar)Ar.hasOwnProperty(b)&&(a[Ar[b]]=jn(b));return"G1"+ef(a[1]||0)+ef(a[2]||0)}var Gr={},Hr=(Gr[J.m.U]=0,Gr[J.m.ja]=1,Gr[J.m.V]=2,Gr[J.m.Ka]=3,Gr);function Ir(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Jr(a){for(var b="1",c=0;c<yr.length;c++){var d=b,e,f=yr[c],g=gn.delegatedConsentTypes[f];e=g===void 0?0:Hr.hasOwnProperty(g)?12|Hr[g]:8;var h=$m();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Ir(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Ir(m.declare)<<4|Ir(m.default)<<2|Ir(m.update)])}var n=b,p=(Cr()?1:0)<<3,q=(ln()?1:0)<<2,r=Br(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[gn.containerScopedDefaults.ad_storage<<4|gn.containerScopedDefaults.analytics_storage<<2|gn.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(gn.usedContainerScopedDefaults?1:0)<<2|gn.containerScopedDefaults.ad_personalization]}
function Kr(){if(!hn(J.m.V))return"-";for(var a=Object.keys(Ko),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=gn.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Ko[m])}(gn.usedCorePlatformServices?gn.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Lr(){return to()||(rr()||sr())&&wr()==="1"?"1":"0"}function Mr(){return(to()?!0:!(!rr()&&!sr())&&wr()==="1")||!hn(J.m.V)}
function Nr(){var a="0",b="0",c;var d=or();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=or();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;to()&&(h|=1);wr()==="1"&&(h|=2);rr()&&(h|=4);var m;var n=or();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);$m().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Or(){return ro()==="US-CO"};var Pr;function Qr(){if(xc===null)return 0;var a=ad();if(!a)return 0;var b=a.getEntriesByName(xc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Rr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Sr(a){a=a===void 0?{}:a;var b=lg.ctid.split("-")[0].toUpperCase(),c={ctid:lg.ctid,yj:bk,Cj:ak,dm:Hm.qe?2:1,Cq:a.Cm,we:lg.canonicalContainerId};if(E(210)){var d;c.sq=(d=Om())==null?void 0:d.canonicalContainerId}if(E(204)){var e;c.No=(e=Pr)!=null?e:Pr=Qr()}c.we!==a.Pa&&(c.Pa=a.Pa);var f=Mm();c.om=f?f.canonicalContainerId:void 0;jk?(c.Uc=Rr[b],c.Uc||(c.Uc=0)):c.Uc=kk?13:10;Vj.C?(c.Ah=0,c.Ql=2):c.Ah=Vj.N?1:3;var g={6:!1};Vj.H===2?g[7]=!0:Vj.H===1&&(g[2]=!0);if(xc){var h=Vk(al(xc),"host");h&&
(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Sl=g;return hf(c,a.oh)}
function Tr(){if(!E(192))return Sr();if(E(193))return hf({yj:bk,Cj:ak});var a=lg.ctid.split("-")[0].toUpperCase(),b={ctid:lg.ctid,yj:bk,Cj:ak,dm:Hm.qe?2:1,we:lg.canonicalContainerId},c=Mm();b.om=c?c.canonicalContainerId:void 0;jk?(b.Uc=Rr[a],b.Uc||(b.Uc=0)):b.Uc=kk?13:10;Vj.C?(b.Ah=0,b.Ql=2):b.Ah=Vj.N?1:3;var d={6:!1};Vj.H===2?d[7]=!0:Vj.H===1&&(d[2]=!0);if(xc){var e=Vk(al(xc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Sl=d;return hf(b)};function Ur(a,b,c,d){var e,f=Number(a.Cc!=null?a.Cc:void 0);f!==0&&(e=new Date((b||Ab())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Ec:d}};var Vr=["ad_storage","ad_user_data"];function Wr(a,b){if(!a)return fb("TAGGING",32),10;if(b===null||b===void 0||b==="")return fb("TAGGING",33),11;var c=Xr(!1);if(c.error!==0)return fb("TAGGING",34),c.error;if(!c.value)return fb("TAGGING",35),2;c.value[a]=b;var d=Yr(c);d!==0&&fb("TAGGING",36);return d}
function Zr(a){if(!a)return fb("TAGGING",27),{error:10};var b=Xr();if(b.error!==0)return fb("TAGGING",29),b;if(!b.value)return fb("TAGGING",30),{error:2};if(!(a in b.value))return fb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(fb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Xr(a){a=a===void 0?!0:a;if(!hn(Vr))return fb("TAGGING",43),{error:3};try{if(!x.localStorage)return fb("TAGGING",44),{error:1}}catch(f){return fb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return fb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return fb("TAGGING",47),{error:12}}}catch(f){return fb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return fb("TAGGING",49),{error:4};
if(b.version!==1)return fb("TAGGING",50),{error:5};try{var e=$r(b);a&&e&&Yr({value:b,error:0})}catch(f){return fb("TAGGING",48),{error:8}}return{value:b,error:0}}
function $r(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,fb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=$r(a[e.value])||c;return c}return!1}
function Yr(a){if(a.error)return a.error;if(!a.value)return fb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return fb("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return fb("TAGGING",53),7}return 0};var as={oj:"value",sb:"conversionCount"},bs={bm:9,wm:10,oj:"timeouts",sb:"timeouts"},cs=[as,bs];function ds(a){if(!es(a))return{};var b=fs(cs),c=b[a.sb];if(c===void 0||c===-1)return b;var d={},e=ma(Object,"assign").call(Object,{},b,(d[a.sb]=c+1,d));return gs(e)?e:b}
function fs(a){var b;a:{var c=Zr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&es(m)){var n=e[m.oj];n===void 0||Number.isNaN(n)?f[m.sb]=-1:f[m.sb]=Number(n)}else f[m.sb]=-1}return f}
function hs(){var a=ds(as),b=a[as.sb];if(b===void 0||b<=0)return"";var c=a[bs.sb];return c===void 0||c<0?b.toString():[b.toString(),c.toString()].join("~")}function gs(a,b){b=b||{};for(var c=Ab(),d=Ur(b,c,!0),e={},f=l(cs),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.sb];m!==void 0&&m!==-1&&(e[h.oj]=m)}e.creationTimeMs=c;return Wr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function es(a){return hn(["ad_storage","ad_user_data"])?!a.wm||Ra(a.wm):!1}
function is(a){return hn(["ad_storage","ad_user_data"])?!a.bm||Ra(a.bm):!1};function js(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var ks={O:{po:0,Ij:1,sg:2,Oj:3,Jh:4,Mj:5,Nj:6,Pj:7,Kh:8,Tk:9,Sk:10,ui:11,Uk:12,Vg:13,Xk:14,Lf:15,no:16,ue:17,Ni:18,Oi:19,Pi:20,Hl:21,Qi:22,Mh:23,Wj:24}};ks.O[ks.O.po]="RESERVED_ZERO";ks.O[ks.O.Ij]="ADS_CONVERSION_HIT";ks.O[ks.O.sg]="CONTAINER_EXECUTE_START";ks.O[ks.O.Oj]="CONTAINER_SETUP_END";ks.O[ks.O.Jh]="CONTAINER_SETUP_START";ks.O[ks.O.Mj]="CONTAINER_BLOCKING_END";ks.O[ks.O.Nj]="CONTAINER_EXECUTE_END";ks.O[ks.O.Pj]="CONTAINER_YIELD_END";ks.O[ks.O.Kh]="CONTAINER_YIELD_START";ks.O[ks.O.Tk]="EVENT_EXECUTE_END";
ks.O[ks.O.Sk]="EVENT_EVALUATION_END";ks.O[ks.O.ui]="EVENT_EVALUATION_START";ks.O[ks.O.Uk]="EVENT_SETUP_END";ks.O[ks.O.Vg]="EVENT_SETUP_START";ks.O[ks.O.Xk]="GA4_CONVERSION_HIT";ks.O[ks.O.Lf]="PAGE_LOAD";ks.O[ks.O.no]="PAGEVIEW";ks.O[ks.O.ue]="SNIPPET_LOAD";ks.O[ks.O.Ni]="TAG_CALLBACK_ERROR";ks.O[ks.O.Oi]="TAG_CALLBACK_FAILURE";ks.O[ks.O.Pi]="TAG_CALLBACK_SUCCESS";ks.O[ks.O.Hl]="TAG_EXECUTE_END";ks.O[ks.O.Qi]="TAG_EXECUTE_START";ks.O[ks.O.Mh]="CUSTOM_PERFORMANCE_START";ks.O[ks.O.Wj]="CUSTOM_PERFORMANCE_END";var ls=[],ms={},ns={};var os=["2"];function ps(a){return a.origin!=="null"};function qs(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ra(11)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};var rs;function ss(a,b,c,d){return ts(d)?qs(a,String(b||us()),c):[]}function vs(a,b,c,d,e){if(ts(e)){var f=ws(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=xs(f,function(g){return g.Yo},b);if(f.length===1)return f[0];f=xs(f,function(g){return g.bq},c);return f[0]}}}function ys(a,b,c,d){var e=us(),f=window;ps(f)&&(f.document.cookie=a);var g=us();return e!==g||c!==void 0&&ss(b,g,!1,d).indexOf(c)>=0}
function zs(a,b,c,d){function e(w,y,z){if(z==null)return delete h[y],w;h[y]=z;return w+"; "+y+"="+z}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!ts(c.Ec))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=As(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Wp);g=e(g,"samesite",c.tq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Bs(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!Cs(u,c.path)&&ys(v,a,b,c.Ec))return Ra(15)&&(rs=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Cs(n,c.path)?1:ys(g,a,b,c.Ec)?0:1}
function Ds(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");if(ls.includes("2")){var d;(d=ad())==null||d.mark("2-"+ks.O.Mh+"-"+(ns["2"]||0))}var e=zs(a,b,c);if(ls.includes("2")){var f="2-"+ks.O.Wj+"-"+(ns["2"]||0),g={start:"2-"+ks.O.Mh+"-"+(ns["2"]||0),end:f},h;(h=ad())==null||h.mark(f);var m,n,p=(n=(m=ad())==null?void 0:m.measure(f,g))==null?void 0:n.duration;p!==void 0&&(ns["2"]=(ns["2"]||0)+1,ms["2"]=p+(ms["2"]||0))}return e}
function xs(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ws(a,b,c){for(var d=[],e=ss(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Po:e[f],Qo:g.join("."),Yo:Number(n[0])||1,bq:Number(n[1])||1})}}}return d}function As(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Es=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Fs=/(^|\.)doubleclick\.net$/i;function Cs(a,b){return a!==void 0&&(Fs.test(window.document.location.hostname)||b==="/"&&Es.test(a))}function Gs(a){if(!a)return 1;var b=a;Ra(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Hs(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Is(a,b){var c=""+Gs(a),d=Hs(b);d>1&&(c+="-"+d);return c}
var us=function(){return ps(window)?window.document.cookie:""},ts=function(a){return a&&Ra(7)?(Array.isArray(a)?a:[a]).every(function(b){return kn(b)&&hn(b)}):!0},Bs=function(){var a=rs,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;Fs.test(g)||Es.test(g)||b.push("none");return b};function Js(a){var b=Math.round(Math.random()*2147483647);return a?String(b^js(a)&2147483647):String(b)}function Ks(a){return[Js(a),Math.round(Ab()/1E3)].join(".")}function Ls(a,b,c,d,e){var f=Gs(b),g;return(g=vs(a,f,Hs(c),d,e))==null?void 0:g.Qo};var Ms;function Ns(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Os,d=Ps,e=Qs();if(!e.init){Lc(A,"mousedown",a);Lc(A,"keyup",a);Lc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Rs(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Qs().decorators.push(f)}
function Ss(a,b,c){for(var d=Qs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Eb(e,g.callback())}}return e}
function Qs(){var a=yc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ts=/(.*?)\*(.*?)\*(.*)/,Us=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Vs=/^(?:www\.|m\.|amp\.)+/,Ws=/([^?#]+)(\?[^#]*)?(#.*)?/;function Xs(a){var b=Ws.exec(a);if(b)return{uj:b[1],query:b[2],fragment:b[3]}}function Ys(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Zs(a,b){var c=[uc.userAgent,(new Date).getTimezoneOffset(),uc.userLanguage||uc.language,Math.floor(Ab()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Ms)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Ms=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Ms[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function $s(a){return function(b){var c=al(x.location.href),d=c.search.replace("?",""),e=Sk(d,"_gl",!1,!0)||"";b.query=at(e)||{};var f=Vk(c,"fragment"),g;var h=-1;if(Gb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=at(g||"")||{};a&&bt(c,d,f)}}function ct(a,b){var c=Ys(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function bt(a,b,c){function d(g,h){var m=ct("_gl",g);m.length&&(m=h+m);return m}if(tc&&tc.replaceState){var e=Ys("_gl");if(e.test(b)||e.test(c)){var f=Vk(a,"path");b=d(b,"?");c=d(c,"#");tc.replaceState({},"",""+f+b+c)}}}function dt(a,b){var c=$s(!!b),d=Qs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Eb(e,f.query),a&&Eb(e,f.fragment));return e}
var at=function(a){try{var b=et(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=db(d[e+1]);c[f]=g}fb("TAGGING",6);return c}}catch(h){fb("TAGGING",8)}};function et(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ts.exec(d);if(f){c=f;break a}d=Uk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Zs(h,p)){m=!0;break a}m=!1}if(m)return h;fb("TAGGING",7)}}}
function ft(a,b,c,d,e){function f(p){p=ct(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Xs(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.uj+h+m}
function gt(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(cb(String(y))))}var z=v.join("*");u=["1",Zs(z),z].join("*");d?(Ra(3)||Ra(1)||!p)&&ht("_gl",u,a,p,q):it("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ss(b,1,d),f=Ss(b,2,d),g=Ss(b,4,d),h=Ss(b,3,d);c(e,!1,!1);c(f,!0,!1);Ra(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
jt(m,h[m],a)}function jt(a,b,c){c.tagName.toLowerCase()==="a"?it(a,b,c):c.tagName.toLowerCase()==="form"&&ht(a,b,c)}function it(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ra(4)||d)){var h=x.location.href,m=Xs(c.href),n=Xs(h);g=!(m&&n&&m.uj===n.uj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=ft(a,b,c.href,d,e);jc.test(p)&&(c.href=p)}}
function ht(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=ft(a,b,f,d,e);jc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Os(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||gt(e,e.hostname)}}catch(g){}}function Ps(a){try{var b=a.getAttribute("action");if(b){var c=Vk(al(b),"host");gt(a,c)}}catch(d){}}function kt(a,b,c,d){Ns();var e=c==="fragment"?2:1;d=!!d;Rs(a,b,e,d,!1);e===2&&fb("TAGGING",23);d&&fb("TAGGING",24)}
function lt(a,b){Ns();Rs(a,[Xk(x.location,"host",!0)],b,!0,!0)}function mt(){var a=A.location.hostname,b=Us.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Uk(f[2])||"":Uk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Vs,""),m=e.replace(Vs,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function nt(a,b){return a===!1?!1:a||b||mt()};var ot=["1"],pt={},qt={};function rt(a,b){b=b===void 0?!0:b;var c=st(a.prefix);if(pt[c])tt(a);else if(ut(c,a.path,a.domain)){var d=qt[st(a.prefix)]||{id:void 0,zh:void 0};b&&vt(a,d.id,d.zh);tt(a)}else{var e=cl("auiddc");if(e)fb("TAGGING",17),pt[c]=e;else if(b){var f=st(a.prefix),g=Ks();wt(f,g,a);ut(c,a.path,a.domain);tt(a,!0)}}}
function tt(a,b){if((b===void 0?0:b)&&es(as)){var c=Xr(!1);c.error!==0?fb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Yr(c)!==0&&fb("TAGGING",41)):fb("TAGGING",40):fb("TAGGING",39)}if(is(as)&&fs([as])[as.sb]===-1){for(var d={},e=(d[as.sb]=0,d),f=l(cs),g=f.next();!g.done;g=f.next()){var h=g.value;h!==as&&is(h)&&(e[h.sb]=0)}gs(e,a)}}
function vt(a,b,c){var d=st(a.prefix),e=pt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Ab()/1E3)));wt(d,h,a,g*1E3)}}}}function wt(a,b,c,d){var e;e=["1",Is(c.domain,c.path),b].join(".");var f=Ur(c,d);f.Ec=xt();Ds(a,e,f)}function ut(a,b,c){var d=Ls(a,b,c,ot,xt());if(!d)return!1;zt(a,d);return!0}
function zt(a,b){var c=b.split(".");c.length===5?(pt[a]=c.slice(0,2).join("."),qt[a]={id:c.slice(2,4).join("."),zh:Number(c[4])||0}):c.length===3?qt[a]={id:c.slice(0,2).join("."),zh:Number(c[2])||0}:pt[a]=b}function st(a){return(a||"_gcl")+"_au"}function At(a){function b(){hn(c)&&a()}var c=xt();on(function(){b();hn(c)||pn(b,c)},c)}
function Bt(a){var b=dt(!0),c=st(a.prefix);At(function(){var d=b[c];if(d){zt(c,d);var e=Number(pt[c].split(".")[1])*1E3;if(e){fb("TAGGING",16);var f=Ur(a,e);f.Ec=xt();var g=["1",Is(a.domain,a.path),d].join(".");Ds(c,g,f)}}})}function Ct(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Ls(a,e.path,e.domain,ot,xt());h&&(g[a]=h);return g};At(function(){kt(f,b,c,d)})}function xt(){return["ad_storage","ad_user_data"]};function Dt(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Fj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Et(a,b){var c=Dt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Fj]||(d[c[e].Fj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Fj].push(g)}}return d};var Ft={},Gt=(Ft.k={ba:/^[\w-]+$/},Ft.b={ba:/^[\w-]+$/,zj:!0},Ft.i={ba:/^[1-9]\d*$/},Ft.h={ba:/^\d+$/},Ft.t={ba:/^[1-9]\d*$/},Ft.d={ba:/^[A-Za-z0-9_-]+$/},Ft.j={ba:/^\d+$/},Ft.u={ba:/^[1-9]\d*$/},Ft.l={ba:/^[01]$/},Ft.o={ba:/^[1-9]\d*$/},Ft.g={ba:/^[01]$/},Ft.s={ba:/^.+$/},Ft);var Ht={},Lt=(Ht[5]={Gh:{2:It},nj:"2",ph:["k","i","b","u"]},Ht[4]={Gh:{2:It,GCL:Jt},nj:"2",ph:["k","i","b"]},Ht[2]={Gh:{GS2:It,GS1:Kt},nj:"GS2",ph:"sogtjlhd".split("")},Ht);function Mt(a,b,c){var d=Lt[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function It(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Lt[b];if(f){for(var g=f.ph,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Gt[p];r&&(r.zj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Nt(a,b,c){var d=Lt[b];if(d)return[d.nj,c||"1",Ot(a,b)].join(".")}
function Ot(a,b){var c=Lt[b];if(c){for(var d=[],e=l(c.ph),f=e.next();!f.done;f=e.next()){var g=f.value,h=Gt[g];if(h){var m=a[g];if(m!==void 0)if(h.zj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Jt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Kt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Pt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Qt(a,b,c){if(Lt[b]){for(var d=[],e=ss(a,void 0,void 0,Pt.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Mt(g.value,b,c);h&&d.push(Rt(h))}return d}}function St(a,b,c,d,e){d=d||{};var f=Is(d.domain,d.path),g=Nt(b,c,f);if(!g)return 1;var h=Ur(d,e,void 0,Pt.get(c));return Ds(a,g,h)}function Tt(a,b){var c=b.ba;return typeof c==="function"?c(a):c.test(a)}
function Rt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Tf:void 0},c=b.next()){var e=c.value,f=a[e];d.Tf=Gt[e];d.Tf?d.Tf.zj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Tt(h,g.Tf)}}(d)):void 0:typeof f==="string"&&Tt(f,d.Tf)||(a[e]=void 0):a[e]=void 0}return a};var Ut=function(){this.value=0};Ut.prototype.set=function(a){return this.value|=1<<a};var Vt=function(a,b){b<=0||(a.value|=1<<b-1)};Ut.prototype.get=function(){return this.value};Ut.prototype.clear=function(a){this.value&=~(1<<a)};Ut.prototype.clearAll=function(){this.value=0};Ut.prototype.equals=function(a){return this.value===a.value};function Wt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Xt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Yt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Ob(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Ob(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(js((""+b+e).toLowerCase()))};var Zt={},$t=(Zt.gclid=!0,Zt.dclid=!0,Zt.gbraid=!0,Zt.wbraid=!0,Zt),au=/^\w+$/,bu=/^[\w-]+$/,cu={},du=(cu.aw="_aw",cu.dc="_dc",cu.gf="_gf",cu.gp="_gp",cu.gs="_gs",cu.ha="_ha",cu.ag="_ag",cu.gb="_gb",cu),eu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,fu=/^www\.googleadservices\.com$/;function gu(){return["ad_storage","ad_user_data"]}function hu(a){return!Ra(7)||hn(a)}function iu(a,b){function c(){var d=hu(b);d&&a();return d}on(function(){c()||pn(c,b)},b)}
function ju(a){return ku(a).map(function(b){return b.gclid})}function lu(a){return mu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function mu(a){var b=nu(a.prefix),c=ou("gb",b),d=ou("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=ku(c).map(e("gb")),g=pu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function qu(a,b,c,d,e,f){var g=pb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Id=f),g.labels=ru(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Id:f})}function pu(a){for(var b=Qt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=su(f);h&&qu(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function ku(a){for(var b=[],c=ss(a,A.cookie,void 0,gu()),d=l(c),e=d.next();!e.done;e=d.next()){var f=tu(e.value);if(f!=null){var g=f;qu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return uu(b)}function vu(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function wu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ja&&b.Ja&&h.Ja.equals(b.Ja)&&(e=h)}if(d){var m,n,p=(m=d.Ja)!=null?m:new Ut,q=(n=b.Ja)!=null?n:new Ut;p.value|=q.value;d.Ja=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Id=b.Id);d.labels=vu(d.labels||[],b.labels||[]);d.Db=vu(d.Db||[],b.Db||[])}else c&&e?ma(Object,"assign").call(Object,e,b):a.push(b)}
function xu(a){if(!a)return new Ut;var b=new Ut;if(a===1)return Vt(b,2),Vt(b,3),b;Vt(b,a);return b}
function yu(){var a=Zr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(bu))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Ut;typeof e==="number"?g=xu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ja:g,Db:[2]}}catch(h){return null}}
function zu(){var a=Zr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(bu))return b;var f=new Ut,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ja:f,Db:[2]});return b},[])}catch(b){return null}}
function Au(a){for(var b=[],c=ss(a,A.cookie,void 0,gu()),d=l(c),e=d.next();!e.done;e=d.next()){var f=tu(e.value);f!=null&&(f.Id=void 0,f.Ja=new Ut,f.Db=[1],wu(b,f))}var g=yu();g&&(g.Id=void 0,g.Db=g.Db||[2],wu(b,g));if(Ra(13)){var h=zu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Id=void 0;p.Db=p.Db||[2];wu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return uu(b)}
function ru(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function nu(a){return a&&typeof a==="string"&&a.match(au)?a:"_gcl"}function Bu(a,b){if(a){var c={value:a,Ja:new Ut};Vt(c.Ja,b);return c}}
function Cu(a,b,c){var d=al(a),e=Vk(d,"query",!1,void 0,"gclsrc"),f=Bu(Vk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=Bu(Sk(g,"gclid",!1),3));e||(e=Sk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Du(a,b){var c=al(a),d=Vk(c,"query",!1,void 0,"gclid"),e=Vk(c,"query",!1,void 0,"gclsrc"),f=Vk(c,"query",!1,void 0,"wbraid");f=Mb(f);var g=Vk(c,"query",!1,void 0,"gbraid"),h=Vk(c,"query",!1,void 0,"gad_source"),m=Vk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Sk(n,"gclid",!1);e=e||Sk(n,"gclsrc",!1);f=f||Sk(n,"wbraid",!1);g=g||Sk(n,"gbraid",!1);h=h||Sk(n,"gad_source",!1)}return Eu(d,e,m,f,g,h)}function Fu(){return Du(x.location.href,!0)}
function Eu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(bu))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&bu.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&bu.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&bu.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Gu(a){for(var b=Fu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Du(x.document.referrer,!1),b.gad_source=void 0);Hu(b,!1,a)}
function Iu(a){Gu(a);var b=Cu(x.location.href,!0,!1);b.length||(b=Cu(x.document.referrer,!1,!0));a=a||{};Ju(a);if(b.length){var c=b[0],d=Ab(),e=Ur(a,d,!0),f=gu(),g=function(){hu(f)&&e.expires!==void 0&&Wr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ja.get()},expires:Number(e.expires)})};on(function(){g();hu(f)||pn(g,f)},f)}}
function Ju(a){var b;if(b=Ra(14)){var c=Ku();b=eu.test(c)||fu.test(c)||Lu()}if(b){var d;a:{for(var e=al(x.location.href),f=Tk(Vk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!$t[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Wt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Xt(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,z=w.next().value,C=y,D=z,G=C&7;if(C>>3===16382){if(G!==0)break;var I=Xt(t,D);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var T=void 0,da=t,N=D;switch(G){case 0:M=(T=Xt(da,N))==null?void 0:T[1];break d;case 1:M=N+8;break d;case 2:var W=Xt(da,N);if(W===void 0)break;var ia=l(W),ka=ia.next().value;M=ia.next().value+ka;break d;case 5:M=N+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(X){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Y=d;Y&&Mu(Y,7,a)}}
function Mu(a,b,c){c=c||{};var d=Ab(),e=Ur(c,d,!0),f=gu(),g=function(){if(hu(f)&&e.expires!==void 0){var h=zu()||[];wu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ja:xu(b)},!0);Wr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ja?m.Ja.get():0},expires:Number(m.expires)}}))}};on(function(){hu(f)?g():pn(g,f)},f)}
function Hu(a,b,c,d,e){c=c||{};e=e||[];var f=nu(c.prefix),g=d||Ab(),h=Math.round(g/1E3),m=gu(),n=!1,p=!1,q=function(){if(hu(m)){var r=Ur(c,g,!0);r.Ec=m;for(var t=function(T,da){var N=ou(T,f);N&&(Ds(N,da,r),T!=="gb"&&(n=!0))},u=function(T){var da=["GCL",h,T];e.length>0&&da.push(e.join("."));return da.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var z=a.gb[0],C=ou("gb",f);!b&&ku(C).some(function(T){return T.gclid===z&&T.labels&&
T.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&hu("ad_storage")&&(p=!0,!n)){var D=a.gbraid,G=ou("ag",f);if(b||!pu(G).some(function(T){return T.gclid===D&&T.labels&&T.labels.length>0})){var I={},M=(I.k=D,I.i=""+h,I.b=e,I);St(G,M,5,c,g)}}Nu(a,f,g,c)};on(function(){q();hu(m)||pn(q,m)},m)}
function Nu(a,b,c,d){if(a.gad_source!==void 0&&hu("ad_storage")){var e=$c();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=ou("gs",b);if(g){var h=Math.floor((Ab()-(Zc()||0))/1E3),m,n=Yt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);St(g,m,5,d,c)}}}}
function Ou(a,b){var c=dt(!0);iu(function(){for(var d=nu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(du[f]!==void 0){var g=ou(f,d),h=c[g];if(h){var m=Math.min(Pu(h),Ab()),n;b:{for(var p=m,q=ss(g,A.cookie,void 0,gu()),r=0;r<q.length;++r)if(Pu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Ur(b,m,!0);t.Ec=gu();Ds(g,h,t)}}}}Hu(Eu(c.gclid,c.gclsrc),!1,b)},gu())}
function Qu(a){var b=["ag"],c=dt(!0),d=nu(a.prefix);iu(function(){for(var e=0;e<b.length;++e){var f=ou(b[e],d);if(f){var g=c[f];if(g){var h=Mt(g,5);if(h){var m=su(h);m||(m=Ab());var n;a:{for(var p=m,q=Qt(f,5),r=0;r<q.length;++r)if(su(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);St(f,h,5,a,m)}}}}},["ad_storage"])}function ou(a,b){var c=du[a];if(c!==void 0)return b+c}function Pu(a){return Ru(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function su(a){return a?(Number(a.i)||0)*1E3:0}function tu(a){var b=Ru(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Ru(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!bu.test(a[2])?[]:a}
function Su(a,b,c,d,e){if(Array.isArray(b)&&ps(x)){var f=nu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=ou(a[m],f);if(n){var p=ss(n,A.cookie,void 0,gu());p.length&&(h[n]=p.sort()[p.length-1])}}return h};iu(function(){kt(g,b,c,d)},gu())}}
function Tu(a,b,c,d){if(Array.isArray(a)&&ps(x)){var e=["ag"],f=nu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=ou(e[m],f);if(!n)return{};var p=Qt(n,5);if(p.length){var q=p.sort(function(r,t){return su(t)-su(r)})[0];h[n]=Nt(q,5)}}return h};iu(function(){kt(g,a,b,c)},["ad_storage"])}}function uu(a){return a.filter(function(b){return bu.test(b.gclid)})}
function Uu(a,b){if(ps(x)){for(var c=nu(b.prefix),d={},e=0;e<a.length;e++)du[a[e]]&&(d[a[e]]=du[a[e]]);iu(function(){tb(d,function(f,g){var h=ss(c+g,A.cookie,void 0,gu());h.sort(function(t,u){return Pu(u)-Pu(t)});if(h.length){var m=h[0],n=Pu(m),p=Ru(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Ru(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Hu(q,!0,b,n,p)}})},gu())}}
function Vu(a){var b=["ag"],c=["gbraid"];iu(function(){for(var d=nu(a.prefix),e=0;e<b.length;++e){var f=ou(b[e],d);if(!f)break;var g=Qt(f,5);if(g.length){var h=g.sort(function(q,r){return su(r)-su(q)})[0],m=su(h),n=h.b,p={};p[c[e]]=h.k;Hu(p,!0,a,m,n)}}},["ad_storage"])}function Wu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Xu(a){function b(h,m,n){n&&(h[m]=n)}if(ln()){var c=Fu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:dt(!1)._gs);if(Wu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);lt(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);lt(function(){return g},1)}}}function Lu(){var a=al(x.location.href);return Vk(a,"query",!1,void 0,"gad_source")}
function Yu(a){if(!Ra(1))return null;var b=dt(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ra(2)){b=Lu();if(b!=null)return b;var c=Fu();if(Wu(c,a))return"0"}return null}function Zu(a){var b=Yu(a);b!=null&&lt(function(){var c={};return c.gad_source=b,c},4)}function $u(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function av(a,b,c,d){var e=[];c=c||{};if(!hu(gu()))return e;var f=ku(a),g=$u(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Ur(c,p,!0);r.Ec=gu();Ds(a,q,r)}return e}
function bv(a,b){var c=[];b=b||{};var d=mu(b),e=$u(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=nu(b.prefix),n=ou(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);St(n,y,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),C=Ur(b,u,!0);C.Ec=gu();Ds(n,z,C)}}return c}
function cv(a,b){var c=nu(b),d=ou(a,c);if(!d)return 0;var e;e=a==="ag"?pu(d):ku(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function dv(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function ev(a){var b=Math.max(cv("aw",a),dv(hu(gu())?Et():{})),c=Math.max(cv("gb",a),dv(hu(gu())?Et("_gac_gb",!0):{}));c=Math.max(c,cv("ag",a));return c>b}
function Ku(){return A.referrer?Vk(al(A.referrer),"host"):""};
var fv=function(a,b){b=b===void 0?!1:b;var c=Cp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},gv=function(a){return bl(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},nv=function(a,b,c,d,e){var f=nu(a.prefix);if(fv(f,!0)){var g=Fu(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=hv(),r=q.Yf,t=q.Yl;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Ed:p});n&&h.push({gclid:n,Ed:"ds"});h.length===2&&L(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Ed:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Ed:"aw.ds"});iv(function(){var u=P(jv());if(u){rt(a);var v=[],w=u?pt[st(a.prefix)]:void 0;w&&v.push("auid="+w);if(P(J.m.V)){e&&v.push("userId="+e);var y=En(An.Z.Bl);if(y===void 0)Dn(An.Z.Cl,!0);else{var z=En(An.Z.jh);v.push("ga_uid="+z+"."+y)}}var C=Ku(),D=u||!d?h:[];D.length===0&&(eu.test(C)||fu.test(C))&&D.push({gclid:"",Ed:""});if(D.length!==0||r!==void 0){C&&v.push("ref="+encodeURIComponent(C));var G=kv();v.push("url="+
encodeURIComponent(G));v.push("tft="+Ab());var I=Zc();I!==void 0&&v.push("tfd="+Math.round(I));var M=Ql(!0);v.push("frm="+M);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var T={};c=pq(fq(new eq(0),(T[J.m.Ga]=Mq.C[J.m.Ga],T)))}v.push("gtm="+Sr({Pa:b}));Er()&&v.push("gcs="+Fr());v.push("gcd="+Jr(c));Mr()&&v.push("dma_cps="+Kr());v.push("dma="+Lr());Dr(c)?v.push("npa=0"):v.push("npa=1");Or()&&v.push("_ng=1");hr(pr())&&
v.push("tcfd="+Nr());var da=wr();da&&v.push("gdpr="+da);var N=vr();N&&v.push("gdpr_consent="+N);E(23)&&v.push("apve=0");E(123)&&dt(!1)._up&&v.push("gtm_up=1");sk()&&v.push("tag_exp="+sk());if(D.length>0)for(var W=0;W<D.length;W++){var ia=D[W],ka=ia.gclid,Y=ia.Ed;if(!lv(a.prefix,Y+"."+ka,w!==void 0)){var X=mv+"?"+v.join("&");ka!==""?X=Y==="gb"?X+"&wbraid="+ka:X+"&gclid="+ka+"&gclsrc="+Y:Y==="aw.ds"&&(X+="&gclsrc=aw.ds");Sc(X)}}else if(r!==void 0&&!lv(a.prefix,"gad",w!==void 0)){var ja=mv+"?"+v.join("&");
Sc(ja)}}}})}},lv=function(a,b,c){var d=Cp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},hv=function(){var a=al(x.location.href),b=void 0,c=void 0,d=Vk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(ov);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Yf:b,Yl:c}},kv=function(){var a=Ql(!1)===1?x.top.location.href:x.location.href;return a=a.replace(/[\?#].*$/,"")},pv=function(a){var b=[];tb(a,function(c,d){d=uu(d);for(var e=
[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},rv=function(a,b){return qv("dc",a,b)},sv=function(a,b){return qv("aw",a,b)},qv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=cl("gcl"+a);if(d)return d.split(".")}var e=nu(b);if(e==="_gcl"){var f=!P(jv())&&c,g;g=Fu()[a]||[];if(g.length>0)return f?["0"]:g}var h=ou(a,e);return h?ju(h):[]},iv=function(a){var b=jv();up(function(){a();P(b)||pn(a,b)},b)},jv=function(){return[J.m.U,J.m.V]},mv=Yi(36,
'https://adservice.google.com/pagead/regclk'),ov=/^gad_source[_=](\d+)$/;function tv(){return Cp("dedupe_gclid",function(){return Ks()})};var uv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,vv=/^www.googleadservices.com$/;function wv(a){a||(a=xv());return a.Lq?!1:a.Dp||a.Fp||a.Ip||a.Gp||a.Yf||a.np||a.Hp||a.tp?!0:!1}function xv(){var a={},b=dt(!0);a.Lq=!!b._up;var c=Fu();a.Dp=c.aw!==void 0;a.Fp=c.dc!==void 0;a.Ip=c.wbraid!==void 0;a.Gp=c.gbraid!==void 0;a.Hp=c.gclsrc==="aw.ds";a.Yf=hv().Yf;var d=A.referrer?Vk(al(A.referrer),"host"):"";a.tp=uv.test(d);a.np=vv.test(d);return a};function yv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function zv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function Av(){return["ad_storage","ad_user_data"]}function Bv(a){if(E(38)&&!En(An.Z.ol)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{yv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Dn(An.Z.ol,function(d){d.gclid&&Mu(d.gclid,5,a)}),zv(c)||L(178))})}catch(c){L(177)}};on(function(){hu(Av())?b():pn(b,Av())},Av())}};var Cv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function Dv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?Dn(An.Z.Nf,{gadSource:a.data.gadSource}):L(173)}
function Ev(a,b){if(E(a)){if(En(An.Z.Nf))return L(176),An.Z.Nf;if(En(An.Z.rl))return L(170),An.Z.Nf;var c=Sl();if(!c)L(171);else if(c.opener){var d=function(g){if(Cv.includes(g.origin)){a===119?Dv(g):a===200&&(Dv(g),g.data.gclid&&Mu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);$q(c,"message",d)}else L(172)};if(Zq(c,"message",d)){Dn(An.Z.rl,!0);for(var e=l(Cv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);L(174);return An.Z.Nf}L(175)}}}
;var Fv=function(){this.C=this.gppString=void 0};Fv.prototype.reset=function(){this.C=this.gppString=void 0};var Gv=new Fv;var Hv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Iv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Jv=/^\d+\.fls\.doubleclick\.net$/,Kv=/;gac=([^;?]+)/,Lv=/;gacgb=([^;?]+)/;
function Mv(a,b){if(Jv.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(Hv)?Uk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Nv(a,b,c){for(var d=hu(gu())?Et("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=av("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{mp:f?e.join(";"):"",lp:Mv(d,Lv)}}function Ov(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Iv)?b[1]:void 0}
function Pv(a){var b={},c,d,e;Jv.test(A.location.host)&&(c=Ov("gclgs"),d=Ov("gclst"),e=Ov("gcllp"));if(c&&d&&e)b.sh=c,b.uh=d,b.th=e;else{var f=Ab(),g=pu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Id});h.length>0&&m.length>0&&n.length>0&&(b.sh=h.join("."),b.uh=m.join("."),b.th=n.join("."))}return b}
function Qv(a,b,c,d){d=d===void 0?!1:d;if(Jv.test(A.location.host)){var e=Ov(c);if(e){if(d){var f=new Ut;Vt(f,2);Vt(f,3);return e.split(".").map(function(h){return{gclid:h,Ja:f,Db:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Au(g):ku(g)}if(b==="wbraid")return ku((a||"_gcl")+"_gb");if(b==="braids")return mu({prefix:a})}return[]}function Rv(a){return Jv.test(A.location.host)?!(Ov("gclaw")||Ov("gac")):ev(a)}
function Sv(a,b,c){var d;d=c?bv(a,b):av((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Tv(){var a=x.__uspapi;if(lb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var Yv=function(a){if(a.eventName===J.m.qa&&R(a,Q.A.ia)===K.J.Ha)if(E(24)){S(a,Q.A.te,O(a.D,J.m.ya)!=null&&O(a.D,J.m.ya)!==!1&&!P([J.m.U,J.m.V]));var b=Uv(a),c=O(a.D,J.m.Ra)!==!1;c||U(a,J.m.Rh,"1");var d=nu(b.prefix),e=R(a,Q.A.eh);if(!R(a,Q.A.da)&&!R(a,Q.A.Qf)&&!R(a,Q.A.se)){var f=O(a.D,J.m.Gb),g=O(a.D,J.m.Ta)||{};Vv({xe:c,Ce:g,Ge:f,Rc:b});if(!e&&!fv(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{U(a,J.m.jd,J.m.Yc);if(R(a,Q.A.da))U(a,J.m.jd,J.m.hn),U(a,J.m.da,"1");else if(R(a,Q.A.Qf))U(a,J.m.jd,
J.m.sn);else if(R(a,Q.A.se))U(a,J.m.jd,J.m.pn);else{var h=Fu();U(a,J.m.Zc,h.gclid);U(a,J.m.gd,h.dclid);U(a,J.m.fk,h.gclsrc);Wv(a,J.m.Zc)||Wv(a,J.m.gd)||(U(a,J.m.ae,h.wbraid),U(a,J.m.Pe,h.gbraid));U(a,J.m.Ya,Ku());U(a,J.m.Ca,kv());if(E(27)&&xc){var m=Vk(al(xc),"host");m&&U(a,J.m.Nk,m)}if(!R(a,Q.A.se)){var n=hv(),p=n.Yl;U(a,J.m.Ne,n.Yf);U(a,J.m.Oe,p)}U(a,J.m.Jc,Ql(!0));var q=xv();wv(q)&&U(a,J.m.ld,"1");U(a,J.m.hk,tv());dt(!1)._up==="1"&&U(a,J.m.Dk,"1")}ao=!0;U(a,J.m.Fb);U(a,J.m.Pb);var r=P([J.m.U,J.m.V]);
r&&(U(a,J.m.Fb,Xv()),c&&(rt(b),U(a,J.m.Pb,pt[st(b.prefix)])));U(a,J.m.nc);U(a,J.m.mb);if(!Wv(a,J.m.Zc)&&!Wv(a,J.m.gd)&&Rv(d)){var t=lu(b);t.length>0&&U(a,J.m.nc,t.join("."))}else if(!Wv(a,J.m.ae)&&r){var u=ju(d+"_aw");u.length>0&&U(a,J.m.mb,u.join("."))}U(a,J.m.Gk,$c());a.D.isGtmEvent&&(a.D.C[J.m.Ga]=Mq.C[J.m.Ga]);Dr(a.D)?U(a,J.m.xc,!1):U(a,J.m.xc,!0);S(a,Q.A.qg,!0);var v=Tv();v!==void 0&&U(a,J.m.Af,v||"error");var w=wr();w&&U(a,J.m.kd,w);if(E(137))try{var y=Intl.DateTimeFormat().resolvedOptions().timeZone;
U(a,J.m.ji,y||"-")}catch(G){U(a,J.m.ji,"e")}var z=vr();z&&U(a,J.m.sd,z);var C=Gv.gppString;C&&U(a,J.m.hf,C);var D=Gv.C;D&&U(a,J.m.ff,D);S(a,Q.A.Ba,!1)}}else a.isAborted=!0},Uv=function(a){var b={prefix:O(a.D,J.m.Rb)||O(a.D,J.m.cb),domain:O(a.D,J.m.ob),Cc:O(a.D,J.m.pb),flags:O(a.D,J.m.zb)};a.D.isGtmEvent&&(b.path=O(a.D,J.m.Sb));return b},Zv=function(a,b){var c,d,e,f,g,h,m,n;c=a.xe;d=a.Ce;e=a.Ge;f=a.Pa;g=a.D;h=a.De;m=a.Hr;n=a.Jm;Vv({xe:c,Ce:d,Ge:e,Rc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,nv(b,
f,g,h,n))},$v=function(a,b){if(!R(a,Q.A.se)){var c=Ev(119);if(c){var d=En(c),e=function(g){S(a,Q.A.se,!0);var h=Wv(a,J.m.Ne),m=Wv(a,J.m.Oe);U(a,J.m.Ne,String(g.gadSource));U(a,J.m.Oe,6);S(a,Q.A.da);S(a,Q.A.Qf);U(a,J.m.da);b();U(a,J.m.Ne,h);U(a,J.m.Oe,m);S(a,Q.A.se,!1)};if(d)e(d);else{var f=void 0;f=Gn(c,function(g,h){e(h);Hn(c,f)})}}}},Vv=function(a){var b,c,d,e;b=a.xe;c=a.Ce;d=a.Ge;e=a.Rc;b&&(nt(c[J.m.he],!!c[J.m.la])&&(Ou(aw,e),Qu(e),Bt(e)),Ql()!==2?(Iu(e),Bv(e),Ev(200,e)):Gu(e),Uu(aw,e),Vu(e));
c[J.m.la]&&(Su(aw,c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e.prefix),Tu(c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e.prefix),Ct(st(e.prefix),c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e),Ct("FPAU",c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e));d&&(E(101)?Xu(bw):Xu(cw));Zu(cw)},dw=function(a,b,c,d){var e,f,g;e=a.Km;f=a.callback;g=a.gm;if(typeof f==="function")if(e===J.m.mb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===J.m.Pb?(L(65),rt(b,!1),f(pt[st(b.prefix)])):f(g)},ew=function(a,b){Array.isArray(b)||
(b=[b]);var c=R(a,Q.A.ia);return b.indexOf(c)>=0},aw=["aw","dc","gb"],cw=["aw","dc","gb","ag"],bw=["aw","dc","gb","ag","gad_source"];function fw(a){var b=O(a.D,J.m.Lc),c=O(a.D,J.m.Kc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Wd&&L(131),a.isAborted=!0):!b&&c&&(L(132),a.isAborted=!0)}function gw(a){var b=P(J.m.U)?Bp.pscdl:"denied";b!=null&&U(a,J.m.Fg,b)}function hw(a){var b=Ql(!0);U(a,J.m.Jc,b)}function iw(a){Or()&&U(a,J.m.ee,1)}
function Xv(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Uk(a.substring(0,b))===void 0;)b--;return Uk(a.substring(0,b))||""}function jw(a){kw(a,Jp.Bf.Tm,O(a.D,J.m.pb))}function kw(a,b,c){Wv(a,J.m.wd)||U(a,J.m.wd,{});Wv(a,J.m.wd)[b]=c}function lw(a){S(a,Q.A.Pf,Zm.X.Fa)}function mw(a){var b=ib("GTAG_EVENT_FEATURE_CHANNEL");b&&(U(a,J.m.jf,b),gb())}function nw(a){var b=a.D.getMergedValues(J.m.sc);b&&a.mergeHitDataForKey(J.m.sc,b)}
function ow(a,b){b=b===void 0?!1:b;var c=R(a,Q.A.Of);if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,Q.A.Hj,!1),b||!pw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,Q.A.Hj,!0)}function qw(a){ql&&(ao=!0,a.eventName===J.m.qa?ho(a.D,a.target.id):(R(a,Q.A.Ke)||(eo[a.target.id]=!0),Ip(R(a,Q.A.hb))))};
var rw=function(a){if(Wv(a,J.m.nc)||Wv(a,J.m.de)){var b=Wv(a,J.m.oc),c=md(R(a,Q.A.xa),null),d=nu(c.prefix);c.prefix=d==="_gcl"?"":d;if(Wv(a,J.m.nc)){var e=Sv(b,c,!R(a,Q.A.Yk));S(a,Q.A.Yk,!0);e&&U(a,J.m.Rk,e)}if(Wv(a,J.m.de)){var f=Nv(b,c).mp;f&&U(a,J.m.yk,f)}}},vw=function(a){var b=new sw;E(101)&&ew(a,[K.J.W])&&U(a,J.m.Pk,dt(!1)._gs);if(E(16)){var c=O(a.D,J.m.Ca);c||(c=Ql(!1)===1?x.top.location.href:x.location.href);var d,e=al(c),f=Vk(e,"query",!1,void 0,"gclid");if(!f){var g=e.hash.replace("#","");
f=f||Sk(g,"gclid",!1)}(d=f?f.length:void 0)&&U(a,J.m.ek,d)}if(P(J.m.U)&&R(a,Q.A.Wc)){var h=R(a,Q.A.xa),m=nu(h.prefix);m==="_gcl"&&(m="");var n=Pv(m);U(a,J.m.Xd,n.sh);U(a,J.m.Zd,n.uh);U(a,J.m.Yd,n.th);Rv(m)?tw(a,b,h,m):uw(a,b,m)}if(E(21)){var p=P(J.m.U)&&P(J.m.V);if(!b.Ep()){var q;var r;b:{var t,u=x,v=[];try{u.navigation&&u.navigation.entries&&(v=u.navigation.entries())}catch(W){}t=v;var w={};try{for(var y=t.length-1;y>=0;y--){var z=t[y]&&t[y].url;if(z){var C=(new URL(z)).searchParams,D=C.get("gclid")||
void 0,G=C.get("gclsrc")||void 0;if(D){w.gclid=D;G&&(w.Ed=G);r=w;break b}}}}catch(W){}r=w}var I=r,M=I.gclid,T=I.Ed,da;if(!M||T!==void 0&&T!=="aw"&&T!=="aw.ds")da=void 0;else if(M!==void 0){var N=new Ut;Vt(N,2);Vt(N,3);da={version:"GCL",timestamp:0,gclid:M,Ja:N,Db:[3]}}else da=void 0;q=da;q&&(p||(q.gclid="0"),b.Nl(q),b.Em(!1))}}b.Nq(a)},uw=function(a,b,c){var d=R(a,Q.A.ia)===K.J.W&&Ql()!==2;Qv(c,"gclid","gclaw",d).forEach(function(f){b.Nl(f)});b.Em(!d);if(!c){var e=Mv(hu(gu())?Et():{},Kv);e&&U(a,J.m.Mg,
e)}},tw=function(a,b,c,d){Qv(d,"braids","gclgb").forEach(function(g){b.Ao(g)});if(!d){var e=Wv(a,J.m.oc);c=md(c,null);c.prefix=d;var f=Nv(e,c,!0).lp;f&&U(a,J.m.de,f)}},sw=function(){this.H=[];this.C=[];this.N=void 0};k=sw.prototype;k.Nl=function(a){wu(this.H,a)};k.Ao=function(a){wu(this.C,a)};k.Ep=function(){return this.C.length>0};k.Em=function(a){this.N!==!1&&(this.N=a)};k.Nq=function(a){if(this.H.length>0){var b=[],c=[],d=[];this.H.forEach(function(f){b.push(f.gclid);var g,h;c.push((h=(g=f.Ja)==
null?void 0:g.get())!=null?h:0);for(var m=d.push,n=0,p=l(f.Db||[0]),q=p.next();!q.done;q=p.next()){var r=q.value;r>0&&(n|=1<<r-1)}m.call(d,n.toString())});b.length>0&&U(a,J.m.mb,b.join("."));this.N||(c.length>0&&U(a,J.m.Le,c.join(".")),d.length>0&&U(a,J.m.Me,d.join(".")))}else{var e=this.C.map(function(f){return f.gclid}).join(".");e&&U(a,J.m.nc,e)}};
var ww=function(a,b){var c=a&&!P([J.m.U,J.m.V]);return b&&c?"0":b},zw=function(a){var b=a.Rc===void 0?{}:a.Rc,c=nu(b.prefix);fv(c)&&up(function(){function d(y,z,C){var D=P([J.m.U,J.m.V]),G=m&&D,I=b.prefix||"_gcl",M=xw(),T=(G?I:"")+"."+(P(J.m.U)?1:0)+"."+(P(J.m.V)?1:0);if(!M[T]){M[T]=!0;var da={},N=function(ja,wa){if(wa||typeof wa==="number")da[ja]=wa.toString()},W="https://www.google.com";Er()&&(N("gcs",Fr()),y&&N("gcu",1));N("gcd",Jr(h));sk()&&N("tag_exp",sk());if(ln()){N("rnd",tv());if((!p||q&&
q!=="aw.ds")&&D){var ia=ju(I+"_aw");N("gclaw",ia.join("."))}N("url",String(x.location).split(/[?#]/)[0]);N("dclid",ww(f,r));D||(W="https://pagead2.googlesyndication.com")}Mr()&&N("dma_cps",Kr());N("dma",Lr());N("npa",Dr(h)?0:1);Or()&&N("_ng",1);hr(pr())&&N("tcfd",Nr());N("gdpr_consent",vr()||"");N("gdpr",wr()||"");dt(!1)._up==="1"&&N("gtm_up",1);N("gclid",ww(f,p));N("gclsrc",q);if(!(da.hasOwnProperty("gclid")||da.hasOwnProperty("dclid")||da.hasOwnProperty("gclaw"))&&(N("gbraid",ww(f,t)),!da.hasOwnProperty("gbraid")&&
ln()&&D)){var ka=ju(I+"_gb");ka.length>0&&N("gclgb",ka.join("."))}N("gtm",Sr({Pa:h.eventMetadata[Q.A.hb],oh:!g}));m&&P(J.m.U)&&(rt(b||{}),G&&N("auid",pt[st(b.prefix)]||""));yw||a.Tl&&N("did",a.Tl);a.bj&&N("gdid",a.bj);a.Xi&&N("edid",a.Xi);a.fj!==void 0&&N("frm",a.fj);E(23)&&N("apve","0");var Y=Object.keys(da).map(function(ja){return ja+"="+encodeURIComponent(da[ja])}),X=W+"/pagead/landing?"+Y.join("&");Sc(X);v&&g!==void 0&&ep({targetId:g,request:{url:X,parameterEncoding:3,endpoint:D?12:13},Oa:{eventId:h.eventId,
priorityId:h.priorityId},qh:z===void 0?void 0:{eventId:z,priorityId:C}})}}var e=!!a.Ti,f=!!a.De,g=a.targetId,h=a.D,m=a.xh===void 0?!0:a.xh,n=Fu(),p=n.gclid||"",q=n.gclsrc,r=n.dclid||"",t=n.wbraid||"",u=!e&&((!p||q&&q!=="aw.ds"?!1:!0)||t),v=ln();if(u||v)if(v){var w=[J.m.U,J.m.V,J.m.Ka];d();(function(){P(w)||tp(function(y){d(!0,y.consentEventId,y.consentPriorityId)},w)})()}else d()},[J.m.U,J.m.V,J.m.Ka])},xw=function(){return Cp("reported_gclid",function(){return{}})},yw=!1;function Aw(a,b,c,d){var e=Hc(),f;if(e===1)a:{var g=mk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};
var Fw=function(a,b){if(a&&(mb(a)&&(a=Mp(a)),a)){var c=void 0,d=!1,e=O(b,J.m.Nn);if(e&&Array.isArray(e)){c=[];for(var f=0;f<e.length;f++){var g=Mp(e[f]);g&&(c.push(g),(a.id===g.id||a.id===a.destinationId&&a.destinationId===g.destinationId)&&(d=!0))}}if(!c||d){var h=O(b,J.m.Lk),m;if(h){m=Array.isArray(h)?h:[h];var n=O(b,J.m.Jk),p=O(b,J.m.Kk),q=O(b,J.m.Mk),r=No(O(b,J.m.Mn)),t=n||p,u=1;a.prefix!=="UA"||c||(u=5);for(var v=0;v<m.length;v++)if(v<u)if(c)Bw(c,m[v],r,b,{Dc:t,options:q});else if(a.prefix===
"AW"&&a.ids[Op[1]])E(155)?Bw([a],m[v],r||"US",b,{Dc:t,options:q}):Cw(a.ids[Op[0]],a.ids[Op[1]],m[v],b,{Dc:t,options:q});else if(a.prefix==="UA")if(E(155))Bw([a],m[v],r||"US",b,{Dc:t});else{var w=a.destinationId,y=m[v],z={Dc:t};L(23);if(y){z=z||{};var C=Dw(Ew,z,w),D={};z.Dc!==void 0?D.receiver=z.Dc:D.replace=y;D.ga_wpid=w;D.destination=y;C(2,zb(),D)}}}}}},Bw=function(a,b,c,d,e){L(21);if(b&&c){e=e||{};for(var f={countryNameCode:c,destinationNumber:b,retrievalTime:zb()},g=0;g<a.length;g++){var h=a[g];
Gw[h.id]||(h&&h.prefix==="AW"&&!f.adData&&h.ids.length>=2?(f.adData={ak:h.ids[Op[0]],cl:h.ids[Op[1]]},Hw(f.adData,d),Gw[h.id]=!0):h&&h.prefix==="UA"&&!f.gaData&&(f.gaData={gaWpid:h.destinationId},Gw[h.id]=!0))}(f.gaData||f.adData)&&Dw(Iw,e,void 0,d)(e.Dc,f,e.options)}},Cw=function(a,b,c,d,e){L(22);if(c){e=e||{};var f=Dw(Jw,e,a,d),g={ak:a,cl:b};e.Dc===void 0&&(g.autoreplace=c);Hw(g,d);f(2,e.Dc,g,c,0,zb(),e.options)}},Hw=function(a,b){a.dma=Lr();Mr()&&(a.dmaCps=Kr());Dr(b)?a.npa="0":a.npa="1"},Dw=function(a,
b,c,d){var e=x;if(e[a.functionName])return b.tj&&Nc(b.tj),e[a.functionName];var f=Kw();e[a.functionName]=f;if(a.additionalQueues)for(var g=0;g<a.additionalQueues.length;g++)e[a.additionalQueues[g]]=e[a.additionalQueues[g]]||Kw();a.idKey&&e[a.idKey]===void 0&&(e[a.idKey]=c);tm({destinationId:lg.ctid,endpoint:0,eventId:d==null?void 0:d.eventId,priorityId:d==null?void 0:d.priorityId},Aw("https://","http://",a.scriptUrl),b.tj,b.Yp);return f},Kw=function(){function a(){a.q=a.q||[];a.q.push(arguments)}
return a},Jw={functionName:"_googWcmImpl",idKey:"_googWcmAk",scriptUrl:"www.gstatic.com/wcm/loader.js"},Ew={functionName:"_gaPhoneImpl",idKey:"ga_wpid",scriptUrl:"www.gstatic.com/gaphone/loader.js"},Lw={Pm:"9",ro:"5"},Iw={functionName:"_googCallTrackingImpl",additionalQueues:[Ew.functionName,Jw.functionName],scriptUrl:"www.gstatic.com/call-tracking/call-tracking_"+(Lw.Pm||Lw.ro)+".js"},Gw={};function Mw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Wv(a,b)},setHitData:function(b,c){U(a,b,c)},setHitDataIfNotDefined:function(b,c){Wv(a,b)===void 0&&U(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.D,b)},Bb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return ld(c)?a.mergeHitDataForKey(b,c):!1}}};var Ow=function(a){var b=Nw[a.target.destinationId];if(!a.isAborted&&b)for(var c=Mw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Pw=function(a,b){var c=Nw[a];c||(c=Nw[a]=[]);c.push(b)},Nw={};var Qw=function(a){if(P(J.m.U)){a=a||{};rt(a,!1);var b,c=nu(a.prefix);if((b=qt[st(c)])&&!(Ab()-b.zh*1E3>18E5)){var d=b.id,e=d.split(".");if(e.length===2&&!(Ab()-(Number(e[1])||0)*1E3>864E5))return d}}};function Rw(a,b){return arguments.length===1?Sw("set",a):Sw("set",a,b)}function Tw(a,b){return arguments.length===1?Sw("config",a):Sw("config",a,b)}function Uw(a,b,c){c=c||{};c[J.m.pd]=a;return Sw("event",b,c)}function Sw(){return arguments};var Vw=function(){var a=uc&&uc.userAgent||"";if(a.indexOf("Safari")<0||/Chrome|Coast|Opera|Edg|Silk|Android/.test(a))return!1;var b=(/Version\/([\d\.]+)/.exec(a)||[])[1]||"";if(b==="")return!1;for(var c=["14","1","1"],d=b.split("."),e=0;e<d.length;e++){if(c[e]===void 0)return!0;if(d[e]!==c[e])return Number(d[e])>Number(c[e])}return d.length>=c.length};var Ww=function(){this.messages=[];this.C=[]};Ww.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=ma(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Ww.prototype.listen=function(a){this.C.push(a)};
Ww.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Ww.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Xw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[Q.A.hb]=lg.canonicalContainerId;Yw().enqueue(a,b,c)}
function Zw(){var a=$w;Yw().listen(a)}function Yw(){return Cp("mb",function(){return new Ww})};var ax,bx=!1;function cx(){bx=!0;ax=ax||{}}function dx(a){bx||cx();return ax[a]};function ex(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function fx(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var px=function(a){return a.tagName+":"+a.isVisible+":"+a.ka.length+":"+ox.test(a.ka)},Dx=function(a){a=a||{Ae:!0,Be:!0,Eh:void 0};a.Zb=a.Zb||{email:!0,phone:!1,address:!1};var b=qx(a),c=rx[b];if(c&&Ab()-c.timestamp<200)return c.result;var d=sx(),e=d.status,f=[],g,h,m=[];if(!E(33)){if(a.Zb&&a.Zb.email){var n=tx(d.elements);f=ux(n,a&&a.Vf);g=vx(f);n.length>10&&(e="3")}!a.Eh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(wx(f[p],!!a.Ae,!!a.Be));m=m.slice(0,10)}else if(a.Zb){}g&&(h=wx(g,!!a.Ae,!!a.Be));var G={elements:m,
xj:h,status:e};rx[b]={timestamp:Ab(),result:G};return G},Ex=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},Gx=function(a){var b=Fx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},Fx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},Cx=function(a,b,c){var d=a.element,e={ka:a.ka,type:a.wa,tagName:d.tagName};b&&(e.querySelector=Hx(d));c&&(e.isVisible=!fx(d));return e},wx=function(a,b,c){return Cx({element:a.element,ka:a.ka,wa:Bx.jc},b,c)},qx=function(a){var b=!(a==null||!a.Ae)+"."+!(a==null||!a.Be);a&&a.Vf&&a.Vf.length&&(b+="."+a.Vf.join("."));a&&a.Zb&&(b+="."+a.Zb.email+"."+a.Zb.phone+"."+a.Zb.address);return b},vx=function(a){if(a.length!==0){var b;b=Ix(a,function(c){return!Jx.test(c.ka)});b=Ix(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=Ix(b,function(c){return!fx(c.element)});return b[0]}},ux=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&xi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},Ix=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},Hx=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=Hx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},tx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(Kx);if(f){var g=f[0],h;if(x.location){var m=Xk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,ka:g})}}}return b},sx=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Lx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Mx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||E(33)&&Nx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Kx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,ox=/@(gmail|googlemail)\./i,Jx=/support|noreply/i,Lx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Mx=
["BR"],Ox=wg('',2),Bx={jc:"1",Cd:"2",vd:"3",Bd:"4",Je:"5",Mf:"6",fh:"7",Mi:"8",Ih:"9",Hi:"10"},rx={},Nx=["INPUT","SELECT"],Px=Fx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var hg;var sy=Number('')||5,ty=Number('')||50,uy=qb();
var wy=function(a,b){a&&(vy("sid",a.targetId,b),vy("cc",a.clientCount,b),vy("tl",a.totalLifeMs,b),vy("hc",a.heartbeatCount,b),vy("cl",a.clientLifeMs,b))},vy=function(a,b,c){b!=null&&c.push(a+"="+b)},xy=function(){var a=A.referrer;if(a){var b;return Vk(al(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},yy="https://"+Yi(21,"www.googletagmanager.com")+"/a?",Ay=function(){this.R=zy;this.N=0};Ay.prototype.H=function(a,b,c,d){var e=xy(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&vy("si",a.gg,g);vy("m",0,g);vy("iss",f,g);vy("if",c,g);wy(b,g);d&&vy("fm",encodeURIComponent(d.substring(0,ty)),g);this.P(g);};Ay.prototype.C=function(a,b,c,d,e){var f=[];vy("m",1,f);vy("s",a,f);vy("po",xy(),f);b&&(vy("st",b.state,f),vy("si",b.gg,f),vy("sm",b.mg,f));wy(c,f);vy("c",d,f);e&&vy("fm",encodeURIComponent(e.substring(0,
ty)),f);this.P(f);};Ay.prototype.P=function(a){a=a===void 0?[]:a;!pl||this.N>=sy||(vy("pid",uy,a),vy("bc",++this.N,a),a.unshift("ctid="+lg.ctid+"&t=s"),this.R(""+yy+a.join("&")))};var By=Number('')||500,Cy=Number('')||5E3,Dy=Number('20')||10,Ey=Number('')||5E3;function Fy(a){return a.performance&&a.performance.now()||Date.now()}
var Gy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{jm:function(){},km:function(){},im:function(){},onFailure:function(){}}:h;this.wo=f;this.C=g;this.N=h;this.fa=this.ma=this.heartbeatCount=this.uo=0;this.gh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=Fy(this.C);this.mg=Fy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Da()};e.prototype.getState=function(){return{state:this.state,
gg:Math.round(Fy(this.C)-this.gg),mg:Math.round(Fy(this.C)-this.mg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.mg=Fy(this.C))};e.prototype.Gl=function(){return String(this.uo++)};e.prototype.Da=function(){var f=this;this.heartbeatCount++;this.Va({type:0,clientId:this.id,requestId:this.Gl(),maxDelay:this.hh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.fa++,g.isDead||f.fa>Dy){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.so();var n,p;(p=(n=f.N).im)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Kl();else{if(f.heartbeatCount>g.stats.heartbeatCount+Dy){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.gh){var u,v;(v=(u=f.N).km)==null||v.call(u)}else{f.gh=!0;var w,y;(y=(w=f.N).jm)==null||y.call(w)}f.fa=0;f.xo();f.Kl()}}})};e.prototype.hh=function(){return this.state===2?
Cy:By};e.prototype.Kl=function(){var f=this;this.C.setTimeout(function(){f.Da()},Math.max(0,this.hh()-(Fy(this.C)-this.ma)))};e.prototype.Bo=function(f,g,h){var m=this;this.Va({type:1,clientId:this.id,requestId:this.Gl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Va=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Kf(t,7)},(p=f.maxDelay)!=null?p:Ey),r={request:f,Am:g,vm:m,Vp:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ma=Fy(this.C);f.vm=!1;this.wo(f.request)};e.prototype.xo=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.vm&&this.sendRequest(h)}};e.prototype.so=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Kf(this.H[g.value],this.R)};e.prototype.Kf=function(f,g){this.rb(f);var h=f.request;h.failure={failureType:g};f.Am(h)};e.prototype.rb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Vp)};e.prototype.Bp=function(f){this.ma=Fy(this.C);var g=this.H[f.requestId];if(g)this.rb(g),g.Am(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var Hy;
var Iy=function(){Hy||(Hy=new Ay);return Hy},zy=function(a){xn(zn(Zm.X.Oc),function(){Kc(a)})},Jy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Ky=function(a){var b=a,c=Vj.Da;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Ly=function(a){var b=En(An.Z.zl);return b&&b[a]},My=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.fa=null;this.initTime=c;this.C=15;this.N=this.So(a);x.setTimeout(function(){f.initialize()},1E3);Nc(function(){f.Mp(a,b,e)})};k=My.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),gg:this.initTime,mg:Math.round(Ab())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Bo(a,b,c)};k.getState=function(){return this.N.getState().state};k.Mp=function(a,b,c){var d=x.location.origin,e=this,
f=Ic();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Jy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Ic(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Bp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.So=function(a){var b=this,c=Gy(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{jm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},km:function(){},im:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Ny(){var a=kg(hg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Oy(a,b){var c=Math.round(Ab());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Ny()||E(168))return;uk()&&(a=""+d+tk()+"/_/service_worker");var e=Ky(a);if(e===null||Ly(e.origin))return;if(!vc()){Iy().H(void 0,void 0,6);return}var f=new My(e,!!a,c||Math.round(Ab()),Iy(),b);Fn(An.Z.zl)[e.origin]=f;}
var Py=function(a,b,c,d){var e;if((e=Ly(a))==null||!e.delegate){var f=vc()?16:6;Iy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Ly(a).delegate(b,c,d);};
function Qy(a,b,c,d,e){var f=Ky();if(f===null){d(vc()?16:6);return}var g,h=(g=Ly(f.origin))==null?void 0:g.initTime,m=Math.round(Ab()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Py(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Ry(a,b,c,d){var e=Ky(a);if(e===null){d("_is_sw=f"+(vc()?16:6)+"te");return}var f=b?1:0,g=Math.round(Ab()),h,m=(h=Ly(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);Py(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Ly(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Sy(a){if(E(10)||uk()||Vj.N||il(a.D)||E(168))return;Oy(void 0,E(131));};var Ty="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Uy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Vy(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=ma(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Wy(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function Xy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Yy(a){if(!Xy(a))return null;var b=Uy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Ty).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var $y=function(a,b){if(a)for(var c=Zy(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;U(b,f,c[f])}},Zy=function(a){var b={};b[J.m.rf]=a.architecture;b[J.m.tf]=a.bitness;a.fullVersionList&&(b[J.m.uf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[J.m.vf]=a.mobile?"1":"0";b[J.m.wf]=a.model;b[J.m.xf]=a.platform;b[J.m.yf]=a.platformVersion;b[J.m.zf]=a.wow64?"1":"0";return b},az=function(a){var b=0,c=function(h,
m){try{a(h,m)}catch(n){}},d=x,e=Vy(d);if(e)c(e);else{var f=Wy(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var g=d.setTimeout(function(){c.hg||(c.hg=!0,L(106),c(null,Error("Timeout")))},b);f.then(function(h){c.hg||(c.hg=!0,L(104),d.clearTimeout(g),c(h))}).catch(function(h){c.hg||(c.hg=!0,L(105),d.clearTimeout(g),c(null,h))})}else c(null)}},cz=function(){var a=x;if(Xy(a)&&(bz=Ab(),!Wy(a))){var b=Yy(a);b&&(b.then(function(){L(95)}),b.catch(function(){L(96)}))}},bz;function dz(a){var b=a.location.href;if(a===a.top)return{url:b,Rp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Rp:c}};
var ez=function(){return[J.m.U,J.m.V]},fz=function(a){E(24)&&a.eventName===J.m.qa&&ew(a,K.J.Ha)&&!R(a,Q.A.da)&&!a.D.isGtmEvent?Fw(a.target,a.D):ew(a,K.J.Kj)&&(Fw(a.target,a.D),a.isAborted=!0)},hz=function(a){var b;if(a.eventName!=="gtag.config"&&R(a,Q.A.yl))switch(R(a,Q.A.ia)){case K.J.Na:b=97;E(223)?S(a,Q.A.Ba,!1):gz(a);break;case K.J.jb:b=98;E(223)?S(a,Q.A.Ba,!1):gz(a);break;case K.J.W:b=99}!R(a,Q.A.Ba)&&b&&L(b);R(a,Q.A.Ba)===!0&&(a.isAborted=!0)},iz=function(a){if(!R(a,Q.A.da)&&E(30)&&ew(a,[K.J.W])){var b=
xv();wv(b)&&(U(a,J.m.ld,"1"),S(a,Q.A.qg,!0))}},jz=function(a){ew(a,[K.J.W])&&a.D.eventMetadata[Q.A.yd]&&U(a,J.m.al,!0)},kz=function(a){var b=P(ez());switch(R(a,Q.A.ia)){case K.J.jb:case K.J.Na:a.isAborted=!b||!!R(a,Q.A.da);break;case K.J.na:a.isAborted=!b;break;case K.J.W:R(a,Q.A.da)&&U(a,J.m.da,!0)}},lz=function(a,b){if((Vj.C||E(168))&&P(ez())&&!pw(a,"ccd_enable_cm",!1)){var c=function(m){var n=R(a,Q.A.Wg);n?n.push(m):S(a,Q.A.Wg,[m])};E(62)&&c(102696396);if(E(63)||E(168)){c(102696397);var d=R(a,
Q.A.ib);S(a,Q.A.ah,!0);S(a,Q.A.Hf,!0);if(kj(d)){c(102780931);S(a,Q.A.Di,!0);var e=b||Ks(),f={},g={eventMetadata:(f[Q.A.xd]=K.J.Na,f[Q.A.ib]=d,f[Q.A.Jl]=e,f[Q.A.Hf]=!0,f[Q.A.ah]=!0,f[Q.A.Di]=!0,f[Q.A.Wg]=[102696397,102780931],f),noGtmEvent:!0},h=Uw(a.target.destinationId,a.eventName,a.D.C);Xw(h,a.D.eventId,g);S(a,Q.A.ib);return e}}}},mz=function(a){if(ew(a,[K.J.W])){var b=R(a,Q.A.xa),c=Qw(b),d=lz(a,c),e=c||d;if(e&&!Wv(a,J.m.La)){var f=Ks(Wv(a,J.m.oc));U(a,J.m.La,f);fb("GTAG_EVENT_FEATURE_CHANNEL",
12)}e&&(U(a,J.m.Wb,e),S(a,Q.A.xl,!0))}},nz=function(a){Sy(a)},oz=function(a){if(ew(a,[K.J.W,K.J.na,K.J.jb,K.J.Na])&&R(a,Q.A.Wc)&&P(J.m.U)){var b=R(a,Q.A.ia)===K.J.na,c=!E(4);if(!b||c){var d=R(a,Q.A.ia)===K.J.W&&a.eventName!==J.m.Eb,e=R(a,Q.A.xa);rt(e,d);P(J.m.V)&&U(a,J.m.Pb,pt[st(e.prefix)])}}},pz=function(a){ew(a,[K.J.W,K.J.jb,K.J.Na])&&vw(a)},qz=function(a){ew(a,[K.J.W])&&S(a,Q.A.te,!!R(a,Q.A.yc)&&!P(ez()))},rz=function(a){ew(a,[K.J.W])&&dt(!1)._up==="1"&&U(a,J.m.Og,!0)},sz=function(a){if(ew(a,
[K.J.W,K.J.na])){var b=Tv();b!==void 0&&U(a,J.m.Af,b||"error");var c=wr();c&&U(a,J.m.kd,c);var d=vr();d&&U(a,J.m.sd,d)}},tz=function(a){if(ew(a,[K.J.W,K.J.na])){var b=x;if(b.__gsaExp&&b.__gsaExp.id){var c=b.__gsaExp.id;if(lb(c))try{var d=Number(c());isNaN(d)||U(a,J.m.Ck,d)}catch(e){}}}},uz=function(a){Ow(a);},vz=function(a){E(47)&&ew(a,K.J.W)&&(a.copyToHitData(J.m.Th),a.copyToHitData(J.m.Uh),a.copyToHitData(J.m.Sh))},wz=function(a){ew(a,
K.J.W)&&(a.copyToHitData(J.m.lf),a.copyToHitData(J.m.Ze),a.copyToHitData(J.m.je),a.copyToHitData(J.m.cf),a.copyToHitData(J.m.fd),a.copyToHitData(J.m.be))},xz=function(a){if(ew(a,[K.J.W,K.J.na,K.J.jb,K.J.Na])){var b=a.D;if(ew(a,[K.J.W,K.J.na])){var c=O(b,J.m.Vb);c!==!0&&c!==!1||U(a,J.m.Vb,c)}Dr(b)?U(a,J.m.xc,!1):(U(a,J.m.xc,!0),ew(a,K.J.na)&&(a.isAborted=!0))}},yz=function(a){if(ew(a,[K.J.W,K.J.na])){var b=R(a,Q.A.ia)===K.J.W;b&&a.eventName!==J.m.lb||(a.copyToHitData(J.m.sa),b&&(a.copyToHitData(J.m.Eg),
a.copyToHitData(J.m.Cg),a.copyToHitData(J.m.Dg),a.copyToHitData(J.m.Bg),U(a,J.m.gk,a.eventName),E(113)&&(a.copyToHitData(J.m.od),a.copyToHitData(J.m.md),a.copyToHitData(J.m.nd))))}},zz=function(a){var b=a.D;if(!E(6)){var c=b.getMergedValues(J.m.oa);U(a,J.m.Pg,Kb(ld(c)?c:{}))}var d=b.getMergedValues(J.m.oa,1,Lo(Mq.C[J.m.oa])),e=b.getMergedValues(J.m.oa,2);U(a,J.m.Ub,Kb(ld(d)?d:{},"."));U(a,J.m.Tb,Kb(ld(e)?e:{},"."))},Az=function(a){if(a!=null){var b=String(a).substring(0,512),c=b.indexOf("#");return c===
-1?b:b.substring(0,c)}return""},Bz=function(a){ew(a,K.J.W)&&P(J.m.U)&&rw(a)},Cz=function(a){if(a.eventName===J.m.Eb&&!a.D.isGtmEvent){if(!R(a,Q.A.da)&&ew(a,K.J.W)){var b=O(a.D,J.m.Ic);if(typeof b!=="function")return;var c=String(O(a.D,J.m.rc)),d=Wv(a,c),e=O(a.D,c);c===J.m.mb||c===J.m.Pb?dw({Km:c,callback:b,gm:e},R(a,Q.A.xa),R(a,Q.A.yc),sv):b(d||e)}a.isAborted=!0}},Dz=function(a){if(!pw(a,"hasPreAutoPiiCcdRule",!1)&&ew(a,K.J.W)&&P(J.m.U)){var b=O(a.D,J.m.Zh)||{},c=String(Wv(a,J.m.oc)),d=b[c],e=Wv(a,
J.m.Ye),f;if(!(f=Pk(d)))if(vo()){var g=dx("AW-"+e);f=!!g&&!!g.preAutoPii}else f=!1;if(f){var h=Ab(),m=Dx({Ae:!0,Be:!0,Eh:!0});if(m.elements.length!==0){for(var n=[],p=0;p<m.elements.length;++p){var q=m.elements[p];n.push(q.querySelector+"*"+px(q)+"*"+q.type)}U(a,J.m.oi,n.join("~"));var r=m.xj;r&&(U(a,J.m.ri,r.querySelector),U(a,J.m.ni,px(r)));U(a,J.m.mi,String(Ab()-h));U(a,J.m.si,m.status)}}}},Ez=function(a){if(a.eventName===J.m.qa&&!R(a,Q.A.da)&&(S(a,Q.A.bo,!0),ew(a,K.J.W)&&S(a,Q.A.Ba,!0),ew(a,K.J.na)&&
(O(a.D,J.m.bd)===!1||O(a.D,J.m.qb)===!1)&&S(a,Q.A.Ba,!0),ew(a,K.J.Fi))){var b=O(a.D,J.m.Ta)||{},c=O(a.D,J.m.Gb),d=R(a,Q.A.Wc),e=R(a,Q.A.hb),f=R(a,Q.A.yc),g={xe:d,Ce:b,Ge:c,Pa:e,D:a.D,De:f,Jm:O(a.D,J.m.Ma)},h=R(a,Q.A.xa);Zv(g,h);Fw(a.target,a.D);var m={Ti:!1,De:f,targetId:a.target.id,D:a.D,Rc:d?h:void 0,xh:d,Tl:Wv(a,J.m.Pg),bj:Wv(a,J.m.Ub),Xi:Wv(a,J.m.Tb),fj:Wv(a,J.m.Jc)};zw(m);a.isAborted=!0}},Fz=function(a){ew(a,[K.J.W,K.J.na])&&(a.D.isGtmEvent?R(a,Q.A.ia)!==K.J.W&&a.eventName&&U(a,J.m.jd,a.eventName):
U(a,J.m.jd,a.eventName),tb(a.D.C,function(b,c){ui[b.split(".")[0]]||U(a,b,c)}))},Gz=function(a){if(!R(a,Q.A.ah)){var b=!R(a,Q.A.yl)&&ew(a,[K.J.W,K.J.Na]),c=!pw(a,"ccd_add_1p_data",!1)&&ew(a,K.J.jb);if((b||c)&&P(J.m.U)){var d=R(a,Q.A.ia)===K.J.W,e=a.D,f=void 0,g=O(e,J.m.eb);if(d){var h=O(e,J.m.Ag)===!0,m=O(e,J.m.Zh)||{},n=String(Wv(a,J.m.oc)),p=m[n];p&&fb("GTAG_EVENT_FEATURE_CHANNEL",19);if(a.D.isGtmEvent&&p===void 0)return;if(h||p){var q;var r;p?r=Mk(p,g):(r=x.enhanced_conversion_data)&&fb("GTAG_EVENT_FEATURE_CHANNEL",
8);var t=(p||{}).enhanced_conversions_mode,u;if(r){if(t==="manual")switch(r._tag_mode){case "CODE":u="c";break;case "AUTO":u="a";break;case "MANUAL":u="m";break;default:u="c"}else u=t==="automatic"?Pk(p)?"a":"m":"c";q={ka:r,Im:u}}else q={ka:r,Im:void 0};var v=q,w=v.Im;f=v.ka;Ei(f);U(a,J.m.wc,w)}}S(a,Q.A.ib,f)}}},Hz=function(a){if(pw(a,"ccd_add_1p_data",!1)&&P(ez())){var b=a.D.H[J.m.Ug];if(Nk(b)){var c=O(a.D,J.m.eb);if(c===null)S(a,Q.A.ve,null);else if(b.enable_code&&ld(c)&&(Ei(c),S(a,Q.A.ve,c)),ld(b.selectors)){var d=
{};S(a,Q.A.mh,Lk(b.selectors,d,E(178)));E(60)&&a.mergeHitDataForKey(J.m.sc,{ec_data_layer:Hk(d)})}}}},Iz=function(a){S(a,Q.A.Wc,O(a.D,J.m.Ra)!==!1);S(a,Q.A.xa,Uv(a));S(a,Q.A.yc,O(a.D,J.m.ya)!=null&&O(a.D,J.m.ya)!==!1);S(a,Q.A.Hh,Dr(a.D))},Jz=function(a){if(ew(a,[K.J.W,K.J.na])&&!E(189)&&E(34)){var b=function(d){return E(35)?(fb("fdr",d),!0):!1};if(P(J.m.U)||b(0))if(P(J.m.V)||b(1))if(O(a.D,J.m.nb)!==!1||b(2))if(Dr(a.D)||b(3))if(O(a.D,J.m.bd)!==!1||b(4)){var c;E(36)?c=a.eventName===J.m.qa?O(a.D,J.m.qb):
void 0:c=O(a.D,J.m.qb);if(c!==!1||b(5))if(Ul()||b(6))E(35)&&jb()?(U(a,J.m.nk,ib("fdr")),delete eb.fdr):(U(a,J.m.pk,"1"),S(a,Q.A.ih,!0))}}},Kz=function(a){ew(a,[K.J.W])&&P(J.m.V)&&(x._gtmpcm===!0||Vw()?U(a,J.m.dd,"2"):E(39)&&Tl("attribution-reporting")&&U(a,J.m.dd,"1"))},Lz=function(a){if(!Xy(x))L(87);else if(bz!==void 0){L(85);var b=Vy(x);b?$y(b,a):L(86)}},Mz=function(a){if(ew(a,[K.J.W,K.J.na,K.J.Ha,K.J.jb,K.J.Na])&&P(J.m.V)){a.copyToHitData(J.m.Ma);var b=En(An.Z.Bl);if(b===void 0)Dn(An.Z.Cl,!0);
else{var c=En(An.Z.jh);U(a,J.m.qf,c+"."+b)}}},Nz=function(a){ew(a,[K.J.W,K.J.na])&&(a.copyToHitData(J.m.La),a.copyToHitData(J.m.za),a.copyToHitData(J.m.Sa))},Oz=function(a){if(!R(a,Q.A.da)&&ew(a,[K.J.W,K.J.na])){var b=Ql(!1);U(a,J.m.Jc,b);var c=O(a.D,J.m.Ca);c||(c=b===1?x.top.location.href:x.location.href);U(a,J.m.Ca,Az(c));a.copyToHitData(J.m.Ya,A.referrer);U(a,J.m.Fb,Xv());a.copyToHitData(J.m.Ab);var d=ex();U(a,J.m.Nc,d.width+"x"+d.height);var e=Sl(),f=dz(e);f.url&&c!==f.url&&U(a,J.m.ki,Az(f.url))}},
Pz=function(a){ew(a,[K.J.W,K.J.na])},Qz=function(a){if(ew(a,[K.J.W,K.J.na,K.J.jb,K.J.Na])){var b=Wv(a,J.m.oc),c=O(a.D,J.m.Ph)===!0;c&&S(a,Q.A.oo,!0);switch(R(a,Q.A.ia)){case K.J.W:!c&&b&&gz(a);(Ok()||Cc())&&S(a,Q.A.ne,!0);Ok()||Cc()||S(a,Q.A.Ci,!0);break;case K.J.jb:case K.J.Na:!c&&b&&(a.isAborted=!0);break;case K.J.na:!c&&b||gz(a)}ew(a,[K.J.W,K.J.na])&&(R(a,Q.A.ne)?U(a,J.m.yi,"www.google.com"):U(a,J.m.yi,"www.googleadservices.com"))}},Rz=function(a){var b=a.target.ids[Op[0]];if(b){U(a,J.m.Ye,b);
var c=a.target.ids[Op[1]];c&&U(a,J.m.oc,c)}else a.isAborted=!0},gz=function(a){R(a,Q.A.Dl)||S(a,Q.A.Ba,!1)};function Uz(a,b){var c=!!uk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?tk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(90)&&uo()?Sz():""+tk()+"/ag/g/c":Sz();case 16:return c?E(90)&&uo()?Tz():""+tk()+"/ga/g/c":Tz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
tk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?tk()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Co+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?tk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return c?tk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?tk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return c?tk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?tk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return E(205)?"https://www.google.com/measurement/conversion/":
c?tk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?tk()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:mc(a,"Unknown endpoint")}};function Vz(a){a=a===void 0?[]:a;return Wj(a).join("~")}function Wz(){if(!E(118))return"";var a,b;return(((a=Nm(Cm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Xz(a,b){b&&tb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var Zz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Wv(a,g),m=Yz[g];m&&h!==void 0&&h!==""&&(!R(a,Q.A.te)||g!==J.m.Zc&&g!==J.m.gd&&g!==J.m.ae&&g!==J.m.Pe||(h="0"),d(m,h))}d("gtm",Sr({Pa:R(a,Q.A.hb)}));Er()&&d("gcs",Fr());d("gcd",Jr(a.D));Mr()&&d("dma_cps",Kr());d("dma",Lr());hr(pr())&&d("tcfd",Nr());Vz()&&d("tag_exp",Vz());Wz()&&d("ptag_exp",Wz());if(R(a,Q.A.qg)){d("tft",
Ab());var n=Zc();n!==void 0&&d("tfd",Math.round(n))}E(24)&&d("apve","1");(E(25)||E(26))&&d("apvf",Wc()?E(26)?"f":"sb":"nf");rn[Zm.X.Fa]!==Ym.Ia.pe||un[Zm.X.Fa].isConsentGranted()||(c.limited_ads="1");b(c)},$z=function(a,b,c){var d=b.D;ep({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Oa:{eventId:d.eventId,priorityId:d.priorityId},qh:{eventId:R(b,Q.A.He),priorityId:R(b,Q.A.Ie)}})},aA=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,
priorityId:b.D.priorityId};$z(a,b,c);sm(d,a,void 0,{Ch:!0,method:"GET"},function(){},function(){rm(d,a+"&img=1")})},bA=function(a){var b=Cc()||Ac()?"www.google.com":"www.googleadservices.com",c=[];tb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},cA=function(a){Zz(a,function(b){if(R(a,Q.A.ia)===K.J.Ha){var c=[];a.target.destinationId&&c.push("tid="+a.target.destinationId);
tb(b,function(r,t){c.push(r+"="+t)});var d=P([J.m.U,J.m.V])?45:46,e=Uz(d)+"?"+c.join("&");$z(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(E(26)&&Wc()){sm(g,e,void 0,{Ch:!0},function(){},function(){rm(g,e+"&img=1")});var h=P([J.m.U,J.m.V]),m=Wv(a,J.m.ld)==="1",n=Wv(a,J.m.Rh)==="1";if(h&&m&&!n){var p=bA(b),q=Cc()||Ac()?58:57;aA(p,a,q)}}else qm(g,e)||rm(g,e+"&img=1");if(lb(a.D.onSuccess))a.D.onSuccess()}})},dA={},Yz=(dA[J.m.da]="gcu",
dA[J.m.nc]="gclgb",dA[J.m.mb]="gclaw",dA[J.m.Ne]="gad_source",dA[J.m.Oe]="gad_source_src",dA[J.m.Zc]="gclid",dA[J.m.fk]="gclsrc",dA[J.m.Pe]="gbraid",dA[J.m.ae]="wbraid",dA[J.m.Pb]="auid",dA[J.m.hk]="rnd",dA[J.m.Rh]="ncl",dA[J.m.Vh]="gcldc",dA[J.m.gd]="dclid",dA[J.m.Tb]="edid",dA[J.m.jd]="en",dA[J.m.kd]="gdpr",dA[J.m.Ub]="gdid",dA[J.m.ee]="_ng",dA[J.m.ff]="gpp_sid",dA[J.m.hf]="gpp",dA[J.m.jf]="_tu",dA[J.m.Dk]="gtm_up",dA[J.m.Jc]="frm",dA[J.m.ld]="lps",dA[J.m.Pg]="did",dA[J.m.Gk]="navt",dA[J.m.Ca]=
"dl",dA[J.m.Ya]="dr",dA[J.m.Fb]="dt",dA[J.m.Nk]="scrsrc",dA[J.m.qf]="ga_uid",dA[J.m.sd]="gdpr_consent",dA[J.m.ji]="u_tz",dA[J.m.Ma]="uid",dA[J.m.Af]="us_privacy",dA[J.m.xc]="npa",dA);var eA={};eA.O=ks.O;var fA={jr:"L",qo:"S",Ar:"Y",Oq:"B",Yq:"E",gr:"I",xr:"TC",er:"HTC"},gA={qo:"S",Xq:"V",Rq:"E",wr:"tag"},hA={},iA=(hA[eA.O.Oi]="6",hA[eA.O.Pi]="5",hA[eA.O.Ni]="7",hA);function jA(){function a(c,d){var e=ib(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var kA=!1;
function DA(a){}function EA(a){}
function FA(){}function GA(a){}
function HA(a){}function IA(a){}
function JA(){}function KA(a,b){}
function LA(a,b,c){}
function MA(){};var NA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function OA(a,b,c,d,e,f,g,h){var m=ma(Object,"assign").call(Object,{},NA);c&&(m.body=c,m.method="POST");ma(Object,"assign").call(Object,m,e);h==null||hm(h);x.fetch(b,m).then(function(n){h==null||im(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function t(){p.read().then(function(u){var v;v=u.done;var w=q.decode(u.value,{stream:!v});PA(d,w);v?(f==null||f(),r()):t()}).catch(function(){r()})}t()})}}).catch(function(){h==null||im(h);
g?g():E(128)&&(b+="&_z=retryFetch",c?qm(a,b,c):pm(a,b))})};var QA=function(a){this.P=a;this.C=""},RA=function(a,b){a.H=b;return a},SA=function(a,b){a.N=b;return a},PA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}TA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},UA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};TA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},TA=function(a,b){b&&(VA(b.send_pixel,b.options,a.P),VA(b.create_iframe,b.options,a.H),VA(b.fetch,b.options,a.N))};function WA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function VA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=ld(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var XA=function(a,b){this.Zp=a;this.timeoutMs=b;this.Xa=void 0},hm=function(a){a.Xa||(a.Xa=setTimeout(function(){a.Zp();a.Xa=void 0},a.timeoutMs))},im=function(a){a.Xa&&(clearTimeout(a.Xa),a.Xa=void 0)};
var YA=function(a,b){return R(a,Q.A.Ci)&&(b===3||b===6)},ZA=function(a){return new QA(function(b,c){var d;if(c.fallback_url){var e=c.fallback_url,f=c.fallback_url_method;d=function(){switch(f){case "send_pixel":rm(a,e);break;default:sm(a,e)}}}rm(a,b,void 0,d)})},$A=function(a){if(a!==void 0)return Math.round(a/10)*10},aB=function(a){for(var b={},c=0;c<a.length;c++){var d=a[c],e=void 0;if(d.hasOwnProperty("google_business_vertical")){e=d.google_business_vertical;var f={};b[e]=b[e]||(f.google_business_vertical=
e,f)}else e="",b.hasOwnProperty(e)||(b[e]={});var g=b[e],h;for(h in d)h!=="google_business_vertical"&&(h in g||(g[h]=[]),g[h].push(d[h]))}return Object.keys(b).map(function(m){return b[m]})},bB=function(a){var b=Wv(a,J.m.sa);if(!b||!b.length)return[];for(var c=[],d=0;d<b.length;++d){var e=b[d];if(e){var f={};c.push((f.id=hi(e),f.origin=e.origin,f.destination=e.destination,f.start_date=e.start_date,f.end_date=e.end_date,f.location_id=e.location_id,f.google_business_vertical=e.google_business_vertical,
f))}}return c},hi=function(a){a.item_id!=null&&(a.id!=null?(L(138),a.id!==a.item_id&&L(148)):L(153));return E(20)?ii(a):a.id},dB=function(a){if(!a||typeof a!=="object"||typeof a.join==="function")return"";var b=[];tb(a,function(c,d){var e,f;if(Array.isArray(d)){for(var g=[],h=0;h<d.length;++h){var m=cB(d[h]);m!==void 0&&g.push(m)}f=g.length!==0?g.join(","):void 0}else f=cB(d);e=f;var n=cB(c);n&&e!==void 0&&b.push(n+"="+e)});return b.join(";")},cB=function(a){var b=typeof a;if(a!=null&&b!=="object"&&
b!=="function")return String(a).replace(/,/g,"\\,").replace(/;/g,"\\;").replace(/=/g,"\\=")},eB=function(a,b){var c=[],d=function(g,h){var m=Dg[g]===!0;h==null||!m&&h===""||(h===!0&&(h=1),h===!1&&(h=0),c.push(g+"="+encodeURIComponent(h)))},e=R(a,Q.A.ia);if(e===K.J.W||e===K.J.na||e===K.J.Cf){var f=b.random||R(a,Q.A.fb);d("random",f);delete b.random}tb(b,d);return c.join("&")},fB=function(a,b,c){if(R(a,Q.A.ih)){R(a,Q.A.ia)===K.J.W&&(b.ct_cookie_present=0);var d=eB(a,b);return{zc:"https://td.doubleclick.net/td/rul/"+
c+"?"+d,format:4,Qa:!1,endpoint:44}}},hB=function(a,b){var c=P(gB)?54:55,d=Uz(c),e=eB(a,b);return{zc:d+"?"+e,format:5,Qa:!0,endpoint:c}},iB=function(a,b,c){var d=Uz(21),e=eB(a,b);return{zc:kl(d+"/"+c+"?"+e),format:1,Qa:!0,endpoint:21}},jB=function(a,b,c){var d=eB(a,b);return{zc:Uz(11)+"/"+c+"?"+d,format:1,Qa:!0,endpoint:11}},lB=function(a,b,c){if(R(a,Q.A.ne)&&P(gB))return kB(a,b,c,"&gcp=1&ct_cookie_present=1",2)},nB=function(a,b,c){if(R(a,Q.A.xl)){var d=22;P(gB)?R(a,Q.A.ne)&&(d=23):d=60;var e=!!R(a,
Q.A.Hf);R(a,Q.A.ah)&&(b=ma(Object,"assign").call(Object,{},b),delete b.item);var f=eB(a,b),g=mB(a),h=Uz(d)+"/"+c+"/?"+(""+f+g);e&&(h=kl(h));return{zc:h,format:2,Qa:!0,endpoint:d}}},oB=function(a,b,c,d){for(var e=[],f=b.data||"",g=0;g<d.length;g++){var h=dB(d[g]);b.data=""+f+(f&&h?";":"")+h;e.push(kB(a,b,c));var m=fB(a,b,c);m&&e.push(m);S(a,Q.A.fb,R(a,Q.A.fb)+1)}return e},qB=function(a,b,c){if(uk()&&E(148)&&P(gB)){var d=pB(a).endpoint,e=R(a,Q.A.fb)+1;b=ma(Object,"assign").call(Object,{},b,{random:e,
adtest:"on",exp_1p:"1"});var f=eB(a,b),g=mB(a),h;a:{switch(d){case 5:h=tk()+"/as/d/pagead/conversion";break a;case 6:h=tk()+"/gs/pagead/conversion";break a;case 8:h=tk()+"/g/d/pagead/1p-conversion";break a;default:mc(d,"Unknown endpoint")}h=void 0}return{zc:h+"/"+c+"/?"+f+g,format:3,Qa:!0,endpoint:d}}},kB=function(a,b,c,d,e){d=d===void 0?"":d;var f=Uz(9),g=eB(a,b);return{zc:f+"/"+c+"/?"+g+d,format:e!=null?e:3,Qa:!0,endpoint:9}},rB=function(a,b,c){var d=pB(a).endpoint,e=P(gB),f="&gcp=1&sscte=1&ct_cookie_present=1";
uk()&&E(148)&&P(gB)&&(f="&exp_ph=1&gcp=1&sscte=1&ct_cookie_present=1",b=ma(Object,"assign").call(Object,{},b,{exp_1p:"1"}));var g=eB(a,b),h=mB(a),m=e?37:162,n={zc:Uz(d)+"/"+c+"/?"+g+h,format:E(m)?Wc()?e?6:5:2:3,Qa:!0,endpoint:d};P(J.m.V)&&(n.attributes={attributionsrc:""});if(e&&R(a,Q.A.Ci)){var p=E(175)?Uz(8):""+jl("https://www.google.com",!0,"")+"/pagead/1p-conversion";n.jp=p+"/"+c+"/"+("?"+g+f);n.Wf=8}return n},pB=function(a){var b="/pagead/conversion",c="https://www.googleadservices.com",d=5;
P(gB)?R(a,Q.A.ne)&&(c="https://www.google.com",b="/pagead/1p-conversion",d=8):(c="https://pagead2.googlesyndication.com",d=6);return{Ir:c,Er:b,endpoint:d}},mB=function(a){return R(a,Q.A.ne)?"&gcp=1&sscte=1&ct_cookie_present=1":""},sB=function(a,b){var c=R(a,Q.A.ia),d=Wv(a,J.m.Ye),e=[],f=function(h){h&&e.push(h)};switch(c){case K.J.W:e.push(rB(a,b,d));f(qB(a,b,d));f(nB(a,b,d));f(lB(a,b,d));f(fB(a,b,d));break;case K.J.na:var g=aB(bB(a));g.length?e.push.apply(e,ya(oB(a,b,d,g))):(e.push(kB(a,b,d)),f(fB(a,
b,d)));break;case K.J.jb:e.push(jB(a,b,d));break;case K.J.Na:e.push(iB(a,b,d));break;case K.J.Cf:e.push(hB(a,b))}return{Jp:e}},vB=function(a,b,c,d,e,f,g,h){var m=YA(c,b),n=P(gB),p=R(c,Q.A.ia);m||tB(a,c,e);EA(c.D.eventId);var q=function(){f&&(f(),m&&tB(a,c,e))},r={destinationId:c.target.destinationId,endpoint:e,priorityId:c.D.priorityId,eventId:c.D.eventId};switch(b){case 1:pm(r,a);f&&f();break;case 2:rm(r,a,q,g,h);break;case 3:var t=!1;try{t=vm(r,x,A,a,q,g,h,uB(c,dj.Eo))}catch(C){t=!1}t||vB(a,2,c,
d,e,q,g,h);break;case 4:var u="AW-"+Wv(c,J.m.Ye),v=Wv(c,J.m.oc);v&&(u=u+"/"+v);wm(r,a,u);break;case 5:var w=a;n||p!==K.J.W||(w=fm(a,"fmt",8));sm(r,w,void 0,void 0,f,g);break;case 6:var y=fm(a,"fmt",7);ql&&lm(r,2,y);var z={};"setAttributionReporting"in XMLHttpRequest.prototype&&(z={attributionReporting:wB});OA(r,y,void 0,ZA(r),z,q,g,uB(c,dj.Do))}},uB=function(a,b){if(R(a,Q.A.ia)===K.J.W){var c=fs([bs])[bs.sb];if(!(c===void 0||c<0||b<=0))return new XA(function(){ds(bs)},b)}},tB=function(a,b,c){var d=
b.D;ep({targetId:b.target.destinationId,request:{url:a,parameterEncoding:3,endpoint:c},Oa:{eventId:d.eventId,priorityId:d.priorityId},qh:{eventId:R(b,Q.A.He),priorityId:R(b,Q.A.Ie)}})},xB=function(a){if(!Wv(a,J.m.Le)||!Wv(a,J.m.Me))return"";var b=Wv(a,J.m.Le).split("."),c=Wv(a,J.m.Me).split(".");if(!b.length||!c.length||b.length!==c.length)return"";for(var d=[],e=0;e<b.length;++e)d.push(b[e]+"_"+c[e]);return d.join(".")},AB=function(a,b,c){var d=jj(R(a,Q.A.ib)),e=ij(d,c),f=e.Dj,g=e.ng,h=e.Za,m=e.ap,
n=e.encryptionKeyString,p=[];yB(c)||p.push("&em="+f);c===2&&p.push("&eme="+m);return{ng:g,Gq:p,Mr:d,Za:h,encryptionKeyString:n,Bq:function(q,r){return function(t){var u,v=r.zc;if(t){var w;w=R(a,Q.A.hb);var y=Sr({Pa:w,Cm:t});v=v.replace(b.gtm,y)}u=v;if(c===1)zB(r,a,b,u,c,q)(zj(R(a,Q.A.ib)));else{var z;var C=R(a,Q.A.ib);z=c===0?xj(C,!1):c===2?xj(C,!0,!0):void 0;var D=zB(r,a,b,u,c,q);z?z.then(D):D(void 0)}}}}},zB=function(a,b,c,d,e,f){return function(g){if(!yB(e)){var h=(g==null?0:g.Kb)?g.Kb:uj({Tc:[]}).Kb;
d+="&em="+encodeURIComponent(h)}vB(d,a.format,b,c,a.endpoint,a.Qa?f:void 0,void 0,a.attributes)}},yB=function(a){return E(125)?!0:a!==2&&a!==3?!1:Vj.C&&E(19)||E(168)?!0:!1},CB=function(a,b,c){return function(d){var e=d.Kb;yB(d.Ib?2:0)||(b.em=e);if(d.Za&&d.time!==void 0){var f,g=$A(d.time);f=["t."+(g!=null?g:""),"l."+$A(e.length)].join("~");b._ht=f}d.Za&&BB(a,b,c);
}},BB=function(a,b,c){if(a===K.J.Na){var d=R(c,Q.A.xa),e;if(!(e=R(c,Q.A.Jl))){var f;f=d||{};var g;if(P(J.m.U)){(g=Qw(f))||(g=Ks());var h=st(f.prefix);vt(f,g);delete pt[h];delete qt[h];ut(h,f.path,f.domain);e=Qw(f)}else e=void 0}b.ecsid=e}},DB=function(a,b,c,d,e){if(a)try{CB(c,d,b)(a)}catch(f){}e(d)},EB=function(a,b,c,d,e){if(a)try{a.then(CB(c,d,b)).then(function(){e(d)});return}catch(f){}e(d)},
FB=function(a){var b=js(a);if(b&&b!==1)return b&1023},GB=function(a){var b=dj.Zo;return{Jq:E(164)||(a===void 0?!1:a>=512-b&&a<512),control:a===void 0?!1:a>=1024-b&&a<1024}},JB=function(a){if(R(a,Q.A.ia)===K.J.Ha)cA(a);else{var b=E(22)?Cb(a.D.onFailure):void 0;HB(a,function(c,d){E(125)&&delete c.em;for(var e=sB(a,c).Jp,f=((d==null?void 0:d.Pr)||new IB(a)).H(e.filter(function(C){return C.Qa}).length),g={},h=0;h<e.length;g={Zi:void 0,Wf:void 0,Qa:void 0,Si:void 0,Wi:void 0},h++){var m=e[h],n=m.zc,p=
m.format;g.Qa=m.Qa;g.Si=m.attributes;g.Wi=m.endpoint;g.Zi=m.jp;g.Wf=m.Wf;var q=void 0,r=(q=d)==null?void 0:q.serviceWorker;if(r){var t=r.Bq(f,e[h]),u=r,v=u.ng,w=u.encryptionKeyString,y=""+n+u.Gq.join("");Qy(y,v,function(C){return function(D){tB(D.data,a,C.Wi);C.Qa&&typeof f==="function"&&f()}}(g),t,w)}else{var z=b;g.Zi&&g.Wf&&(z=function(C){return function(){vB(C.Zi,5,a,c,C.Wf,C.Qa?f:void 0,C.Qa?b:void 0,C.Si)}}(g));vB(n,p,a,c,g.Wi,g.Qa?f:void 0,g.Qa?z:void 0,g.Si)}}})}},wB={eventSourceEligible:!1,
triggerEligible:!0},IB=function(a){this.C=1;this.onSuccess=a.D.onSuccess};IB.prototype.H=function(a){var b=this;return Lb(function(){b.N()},a||1)};IB.prototype.N=function(){this.C--;if(lb(this.onSuccess)&&this.C===0)this.onSuccess()};var gB=[J.m.U,J.m.V],HB=function(a,b){var c=R(a,Q.A.ia),d={},e={},f=R(a,Q.A.fb);c===K.J.W||c===K.J.na?(d.cv="11",d.fst=f,d.fmt=3,d.bg="ffffff",d.guid="ON",d.async="1",d.en=a.eventName):c===K.J.Cf&&(d.cv="11",d.tid=a.target.destinationId,d.fst=f,d.fmt=6,d.en=a.eventName);
if(c===K.J.W){var g=hs();g&&(d.gcl_ctr=g)}var h=Yu(["aw","dc"]);h!=null&&(d.gad_source=h);d.gtm=Sr({Pa:R(a,Q.A.hb)});c!==K.J.na&&Er()&&(d.gcs=Fr());d.gcd=Jr(a.D);Mr()&&(d.dma_cps=Kr());d.dma=Lr();hr(pr())&&(d.tcfd=Nr());var m=function(){var Wa=(R(a,Q.A.Wg)||[]).slice(0);return function(Db){Db!==void 0&&Wa.push(Db);if(Vz()||Wa.length)d.tag_exp=Vz(Wa)}}();m();Wz()&&(d.ptag_exp=Wz());rn[Zm.X.Fa]!==Ym.Ia.pe||un[Zm.X.Fa].isConsentGranted()||(d.limited_ads="1");Wv(a,J.m.Nc)&&ei(Wv(a,J.m.Nc),d);if(Wv(a,
J.m.Ab)){var n=Wv(a,J.m.Ab);n&&(n.length===2?fi(d,"hl",n):n.length===5&&(fi(d,"hl",n.substring(0,2)),fi(d,"gl",n.substring(3,5))))}var p=R(a,Q.A.te),q=function(Wa,Db){var Ub=Wv(a,Db);Ub&&(d[Wa]=p?gv(Ub):Ub)};q("url",J.m.Ca);q("ref",J.m.Ya);q("top",J.m.ki);var r=xB(a);r&&(d.gclaw_src=r);for(var t=l(Object.keys(a.C)),u=t.next();!u.done;u=t.next()){var v=u.value,w=Wv(a,v);if(di.hasOwnProperty(v)){var y=di[v];y&&(d[y]=w)}else e[v]=w}Xz(d,Wv(a,J.m.wd));var z=Wv(a,J.m.lf);z!==void 0&&z!==""&&(d.vdnc=String(z));
var C=Wv(a,J.m.be);C!==void 0&&(d.shf=C);var D=Wv(a,J.m.fd);D!==void 0&&(d.delc=D);if(E(30)&&R(a,Q.A.qg)){d.tft=Ab();var G=Zc();G!==void 0&&(d.tfd=Math.round(G))}c!==K.J.Cf&&(d.data=dB(e));var I=Wv(a,J.m.sa);!I||c!==K.J.W&&c!==K.J.Cf||(d.iedeld=li(I),d.item=gi(I));var M=Wv(a,J.m.sc);if(M&&typeof M==="object")for(var T=l(Object.keys(M)),da=T.next();!da.done;da=T.next()){var N=da.value;d["gap."+N]=M[N]}R(a,Q.A.Di)&&(d.aecs="1");if(c!==K.J.W&&c!==K.J.jb&&c!==K.J.Na||!R(a,Q.A.ib))b(d);else if(P(J.m.V)&&
P(J.m.U)){var W;a:switch(c){case K.J.Na:W=!Vj.C&&E(68)||E(168)?!0:Vj.C;break a;default:W=!1}W&&S(a,Q.A.Hf,!0);var ia=!!R(a,Q.A.Hf),ka=FB(Wv(a,J.m.Pb)||"");if(c!==K.J.W){d.gtm=Sr({Pa:R(a,Q.A.hb),Cm:3});var Y=AB(a,d,ia?2:1);Y.Za&&BB(c,d,a);b(d,{serviceWorker:Y})}else{var X=R(a,Q.A.ib),ja=GB(ka),wa=ja.Jq,sa=ja.control;ia||(wa?m(103308613):sa&&m(103308615));if(ia||!wa){var Va=xj(X,ia,void 0,void 0,sa);EB(Va,a,c,d,b)}else DB(zj(X),a,c,d,b)}}else d.ec_mode=void 0,b(d)};var KB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),LB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},MB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},NB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function OB(){var a=Ak("gtm.allowlist")||Ak("gtm.whitelist");a&&L(9);jk&&!E(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:E(212)&&(a=void 0);KB.test(x.location&&x.location.hostname)&&(jk?L(116):(L(117),PB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Fb(xb(a),LB),c=Ak("gtm.blocklist")||Ak("gtm.blacklist");c||(c=Ak("tagTypeBlacklist"))&&L(3);c?L(8):c=[];KB.test(x.location&&x.location.hostname)&&(c=xb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));xb(c).indexOf("google")>=0&&L(2);var d=c&&Fb(xb(c),MB),e={};return function(f){var g=f&&f[jf.Ua];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=qk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(jk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){L(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=rb(d,h||[]);t&&
L(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:jk&&h.indexOf("cmpPartners")>=0?!QB():b&&b.indexOf("sandboxedScripts")!==-1?0:rb(d,NB))&&(u=!0);return e[g]=u}}function QB(){var a=kg(hg.C,lg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var PB=!1;PB=!0;function RB(a,b,c,d,e){if(!Sm(a)){d.loadExperiments=Xj();Bm(a,d,e);var f=SB(a),g=function(){Dm().container[a]&&(Dm().container[a].state=3);TB()},h={destinationId:a,endpoint:0};if(uk())tm(h,tk()+"/"+f,void 0,g);else{var m=Gb(a,"GTM-"),n=hl(),p=c?"/gtag/js":"/gtm.js",q=gl(b,p+f);if(!q){var r=Zj.wg+p;n&&xc&&m&&(r=xc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=Aw("https://","http://",r+f)}tm(h,q,void 0,g)}}}function TB(){Um()||tb(Vm(),function(a,b){UB(a,b.transportUrl,b.context);L(92)})}
function UB(a,b,c,d){if(!Tm(a))if(c.loadExperiments||(c.loadExperiments=Xj()),Um()){var e;(e=Dm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:Cm()});Dm().destination[a].state=0;Em({ctid:a,isDestination:!0},d);L(91)}else{var f;(f=Dm().destination)[a]!=null||(f[a]={context:c,state:1,parent:Cm()});Dm().destination[a].state=1;Em({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(uk())tm(g,tk()+("/gtd"+SB(a,!0)));else{var h="/gtag/destination"+SB(a,!0),m=gl(b,
h);m||(m=Aw("https://","http://",Zj.wg+h));tm(g,m)}}}function SB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);ck!=="dataLayer"&&(c+="&l="+ck);if(!Gb(a,"GTM-")||b)c=E(130)?c+(uk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Tr();hl()&&(c+="&sign="+Zj.Ki);var d=Vj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Xj().join("~")&&(c+="&tag_exp="+Xj().join("~"));return c};var VB=function(){this.H=0;this.C={}};VB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Fe:c};return d};VB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var XB=function(a,b){var c=[];tb(WB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Fe===void 0||b.indexOf(e.Fe)>=0)&&c.push(e.listener)});return c};function YB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:lg.ctid}};function ZB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var aC=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;$B(this,a,b)},bC=function(a,b,c,d){if(ek.hasOwnProperty(b)||b==="__zone")return-1;var e={};ld(d)&&(e=md(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},cC=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},dC=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},$B=function(a,b,c){b!==void 0&&a.Rf(b);c&&x.setTimeout(function(){dC(a)},
Number(c))};aC.prototype.Rf=function(a){var b=this,c=Cb(function(){Nc(function(){a(lg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var eC=function(a){a.N++;return Cb(function(){a.H++;a.R&&a.H>=a.N&&dC(a)})},fC=function(a){a.R=!0;a.H>=a.N&&dC(a)};var gC={};function hC(){return x[iC()]}
function iC(){return x.GoogleAnalyticsObject||"ga"}function lC(){var a=lg.ctid;}
function mC(a,b){return function(){var c=hC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var sC=["es","1"],tC={},uC={};function vC(a,b){if(pl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";tC[a]=[["e",c],["eid",a]];Eq(a)}}function wC(a){var b=a.eventId,c=a.Qd;if(!tC[b])return[];var d=[];uC[b]||d.push(sC);d.push.apply(d,ya(tC[b]));c&&(uC[b]=!0);return d};var xC={},yC={},zC={};function AC(a,b,c,d){pl&&E(120)&&((d===void 0?0:d)?(zC[b]=zC[b]||0,++zC[b]):c!==void 0?(yC[a]=yC[a]||{},yC[a][b]=Math.round(c)):(xC[a]=xC[a]||{},xC[a][b]=(xC[a][b]||0)+1))}function BC(a){var b=a.eventId,c=a.Qd,d=xC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete xC[b];return e.length?[["md",e.join(".")]]:[]}
function CC(a){var b=a.eventId,c=a.Qd,d=yC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete yC[b];return e.length?[["mtd",e.join(".")]]:[]}function DC(){for(var a=[],b=l(Object.keys(zC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+zC[d])}return a.length?[["mec",a.join(".")]]:[]};var EC={},FC={};function GC(a,b,c){if(pl&&b){var d=ll(b);EC[a]=EC[a]||[];EC[a].push(c+d);var e=b[jf.Ua];if(!e)throw Error("Error: No function name given for function call.");var f=(Lf[e]?"1":"2")+d;FC[a]=FC[a]||[];FC[a].push(f);Eq(a)}}function HC(a){var b=a.eventId,c=a.Qd,d=[],e=EC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=FC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete EC[b],delete FC[b]);return d};function IC(a,b,c){c=c===void 0?!1:c;JC().addRestriction(0,a,b,c)}function KC(a,b,c){c=c===void 0?!1:c;JC().addRestriction(1,a,b,c)}function LC(){var a=Km();return JC().getRestrictions(1,a)}var MC=function(){this.container={};this.C={}},NC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
MC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=NC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
MC.prototype.getRestrictions=function(a,b){var c=NC(this,b);if(a===0){var d,e;return[].concat(ya((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ya((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ya((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ya((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
MC.prototype.getExternalRestrictions=function(a,b){var c=NC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};MC.prototype.removeExternalRestrictions=function(a){var b=NC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function JC(){return Cp("r",function(){return new MC})};function OC(a,b,c,d){var e=Jf[a],f=PC(a,b,c,d);if(!f)return null;var g=Xf(e[jf.Al],c,[]);if(g&&g.length){var h=g[0];f=OC(h.index,{onSuccess:f,onFailure:h.Wl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function PC(a,b,c,d){function e(){function w(){io(3);var M=Ab()-I;GC(c.id,f,"7");cC(c.Pc,D,"exception",M);E(109)&&LA(c,f,eA.O.Ni);G||(G=!0,h())}if(f[jf.jo])h();else{var y=Wf(f,c,[]),z=y[jf.Qm];if(z!=null)for(var C=0;C<z.length;C++)if(!P(z[C])){h();return}var D=bC(c.Pc,String(f[jf.Ua]),Number(f[jf.kh]),y[jf.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=Ab()-I;GC(c.id,Jf[a],"5");cC(c.Pc,D,"success",M);E(109)&&LA(c,f,eA.O.Pi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=Ab()-
I;GC(c.id,Jf[a],"6");cC(c.Pc,D,"failure",M);E(109)&&LA(c,f,eA.O.Oi);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);GC(c.id,f,"1");E(109)&&KA(c,f);var I=Ab();try{Yf(y,{event:c,index:a,type:1})}catch(M){w(M)}E(109)&&LA(c,f,eA.O.Hl)}}var f=Jf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Xf(f[jf.Il],c,[]);if(n&&n.length){var p=n[0],q=OC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Wl===
2?m:q}if(f[jf.pl]||f[jf.lo]){var r=f[jf.pl]?Kf:c.Eq,t=g,u=h;if(!r[a]){var v=QC(a,r,Cb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function QC(a,b,c){var d=[],e=[];b[a]=RC(d,e,c);return{onSuccess:function(){b[a]=SC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=TC;for(var f=0;f<e.length;f++)e[f]()}}}function RC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function SC(a){a()}function TC(a,b){b()};var WC=function(a,b){for(var c=[],d=0;d<Jf.length;d++)if(a[d]){var e=Jf[d];var f=eC(b.Pc);try{var g=OC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[jf.Ua];if(!h)throw Error("Error: No function name given for function call.");var m=Lf[h];c.push({Gm:d,priorityOverride:(m?m.priorityOverride||0:0)||ZB(e[jf.Ua],1)||0,execute:g})}else UC(d,b),f()}catch(p){f()}}c.sort(VC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function XC(a,b){if(!WB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=XB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=eC(b);try{d[e](a,f)}catch(g){f()}}return!0}function VC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Gm,h=b.Gm;f=g>h?1:g<h?-1:0}return f}
function UC(a,b){if(pl){var c=function(d){var e=b.isBlocked(Jf[d])?"3":"4",f=Xf(Jf[d][jf.Al],b,[]);f&&f.length&&c(f[0].index);GC(b.id,Jf[d],e);var g=Xf(Jf[d][jf.Il],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var YC=!1,WB;function ZC(){WB||(WB=new VB);return WB}
function $C(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(YC)return!1;YC=!0}var e=!1,f=LC(),g=md(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}vC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:aD(g,e),Eq:[],logMacroError:function(){L(6);io(0)},cachedModelValues:bD(),Pc:new aC(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&pl&&(n.reportMacroDiscrepancy=AC);E(109)&&HA(n.id);var p=cg(n);E(109)&&IA(n.id);e&&(p=cD(p));E(109)&&GA(b);var q=WC(p,n),r=XC(a,n.Pc);fC(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||lC();return dD(p,q)||r}function bD(){var a={};a.event=Fk("event",1);a.ecommerce=Fk("ecommerce",1);a.gtm=Fk("gtm");a.eventModel=Fk("eventModel");return a}
function aD(a,b){var c=OB();return function(d){if(c(d))return!0;var e=d&&d[jf.Ua];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Km();f=JC().getRestrictions(0,g);var h=a;b&&(h=md(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=qk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function cD(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Jf[c][jf.Ua]);if(dk[d]||Jf[c][jf.mo]!==void 0||ZB(d,2))b[c]=!0}return b}function dD(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Jf[c]&&!ek[String(Jf[c][jf.Ua])])return!0;return!1};function eD(){ZC().addListener("gtm.init",function(a,b){Vj.fa=!0;Un();b()})};var fD=!1,gD=0,hD=[];function iD(a){if(!fD){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){fD=!0;for(var e=0;e<hD.length;e++)Nc(hD[e])}hD.push=function(){for(var f=Ca.apply(0,arguments),g=0;g<f.length;g++)Nc(f[g]);return 0}}}function jD(){if(!fD&&gD<140){gD++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");iD()}catch(c){x.setTimeout(jD,50)}}}
function kD(){var a=x;fD=!1;gD=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")iD();else{Lc(A,"DOMContentLoaded",iD);Lc(A,"readystatechange",iD);if(A.createEventObject&&A.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&jD()}Lc(a,"load",iD)}}function lD(a){fD?a():hD.push(a)};var mD={},nD={};function oD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={wj:void 0,cj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.wj=Mp(g,b),e.wj){var h=Jm();pb(h,function(r){return function(t){return r.wj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=mD[g]||[];e.cj={};m.forEach(function(r){return function(t){r.cj[t]=!0}}(e));for(var n=Lm(),p=0;p<n.length;p++)if(e.cj[n[p]]){c=c.concat(Jm());break}var q=nD[g]||[];q.length&&(c=c.concat(q))}}return{qj:c,Xp:d}}
function pD(a){tb(mD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function qD(a){tb(nD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var rD=!1,sD=!1;function tD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=md(b,null),b[J.m.df]&&(d.eventCallback=b[J.m.df]),b[J.m.Kg]&&(d.eventTimeout=b[J.m.Kg]));return d}function uD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Fp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function vD(a,b){var c=a&&a[J.m.pd];c===void 0&&(c=Ak(J.m.pd,2),c===void 0&&(c="default"));if(mb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?mb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=oD(d,b.isGtmEvent),f=e.qj,g=e.Xp;if(g.length)for(var h=wD(a),m=0;m<g.length;m++){var n=Mp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=Dm().destination[q];r&&r.state===0||UB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{qj:Np(f,b.isGtmEvent),
Fo:Np(t,b.isGtmEvent)}}}var xD=void 0,yD=void 0;function zD(a,b,c){var d=md(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&L(136);var e=md(b,null);md(c,e);Xw(Tw(Lm()[0],e),a.eventId,d)}function wD(a){for(var b=l([J.m.rd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Mq.C[d];if(e)return e}}
var AD={config:function(a,b){var c=uD(a,b);if(!(a.length<2)&&mb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!ld(a[2])||a.length>3)return;d=a[2]}var e=Mp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Hm.qe){var m=Nm(Cm());if(Wm(m)){var n=m.parent,p=n.isDestination;h={aq:Nm(n),Tp:p};break a}}h=void 0}var q=h;q&&(f=q.aq,g=q.Tp);vC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Jm().indexOf(r)===-1:Lm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Lc]){var u=wD(d);if(t)UB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;xD?zD(b,v,xD):yD||(yD=md(v,null))}else RB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(L(128),g&&L(130),b.inheritParentConfig)){var w;var y=d;yD?(zD(b,yD,y),w=!1):(!y[J.m.ud]&&gk&&xD||(xD=md(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}ql&&(Hp===1&&(Nn.mcc=!1),Hp=2);if(gk&&!t&&!d[J.m.ud]){var z=sD;sD=!0;if(z)return}rD||L(43);if(!b.noTargetGroup)if(t){qD(e.id);
var C=e.id,D=d[J.m.Ng]||"default";D=String(D).split(",");for(var G=0;G<D.length;G++){var I=nD[D[G]]||[];nD[D[G]]=I;I.indexOf(C)<0&&I.push(C)}}else{pD(e.id);var M=e.id,T=d[J.m.Ng]||"default";T=T.toString().split(",");for(var da=0;da<T.length;da++){var N=mD[T[da]]||[];mD[T[da]]=N;N.indexOf(M)<0&&N.push(M)}}delete d[J.m.Ng];var W=b.eventMetadata||{};W.hasOwnProperty(Q.A.yd)||(W[Q.A.yd]=!b.fromContainerExecution);b.eventMetadata=W;delete d[J.m.df];for(var ia=t?[e.id]:Jm(),ka=0;ka<ia.length;ka++){var Y=
d,X=ia[ka],ja=md(b,null),wa=Mp(X,ja.isGtmEvent);wa&&Mq.push("config",[Y],wa,ja)}}}}},consent:function(a,b){if(a.length===3){L(39);var c=uD(a,b),d=a[1],e={},f=Lo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.rg?Array.isArray(h)?NaN:Number(h):g===J.m.hc?(Array.isArray(h)?h:[h]).map(Mo):No(h)}b.fromContainerExecution||(e[J.m.V]&&L(139),e[J.m.Ka]&&L(140));d==="default"?op(e):d==="update"?qp(e,c):d==="declare"&&b.fromContainerExecution&&np(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&mb(c)){var d=void 0;if(a.length>2){if(!ld(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=tD(c,d),f=uD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=vD(d,b);if(m){for(var n=m.qj,p=m.Fo,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),t=n.map(function(M){return M.id}),u=l(Jm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<0&&t.push(w)}vC(g,
c);for(var y=l(t),z=y.next();!z.done;z=y.next()){var C=z.value,D=md(b,null),G=md(d,null);delete G[J.m.df];var I=D.eventMetadata||{};I.hasOwnProperty(Q.A.yd)||(I[Q.A.yd]=!D.fromContainerExecution);I[Q.A.Ii]=q.slice();I[Q.A.Of]=r.slice();D.eventMetadata=I;Nq(c,G,C,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.pd]=q.join(","):delete e.eventModel[J.m.pd];rD||L(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[Q.A.Fl]&&(b.noGtmEvent=!0);e.eventModel[J.m.Kc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){L(53);if(a.length===4&&mb(a[1])&&mb(a[2])&&lb(a[3])){var c=Mp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){rD||L(43);var f=wD();if(pb(Jm(),function(h){return c.destinationId===h})){uD(a,b);var g={};md((g[J.m.rc]=d,g[J.m.Ic]=e,g),null);Oq(d,function(h){Nc(function(){e(h)})},c.id,b)}else UB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){rD=!0;var c=uD(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&mb(a[1])&&lb(a[2])){if(ig(a[1],a[2]),L(74),a[1]==="all"){L(75);var b=!1;try{b=a[2](lg.ctid,"unknown",{})}catch(c){}b||L(76)}}else L(73)},set:function(a,b){var c=void 0;a.length===2&&ld(a[1])?c=md(a[1],null):a.length===3&&mb(a[1])&&(c={},ld(a[2])||Array.isArray(a[2])?c[a[1]]=md(a[2],null):c[a[1]]=a[2]);if(c){var d=uD(a,b),e=d.eventId,f=d.priorityId;
md(c,null);var g=md(c,null);Mq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},BD={policy:!0};var DD=function(a){if(CD(a))return a;this.value=a};DD.prototype.getUntrustedMessageValue=function(){return this.value};var CD=function(a){return!a||jd(a)!=="object"||ld(a)?!1:"getUntrustedMessageValue"in a};DD.prototype.getUntrustedMessageValue=DD.prototype.getUntrustedMessageValue;var ED=!1,FD=[];function GD(){if(!ED){ED=!0;for(var a=0;a<FD.length;a++)Nc(FD[a])}}function HD(a){ED?Nc(a):FD.push(a)};var ID=0,JD={},KD=[],LD=[],MD=!1,ND=!1;function OD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function PD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return QD(a)}function RD(a,b){if(!nb(b)||b<0)b=0;var c=Bp[ck],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function SD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(ub(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function TD(){var a;if(LD.length)a=LD.shift();else if(KD.length)a=KD.shift();else return;var b;var c=a;if(MD||!SD(c.message))b=c;else{MD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Fp(),f=Fp(),c.message["gtm.uniqueEventId"]=Fp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};KD.unshift(n,c);b=h}return b}
function UD(){for(var a=!1,b;!ND&&(b=TD());){ND=!0;delete xk.eventModel;zk();var c=b,d=c.message,e=c.messageContext;if(d==null)ND=!1;else{e.fromContainerExecution&&Ek();try{if(lb(d))try{d.call(Bk)}catch(G){}else if(Array.isArray(d)){if(mb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=Ak(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(G){}}}else{var n=void 0;if(ub(d))a:{if(d.length&&mb(d[0])){var p=AD[d[0]];if(p&&(!e.fromContainerExecution||!BD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,t=r._clear||e.overwriteModelFields,u=l(Object.keys(r)),v=u.next();!v.done;v=u.next()){var w=v.value;w!=="_clear"&&(t&&Dk(w),Dk(w,r[w]))}nk||(nk=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Fp(),r["gtm.uniqueEventId"]=y,Dk("gtm.uniqueEventId",y)),q=$C(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&zk(!0);var z=d["gtm.uniqueEventId"];if(typeof z==="number"){for(var C=JD[String(z)]||[],D=0;D<C.length;D++)LD.push(VD(C[D]));C.length&&LD.sort(OD);
delete JD[String(z)];z>ID&&(ID=z)}ND=!1}}}return!a}
function WD(){if(E(109)){var a=!Vj.ma;}var c=UD();if(E(109)){}try{var e=lg.ctid,f=x[ck].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function $w(a){if(ID<a.notBeforeEventId){var b=String(a.notBeforeEventId);JD[b]=JD[b]||[];JD[b].push(a)}else LD.push(VD(a)),LD.sort(OD),Nc(function(){ND||UD()})}function VD(a){return{message:a.message,messageContext:a.messageContext}}
function XD(){function a(f){var g={};if(CD(f)){var h=f;f=CD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=yc(ck,[]),c=Bp[ck]=Bp[ck]||{};c.pruned===!0&&L(83);JD=Yw().get();Zw();lD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});HD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Bp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new DD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});KD.push.apply(KD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(L(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return UD()&&p};var e=b.slice(0).map(function(f){return a(f)});KD.push.apply(KD,e);if(!Vj.ma){if(E(109)){}Nc(WD)}}var QD=function(a){return x[ck].push(a)};function YD(a){QD(a)};function ZD(){var a,b=al(x.location.href);(a=b.hostname+b.pathname)&&Qn("dl",encodeURIComponent(a));var c;var d=lg.ctid;if(d){var e=Hm.qe?1:0,f,g=Nm(Cm());f=g&&g.context;c=d+";"+lg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Qn("tdp",h);var m=Ql(!0);m!==void 0&&Qn("frm",String(m))};var $D={},aE=void 0;
function bE(){if(Yo()||ql)Qn("csp",function(){return Object.keys($D).join("~")||void 0},!1),x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){L(179);var b=om(a.effectiveDirective);if(b){var c;var d=mm(b,a.blockedURI);c=d?km[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=
n.value;if(!p.zm){p.zm=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if(Yo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Yo()){var u=dp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Xo(u)}}}cE(p.endpoint)}}nm(b,a.blockedURI)}}}}})}
function cE(a){var b=String(a);$D.hasOwnProperty(b)||($D[b]=!0,Rn("csp",!0),aE===void 0&&E(171)&&(aE=x.setTimeout(function(){if(E(171)){var c=Nn.csp;Nn.csp=!0;Nn.seq=!1;var d=Sn(!1);Nn.csp=c;Nn.seq=!0;Gc(d+"&script=1")}aE=void 0},500)))};function dE(){var a;var b=Mm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Qn("pcid",e)};var eE=/^(https?:)?\/\//;
function fE(){var a=Om();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=ad())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(eE,"")===d.replace(eE,""))){b=g;break a}}L(146)}else L(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Qn("rtg",String(a.canonicalContainerId)),Qn("slo",String(p)),Qn("hlo",a.htmlLoadOrder||"-1"),
Qn("lst",String(a.loadScriptType||"0")))}else L(144)};function gE(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var t=!1;return t}();a.push({Fh:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:c,active:d,Uf:0});var e=Number('')||0,f=Number('1')||0;f||(f=e/100);var g=function(){var t=!1;
return t}();a.push({Fh:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:f,active:g,Uf:0});var h=Number('')||0,m=Number('')||0;m||(m=h/100);var n=function(){var t=!1;return t}();a.push({Fh:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,
probability:m,active:n,Uf:1});var p=Number('')||0,q=Number('')||0;q||(q=p/100);var r=function(){var t=!1;return t}();a.push({Fh:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:q,active:r,Uf:0});return a};var hE={};function iE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Vj.R.H.add(Number(c.value))}function jE(a){var b=Fn(An.Z.ql);return!!mi[a].active||mi[a].probability>.5||!!(b.exp||{})[mi[a].experimentId]||!!mi[a].active||mi[a].probability>.5||!!(hE.exp||{})[mi[a].experimentId]}
function kE(){for(var a=l(gE()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Fh;mi[d]=c;if(c.Uf===1){var e=d,f=Fn(An.Z.ql);qi(f,e);iE(f);jE(e)&&B(e)}else if(c.Uf===0){var g=d,h=hE;qi(h,g);iE(h);jE(g)&&B(g)}}};
function FE(){};var GE=function(){};GE.prototype.toString=function(){return"undefined"};var HE=new GE;function OE(){E(212)&&jk&&(ig("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),IC(Km(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return ZB(d,5)||!(!Lf[d]||!Lf[d][5])||c.includes("cmpPartners")}))};function PE(a,b){function c(g){var h=al(g),m=Vk(h,"protocol"),n=Vk(h,"host",!0),p=Vk(h,"port"),q=Vk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function QE(a){return RE(a)?1:0}
function RE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=md(a,{});md({arg1:c[d],any_of:void 0},e);if(QE(e))return!0}return!1}switch(a["function"]){case "_cn":return Rg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Mg.length;g++){var h=Mg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Ng(b,c);case "_eq":return Sg(b,c);case "_ge":return Tg(b,c);case "_gt":return Vg(b,c);case "_lc":return Og(b,c);case "_le":return Ug(b,
c);case "_lt":return Wg(b,c);case "_re":return Qg(b,c,a.ignore_case);case "_sw":return Xg(b,c);case "_um":return PE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var SE=function(a,b,c,d){dr.call(this);this.gh=b;this.Kf=c;this.rb=d;this.Va=new Map;this.hh=0;this.ma=new Map;this.Da=new Map;this.R=void 0;this.H=a};va(SE,dr);SE.prototype.N=function(){delete this.C;this.Va.clear();this.ma.clear();this.Da.clear();this.R&&($q(this.H,"message",this.R),delete this.R);delete this.H;delete this.rb;dr.prototype.N.call(this)};
var TE=function(a){if(a.C)return a.C;a.Kf&&a.Kf(a.H)?a.C=a.H:a.C=Pl(a.H,a.gh);var b;return(b=a.C)!=null?b:null},VE=function(a,b,c){if(TE(a))if(a.C===a.H){var d=a.Va.get(b);d&&d(a.C,c)}else{var e=a.ma.get(b);if(e&&e.pj){UE(a);var f=++a.hh;a.Da.set(f,{Dh:e.Dh,Wo:e.fm(c),persistent:b==="addEventListener"});a.C.postMessage(e.pj(c,f),"*")}}},UE=function(a){a.R||(a.R=function(b){try{var c;c=a.rb?a.rb(b):void 0;if(c){var d=c.fq,e=a.Da.get(d);if(e){e.persistent||a.Da.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.Wo,c.payload)}}}catch(g){}},Zq(a.H,"message",a.R))};var WE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},XE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},YE={fm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},ZE={fm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function $E(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,fq:b.__gppReturn.callId}}
var aF=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;dr.call(this);this.caller=new SE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},$E);this.caller.Va.set("addEventListener",WE);this.caller.ma.set("addEventListener",YE);this.caller.Va.set("removeEventListener",XE);this.caller.ma.set("removeEventListener",ZE);this.timeoutMs=c!=null?c:500};va(aF,dr);aF.prototype.N=function(){this.caller.dispose();dr.prototype.N.call(this)};
aF.prototype.addEventListener=function(a){var b=this,c=sl(function(){a(bF,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);VE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(cF,!0);return}a(dF,!0)}}})};
aF.prototype.removeEventListener=function(a){VE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var dF={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},bF={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},cF={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function eF(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Gv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Gv.C=d}}function fF(){try{var a=new aF(x,{timeoutMs:-1});TE(a.caller)&&a.addEventListener(eF)}catch(b){}};function gF(){var a=[["cv",Zi(1)],["rv",ak],["tc",Jf.filter(function(b){return b}).length]];bk&&a.push(["x",bk]);sk()&&a.push(["tag_exp",sk()]);return a};var hF={};function bj(a){hF[a]=(hF[a]||0)+1}function iF(){for(var a=[],b=l(Object.keys(hF)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+hF[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var jF={},kF={};function lF(a){var b=a.eventId,c=a.Qd,d=[],e=jF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=kF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete jF[b],delete kF[b]);return d};function mF(){return!1}function nF(){var a={};return function(b,c,d){}};function oF(){var a=pF;return function(b,c,d){var e=d&&d.event;qF(c);var f=Ch(b)?void 0:1,g=new Ya;tb(c,function(r,t){var u=Cd(t,void 0,f);u===void 0&&t!==void 0&&L(44);g.set(r,u)});a.Nb(ag());var h={Pl:pg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Rf:e!==void 0?function(r){e.Pc.Rf(r)}:void 0,Jb:function(){return b},log:function(){},hp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},oq:!!ZB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(mF()){var m=nF(),n,p;h.xb={Ej:[],Sf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Bh:Uh()};h.log=function(r){var t=Ca.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=$e(a,h,[b,g]);a.Nb();q instanceof Fa&&(q.type==="return"?q=q.data:q=void 0);return Bd(q,void 0,f)}}function qF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;lb(b)&&(a.gtmOnSuccess=function(){Nc(b)});lb(c)&&(a.gtmOnFailure=function(){Nc(c)})};function rF(a){}rF.M="internal.addAdsClickIds";function sF(a,b){var c=this;}sF.publicName="addConsentListener";var tF=!1;function uF(a){for(var b=0;b<a.length;++b)if(tF)try{a[b]()}catch(c){L(77)}else a[b]()}function vF(a,b,c){var d=this,e;return e}vF.M="internal.addDataLayerEventListener";function wF(a,b,c){}wF.publicName="addDocumentEventListener";function xF(a,b,c,d){}xF.publicName="addElementEventListener";function yF(a){return a.K.ub()};function zF(a){}zF.publicName="addEventCallback";
function OF(a){}OF.M="internal.addFormAbandonmentListener";function PF(a,b,c,d){}
PF.M="internal.addFormData";var QF={},RF=[],SF={},TF=0,UF=0;
function aG(a,b){}aG.M="internal.addFormInteractionListener";
function hG(a,b){}hG.M="internal.addFormSubmitListener";
function mG(a){}mG.M="internal.addGaSendListener";function nG(a){if(!a)return{};var b=a.hp;return YB(b.type,b.index,b.name)}function oG(a){return a?{originatingEntity:nG(a)}:{}};function wG(a){var b=Bp.zones;return b?b.getIsAllowedFn(Lm(),a):function(){return!0}}function xG(){var a=Bp.zones;a&&a.unregisterChild(Lm())}
function yG(){KC(Km(),function(a){var b=Bp.zones;return b?b.isActive(Lm(),a.originalEventData["gtm.uniqueEventId"]):!0});IC(Km(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return wG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var zG=function(a,b){this.tagId=a;this.we=b};
function AG(a,b){var c=this;return a}AG.M="internal.loadGoogleTag";function BG(a){return new td("",function(b){var c=this.evaluate(b);if(c instanceof td)return new td("",function(){var d=Ca.apply(0,arguments),e=this,f=md(yF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.tb();h.Od(f);return c.Lb.apply(c,[h].concat(ya(g)))})})};function CG(a,b,c){var d=this;}CG.M="internal.addGoogleTagRestriction";var DG={},EG=[];
function LG(a,b){}
LG.M="internal.addHistoryChangeListener";function MG(a,b,c){}MG.publicName="addWindowEventListener";function NG(a,b){return!0}NG.publicName="aliasInWindow";function OG(a,b,c){}OG.M="internal.appendRemoteConfigParameter";function PG(a){var b;return b}
PG.publicName="callInWindow";function QG(a){}QG.publicName="callLater";function RG(a){}RG.M="callOnDomReady";function SG(a){}SG.M="callOnWindowLoad";function TG(a,b){var c;return c}TG.M="internal.computeGtmParameter";function UG(a,b){var c=this;}UG.M="internal.consentScheduleFirstTry";function VG(a,b){var c=this;}VG.M="internal.consentScheduleRetry";function WG(a){var b;return b}WG.M="internal.copyFromCrossContainerData";function XG(a,b){var c;if(!nh(a)||!sh(b)&&b!==null&&!ih(b))throw F(this.getName(),["string","number|undefined"],arguments);H(this,"read_data_layer",a);c=(b||2)!==2?Ak(a,1):Ck(a,[x,A]);var d=Cd(c,this.K,Ch(yF(this).Jb())?2:1);d===void 0&&c!==void 0&&L(45);return d}XG.publicName="copyFromDataLayer";
function YG(a){var b=void 0;return b}YG.M="internal.copyFromDataLayerCache";function ZG(a){var b;return b}ZG.publicName="copyFromWindow";function $G(a){var b=void 0;return Cd(b,this.K,1)}$G.M="internal.copyKeyFromWindow";var aH=function(a){return a===Zm.X.Fa&&rn[a]===Ym.Ia.pe&&!P(J.m.U)};var bH=function(){return"0"},cH=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return bl(a,b,"0")};var dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH={},wH={},xH={},yH={},zH={},AH={},BH={},CH={},DH=(CH[J.m.Ma]=(dH[2]=[aH],dH),CH[J.m.qf]=(eH[2]=[aH],eH),CH[J.m.ef]=(fH[2]=[aH],fH),CH[J.m.mi]=(gH[2]=[aH],gH),CH[J.m.ni]=(hH[2]=[aH],hH),CH[J.m.oi]=(iH[2]=[aH],iH),CH[J.m.ri]=(jH[2]=[aH],jH),CH[J.m.si]=(kH[2]=[aH],kH),CH[J.m.wc]=(lH[2]=[aH],lH),CH[J.m.rf]=(mH[2]=[aH],mH),CH[J.m.tf]=(nH[2]=[aH],nH),CH[J.m.uf]=(oH[2]=[aH],oH),CH[J.m.vf]=(pH[2]=
[aH],pH),CH[J.m.wf]=(qH[2]=[aH],qH),CH[J.m.xf]=(rH[2]=[aH],rH),CH[J.m.yf]=(sH[2]=[aH],sH),CH[J.m.zf]=(tH[2]=[aH],tH),CH[J.m.mb]=(uH[1]=[aH],uH),CH[J.m.Zc]=(vH[1]=[aH],vH),CH[J.m.gd]=(wH[1]=[aH],wH),CH[J.m.ae]=(xH[1]=[aH],xH),CH[J.m.Pe]=(yH[1]=[function(a){return E(102)&&aH(a)}],yH),CH[J.m.hd]=(zH[1]=[aH],zH),CH[J.m.Ca]=(AH[1]=[aH],AH),CH[J.m.Ya]=(BH[1]=[aH],BH),CH),EH={},FH=(EH[J.m.mb]=bH,EH[J.m.Zc]=bH,EH[J.m.gd]=bH,EH[J.m.ae]=bH,EH[J.m.Pe]=bH,EH[J.m.hd]=function(a){if(!ld(a))return{};var b=md(a,
null);delete b.match_id;return b},EH[J.m.Ca]=cH,EH[J.m.Ya]=cH,EH),GH={},HH={},IH=(HH[Q.A.ib]=(GH[2]=[aH],GH),HH),JH={};var KH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};KH.prototype.getValue=function(a){a=a===void 0?Zm.X.Hb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};KH.prototype.H=function(){return jd(this.C)==="array"||ld(this.C)?md(this.C,null):this.C};
var LH=function(){},MH=function(a,b){this.conditions=a;this.C=b},NH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new KH(c,e,g,a.C[b]||LH)},OH,PH;var QH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}},Wv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,Q.A.Pf))},U=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(OH!=null||(OH=new MH(DH,FH)),e=NH(OH,b,c));d[b]=e};
QH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return U(this,a,b),!0;if(!ld(c))return!1;U(this,a,ma(Object,"assign").call(Object,c,b));return!0};var RH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
QH.prototype.copyToHitData=function(a,b,c){var d=O(this.D,a);d===void 0&&(d=b);if(mb(d)&&c!==void 0&&E(92))try{d=c(d)}catch(e){}d!==void 0&&U(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===Q.A.Pf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,Q.A.Pf))},S=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:(PH!=null||(PH=new MH(IH,JH)),e=NH(PH,b,c));d[b]=e},SH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},pw=function(a,b,c){var d=dx(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function TH(a,b){var c;return c}TH.M="internal.copyPreHit";function UH(a,b){var c=null;return Cd(c,this.K,2)}UH.publicName="createArgumentsQueue";function VH(a){return Cd(function(c){var d=hC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
hC(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}VH.M="internal.createGaCommandQueue";function WH(a){return Cd(function(){if(!lb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Ch(yF(this).Jb())?2:1)}WH.publicName="createQueue";function XH(a,b){var c=null;if(!nh(a)||!oh(b))throw F(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new yd(new RegExp(a,d))}catch(e){}return c}XH.M="internal.createRegex";function YH(a){}YH.M="internal.declareConsentState";function ZH(a){var b="";return b}ZH.M="internal.decodeUrlHtmlEntities";function $H(a,b,c){var d;return d}$H.M="internal.decorateUrlWithGaCookies";function aI(){}aI.M="internal.deferCustomEvents";function bI(a){var b;return b}bI.M="internal.detectUserProvidedData";
function gI(a,b){return f}gI.M="internal.enableAutoEventOnClick";
function oI(a,b){return p}oI.M="internal.enableAutoEventOnElementVisibility";function pI(){}pI.M="internal.enableAutoEventOnError";var qI={},rI=[],sI={},tI=0,uI=0;
function AI(a,b){var c=this;return d}AI.M="internal.enableAutoEventOnFormInteraction";
function FI(a,b){var c=this;return f}FI.M="internal.enableAutoEventOnFormSubmit";
function KI(){var a=this;}KI.M="internal.enableAutoEventOnGaSend";var LI={},MI=[];
function TI(a,b){var c=this;return f}TI.M="internal.enableAutoEventOnHistoryChange";var UI=["http://","https://","javascript:","file://"];
function YI(a,b){var c=this;return h}YI.M="internal.enableAutoEventOnLinkClick";var ZI,$I;
function kJ(a,b){var c=this;return d}kJ.M="internal.enableAutoEventOnScroll";function lJ(a){return function(){if(a.limit&&a.sj>=a.limit)a.yh&&x.clearInterval(a.yh);else{a.sj++;var b=Ab();QD({event:a.eventName,"gtm.timerId":a.yh,"gtm.timerEventNumber":a.sj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Fm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Fm,"gtm.triggers":a.Kq})}}}
function mJ(a,b){
return f}mJ.M="internal.enableAutoEventOnTimer";var oc=Aa(["data-gtm-yt-inspected-"]),oJ=["www.youtube.com","www.youtube-nocookie.com"],pJ,qJ=!1;
function AJ(a,b){var c=this;return e}AJ.M="internal.enableAutoEventOnYouTubeActivity";qJ=!1;function BJ(a,b){if(!nh(a)||!hh(b))throw F(this.getName(),["string","Object|undefined"],arguments);var c=b?Bd(b):{},d=a,e=!1;return e}BJ.M="internal.evaluateBooleanExpression";var CJ;function DJ(a){var b=!1;return b}DJ.M="internal.evaluateMatchingRules";
var EJ=function(a){switch(a){case K.J.Ha:return[ow,lw,jw,iw,qw,fz,Yv,Mz,zz,nw,nz,uz,mw];case K.J.Kj:return[ow,lw,iw,qw,fz];case K.J.W:return[ow,fw,lw,iw,qw,Iz,Rz,Fz,Qz,Pz,Oz,Nz,Mz,zz,yz,wz,vz,tz,jz,iz,xz,nz,Ez,sz,rz,pz,Hz,Dz,jw,gw,nw,Cz,oz,Lz,uz,Gz,hz,mz,Bz,qz,Jz,Kz,kz,mw];case K.J.Fi:return[ow,fw,lw,iw,qw,Iz,Rz,zz,hw,nz,Ez,Hz,gw,jw,nw,Cz,Lz,uz,Gz,hz,kz,mw];case K.J.na:return[ow,fw,lw,iw,qw,Iz,Rz,Fz,Qz,Pz,Oz,Nz,Mz,zz,yz,tz,xz,nz,Ez,sz,Hz,gw,jw,nw,Cz,oz,Lz,uz,Gz,hz,Jz,kz,mw];case K.J.jb:return[ow,
fw,lw,iw,qw,Iz,Rz,Qz,Mz,zz,xz,nz,hw,Ez,pz,Hz,gw,jw,nw,Cz,oz,Lz,uz,Gz,hz,kz,mw];case K.J.Na:return[ow,fw,lw,iw,qw,Iz,Rz,Qz,Mz,zz,xz,nz,hw,Ez,pz,Hz,gw,jw,nw,Cz,oz,Lz,uz,Gz,hz,kz,mw];default:return[ow,fw,lw,iw,qw,Iz,Rz,Fz,Qz,Pz,Oz,Nz,Mz,zz,yz,wz,vz,tz,jz,iz,xz,nz,Ez,sz,rz,pz,Hz,Dz,gw,jw,nw,Cz,oz,Lz,uz,Gz,hz,mz,Bz,qz,Jz,Kz,kz,mw]}},FJ=function(a){for(var b=EJ(R(a,Q.A.ia)),c=0;c<b.length&&(b[c](a),!a.isAborted);c++);},GJ=function(a,b,c,d){var e=new QH(b,c,d);S(e,Q.A.ia,a);S(e,Q.A.Ba,!0);S(e,Q.A.fb,Ab());
S(e,Q.A.Dl,d.eventMetadata[Q.A.Ba]);return e},HJ=function(a,b,c,d){function e(t,u){for(var v=l(h),w=v.next();!w.done;w=v.next()){var y=w.value;y.isAborted=!1;S(y,Q.A.Ba,!0);S(y,Q.A.da,!0);S(y,Q.A.fb,Ab());S(y,Q.A.He,t);S(y,Q.A.Ie,u)}}function f(t){for(var u={},v=0;v<h.length;u={kb:void 0},v++)if(u.kb=h[v],!t||t(R(u.kb,Q.A.ia)))if(!R(u.kb,Q.A.da)||R(u.kb,Q.A.ia)===K.J.Ha||P(q))FJ(h[v]),R(u.kb,Q.A.Ba)||u.kb.isAborted||(JB(u.kb),R(u.kb,Q.A.ia)===K.J.Ha&&($v(u.kb,function(){f(function(w){return w===K.J.Ha})}),
Wv(u.kb,J.m.qf)===void 0&&r===void 0&&(r=Gn(An.Z.jh,function(w){return function(){P(J.m.V)&&(S(w.kb,Q.A.Qf,!0),S(w.kb,Q.A.da,!1),U(w.kb,J.m.da),f(function(y){return y===K.J.Ha}),S(w.kb,Q.A.Qf,!1),Hn(An.Z.jh,r),r=void 0)}}(u)))))}var g=d.isGtmEvent&&a===""?{id:"",prefix:"",destinationId:"",ids:[]}:Mp(a,d.isGtmEvent);if(g){var h=[];if(d.eventMetadata[Q.A.xd]){var m=d.eventMetadata[Q.A.xd];Array.isArray(m)||(m=[m]);for(var n=0;n<m.length;n++){var p=GJ(m[n],g,b,d);E(223)||S(p,Q.A.Ba,!1);h.push(p)}}else b===
J.m.qa&&(E(24)?h.push(GJ(K.J.Ha,g,b,d)):h.push(GJ(K.J.Fi,g,b,d))),h.push(GJ(K.J.W,g,b,d)),h.push(GJ(K.J.jb,g,b,d)),h.push(GJ(K.J.Na,g,b,d)),h.push(GJ(K.J.na,g,b,d));var q=[J.m.U,J.m.V],r=void 0;up(function(){f();var t=E(29)&&!P([J.m.Ka]);if(!P(q)||t){var u=q;t&&(u=[].concat(ya(u),[J.m.Ka]));tp(function(v){var w,y,z;w=v.consentEventId;y=v.consentPriorityId;z=v.consentTypes;e(w,y);z&&z.length===1&&z[0]===J.m.Ka?f(function(C){return C===K.J.na}):f()},u)}},q)}};function mK(){return xr(7)&&xr(9)&&xr(10)};function hL(a,b,c,d){}hL.M="internal.executeEventProcessor";function iL(a){var b;return Cd(b,this.K,1)}iL.M="internal.executeJavascriptString";function jL(a){var b;return b};function kL(a){var b="";return b}kL.M="internal.generateClientId";function lL(a){var b={};return Cd(b)}lL.M="internal.getAdsCookieWritingOptions";function mL(a,b){var c=!1;return c}mL.M="internal.getAllowAdPersonalization";function nL(){var a;return a}nL.M="internal.getAndResetEventUsage";function oL(a,b){b=b===void 0?!0:b;var c;return c}oL.M="internal.getAuid";var pL=null;
function qL(){var a=new Ya;H(this,"read_container_data"),E(49)&&pL?a=pL:(a.set("containerId",'AW-10793199209'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",qg),a.set("previewMode",rg.Hm),a.set("environmentMode",rg.cp),a.set("firstPartyServing",uk()||Vj.N),a.set("containerUrl",xc),a.Wa(),E(49)&&(pL=a));return a}
qL.publicName="getContainerVersion";function rL(a,b){b=b===void 0?!0:b;var c;return c}rL.publicName="getCookieValues";function sL(){var a="";return a}sL.M="internal.getCorePlatformServicesParam";function tL(){return qo()}tL.M="internal.getCountryCode";function uL(){var a=[];return Cd(a)}uL.M="internal.getDestinationIds";function vL(a){var b=new Ya;return b}vL.M="internal.getDeveloperIds";function wL(a){var b;return b}wL.M="internal.getEcsidCookieValue";function xL(a,b){var c=null;return c}xL.M="internal.getElementAttribute";function yL(a){var b=null;return b}yL.M="internal.getElementById";function zL(a){var b="";return b}zL.M="internal.getElementInnerText";function AL(a,b){var c=null;return Cd(c)}AL.M="internal.getElementProperty";function BL(a){var b;return b}BL.M="internal.getElementValue";function CL(a){var b=0;return b}CL.M="internal.getElementVisibilityRatio";function DL(a){var b=null;return b}DL.M="internal.getElementsByCssSelector";
function EL(a){var b;if(!nh(a))throw F(this.getName(),["string"],arguments);H(this,"read_event_data",a);var c;a:{var d=a,e=yF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",z=l(n),C=z.next();!C.done;C=
z.next()){var D=C.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Cd(c,this.K,1);return b}EL.M="internal.getEventData";var FL={};FL.disableUserDataWithoutCcd=E(223);FL.enableDecodeUri=E(92);FL.enableGaAdsConversions=E(122);FL.enableGaAdsConversionsClientId=E(121);FL.enableOverrideAdsCps=E(170);FL.enableUrlDecodeEventUsage=E(139);function GL(){return Cd(FL)}GL.M="internal.getFlags";function HL(){var a;return a}HL.M="internal.getGsaExperimentId";function IL(){return new yd(HE)}IL.M="internal.getHtmlId";function JL(a){var b;return b}JL.M="internal.getIframingState";function KL(a,b){var c={};return Cd(c)}KL.M="internal.getLinkerValueFromLocation";function LL(){var a=new Ya;if(arguments.length!==0)throw F(this.getName(),[],arguments);var b=Tv();b!==void 0&&a.set(J.m.Af,b||"error");var c=wr();c&&a.set(J.m.kd,c);var d=vr();d&&a.set(J.m.sd,d);var e=Gv.gppString;e&&a.set(J.m.hf,e);var f=Gv.C;f&&a.set(J.m.ff,f);return a}LL.M="internal.getPrivacyStrings";function ML(a,b){var c;return c}ML.M="internal.getProductSettingsParameter";function NL(a,b){var c;return c}NL.publicName="getQueryParameters";function OL(a,b){var c;return c}OL.publicName="getReferrerQueryParameters";function PL(a){var b="";return b}PL.publicName="getReferrerUrl";function QL(){return ro()}QL.M="internal.getRegionCode";function RL(a,b){var c;return c}RL.M="internal.getRemoteConfigParameter";function SL(){var a=new Ya;a.set("width",0);a.set("height",0);return a}SL.M="internal.getScreenDimensions";function TL(){var a="";return a}TL.M="internal.getTopSameDomainUrl";function UL(){var a="";return a}UL.M="internal.getTopWindowUrl";function VL(a){var b="";return b}VL.publicName="getUrl";function WL(){H(this,"get_user_agent");return uc.userAgent}WL.M="internal.getUserAgent";function XL(){var a;return a?Cd(Zy(a)):a}XL.M="internal.getUserAgentClientHints";function eM(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function fM(){var a=eM();a.hid=a.hid||qb();return a.hid}function gM(a,b){var c=eM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function EM(a){(qy(a)||uk())&&U(a,J.m.Qk,ro()||qo());!qy(a)&&uk()&&U(a,J.m.bl,"::")}function FM(a){if(uk()&&!qy(a)&&(uo()||U(a,J.m.Ek,!0),E(78))){jw(a);kw(a,Jp.Bf.Vm,Oo(O(a.D,J.m.cb)));var b=Jp.Bf.Wm;var c=O(a.D,J.m.Hc);kw(a,b,c===!0?1:c===!1?0:void 0);kw(a,Jp.Bf.Um,Oo(O(a.D,J.m.zb)));kw(a,Jp.Bf.Sm,Is(No(O(a.D,J.m.ob)),No(O(a.D,J.m.Sb))))}};var $M={AW:An.Z.Mm,G:An.Z.Xn,DC:An.Z.Vn};function aN(a){var b=jj(a);return""+js(b.map(function(c){return c.value}).join("!"))}function bN(a){var b=Mp(a);return b&&$M[b.prefix]}function cN(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};var HN=window,IN=document,JN=function(a){var b=HN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||IN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&HN["ga-disable-"+a]===!0)return!0;try{var c=HN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(IN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return IN.getElementById("__gaOptOutExtension")?!0:!1};
function VN(a){tb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Xb]||{};tb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function DO(a,b){}function EO(a,b){var c=function(){};return c}
function FO(a,b,c){};var GO=EO;function IO(a,b,c){var d=this;}IO.M="internal.gtagConfig";
function KO(a,b){}
KO.publicName="gtagSet";function LO(){var a={};return a};function MO(a){}MO.M="internal.initializeServiceWorker";function NO(a,b){}NO.publicName="injectHiddenIframe";var OO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function PO(a,b,c,d,e){}PO.M="internal.injectHtml";var TO={};
function VO(a,b,c,d){}var WO={dl:1,id:1},XO={};
function YO(a,b,c,d){}E(160)?YO.publicName="injectScript":VO.publicName="injectScript";YO.M="internal.injectScript";function ZO(){return vo()}ZO.M="internal.isAutoPiiEligible";function $O(a){var b=!0;return b}$O.publicName="isConsentGranted";function aP(a){var b=!1;return b}aP.M="internal.isDebugMode";function bP(){return to()}bP.M="internal.isDmaRegion";function cP(a){var b=!1;return b}cP.M="internal.isEntityInfrastructure";function dP(a){var b=!1;if(!sh(a))throw F(this.getName(),["number"],[a]);b=E(a);return b}dP.M="internal.isFeatureEnabled";function eP(){var a=!1;return a}eP.M="internal.isFpfe";function fP(){var a=!1;return a}fP.M="internal.isGcpConversion";function gP(){var a=!1;return a}gP.M="internal.isLandingPage";function hP(){var a=!1;return a}hP.M="internal.isOgt";function iP(){var a;return a}iP.M="internal.isSafariPcmEligibleBrowser";function jP(){var a=Ph(function(b){yF(this).log("error",b)});a.publicName="JSON";return a};function kP(a){var b=void 0;return Cd(b)}kP.M="internal.legacyParseUrl";function lP(){return!1}
var mP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function nP(){}nP.publicName="logToConsole";function oP(a,b){}oP.M="internal.mergeRemoteConfig";function pP(a,b,c){c=c===void 0?!0:c;var d=[];return Cd(d)}pP.M="internal.parseCookieValuesFromString";function qP(a){var b=void 0;return b}qP.publicName="parseUrl";function rP(a){}rP.M="internal.processAsNewEvent";function sP(a,b,c){var d;return d}sP.M="internal.pushToDataLayer";function tP(a){var b=Ca.apply(1,arguments),c=!1;return c}tP.publicName="queryPermission";function uP(a){var b=this;}uP.M="internal.queueAdsTransmission";function vP(a,b){var c=void 0;return c}vP.publicName="readAnalyticsStorage";function wP(){var a="";return a}wP.publicName="readCharacterSet";function xP(){return ck}xP.M="internal.readDataLayerName";function yP(){var a="";return a}yP.publicName="readTitle";function zP(a,b){var c=this;if(!nh(a)||!jh(b))throw F(this.getName(),["string","function"],arguments);Pw(a,function(d){b.invoke(c.K,Cd(d,c.K,1))});}zP.M="internal.registerCcdCallback";function AP(a,b){return!0}AP.M="internal.registerDestination";var BP=["config","event","get","set"];function CP(a,b,c){}CP.M="internal.registerGtagCommandListener";function DP(a,b){var c=!1;return c}DP.M="internal.removeDataLayerEventListener";function EP(a,b){}
EP.M="internal.removeFormData";function FP(){}FP.publicName="resetDataLayer";function GP(a,b,c){var d=void 0;return d}GP.M="internal.scrubUrlParams";function HP(a){}HP.M="internal.sendAdsHit";function IP(a,b,c,d){}IP.M="internal.sendGtagEvent";function JP(a,b,c){}JP.publicName="sendPixel";function KP(a,b){}KP.M="internal.setAnchorHref";function LP(a){}LP.M="internal.setContainerConsentDefaults";function MP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}MP.publicName="setCookie";function NP(a){}NP.M="internal.setCorePlatformServices";function OP(a,b){}OP.M="internal.setDataLayerValue";function PP(a){}PP.publicName="setDefaultConsentState";function QP(a,b){}QP.M="internal.setDelegatedConsentType";function RP(a,b){}RP.M="internal.setFormAction";function SP(a,b,c){c=c===void 0?!1:c;}SP.M="internal.setInCrossContainerData";function TP(a,b,c){return!1}TP.publicName="setInWindow";function UP(a,b,c){}UP.M="internal.setProductSettingsParameter";function VP(a,b,c){}VP.M="internal.setRemoteConfigParameter";function WP(a,b){}WP.M="internal.setTransmissionMode";function XP(a,b,c,d){var e=this;}XP.publicName="sha256";function YP(a,b,c){}
YP.M="internal.sortRemoteConfigParameters";function ZP(a){}ZP.M="internal.storeAdsBraidLabels";function $P(a,b){var c=void 0;return c}$P.M="internal.subscribeToCrossContainerData";var aQ={},bQ={};aQ.getItem=function(a){var b=null;return b};aQ.setItem=function(a,b){};
aQ.removeItem=function(a){};aQ.clear=function(){};aQ.publicName="templateStorage";function cQ(a,b){var c=!1;if(!mh(a)||!nh(b))throw F(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof RegExp))return!1;c=d.test(b);return c}cQ.M="internal.testRegex";function dQ(a){var b;return b};function eQ(a,b){var c;return c}eQ.M="internal.unsubscribeFromCrossContainerData";function fQ(a){}fQ.publicName="updateConsentState";function gQ(a){var b=!1;return b}gQ.M="internal.userDataNeedsEncryption";var hQ;function iQ(a,b,c){hQ=hQ||new $h;hQ.add(a,b,c)}function jQ(a,b){var c=hQ=hQ||new $h;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=lb(b)?vh(a,b):wh(a,b)}
function kQ(){return function(a){var b;var c=hQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.ub();if(e){var f=!1,g=e.Jb();if(g){Ch(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function lQ(){var a=function(c){return void jQ(c.M,c)},b=function(c){return void iQ(c.publicName,c)};b(sF);b(zF);b(NG);b(PG);b(QG);b(XG);b(ZG);b(UH);b(jP());b(WH);b(qL);b(rL);b(NL);b(OL);b(PL);b(VL);b(KO);b(NO);b($O);b(nP);b(qP);b(tP);b(wP);b(yP);b(JP);b(MP);b(PP);b(TP);b(XP);b(aQ);b(fQ);iQ("Math",Ah());iQ("Object",Yh);iQ("TestHelper",bi());iQ("assertApi",xh);iQ("assertThat",yh);iQ("decodeUri",Dh);iQ("decodeUriComponent",Eh);iQ("encodeUri",Fh);iQ("encodeUriComponent",Gh);iQ("fail",Lh);iQ("generateRandom",
Mh);iQ("getTimestamp",Nh);iQ("getTimestampMillis",Nh);iQ("getType",Oh);iQ("makeInteger",Qh);iQ("makeNumber",Rh);iQ("makeString",Sh);iQ("makeTableMap",Th);iQ("mock",Wh);iQ("mockObject",Xh);iQ("fromBase64",jL,!("atob"in x));iQ("localStorage",mP,!lP());iQ("toBase64",dQ,!("btoa"in x));a(rF);a(vF);a(PF);a(aG);a(hG);a(mG);a(CG);a(LG);a(OG);a(RG);a(SG);a(TG);a(UG);a(VG);a(WG);a(YG);a($G);a(TH);a(VH);a(XH);a(YH);a(ZH);a($H);a(aI);a(bI);a(gI);a(oI);a(pI);a(AI);a(FI);a(KI);a(TI);a(YI);a(kJ);a(mJ);a(AJ);a(BJ);
a(DJ);a(hL);a(iL);a(kL);a(lL);a(mL);a(nL);a(oL);a(tL);a(uL);a(vL);a(wL);a(xL);a(yL);a(zL);a(AL);a(BL);a(CL);a(DL);a(EL);a(GL);a(HL);a(IL);a(JL);a(KL);a(LL);a(ML);a(QL);a(RL);a(SL);a(TL);a(UL);a(XL);a(IO);a(MO);a(PO);a(YO);a(ZO);a(aP);a(bP);a(cP);a(dP);a(eP);a(fP);a(gP);a(hP);a(iP);a(kP);a(AG);a(oP);a(pP);a(rP);a(sP);a(uP);a(xP);a(zP);a(AP);a(CP);a(DP);a(EP);a(GP);a(HP);a(IP);a(KP);a(LP);a(NP);a(OP);a(QP);a(RP);a(SP);a(UP);a(VP);a(WP);a(YP);a(ZP);a($P);a(cQ);a(eQ);a(gQ);jQ("internal.IframingStateSchema",
LO());
E(104)&&a(sL);E(160)?b(YO):b(VO);E(177)&&b(vP);return kQ()};var pF;
function mQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;pF=new Xe;nQ();Ff=oF();var e=pF,f=lQ(),g=new ud("require",f);g.Wa();e.C.C.set("require",g);Sa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&$f(n,d[m]);try{pF.execute(n),E(120)&&pl&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Sf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");qk[q]=["sandboxedScripts"]}oQ(b)}function nQ(){pF.Vc(function(a,b,c){Bp.SANDBOXED_JS_SEMAPHORE=Bp.SANDBOXED_JS_SEMAPHORE||0;Bp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Bp.SANDBOXED_JS_SEMAPHORE--}})}function oQ(a){a&&tb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");qk[e]=qk[e]||[];qk[e].push(b)}})};function pQ(a){Xw(Rw("developer_id."+a,!0),0,{})};var qQ=Array.isArray;function rQ(a,b){return md(a,b||null)}function V(a){return window.encodeURIComponent(a)}function sQ(a,b,c){Kc(a,b,c)}
function tQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Vk(al(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function uQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function vQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=uQ(b,"parameter","parameterValue");e&&(c=rQ(e,c))}return c}function wQ(a,b,c){return a===void 0||a===c?b:a}function xQ(a,b,c){return Gc(a,b,c,void 0)}function yQ(){return x.location.href}function zQ(a,b){return Ak(a,b||2)}function AQ(a,b){x[a]=b}function BQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}

var CQ={};var Z={securityGroups:{}};
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=zQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.F="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v["5"]=!0;
Z.securityGroups.rep=["google"],Z.__rep=function(a){var b=Mp(a.vtp_containerId,!0);if(b){var c,d;switch(b.prefix){case "AW":c=HJ;d=Zm.X.Fa;break;case "DC":c=YJ;d=Zm.X.Fa;break;case "GF":c=cK;d=Zm.X.Hb;break;case "HA":c=iK;d=Zm.X.Hb;break;case "UA":c=GK;d=Zm.X.Hb;break;case "MC":c=GO(b,a.vtp_gtmEventId);d=Zm.X.Fc;break;default:Nc(a.vtp_gtmOnFailure);return}c?(Nc(a.vtp_gtmOnSuccess),E(185)?Lq(a.vtp_containerId,c,d,a.vtp_remoteConfig):(Lq(a.vtp_containerId,c,d),a.vtp_remoteConfig&&Rq(a.vtp_containerId,
a.vtp_remoteConfig||{}))):Nc(a.vtp_gtmOnFailure)}else Nc(a.vtp_gtmOnFailure)},Z.__rep.F="rep",Z.__rep.isVendorTemplate=!0,Z.__rep.priorityOverride=0,Z.__rep.isInfrastructure=!1,Z.__rep["5"]=!0;
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!mb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Lg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.F="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer["5"]=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!mb(g))throw e(f,{},"Keys must be strings.");if(c!=="any"){try{if(Lg(g,
d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},T:a}})}();





Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},T:function(){return{}}}},Z.__read_container_data.F="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data["5"]=!1;










Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Uw(String(b.streamId),d,c);Xw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get["5"]=!1;



var Ep={dataLayer:Bk,callback:function(a){pk.hasOwnProperty(a)&&lb(pk[a])&&pk[a]();delete pk[a]},bootstrap:0};
function DQ(){Dp();Qm();TB();Eb(qk,Z.securityGroups);var a=Nm(Cm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;cp(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||L(142);Rf={Oo:fg}}var EQ=!1;
function no(){try{if(EQ||!Xm()){Yj();Vj.P=Yi(18,"");
Vj.rb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Vj.Va="ad_storage|analytics_storage|ad_user_data";Vj.Da="57f0";Vj.Da="57f0";Vj.ma=!0;if(E(109)){}Pa[7]=!0;var a=Cp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});jp(a);Ap();fF();qr();Gp();if(Rm()){xG();JC().removeExternalRestrictions(Km());}else{cz();Pf();Lf=Z;Mf=QE;hg=new og;mQ();DQ();OE();lo||(ko=po());
xp();XD();kD();ED=!1;A.readyState==="complete"?GD():Lc(x,"load",GD);eD();pl&&(tq(Hq),x.setInterval(Gq,864E5),tq(gF),tq(wC),tq(jA),tq(Kq),tq(lF),tq(HC),E(120)&&(tq(BC),tq(CC),tq(DC)),hF={},tq(iF),aj());ql&&(Yn(),$p(),ZD(),fE(),dE(),Qn("bt",String(Vj.C?2:Vj.N?1:0)),Qn("ct",String(Vj.C?0:Vj.N?1:3)),bE());
FE();io(1);yG();kE();ok=Ab();Ep.bootstrap=ok;Vj.ma&&WD();E(109)&&FA();E(134)&&(typeof x.name==="string"&&Gb(x.name,"web-pixel-sandbox-CUSTOM")&&bd()?pQ("dMDg0Yz"):x.Shopify&&(pQ("dN2ZkMj"),bd()&&pQ("dNTU0Yz")))}}}catch(b){io(4),Dq()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Qo(n)&&(m=h.Wk)}function c(){m&&xc?g(m):a()}if(!x[Yi(37,"__TAGGY_INSTALLED")]){var d=!1;if(A.referrer){var e=al(A.referrer);d=Xk(e,"host")===Yi(38,"cct.google")}if(!d){var f=ss(Yi(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Yi(37,"__TAGGY_INSTALLED")]=!0,Gc(Yi(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";jk&&(v="OGT",w="GTAG");
var y=Yi(23,"google.tagmanager.debugui2.queue"),z=x[y];z||(z=[],x[y]=z,Gc("https://"+Zj.wg+"/debug/bootstrap?id="+lg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Sr()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:xc,containerProduct:v,debug:!1,id:lg.ctid,targetRef:{ctid:lg.ctid,isDestination:Im()},aliases:Lm(),destinations:Jm()}};C.data.resume=function(){a()};Zj.Rm&&(C.data.initialPublish=!0);z.push(C)},h={ao:1,Zk:2,vl:3,Vj:4,Wk:5};h[h.ao]="GTM_DEBUG_LEGACY_PARAM";h[h.Zk]="GTM_DEBUG_PARAM";h[h.vl]="REFERRER";
h[h.Vj]="COOKIE";h[h.Wk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Vk(x.location,"query",!1,void 0,"gtm_debug");Qo(p)&&(m=h.Zk);if(!m&&A.referrer){var q=al(A.referrer);Xk(q,"host")===Yi(24,"tagassistant.google.com")&&(m=h.vl)}if(!m){var r=ss("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Vj)}m||b();if(!m&&Po(n)){var t=!1;Lc(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&EQ&&!po()["0"]?mo():no()});

})()

