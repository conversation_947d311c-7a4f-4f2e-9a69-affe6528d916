(function(){var q,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},r=ca(this),t=function(a,b){if(b)a:{var c=r;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}};
t("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
t("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=r[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return da(aa(this))}})}return a});
var da=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},ea=function(a){return a.raw=a},u=function(a,b){a.raw=b;return a},v=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},fa=function(){this.j=!1;this.h=null;this.m=void 0;this.g=1;this.o=0;this.i=null},ha=function(a){if(a.j)throw new TypeError("Generator is already running");
a.j=!0};fa.prototype.l=function(a){this.m=a};var ia=function(a,b){a.i={ga:b,oa:!0};a.g=a.o};fa.prototype.return=function(a){this.i={return:a};this.g=this.o};
var x=function(a,b,c){a.g=c;return{value:b}},ja=function(a){this.g=new fa;this.h=a},ma=function(a,b){ha(a.g);var c=a.g.h;if(c)return ka(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return la(a)},ka=function(a,b,c,d){try{var e=b.call(a.g.h,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.j=!1,e;var f=e.value}catch(g){return a.g.h=null,ia(a.g,g),la(a)}a.g.h=null;d.call(a.g,f);return la(a)},
la=function(a){for(;a.g.g;)try{var b=a.h(a.g);if(b)return a.g.j=!1,{value:b.value,done:!1}}catch(c){a.g.m=void 0,ia(a.g,c)}a.g.j=!1;if(a.g.i){b=a.g.i;a.g.i=null;if(b.oa)throw b.ga;return{value:b.return,done:!0}}return{value:void 0,done:!0}},na=function(a){this.next=function(b){ha(a.g);a.g.h?b=ka(a,a.g.h.next,b,a.g.l):(a.g.l(b),b=la(a));return b};this.throw=function(b){ha(a.g);a.g.h?b=ka(a,a.g.h["throw"],b,a.g.l):(ia(a.g,b),b=la(a));return b};this.return=function(b){return ma(a,b)};this[Symbol.iterator]=
function(){return this}},oa=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})},y=function(a){return oa(new na(new ja(a)))},pa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};
t("Promise",function(a){function b(){this.g=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.h=function(g){if(null==this.g){this.g=[];var h=this;this.i(function(){h.l()})}this.g.push(g)};var d=r.setTimeout;b.prototype.i=function(g){d(g,0)};b.prototype.l=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=g[h];g[h]=null;try{k()}catch(l){this.j(l)}}}this.g=null};b.prototype.j=function(g){this.i(function(){throw g;
})};var e=function(g){this.g=0;this.i=void 0;this.h=[];this.o=!1;var h=this.j();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.j=function(){function g(l){return function(m){k||(k=!0,l.call(h,m))}}var h=this,k=!1;return{resolve:g(this.D),reject:g(this.l)}};e.prototype.D=function(g){if(g===this)this.l(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof e)this.G(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=!1}h?
this.C(g):this.m(g)}};e.prototype.C=function(g){var h=void 0;try{h=g.then}catch(k){this.l(k);return}"function"==typeof h?this.H(h,g):this.m(g)};e.prototype.l=function(g){this.u(2,g)};e.prototype.m=function(g){this.u(1,g)};e.prototype.u=function(g,h){if(0!=this.g)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.i=h;2===this.g&&this.F();this.B()};e.prototype.F=function(){var g=this;d(function(){if(g.I()){var h=r.console;"undefined"!==typeof h&&h.error(g.i)}},
1)};e.prototype.I=function(){if(this.o)return!1;var g=r.CustomEvent,h=r.Event,k=r.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=r.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.i;return k(g)};e.prototype.B=function(){if(null!=this.h){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};
var f=new b;e.prototype.G=function(g){var h=this.j();g.M(h.resolve,h.reject)};e.prototype.H=function(g,h){var k=this.j();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.then=function(g,h){function k(w,z){return"function"==typeof w?function(N){try{l(w(N))}catch(n){m(n)}}:z}var l,m,p=new e(function(w,z){l=w;m=z});this.M(k(g,l),k(h,m));return p};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.M=function(g,h){function k(){switch(l.g){case 1:g(l.i);break;case 2:h(l.i);
break;default:throw Error("Unexpected state: "+l.g);}}var l=this;null==this.h?f.h(k):this.h.push(k);this.o=!0};e.resolve=c;e.reject=function(g){return new e(function(h,k){k(g)})};e.race=function(g){return new e(function(h,k){for(var l=v(g),m=l.next();!m.done;m=l.next())c(m.value).M(h,k)})};e.all=function(g){var h=v(g),k=h.next();return k.done?c([]):new e(function(l,m){function p(N){return function(n){w[N]=n;z--;0==z&&l(w)}}var w=[],z=0;do w.push(void 0),z++,c(k.value).M(p(w.length-1),m),k=h.next();
while(!k.done)})};return e});var A=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
t("WeakMap",function(a){function b(){}function c(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function d(k){if(!A(k,f)){var l=new b;ba(k,f,{value:l})}}function e(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof b)return m;Object.isExtensible(m)&&d(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.g=(g+=Math.random()+1).toString();if(k){k=v(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};h.prototype.set=function(k,l){if(!c(k))throw Error("Invalid WeakMap key");d(k);if(!A(k,f))throw Error("WeakMap key fail: "+k);k[f][this.g]=l;return this};h.prototype.get=function(k){return c(k)&&A(k,f)?k[f][this.g]:void 0};h.prototype.has=function(k){return c(k)&&A(k,f)&&A(k[f],this.g)};
h.prototype.delete=function(k){return c(k)&&A(k,f)&&A(k[f],this.g)?delete k[f][this.g]:!1};return h});
t("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(v([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||"s"!=m.value[1])return!1;m=l.next();return m.done||4!=m.value[0].x||"t"!=m.value[1]||!l.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
f();this.size=0;if(h){h=v(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=0===h?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.s?l.s.value=k:(l.s={next:this[1],A:this[1].A,head:this[1],key:h,value:k},l.list.push(l.s),this[1].A.next=l.s,this[1].A=l.s,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.s&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.s.A.next=h.s.next,h.s.next.A=h.s.A,h.s.head=
null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].A=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).s};c.prototype.get=function(h){return(h=d(this,h).s)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=
m.value,h.call(k,m[1],m[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var l=k&&typeof k;"object"==l||"function"==l?b.has(k)?l=b.get(k):(l=""+ ++g,b.set(k,l)):l="p_"+k;var m=h[0][l];if(m&&A(h[0],l))for(h=0;h<m.length;h++){var p=m[h];if(k!==k&&p.key!==p.key||k===p.key)return{id:l,list:m,index:h,s:p}}return{id:l,list:m,index:-1,s:void 0}},e=function(h,k){var l=h[1];return da(function(){if(l){for(;l.head!=h[1];)l=l.A;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};
l=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.A=h.next=h.head=h},g=0;return c});t("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
t("String.prototype.startsWith",function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.startsWith must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.startsWith must not be a regular expression");var d=this+"";b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});
var qa=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};t("Array.prototype.entries",function(a){return a?a:function(){return qa(this,function(b,c){return[b,c]})}});t("Array.prototype.keys",function(a){return a?a:function(){return qa(this,function(b){return b})}});
t("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)A(b,d)&&c.push(b[d]);return c}});t("Array.prototype.values",function(a){return a?a:function(){return qa(this,function(b,c){return c})}});t("Math.log2",function(a){return a?a:function(b){return Math.log(b)/Math.LN2}});
t("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});t("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)A(b,d)&&c.push([d,b[d]]);return c}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ra=ra||{},B=this||self,sa=function(a){var b=typeof a;return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"},ta=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b},ua=function(a,b,c){return a.call.apply(a.bind,arguments)},va=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,
arguments)}},C=function(a,b,c){C=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?ua:va;return C.apply(null,arguments)},wa=function(a,b){a=a.split(".");var c=B;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b},D=function(a,b){function c(){}c.prototype=b.prototype;a.O=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ia=
function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}},xa=function(a){return a};function ya(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,ya);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}D(ya,Error);ya.prototype.name="CustomError";var za;function Aa(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");ya.call(this,c+a[d])}D(Aa,ya);Aa.prototype.name="AssertionError";function Ba(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var f=d}else a&&(e+=": "+a,f=b);throw new Aa(""+e,f||[]);}
var E=function(a,b,c){a||Ba("",null,b,Array.prototype.slice.call(arguments,2))},Ca=function(a,b){throw new Aa("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1));},Da=function(a,b,c){"number"!==typeof a&&Ba("Expected number but got %s: %s.",[sa(a),a],b,Array.prototype.slice.call(arguments,2));return a},Ea=function(a,b,c){Array.isArray(a)||Ba("Expected array but got %s: %s.",[sa(a),a],b,Array.prototype.slice.call(arguments,2))},Fa=function(a,b,c){"boolean"!==typeof a&&Ba("Expected boolean but got %s: %s.",
[sa(a),a],b,Array.prototype.slice.call(arguments,2));return a};var Ga=new function(a,b){this.g=a;this.defaultValue=void 0===b?!1:b}(1933);var Ha=function(a){Ha[" "](a);return a};Ha[" "]=function(){};var Ia=function(a,b){try{return Ha(a[b]),!0}catch(c){}return!1};var Ja=[],La=function(){var a=Ka;E(!Object.isSealed(a),"Cannot use getInstance() with a sealed constructor.");var b="V";if(a.V&&a.hasOwnProperty(b))return a.V;Ja.push(a);var c=new a;a.V=c;E(a.hasOwnProperty(b),"Could not instantiate singleton.");return c};var Ka=function(){var a={};this.g=function(){var b=Ga.g,c=Ga.defaultValue;return null!=a[b]?Fa(a[b]):c}};var Ma=Array.prototype.indexOf?function(a,b){E(null!=a.length);return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};function Na(a,b){b=Ma(a,b);var c;if(c=0<=b)E(null!=a.length),Array.prototype.splice.call(a,b,1);return c};var Oa,Pa;a:{for(var Qa=["CLOSURE_FLAGS"],Ra=B,Sa=0;Sa<Qa.length;Sa++)if(Ra=Ra[Qa[Sa]],null==Ra){Pa=null;break a}Pa=Ra}var Ta=Pa&&Pa[610401301];Oa=null!=Ta?Ta:!1;function Ua(){var a=B.navigator;return a&&(a=a.userAgent)?a:""}var Va,Wa=B.navigator;Va=Wa?Wa.userAgentData||null:null;function Xa(a){return Oa?Va?Va.brands.some(function(b){return(b=b.brand)&&-1!=b.indexOf(a)}):!1:!1}function F(a){return-1!=Ua().indexOf(a)};function G(){return Oa?!!Va&&0<Va.brands.length:!1}function Ya(){return G()?Xa("Chromium"):(F("Chrome")||F("CriOS"))&&!(G()?0:F("Edge"))||F("Silk")};var Za=G()?!1:F("Trident")||F("MSIE"),$a=F("Gecko")&&!(-1!=Ua().toLowerCase().indexOf("webkit")&&!F("Edge"))&&!(F("Trident")||F("MSIE"))&&!F("Edge");var ab="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function bb(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<ab.length;f++)c=ab[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var cb;var eb=function(a,b){if(b!==db)throw Error("TrustedResourceUrl is not meant to be built directly");this.g=a};eb.prototype.toString=function(){return this.g+""};var db={},fb=function(a){if(void 0===cb){var b=null;var c=B.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:xa,createScript:xa,createScriptURL:xa})}catch(d){B.console&&B.console.error(d.message)}cb=b}else cb=b}a=(b=cb)?b.createScriptURL(a):a;return new eb(a,db)};var H=function(a,b){if(b!==gb)throw Error("SafeUrl is not meant to be built directly");this.g=a};H.prototype.toString=function(){return this.g.toString()};var gb={},hb=new H("about:invalid#zClosurez",gb);new H("about:blank",gb);var ib={},jb=function(){if(ib!==ib)throw Error("SafeStyle is not meant to be built directly");};jb.prototype.toString=function(){return"".toString()};new jb;var kb={},lb=function(){if(kb!==kb)throw Error("SafeStyleSheet is not meant to be built directly");};lb.prototype.toString=function(){return"".toString()};new lb;var mb={},nb=function(){var a=B.trustedTypes&&B.trustedTypes.emptyHTML||"";if(mb!==mb)throw Error("SafeHtml is not meant to be built directly");this.g=a};nb.prototype.toString=function(){return this.g.toString()};new nb;var ob=function(a,b){var c;var d=document;b=b||d;if(b.querySelectorAll&&b.querySelector&&a)return b.querySelectorAll(a?"."+a:"");if(a&&b.getElementsByClassName){var e=b.getElementsByClassName(a);return e}e=b.getElementsByTagName("*");if(a){var f={};for(d=c=0;b=e[d];d++){var g=b.className,h;if(h="function"==typeof g.split)h=0<=Ma(g.split(/\s+/),a);h&&(f[c++]=b)}f.length=c;return f}return e},pb=function(a){this.g=a||B.document||document};
pb.prototype.appendChild=function(a,b){E(null!=a&&null!=b,"goog.dom.appendChild expects non-null arguments");a.appendChild(b)};var qb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),rb=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var sb=Object.freeze||function(a){return a};var I=function(a,b){this.name=a;this.value=b};I.prototype.toString=function(){return this.name};var tb=new I("OFF",Infinity),ub=new I("SEVERE",1E3),vb=new I("WARNING",900),wb=new I("CONFIG",700),xb=new I("FINE",500),yb=function(){this.clear()},zb;yb.prototype.clear=function(){};var Ab=function(a,b,c){this.reset(a||tb,b,c,void 0,void 0)};Ab.prototype.reset=function(){};
var Bb=function(a,b){this.g=null;this.j=[];this.parent=(void 0===b?null:b)||null;this.i=[];this.h={g:function(){return a}}},Cb=function(a){if(a.g)return a.g;if(a.parent)return Cb(a.parent);Ca("Root logger has no level set.");return tb},Db=function(a,b){for(;a;)a.j.forEach(function(c){c(b)}),a=a.parent},Eb=function(){this.entries={};var a=new Bb("");a.g=wb;this.entries[""]=a},Fb,Gb=function(a,b){var c=a.entries[b];if(c)return c;c=Gb(a,b.slice(0,Math.max(b.lastIndexOf("."),0)));var d=new Bb(b,c);a.entries[b]=
d;c.i.push(d);return d},Hb=function(){Fb||(Fb=new Eb);return Fb},Ib=function(a,b,c){var d;if(d=a)if(d=a&&b){d=b.value;var e=a?Cb(Gb(Hb(),a.g())):tb;d=d>=e.value}d&&(b=b||tb,d=Gb(Hb(),a.g()),"function"===typeof c&&(c=c()),zb||(zb=new yb),a=new Ab(b,c,a.g()),Db(d,a))},Jb=function(a,b){a&&Ib(a,ub,b)},J=function(a,b){a&&Ib(a,xb,b)};/*

 SPDX-License-Identifier: Apache-2.0
*/
var Kb=ea([""]),Lb=u(["\x00"],["\\0"]),Mb=u(["\n"],["\\n"]),Nb=u(["\x00"],["\\u0000"]),Ob=ea([""]),Pb=u(["\x00"],["\\0"]),Qb=u(["\n"],["\\n"]),Rb=u(["\x00"],["\\u0000"]);function Sb(a){return Object.isFrozen(a)&&Object.isFrozen(a.raw)}function Tb(a){return-1===a.toString().indexOf("`")}var Ub=Tb(function(a){return a(Kb)})||Tb(function(a){return a(Lb)})||Tb(function(a){return a(Mb)})||Tb(function(a){return a(Nb)}),Vb=Sb(Ob)&&Sb(Pb)&&Sb(Qb)&&Sb(Rb);var Wb=function(a){this.pa=a};function K(a){return new Wb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Xb=K("tel"),Yb=[K("data"),K("http"),K("https"),K("mailto"),K("ftp"),new Wb(function(a){return/^[^:]*([/?#]|$)/.test(a)})],Zb="function"===typeof URL,$b=[],ac=function(){};bc(function(a){var b=Gb(Hb(),"safevalues").h;b&&Ib(b,vb,"A URL with content '"+a+"' was sanitized away.")});function bc(a){-1===$b.indexOf(a)&&$b.push(a);ac=function(b){$b.forEach(function(c){c(b)})}};var cc={},dc=function(a){cc.TAGGING=cc.TAGGING||[];cc.TAGGING[a]=!0};var ec=function(a){return"string"===typeof a},fc=Array.isArray,gc=function(a,b){if(a&&fc(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]};var hc=window,ic=document;!F("Android")||Ya();Ya();F("Safari")&&(Ya()||(G()?0:F("Coast"))||(G()?0:F("Opera"))||(G()?0:F("Edge"))||(G()?Xa("Microsoft Edge"):F("Edg/"))||G()&&Xa("Opera"));var jc=[];var kc={},lc={ad_storage:!1,ad_user_data:!1,ad_personalization:!1};function mc(){var a={};var b=hc.google_tag_data;hc.google_tag_data=void 0===b?a:b;a=hc.google_tag_data;return a.ics=a.ics||new nc}var nc=function(){this.entries={};this.cps={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedSetCps=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.g=[]};
nc.prototype.default=function(a,b,c,d,e,f){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;dc(19);void 0==b?dc(18):oc(this,a,"granted"===b,c,d,e,f)};nc.prototype.waitForUpdate=function(a,b){for(var c=0;c<a.length;c++)oc(this,a[c],void 0,void 0,"","",b)};
var oc=function(a,b,c,d,e,f,g){var h=a.entries,k=h[b]||{},l=k.region;d=d&&ec(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(pc(d,l,e,f)){f=!!(g&&0<g&&void 0===k.update);var m={region:d,declare_region:k.declare_region,implicit:k.implicit,default:void 0!==c?c:k.default,declare:k.declare,update:k.update,quiet:f};if(""!==e||!1!==k.default)h[b]=m;f&&hc.setTimeout(function(){h[b]===m&&m.quiet&&(dc(2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0),a.notifyListeners())},g)}};q=nc.prototype;
q.clearTimeout=function(a,b){var c=[a];for(d in kc)kc.hasOwnProperty(d)&&kc[d]===a&&c.push(d);var d=this.entries[a]||{};a=this.getConsentState(a);if(d.quiet)for(d.quiet=!1,b=v(c),c=b.next();!c.done;c=b.next())qc(this,c.value);else if(void 0!==b&&a!==b)for(b=v(c),c=b.next();!c.done;c=b.next())qc(this,c.value)};
q.update=function(a,b){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(void 0!=b){var c=this.getConsentState(a),d=this.entries;(d[a]=d[a]||{}).update="granted"===b;this.clearTimeout(a,c)}};
q.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region;c=c&&ec(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();pc(c,h,d,e)&&(b={region:g.region,declare_region:c,declare:"granted"===b,implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet},""!==d||!1!==g.declare)&&(f[a]=b)};q.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries;a=c[a]=c[a]||{};!1!==a.implicit&&(a.implicit="granted"===b)};
q.getConsentState=function(a){var b=this.entries,c=b[a]||{},d=c.update;if(void 0!==d)return d?1:2;d=c.default;if(void 0!==d)return d?1:2;if(kc.hasOwnProperty(a)){b=b[kc[a]]||{};d=b.update;if(void 0!==d)return d?1:2;d=b.default;if(void 0!==d)return d?1:2}d=c.declare;if(void 0!==d)return d?1:2;if(void 0==jc[3]?0:jc[3]){d=c.implicit;if(void 0!==d)return d?3:4;if(lc.hasOwnProperty(a))return dc(22),lc[a]?3:4}return 0};
q.setCps=function(a,b,c,d,e){a:{var f=this.cps,g=f[a]||{},h=g.region;c=c&&ec(c)?c.toUpperCase():void 0;d=d.toUpperCase();if(pc(c,h,d,e.toUpperCase())&&(b={enabled:"granted"===b,region:c},""!==d||!1!==g.enabled)){f[a]=b;a=!0;break a}a=!1}a&&(this.usedSetCps=!0)};q.addListener=function(a,b){this.g.push({consentTypes:a,ha:b})};var qc=function(a,b){for(var c=0;c<a.g.length;++c){var d=a.g[c];fc(d.consentTypes)&&-1!==d.consentTypes.indexOf(b)&&(d.ca=!0)}};
nc.prototype.notifyListeners=function(a,b){for(var c=0;c<this.g.length;++c){var d=this.g[c];if(d.ca){d.ca=!1;try{d.ha({consentEventId:a,consentPriorityId:b})}catch(e){}}}};function pc(a,b,c,d){return""===c||a===d?!0:a===c?b!==d:!a&&!b}var rc=function(a){var b=mc();b.accessedAny=!0;return(ec(a)?[a]:a).every(function(c){switch(b.getConsentState(c)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})};var tc=function(a){var b=ic.cookie;if(sc()){var c=[];b=String(b||("null"!==window.origin?window.document.cookie:"")).split(";");for(var d=0;d<b.length;d++){var e=b[d].split("="),f=e[0].replace(/^\s*|\s*$/g,"");f&&f==a&&(e=e.slice(1).join("=").replace(/^\s*|\s*$/g,""),c.push(e))}a=c}else a=[];return a},sc=function(){var a=uc();return a&&La().g()?(ec(a)?[a]:a).every(function(b){var c=mc();c.accessedAny=!0;return!(c.entries[b]||{}).quiet&&rc(b)}):!0};var vc=function(){for(var a=[],b=ic.cookie.split(";"),c=RegExp("^\\s*_gac_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),d=0;d<b.length;d++){var e=b[d].match(c);e&&a.push({X:e[1],value:e[2],timestamp:Number(e[2].split(".")[1])||0})}a.sort(function(f,g){return g.timestamp-f.timestamp});return a};var wc=/^[\w-]+$/;function uc(){return(void 0==jc[14]?0:jc[14])?["ad_storage","ad_user_data"]:["ad_storage"]}
var Ac=function(a){var b=[];a=tc(a);if(!a||0==a.length)return b;for(var c={},d=0;d<a.length;c={v:void 0},d++){var e=xc(a[d]);if(null!=e){var f=e;e=f.version;c.v=f.v;var g=f.timestamp;f=f.labels;var h=gc(b,function(k){return function(l){return l.v===k.v}}(c));h?(h.timestamp=Math.max(h.timestamp,g),h.labels=yc(h.labels,f||[])):b.push({version:e,v:c.v,timestamp:g,labels:f})}}b.sort(function(k,l){return l.timestamp-k.timestamp});return zc(b)};
function yc(a,b){for(var c={},d=[],e=0;e<a.length;e++)c[a[e]]=!0,d.push(a[e]);for(a=0;a<b.length;a++)c[b[a]]||d.push(b[a]);return d}function xc(a){a=a.split(".");a=3>a.length||"GCL"!==a[0]&&"1"!==a[0]||!/^\d+$/.test(a[1])||!wc.test(a[2])?[]:a;return 0===a.length?null:{version:a[0],v:a[2],timestamp:1E3*(Number(a[1])||0),labels:a.slice(3)}}var zc=function(a){return a.filter(function(b){return wc.test(b.v)})};var Bc=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Cc=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Dc=/^\d+\.fls\.doubleclick\.net$/,Ec=/;gac=([^;?]+)/,Fc=/;gclaw=([^;?]+)/;function Gc(a,b){if(Dc.test(a.location.host)){if((a=a.location.href.match(Fc))&&2==a.length&&a[1].match(Cc))return[{v:a[1]}]}else return Ac((b||"_gcl")+"_aw");return[]}var Hc=function(a,b){return Gc(a,b).map(function(c){return c.v}).join(".")};var Ic="function"===typeof Symbol&&"symbol"===typeof Symbol()?Symbol("INTERNAL_ARRAY_STATE"):void 0;var Jc=Math,Kc=Jc.round,Lc=Math,Mc=Lc.log2,Nc=Math.max,Oc=Nc.apply,Pc=Object.values({Ca:1,Aa:2,za:4,Fa:8,Ea:16,Da:32,ua:64,Ha:128,ya:256,xa:512,Ba:1024,va:2048,Ga:4096,wa:8192}),Qc;if(Pc instanceof Array)Qc=Pc;else{for(var Rc=v(Pc),Sc,Tc=[];!(Sc=Rc.next()).done;)Tc.push(Sc.value);Qc=Tc}E(13===Kc.call(Jc,Mc.call(Lc,Oc.call(Nc,Math,Qc))));var Uc=Object.getOwnPropertyDescriptor(Array.prototype,"qa");
Object.defineProperties(Array.prototype,{qa:{get:function(){function a(e,f){e&b&&c.push(f)}var b=Vc(this),c=[];a(1,"IS_REPEATED_FIELD");a(2,"IS_IMMUTABLE_ARRAY");a(4,"IS_API_FORMATTED");a(4096,"STRING_FORMATTED");a(8192,"GBIGINT_FORMATTED");a(8,"ONLY_MUTABLE_VALUES");a(32,"MUTABLE_REFERENCES_ARE_OWNED");a(64,"CONSTRUCTED");a(128,"TRANSFERRED");a(256,"HAS_SPARSE_OBJECT");a(512,"HAS_MESSAGE_ID");a(2048,"FROZEN_ARRAY");var d=Wc(b);536870912!==d&&c.push("pivot: "+d);d=c.join(",");return Uc?Uc.get.call(this)+
"|"+d:d},configurable:!0,enumerable:!1}});var Vc=Ic?function(a){Ea(a,"state is only maintained on arrays.");return a[Ic]|0}:function(a){Ea(a,"state is only maintained on arrays.");return a.W|0};function Wc(a){a=a>>14&1023;return 0===a?536870912:a};var Xc=[];(Ic?function(a,b){Ea(a,"state is only maintained on arrays.");E((b&16777215)==b);a[Ic]=b}:function(a,b){Ea(a,"state is only maintained on arrays.");E((b&16777215)==b);void 0!==a.W?a.W=b:Object.defineProperties(a,{W:{value:b,configurable:!0,writable:!0,enumerable:!1}})})(Xc,55);Object.freeze(Xc);var Yc="undefined"!=typeof Symbol&&"undefined"!=typeof Symbol.hasInstance;Object.freeze(new function(){});Object.freeze(new function(){});var Zc=function(){throw Error("please construct maps as mutable then call toImmutable");};if(Yc){var $c=function(){throw Error("Cannot perform instanceof checks on ImmutableMap: please use isImmutableMap or isMutableMap to assert on the mutability of a map. See go/jspb-api-gotchas#immutable-classes for more information");},ad={};Object.defineProperties(Zc,(ad[Symbol.hasInstance]={value:$c,configurable:!1,writable:!1,enumerable:!1},ad));E(Zc[Symbol.hasInstance]===$c,"defineProperties did not work: was it monkey-patched?")};if("undefined"!==typeof Proxy){var L=bd;new Proxy({},{getPrototypeOf:L,setPrototypeOf:L,isExtensible:L,preventExtensions:L,getOwnPropertyDescriptor:L,defineProperty:L,has:L,get:L,set:L,deleteProperty:L,apply:L,construct:L})}function bd(){throw Error("this array or object is owned by JSPB and should not be reused, did you mean to copy it with copyJspbArray? See go/jspb-api-gotchas#construct_from_array");throw Error();};E(!0);function cd(){};(function(){var a=B.jspbGetTypeName;B.jspbGetTypeName=a?function(b){return a(b)||void 0}:cd})();var dd="google_conversion_id google_conversion_format google_conversion_type google_conversion_order_id google_conversion_language google_conversion_value google_conversion_currency google_conversion_domain google_conversion_label google_conversion_color google_disable_viewthrough google_enable_display_cookie_match google_gtag_event_data google_remarketing_only google_conversion_linker google_tag_for_child_directed_treatment google_tag_for_under_age_of_consent google_allow_ad_personalization_signals google_restricted_data_processing google_conversion_items google_conversion_merchant_id google_user_id google_custom_params google_conversion_date google_conversion_time google_conversion_js_version onload_callback opt_image_generator google_gtm_url_processor google_conversion_page_url google_conversion_referrer_url google_gcl_cookie_prefix google_gcl_cookie_path google_gcl_cookie_flags google_gcl_cookie_domain google_gcl_cookie_max_age_seconds google_read_gcl_cookie_opt_out google_basket_feed_country google_basket_feed_language google_basket_discount google_basket_transaction_type google_additional_conversion_params google_additional_params google_transport_url google_gtm_experiments".split(" ");function ed(a){var b=a.split(/\?|#/),c=/\?/.test(a)?"?"+b[1]:"";return{path:b[0],sa:c,hash:/#/.test(a)?"#"+(c?b[2]:b[1]):""}}
function fd(a){var b=pa.apply(1,arguments);if(!Array.isArray(a)||!Array.isArray(a.raw)||a.length!==a.raw.length||!Ub&&a===a.raw||!(Ub&&!Vb||Sb(a))||b.length+1!==a.length)throw new TypeError("\n    ############################## ERROR ##############################\n\n    It looks like you are trying to call a template tag function (fn`...`)\n    using the normal function syntax (fn(...)), which is not supported.\n\n    The functions in the safevalues library are not designed to be called\n    like normal functions, and doing so invalidates the security guarantees\n    that safevalues provides.\n\n    If you are stuck and not sure how to proceed, please reach out to us\n    instead through:\n     - go/ise-hardening-yaqs (preferred) // LINE-INTERNAL\n     - g/ise-hardening // LINE-INTERNAL\n     - https://github.com/google/safevalues/issues\n\n    ############################## ERROR ##############################");if(0===
b.length)return fb(a[0]);var c=a[0].toLowerCase();if(/^data:/.test(c))throw Error("Data URLs cannot have expressions in the template literal input.");if(/^https:\/\//.test(c)||/^\/\//.test(c)){var d=c.indexOf("//")+2;var e=c.indexOf("/",d);if(e<=d)throw Error("Can't interpolate data in a url's origin, Please make sure to fully specify the origin, terminated with '/'.");d=c.substring(d,e);if(!/^[0-9a-z.:-]+$/i.test(d))throw Error("The origin contains unsupported characters.");if(!/^[^:]*(:[0-9]+)?$/i.test(d))throw Error("Invalid port number.");
if(!/(^|\.)[a-z][^.]*$/i.test(d))throw Error("The top-level domain must start with a letter.");d=!0}else d=!1;if(!d)if(/^\//.test(c))if("/"===c||1<c.length&&"/"!==c[1]&&"\\"!==c[1])d=!0;else throw Error("The path start in the url is invalid.");else d=!1;if(!(d=d||RegExp("^[^:\\s\\\\/]+/").test(c)))if(/^about:blank/.test(c)){if("about:blank"!==c&&!/^about:blank#/.test(c))throw Error("The about url is invalid.");d=!0}else d=!1;if(!d)throw Error("Trying to interpolate expressions in an unsupported url format.");
c=a[0];for(d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return fb(c)}
function gd(a,b){a instanceof eb&&a.constructor===eb?a=a.g:(Ca("expected object of type TrustedResourceUrl, got '%s' of type %s",a,sa(a)),a="type_error:TrustedResourceUrl");a=ed(a.toString());var c=a.sa,d=c.length?"&":"?";b.forEach(function(e,f){e=e instanceof Array?e:[e];for(var g=0;g<e.length;g++){var h=e[g];null!==h&&void 0!==h&&(c+=d+encodeURIComponent(f)+"="+encodeURIComponent(String(h)),d="&")}});return fb(a.path+c+a.hash)};function hd(){var a=void 0===a?B:a;var b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch(e){}var c,d;return(null==(c=b)?0:c.pageViewId)&&(null==(d=b)?0:d.canonicalUrl)?b:null};var jd=function(){var a;if(a=void 0===a?hd():a){var b=a.master;try{var c=!!b&&null!=b.location.href&&Ia(b,"foo")}catch(d){c=!1}a=c?a.master:null}else a=null;return a||window};var M=navigator,kd=function(a){var b=1,c;if(void 0!=a&&""!=a)for(b=0,c=a.length-1;0<=c;c--){var d=a.charCodeAt(c);b=(b<<6&268435455)+d+(d<<14);d=b&266338304;b=0!=d?b^d>>21:b}return b},ld=function(a,b){if(!a||"none"==a)return 1;a=String(a);"auto"==a&&(a=b,"www."==a.substring(0,4)&&(a=a.substring(4,a.length)));return kd(a.toLowerCase())},md=RegExp("^\\s*_ga=\\s*1\\.(\\d+)[^.]*\\.(.*?)\\s*$"),nd=RegExp("^[^=]+=\\s*GA1\\.(\\d+)[^.]*\\.(.*?)\\s*$"),od=RegExp("^\\s*_ga=\\s*()(amp-[\\w.-]{22,64})$");var pd=function(){this.u=this.u;this.D=this.D};pd.prototype.u=!1;pd.prototype.o=function(){if(this.D)for(;this.D.length;)this.D.shift()()};var O=function(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1};O.prototype.h=function(){this.defaultPrevented=!0};var qd=function(){if(!B.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};B.addEventListener("test",c,b);B.removeEventListener("test",c,b)}catch(d){}return a}();var P=function(a,b){O.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.i=null;a&&this.init(a,b)};D(P,O);var rd=sb({2:"touch",3:"pen",4:"mouse"});
P.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;(b=a.relatedTarget)?$a&&(Ia(b,"nodeName")||(b=null)):"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=void 0!==a.clientX?a.clientX:
a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:rd[a.pointerType]||"";this.state=a.state;this.i=a;a.defaultPrevented&&P.O.h.call(this)};
P.prototype.h=function(){P.O.h.call(this);var a=this.i;a.preventDefault?a.preventDefault():a.returnValue=!1};var sd="closure_listenable_"+(1E6*Math.random()|0);var td=0;var ud=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.N=e;this.key=++td;this.J=this.L=!1},vd=function(a){a.J=!0;a.listener=null;a.proxy=null;a.src=null;a.N=null};var wd=function(a){this.src=a;this.g={};this.h=0};wd.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=xd(a,b,d,e);-1<g?(b=a[g],c||(b.L=!1)):(b=new ud(b,this.src,f,!!d,e),b.L=c,a.push(b));return b};var yd=function(a,b){var c=b.type;c in a.g&&Na(a.g[c],b)&&(vd(b),0==a.g[c].length&&(delete a.g[c],a.h--))},xd=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.J&&f.listener==b&&f.capture==!!c&&f.N==d)return e}return-1};var zd="closure_lm_"+(1E6*Math.random()|0),Ad={},Bd=0,Dd=function(a,b,c,d,e){if(d&&d.once)return Cd(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)Dd(a,b[f],c,d,e);return null}c=Ed(c);return a&&a[sd]?Fd(a,b,c,ta(d)?!!d.capture:!!d,e):Gd(a,b,c,!1,d,e)},Gd=function(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=ta(e)?!!e.capture:!!e,h=Hd(a);h||(a[zd]=h=new wd(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Id();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)qd||(e=g),void 0===
e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Jd(b.toString()),d);else if(a.addListener&&a.removeListener)E("change"===b,"MediaQueryList only has a change event"),a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Bd++;return c},Id=function(){var a=Kd,b=function(c){return a.call(b.src,b.listener,c)};return b},Cd=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)Cd(a,b[f],c,d,e);return null}c=Ed(c);return a&&
a[sd]?a.i.add(String(b),c,!0,ta(d)?!!d.capture:!!d,e):Gd(a,b,c,!0,d,e)},Ld=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ld(a,b[f],c,d,e);else(d=ta(d)?!!d.capture:!!d,c=Ed(c),a&&a[sd])?(a=a.i,b=String(b).toString(),b in a.g&&(f=a.g[b],c=xd(f,c,d,e),-1<c&&(vd(f[c]),E(null!=f.length),Array.prototype.splice.call(f,c,1),0==f.length&&(delete a.g[b],a.h--)))):a&&(a=Hd(a))&&(b=a.g[b.toString()],a=-1,b&&(a=xd(b,c,d,e)),(c=-1<a?b[a]:null)&&Md(c))},Md=function(a){if("number"!==typeof a&&
a&&!a.J){var b=a.src;if(b&&b[sd])yd(b.i,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Jd(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Bd--;(c=Hd(b))?(yd(c,a),0==c.h&&(c.src=null,b[zd]=null)):vd(a)}}},Jd=function(a){return a in Ad?Ad[a]:Ad[a]="on"+a},Kd=function(a,b){if(a.J)a=!0;else{b=new P(b,this);var c=a.listener,d=a.N||a.src;a.L&&Md(a);a=c.call(d,b)}return a},Hd=function(a){a=a[zd];return a instanceof wd?a:null},
Nd="__closure_events_fn_"+(1E9*Math.random()>>>0),Ed=function(a){E(a,"Listener can not be null.");if("function"===typeof a)return a;E(a.handleEvent,"An object listener must have handleEvent method.");a[Nd]||(a[Nd]=function(b){return a.handleEvent(b)});return a[Nd]};var Q=function(){pd.call(this);this.i=new wd(this);this.da=this;this.T=null};D(Q,pd);Q.prototype[sd]=!0;Q.prototype.removeEventListener=function(a,b,c,d){Ld(this,a,b,c,d)};
var R=function(a,b){Od(a);var c=a.T;if(c){var d=[];for(var e=1;c;c=c.T)d.push(c),E(1E3>++e,"infinite loop")}a=a.da;c=b.type||b;"string"===typeof b?b=new O(b,a):b instanceof O?b.target=b.target||a:(e=b,b=new O(c,a),bb(b,e));e=!0;if(d)for(var f=d.length-1;0<=f;f--){var g=b.g=d[f];e=Pd(g,c,!0,b)&&e}g=b.g=a;e=Pd(g,c,!0,b)&&e;e=Pd(g,c,!1,b)&&e;if(d)for(f=0;f<d.length;f++)g=b.g=d[f],e=Pd(g,c,!1,b)&&e};
Q.prototype.o=function(){Q.O.o.call(this);if(this.i){var a=this.i,b=0,c;for(c in a.g){for(var d=a.g[c],e=0;e<d.length;e++)++b,vd(d[e]);delete a.g[c];a.h--}}this.T=null};var Fd=function(a,b,c,d,e){Od(a);return a.i.add(String(b),c,!1,d,e)},Pd=function(a,b,c,d){b=a.i.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.J&&g.capture==c){var h=g.listener,k=g.N||g.src;g.L&&yd(a.i,g);e=!1!==h.call(k,d)&&e}}return e&&!d.defaultPrevented},Od=function(a){E(a.i,"Event target is not initialized. Did you call the superclass (goog.events.EventTarget) constructor?")};var Qd=function(a){try{return B.JSON.parse(a)}catch(b){}a=String(a);if(/^\s*$/.test(a)?0:/^[\],:{}\s\u2028\u2029]*$/.test(a.replace(/\\["\\\/bfnrtu]/g,"@").replace(/(?:"[^"\\\n\r\u2028\u2029\x00-\x08\x0a-\x1f]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)[\s\u2028\u2029]*(?=:|,|]|}|$)/g,"]").replace(/(?:^|:|,)(?:[\s\u2028\u2029]*\[)+/g,"")))try{return eval("("+a+")")}catch(b){}throw Error("Invalid JSON string: "+a);};var Rd=function(){};Rd.prototype.g=null;var Td=function(a){var b;(b=a.g)||(b={},Sd(a)&&(b[0]=!0,b[1]=!0),b=a.g=b);return b};var Ud,Vd=function(){};D(Vd,Rd);var Wd=function(a){return(a=Sd(a))?new ActiveXObject(a):new XMLHttpRequest},Sd=function(a){if(!a.h&&"undefined"==typeof XMLHttpRequest&&"undefined"!=typeof ActiveXObject){for(var b=["MSXML2.XMLHTTP.6.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],c=0;c<b.length;c++){var d=b[c];try{return new ActiveXObject(d),a.h=d}catch(e){}}throw Error("Could not create ActiveXObject. ActiveX might be disabled, or MSXML might not be installed");}return a.h};Ud=new Vd;var Xd=function(a,b,c){if("function"===typeof a)c&&(a=C(a,c));else if(a&&"function"==typeof a.handleEvent)a=C(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(b)?-1:B.setTimeout(a,b||0)};var S=function(a){Q.call(this);this.headers=new Map;this.K=a||null;this.j=!1;this.H=this.g=null;this.B=this.Y=this.C="";this.l=this.R=this.I=this.P=!1;this.m=0;this.F=null;this.aa="";this.G=this.U=!1};D(S,Q);S.prototype.h=Gb(Hb(),"goog.net.XhrIo").h;var Yd=/^https?$/i,Zd=["POST","PUT"],$d=[],be=function(a,b){var c=new S;$d.push(c);b&&Fd(c,"complete",b);c.i.add("ready",c.ea,!0,void 0,void 0);c.m=5E3;c.U=!0;ae(c,a);return c};S.prototype.ea=function(){this.u||(this.u=!0,this.o());Na($d,this)};
var ae=function(a,b){if(a.g)throw Error("[goog.net.XhrIo] Object is active with another request="+a.C+"; newUri="+b);a.C=b;a.B="";a.Y="GET";a.P=!1;a.j=!0;a.g=a.K?Wd(a.K):Wd(Ud);a.H=a.K?Td(a.K):Td(Ud);a.g.onreadystatechange=C(a.Z,a);try{J(a.h,T(a,"Opening Xhr")),a.R=!0,a.g.open("GET",String(b),!0),a.R=!1}catch(e){J(a.h,T(a,"Error opening Xhr: "+e.message));ce(a,e);return}b=new Map(a.headers);var c=Array.from(b.keys()).find(function(e){return"content-type"==e.toLowerCase()}),d=B.FormData&&!1;!(0<=Ma(Zd,
"GET"))||c||d||b.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=v(b);for(c=b.next();!c.done;c=b.next())d=v(c.value),c=d.next().value,d=d.next().value,a.g.setRequestHeader(c,d);a.aa&&(a.g.responseType=a.aa);"withCredentials"in a.g&&a.g.withCredentials!==a.U&&(a.g.withCredentials=a.U);try{de(a),0<a.m&&(a.G=ee(a.g),J(a.h,T(a,"Will abort after "+a.m+"ms if incomplete, xhr2 "+a.G)),a.G?(a.g.timeout=a.m,a.g.ontimeout=C(a.ba,a)):a.F=Xd(a.ba,a.m,a)),J(a.h,T(a,"Sending request")),
a.I=!0,a.g.send(""),a.I=!1}catch(e){J(a.h,T(a,"Send error: "+e.message)),ce(a,e)}},ee=function(a){return Za&&"number"===typeof a.timeout&&void 0!==a.ontimeout};S.prototype.ba=function(){"undefined"!=typeof ra&&this.g&&(this.B="Timed out after "+this.m+"ms, aborting",J(this.h,T(this,this.B)),R(this,"timeout"),this.abort(8))};var ce=function(a,b){a.j=!1;a.g&&(a.l=!0,a.g.abort(),a.l=!1);a.B=b;fe(a);ge(a)},fe=function(a){a.P||(a.P=!0,R(a,"complete"),R(a,"error"))};
S.prototype.abort=function(){this.g&&this.j&&(J(this.h,T(this,"Aborting")),this.j=!1,this.l=!0,this.g.abort(),this.l=!1,R(this,"complete"),R(this,"abort"),ge(this))};S.prototype.o=function(){this.g&&(this.j&&(this.j=!1,this.l=!0,this.g.abort(),this.l=!1),ge(this,!0));S.O.o.call(this)};S.prototype.Z=function(){this.u||(this.R||this.I||this.l?he(this):this.fa())};S.prototype.fa=function(){he(this)};
var he=function(a){if(a.j&&"undefined"!=typeof ra)if(a.H[1]&&4==ie(a)&&2==je(a))J(a.h,T(a,"Local request error detected and ignored"));else if(a.I&&4==ie(a))Xd(a.Z,0,a);else if(R(a,"readystatechange"),4==ie(a)){J(a.h,T(a,"Request complete"));a.j=!1;try{if(ke(a))R(a,"complete"),R(a,"success");else{try{var b=2<ie(a)?a.g.statusText:""}catch(c){J(a.h,"Can not get status: "+c.message),b=""}a.B=b+" ["+je(a)+"]";fe(a)}}finally{ge(a)}}},ge=function(a,b){if(a.g){de(a);var c=a.g,d=a.H[0]?function(){}:null;
a.g=null;a.H=null;b||R(a,"ready");try{c.onreadystatechange=d}catch(e){Jb(a.h,"Problem encountered resetting onreadystatechange: "+e.message)}}},de=function(a){a.g&&a.G&&(a.g.ontimeout=null);a.F&&(B.clearTimeout(a.F),a.F=null)};S.prototype.isActive=function(){return!!this.g};
var ke=function(a){var b=je(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=0===b)a=String(a.C).match(qb)[1]||null,!a&&B.self&&B.self.location&&(a=B.self.location.protocol.slice(0,-1)),b=!Yd.test(a?a.toLowerCase():"");c=b}return c},ie=function(a){return a.g?a.g.readyState:0},je=function(a){try{return 2<ie(a)?a.g.status:-1}catch(b){return-1}},T=function(a,b){return b+" ["+a.Y+" "+a.C+" "+je(a)+"]"};var U=function(a){this.h=this.o=this.j="";this.u=null;this.m=this.g="";this.l=!1;var b;a instanceof U?(this.l=a.l,le(this,a.j),this.o=a.o,this.h=a.h,me(this,a.u),this.g=a.g,ne(this,oe(a.i)),this.m=a.m):a&&(b=String(a).match(qb))?(this.l=!1,le(this,b[1]||"",!0),this.o=pe(b[2]||""),this.h=pe(b[3]||"",!0),me(this,b[4]),this.g=pe(b[5]||"",!0),ne(this,b[6]||"",!0),this.m=pe(b[7]||"")):(this.l=!1,this.i=new V(null,this.l))};
U.prototype.toString=function(){var a=[],b=this.j;b&&a.push(qe(b,re,!0),":");var c=this.h;if(c||"file"==b)a.push("//"),(b=this.o)&&a.push(qe(b,re,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.u,null!=c&&a.push(":",String(c));if(c=this.g)this.h&&"/"!=c.charAt(0)&&a.push("/"),a.push(qe(c,"/"==c.charAt(0)?se:te,!0));(c=this.i.toString())&&a.push("?",c);(c=this.m)&&a.push("#",qe(c,ue));return a.join("")};
U.prototype.resolve=function(a){var b=new U(this),c=!!a.j;c?le(b,a.j):c=!!a.o;c?b.o=a.o:c=!!a.h;c?b.h=a.h:c=null!=a.u;var d=a.g;if(c)me(b,a.u);else if(c=!!a.g){if("/"!=d.charAt(0))if(this.h&&!this.g)d="/"+d;else{var e=b.g.lastIndexOf("/");-1!=e&&(d=b.g.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||1==f.length&&""!=
f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.g=d:c=""!==a.i.toString();c?ne(b,oe(a.i)):c=!!a.m;c&&(b.m=a.m);return b};
var le=function(a,b,c){a.j=c?pe(b,!0):b;a.j&&(a.j=a.j.replace(/:$/,""))},me=function(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.u=b}else a.u=null},ne=function(a,b,c){b instanceof V?(a.i=b,ve(a.i,a.l)):(c||(b=qe(b,we)),a.i=new V(b,a.l))},pe=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},qe=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,xe),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},xe=function(a){a=
a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},re=/[#\/\?@]/g,te=/[#\?:]/g,se=/[#\?]/g,we=/[#\?@]/g,ue=/#/g,V=function(a,b){this.h=this.g=null;this.i=a||null;this.j=!!b},W=function(a){a.g||(a.g=new Map,a.h=0,a.i&&rb(a.i,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};V.prototype.add=function(a,b){W(this);this.i=null;a=X(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h=Da(this.h)+1;return this};
var ye=function(a,b){W(a);b=X(a,b);a.g.has(b)&&(a.i=null,a.h=Da(a.h)-a.g.get(b).length,a.g.delete(b))};V.prototype.clear=function(){this.g=this.i=null;this.h=0};var ze=function(a,b){W(a);b=X(a,b);return a.g.has(b)};V.prototype.forEach=function(a,b){W(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};
var Ae=function(a,b){W(a);var c=[];if("string"===typeof b)ze(a,b)&&(c=c.concat(a.g.get(X(a,b))));else for(a=Array.from(a.g.values()),b=0;b<a.length;b++)c=c.concat(a[b]);return c};V.prototype.set=function(a,b){W(this);this.i=null;a=X(this,a);ze(this,a)&&(this.h=Da(this.h)-this.g.get(a).length);this.g.set(a,[b]);this.h=Da(this.h)+1;return this};V.prototype.get=function(a,b){if(!a)return b;a=Ae(this,a);return 0<a.length?String(a[0]):b};
V.prototype.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=Ae(this,d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.i=a.join("&")};
var oe=function(a){var b=new V;b.i=a.i;a.g&&(b.g=new Map(a.g),b.h=a.h);return b},X=function(a,b){b=String(b);a.j&&(b=b.toLowerCase());return b},ve=function(a,b){b&&!a.j&&(W(a),a.i=null,a.g.forEach(function(c,d){var e=d.toLowerCase();if(d!=e&&(ye(this,d),ye(this,e),0<c.length)){this.i=null;d=this.g;var f=d.set;e=X(this,e);var g=c.length;if(0<g){for(var h=Array(g),k=0;k<g;k++)h[k]=c[k];g=h}else g=[];f.call(d,e,g);this.h=Da(this.h)+c.length}},a));a.j=b};var Be=ea(["https://www.googleadservices.com/pagead/conversion/","/wcm"]),Ce=ea(["https://www.googleadservices.com/ga/phone"]),De=[2],Ee=void 0,Fe=new function(){};Fe.refreshPeriod=3E5;Fe.refreshDuration=108E5;
var Ge=function(){this.autoRefresh=!0},He=function(){},Ie=function(){},Je=function(){},Ke=function(){},Le=function(a){this.g=a},Me=function(a,b){this.g=a;this.h=b},Ne=function(a){var b=a.g.adData;return null==b?(Y("adData is null"),!1):null==b.cl||null==b.ak?(Y("missing conversion label or advertiser key"),!1):null==a.g.destinationNumber?(Y("no replace number"),!1):!0},Oe=function(){},Qe=function(a,b,c){var d=this;this.g=b;this.l=b.destinationNumber;this.j=c||new Ge;this.i=!1;"function"===typeof a?
(this.i=!0,this.j.autoRefresh=!0):null==a&&(a=function(e,f){Pe(d.l,e,f)});this.m=a;this.h=Fe},$e=function(a){var b,c,d,e,f,g;return y(function(h){if(1==h.g){if(!Re(a))return h.return();b=Se(a);if(null==b)return h.return();c=Te(b);d=Ue(c);e=null===d?Ve(b):d;return x(h,We(e).catch(function(k){Y(k);return Fe}),2)}a.h=h.m;f=a.h;Xe(c,e,Date.now()+f.refreshDuration);f.phoneNumber&&f.formattedPhoneNumber&&(g=Ye(f.phoneNumber,a.g.destinationNumber,f.formattedPhoneNumber),Ze(a,g,f.phoneNumber));h.g=0})},bf=
function(a){var b,c,d;return y(function(e){if(1==e.g)return x(e,$e(a),2);if(!a.j.autoRefresh)return e.return();b=a.h.refreshPeriod||3E5;c=setInterval(function(){if(!document.hidden||a.i)return $e(a)},b);d=Dd(document,"visibilitychange",a.o.bind(a));setTimeout(function(){clearInterval(c);Md(d);setTimeout(function(){af(a)},2*b)},a.h.refreshDuration||108E5);e.g=0})};Qe.prototype.o=function(){var a=this;return y(function(b){if(document.hidden&&!a.i)return b.return();af(a);return b.return($e(a))})};
var Re=function(a){return null==a.g.destinationNumber?(Y("no destination number"),!1):null==a.g.countryNameCode?(Y("no country name code"),!1):"function"!==typeof a.m&&"string"!==typeof a.m?(Y("Invalid receiver."),!1):!0},Se=function(a){var b=new Oe;if(null==a.g.adData)var c=null;else{var d=new Me(a.g,a.j);if(Ne(d)){c=new He;var e=d.g.adData;c.ak=e.ak;c.cl=e.cl;null!=e.dma&&(c.dma=e.dma);null!=e.dmaCps&&(c.dmaCps=e.dmaCps);null!=e.npa&&(c.npa=e.npa);c.alloc=d.h&&d.h.alloc;e=window;d=document;var f=
e.location.href;f=(f instanceof U?new U(f):new U(f)).i.get("gclid");c.v=f||"";c.ta=d.referrer;f={};for(var g=0;g<dd.length;g++){var h=dd[g];f[h]=e[h]}f.onload_callback=e.onload_callback;c.ra=Hc(d,f.google_gcl_cookie_prefix);e=uc();if(!La().g()||rc(e)){e=vc();f={};if(e&&e.length)for(g=0;g<e.length;g++)h=e[g].value.split("."),"1"===h[0]&&3===h.length&&Number(h[1])&&(f[e[g].X]||(f[e[g].X]=[]),f[e[g].X].push({version:h[0],timestamp:1E3*Number(h[1]),v:h[2]}));e=f}else e={};if(Dc.test(d.location.host))var k=
(k=d.location.href.match(Ec))&&2==k.length&&k[1].match(Bc)?decodeURIComponent(k[1]):"";else{d=[];for(k in e){f=[];g=e[k];for(h=0;h<g.length;h++)f.push(g[h].v);d.push(k+":"+f.join(","))}k=0<d.length?d.join(";"):""}c.ja=k}else c=null}b.g=c;if(null==a.g.gaData)var l=null;else{c=new Le(a.g);k=c.g.gaData;if(null==k){Y("gaData is null");var m=!1}else if(null==k.gaWpid)Y("missing gaWpid"),m=!1;else{c:if(k=k.gaWpid,(d=window._gaUserPrefs)&&d.ioo&&d.ioo()||document.getElementById("__gaOptOutExtension")||document.documentElement.hasAttribute("data-google-analytics-opt-out")||
!0===window["ga-disable-"+k])m=!0;else{try{var p=window.external;if(p&&"oo"==p._gaUserPrefs){m=!0;break c}}catch(id){}m=!1}m=m?!1:!0}if(m){m=new Ke;m.gaWpid=c.g.gaData.gaWpid;p=0;try{p=window.history.length}catch(id){}var w=p;p=document.domain;d=document.cookie;h=window.screen;g=document.referrer;if(hd())l=window.gaGlobal||{};else{e=Math.round((new Date).getTime()/1E3);f=window.google_analytics_domain_name;p="undefined"==typeof f?ld("auto",p):ld(f,p);var z=-1<d.indexOf("__utma="+p+"."),N=-1<d.indexOf("__utmb="+
p);(c=jd().gaGlobal)||(c={},jd().gaGlobal=c);k=!1;if(z){var n=d.split("__utma="+p+".")[1].split(";")[0].split(".");N?c.sid=n[3]+"":c.sid||(c.sid=e+"");c.vid=n[0]+"."+n[1];c.from_cookie=!0}else{c.sid||(c.sid=e+"");if(!c.vid){k=!0;N=Math.round(2147483647*Math.random());z=M.appName;var nf=M.version,of=M.language?M.language:M.browserLanguage,pf=M.platform,qf=M.userAgent;try{n=M.javaEnabled()}catch(id){n=!1}n=[z,nf,of,pf,qf,n?1:0].join("");h?n+=h.width+"x"+h.height+h.colorDepth:B.java&&B.java.awt&&(h=
B.java.awt.Toolkit.getDefaultToolkit().getScreenSize(),n+=h.screen.width+"x"+h.screen.height);n=n+d+(g||"");for(g=n.length;0<w;)n+=w--^g++;c.vid=(N^kd(n)&2147483647)+"."+e}c.from_cookie||(c.from_cookie=!1)}if(!c.cid){c:for(e=f,n=999,e&&(e=0==e.indexOf(".")?e.substr(1):e,n=(""+e).split(".").length),e=999,d=d.split(";"),f=0;f<d.length;f++)if(g=md.exec(d[f])||nd.exec(d[f])||od.exec(d[f])){h=g[1]||0;if(h==n){l=g[2];break c}h<e&&(e=h,l=g[2])}k&&l&&-1!=l.search(/^\d+\.\d+$/)?(c.vid=l,c.from_cookie=!0):
l!=c.vid&&(c.cid=l)}c.dh=p;c.hid||(c.hid=Math.round(2147483647*Math.random()));l=c}m.ia=l.cid;m.na=l.vid;m.ka=l.hid;(l=document.location)?(n=l.pathname||"","/"!=n.charAt(0)&&(n="/"+n),l=l.protocol+"//"+l.hostname+n+l.search):l=void 0;m.la=l;n=document;l=n.referrer;if(p=/^(https?|android-app):\/\//i.test(l)){c:{n="//"+n.location.hostname;p=l.indexOf(n);if(5==p||6==p)if(n=l.charAt(p+n.length),"/"==n||"?"==n||""==n||":"==n){n=!0;break c}n=!1}p=!n}m.ma=p?l:void 0;l=m}else l=null}b.h=l;if(null==b.g&&null==
b.h)return Y("missing call tracking data"),null;b.experimentIDs=(a.j||{}).experimentIDs||De;b.i=a.g.destinationNumber.replace(/[^0-9A-Z]/gi,"");b.countryNameCode=a.g.countryNameCode;return b},Ze=function(a,b,c){var d=a.m;if("function"===typeof d)cf()||(Y("Sending ",b," to callback"),Z("success")),d(b,c);else if(d){var e=[],f=document.getElementById(d);f&&e.push(f);(f=document)?(E(f,"Node cannot be null or undefined."),f=new pb(9==f.nodeType?f:f.ownerDocument||f.document)):f=za||(za=new pb);var g=
(f=f.g)||document;f=g.querySelectorAll&&g.querySelector?g.querySelectorAll("."+d):ob(d,f);for(g=0;g!==f.length;++g)e.push(f[g]);if(0===e.length)Y("Found no elements matching ",d),Z("receiver not found");else{for(d=0;d!==e.length;d++){for(g=e[d];f=g.firstChild;)g.removeChild(f);e[d].appendChild(document.createTextNode(b));f=df(e[d],c);null!==f&&void 0===Ee&&(Ee=f)}Y("Updated ",e.length," element(s) with ",b);Z("success")}}a.l=b},af=function(a){!a.i&&Ee&&Ze(a,a.g.destinationNumber,Ee)},ef=null,ff=!1;
function gf(){var a=document.getElementById("google-wcc-debug-window");if(a)return a;var b=function(d){if("string"===typeof d)return document.createTextNode(d);var e=document.createElement(d[0]);e.setAttribute("style",d[1]+";");if(d[2])for(var f=0;f!==d[2].length;++f)e.appendChild(b(d[2][f]));return e},c=b(["div","background-color:#fafafa;color:#000;border:1px solid #ddd;border-radius:3px;font-family:sans-serif;font-size:13px;position:fixed;bottom:0;left:0;width:40em;display:block;text-align:left;z-index:2147483645",
[["div","color:#222;font-size:16px;border-bottom:1px solid #ddd;padding:13px",["Google AdWords Website Call Conversions",["span","float:right",[["button","border:1px solid #ddd;background-color:#fff;font-weight:bold;font-size:12px;padding:4px 8px;color:#444;box-shadow:#eee 0px 1px 0px 0px",["Force"]],["button","border:1px solid #ddd;background-color:#fff;font-weight:bold;font-size:12px;padding:4px 8px;color:#444;box-shadow:#eee 0px 1px 0px 0px",["Close"]]]]]],["div","height:10em;padding:10px;overflow-y:auto"]]]);
document.body.appendChild(c);a=c.childNodes[0].childNodes[1].childNodes;a[0].onclick=function(){return y(function(d){if(ef){var e=ef;var f=e.l.replace(/\d/g,"9");f=["+"+f.replace(/[^0-9]/g,""),f];var g=v(f);f=g.next().value;g=g.next().value;Ze(e,g,f)}d.g=0})};a[1].onclick=function(){c.style.display="none"};a=c.childNodes[1];a.id="google-wcc-debug-window";return a}
function Y(a){if(ff){for(var b="",c=0;c!==arguments.length;++c)"string"!==typeof arguments[c]&&(arguments[c]=JSON.stringify(arguments[c])),b+=arguments[c];""!==b&&("."!==b[b.length-1]&&(b+="."),c=gf(),c.appendChild(document.createTextNode(b)),c.appendChild(document.createElement("br")))}}function Te(a){var b=[];a.g&&b.push(a.g.cl);a.h&&b.push(a.h.gaWpid);b.push(a.i);return b.join(",")}
function Ue(a){var b=window.localStorage.getItem(a+"_expiresAt");return null==b||Number(b)<Date.now()?null:window.localStorage.getItem(a)}function Xe(a,b,c){var d=window.localStorage,e=d.getItem(a+"_expiresAt");if(null==e||Number(e)<Date.now())d.setItem(a+"_expiresAt",c.toString()),d.setItem(a,b)}
function Ve(a){var b=a.h||{},c=a.g||{},d={cc:a.countryNameCode,dn:a.i,ga_wpid:b.gaWpid,ga_vid:b.na,ga_hid:b.ka,ga_cid:b.ia,ga_loc:b.la,ga_ref:b.ma,cl:c.cl,alloc:c.alloc,gclid:c.v,ref:c.ta,gac:c.ja,gclaw:c.ra,dma:c.dma,dma_cps:c.dmaCps,npa:c.npa,ct_eid:(a.experimentIDs||[]).join()};Object.keys(d).forEach(function(e){return(null==d[e]||0===d[e].length)&&delete d[e]});return a.g?gd(fd(Be,c.ak),new Map(Object.entries(d))).toString():gd(fd(Ce),new Map(Object.entries(d))).toString()}
function hf(a){switch(a){case 2:return"no ad click";case 4:return"not tracked";case 8:return"temporary error"}return"error #"+a}
function We(a){return new Promise(function(b,c){Fd(be(a,function(d){if(ke(d.target)&&200==je(d.target)){d=d.target;if(d.g)b:{d=d.g.responseText;if(B.JSON)try{var e=B.JSON.parse(d);E("object"==typeof e);var f=e;break b}catch(g){}f=Qd(d)}else f=void 0;f.errorCode&&Z(hf(f.errorCode));b(f)}else c("request phone number failure")}),"timeout",function(){c("request timeout")})})}
function jf(a){if(a=!window.google_no_debug&&(!a||!a.noDebug))a=window.location.hash,a=a.startsWith("#google-wcc-")||a.startsWith("#google-phone-")||"undefined"!==typeof window.sessionStorage&&"y"===window.sessionStorage.getItem("_goog_wcc_debug");ff=a}function cf(){var a=window.google_replace_number;return"string"===typeof a&&0<a.length}function Z(a){window.google_wcc_status=a}
function Ye(a,b,c){if(a.match(/^\+1\d{10}$/)){var d=b.match(/\d/g);d=d?d.length:0;if(10!==d&&11!==d)b=null;else{d=11===d?1:2;for(var e="",f=0;f!==b.length;++f)e=b.charAt(f).match(/\d/)?e+a.charAt(d++):e+b.charAt(f);b=e}}else b=null;return b?b:c?c:a}
function df(a,b){a:{for(;null!=a;a=a.parentNode)if("A"===a.nodeName){if(a.href&&"tel:"===a.href.substr(0,4))break a;break}a=null}if(null!=a){var c=a.href.slice(4);b="tel:"+b;var d=[Xb];d=void 0===d?Yb:d;a:if(d=void 0===d?Yb:d,b instanceof H)d=b;else{for(var e=0;e<d.length;++e){var f=d[e];if(f instanceof Wb&&f.pa(b)){d=new H(b,gb);break a}}d=void 0}void 0===d&&ac(b.toString());b=d||hb;if(b instanceof H)if(b instanceof H&&b.constructor===H)var g=b.g;else Ca("expected object of type SafeUrl, got '"+
b+"' of type "+sa(b)),g="type_error:SafeUrl";else{b:if(Zb){try{g=new URL(b)}catch(h){g="https:";break b}g=g.protocol}else c:{g=document.createElement("a");try{g.href=b}catch(h){g=void 0;break c}g=g.protocol;g=":"===g||""===g?"https:":g}"javascript:"===g?(ac(b),g=void 0):g=b}void 0!==g&&(a.href=g);return c}return null}
function kf(a,b,c){var d=function(e){if(1===e.nodeType&&("SCRIPT"===e.nodeName||"google-wcc-debug-window"===e.id))return 0;var f=0;if(3===e.nodeType&&-1!==e.data.search(a)){e.data=e.data.replace(a,b);var g=df(e,c);null!==g&&void 0===Ee&&(Ee=g);++f}e=e.childNodes;for(g=0;g!==e.length;g++)f+=d(e[g]);return f};return d(document.body)}
function Pe(a,b,c){var d=10,e=a.replace(/[^0-9]/g,"");if(0===e.length)Y('The specified number "',a,'" cannot be replaced'),Z("autoreplace fail");else{var f=new RegExp("\\(?"+e.split("").join("[^0-9]{0,3}"),"g"),g=function(){var h=kf(f,b,c);if(0<h){if(Y("Updated ",h," element(s) with ",b),Z("success"),"loading"===document.readyState){var k=function(){"complete"===document.readyState?kf(f,b,c):Cd(document,"readystatechange",k)};Cd(document,"readystatechange",k)}}else 0<d--?setTimeout(g,500):(Y("Could not find ",
a," in the page"),Z("autoreplace fail"))};g()}}function lf(a,b,c,d,e,f,g){var h;return y(function(k){jf(g);cf()||c.autoreplace?Y("Attempting to auto-replace"):"function"===typeof b?Y("Using callback as target"):"string"===typeof b&&Y("Using CSS class '",b,"' as target");e&&clearTimeout(e);h=new Je;h.adData=c;h.destinationNumber=d||c.autoreplace;h.countryNameCode="ZZ";return x(k,mf(b,h,g),0)})}
function rf(a,b,c){var d=arguments,e,f,g;return y(function(h){for(e=3;e<d.length;++e)f=d[e],null!=f&&(f.hasOwnProperty("replace")&&(c.replace=d[e].replace),f.hasOwnProperty("receiver")&&(c.receiver=d[e].receiver),f.hasOwnProperty("destination")&&(c.destination=d[e].destination));if(null==c.ga_wpid)return h.return();g=new Je;g.destinationNumber=c.replace||c.destination;g.countryNameCode="US";g.gaData=new Ie;g.gaData.gaWpid=c.ga_wpid;return x(h,mf(c.receiver,g,null),0)})}
function mf(a,b,c){var d;return y(function(e){jf(c);ef=d=new Qe(a,b,c);return x(e,bf(d),0)})}window._googWcmImpl&&function(){var a,b;return y(function(c){if(1==c.g){a=window._googWcmImpl.q;if(!a){c.g=0;return}b=0}if(4!=c.g)return b===a.length?(c.g=0,c=void 0):c=x(c,lf.apply(void 0,[].slice.apply(a[b])),4),c;++b;c.g=3})}();
window._googCallTrackingImpl&&function(){var a,b;return y(function(c){if(1==c.g){a=window._googCallTrackingImpl.q;if(!a){c.g=0;return}b=0}if(4!=c.g)return b===a.length?(c.g=0,c=void 0):c=x(c,mf.apply(void 0,[].slice.apply(a[b])),4),c;++b;c.g=3})}();window._gaPhoneImpl&&function(){var a,b;return y(function(c){if(1==c.g){a=window._gaPhoneImpl.q;if(!a){c.g=0;return}b=0}if(4!=c.g)return b===a.length?(c.g=0,c=void 0):c=x(c,rf.apply(void 0,[].slice.apply(a[b])),4),c;++b;c.g=3})}();wa("_googWcmImpl",lf);
wa("_googWccDebug",function(a){if("undefined"===typeof window.sessionStorage)return"WCC debugging not available.";"enable"===a?window.sessionStorage.setItem("_goog_wcc_debug","y"):"disable"===a&&window.sessionStorage.removeItem("_goog_wcc_debug");return"WCC debugging is "+("y"===window.sessionStorage.getItem("_goog_wcc_debug")?"enabled.":"disabled.")});wa("_googCallTrackingImpl",mf);wa("_gaPhoneImpl",rf);if(cf()){var sf=window.google_replace_number;window._googWcmGet(function(a,b){Pe(sf,a,b)},sf)};}).call(this);
