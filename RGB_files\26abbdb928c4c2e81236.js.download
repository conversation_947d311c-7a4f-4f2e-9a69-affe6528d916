(()=>{var e,t,r={1291:(e,t,r)=>{"use strict";const n=r(6150),o=r(6086);class s extends Error{constructor(e){if(!Array.isArray(e))throw new TypeError("Expected input to be an Array, got "+typeof e);let t=(e=[...e].map((e=>e instanceof Error?e:null!==e&&"object"==typeof e?Object.assign(new Error(e.message),e):new Error(e)))).map((e=>"string"==typeof e.stack?o(e.stack).replace(/\s+at .*aggregate-error\/index.js:\d+:\d+\)?/g,""):String(e))).join("\n");t="\n"+n(t,4),super(t),this.name="AggregateError",Object.defineProperty(this,"_errors",{value:e})}*[Symbol.iterator](){for(const e of this._errors)yield e}}e.exports=s},6086:(e,t,r)=>{"use strict";const n=r(1209),o=/\s+at.*(?:\(|\s)(.*)\)?/,s=/^(?:(?:(?:node|(?:internal\/[\w/]*|.*node_modules\/(?:babel-polyfill|pirates)\/.*)?\w+)\.js:\d+:\d+)|native)/,a=void 0===n.homedir?"":n.homedir();e.exports=(e,t)=>(t=Object.assign({pretty:!1},t),e.replace(/\\/g,"/").split("\n").filter((e=>{const t=e.match(o);if(null===t||!t[1])return!0;const r=t[1];return!r.includes(".app/Contents/Resources/electron.asar")&&!r.includes(".app/Contents/Resources/default_app.asar")&&!s.test(r)})).filter((e=>""!==e.trim())).map((e=>t.pretty?e.replace(o,((e,t)=>e.replace(t,t.replace(a,"~")))):e)).join("\n"))},3531:e=>{e.exports=function(e){return btoa(e)}},8879:function(e){e.exports=function(){"use strict";var e=1e3,t=6e4,r=36e5,n="millisecond",o="second",s="minute",a="hour",i="day",c="week",u="month",d="quarter",l="year",f="date",p="Invalid Date",h=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,y={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},v=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},g={s:v,z:function(e){var t=-e.utcOffset(),r=Math.abs(t),n=Math.floor(r/60),o=r%60;return(t<=0?"+":"-")+v(n,2,"0")+":"+v(o,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),o=t.clone().add(n,u),s=r-o<0,a=t.clone().add(n+(s?-1:1),u);return+(-(n+(r-o)/(s?o-a:a-o))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:u,y:l,w:c,d:i,D:f,h:a,m:s,s:o,ms:n,Q:d}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},_="en",E={};E[_]=y;var w=function(e){return e instanceof O},$=function(e,t,r){var n;if(!e)return _;if("string"==typeof e)E[e]&&(n=e),t&&(E[e]=t,n=e);else{var o=e.name;E[o]=e,n=o}return!r&&n&&(_=n),n||!r&&_},b=function(e,t){if(w(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new O(r)},T=g;T.l=$,T.i=w,T.w=function(e,t){return b(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var O=function(){function y(e){this.$L=$(e.locale,null,!0),this.parse(e)}var v=y.prototype;return v.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(T.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(h);if(n){var o=n[2]-1||0,s=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],o,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)):new Date(n[1],o,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)}}return new Date(t)}(e),this.$x=e.x||{},this.init()},v.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},v.$utils=function(){return T},v.isValid=function(){return!(this.$d.toString()===p)},v.isSame=function(e,t){var r=b(e);return this.startOf(t)<=r&&r<=this.endOf(t)},v.isAfter=function(e,t){return b(e)<this.startOf(t)},v.isBefore=function(e,t){return this.endOf(t)<b(e)},v.$g=function(e,t,r){return T.u(e)?this[t]:this.set(r,e)},v.unix=function(){return Math.floor(this.valueOf()/1e3)},v.valueOf=function(){return this.$d.getTime()},v.startOf=function(e,t){var r=this,n=!!T.u(t)||t,d=T.p(e),p=function(e,t){var o=T.w(r.$u?Date.UTC(r.$y,t,e):new Date(r.$y,t,e),r);return n?o:o.endOf(i)},h=function(e,t){return T.w(r.toDate()[e].apply(r.toDate("s"),(n?[0,0,0,0]:[23,59,59,999]).slice(t)),r)},m=this.$W,y=this.$M,v=this.$D,g="set"+(this.$u?"UTC":"");switch(d){case l:return n?p(1,0):p(31,11);case u:return n?p(1,y):p(0,y+1);case c:var _=this.$locale().weekStart||0,E=(m<_?m+7:m)-_;return p(n?v-E:v+(6-E),y);case i:case f:return h(g+"Hours",0);case a:return h(g+"Minutes",1);case s:return h(g+"Seconds",2);case o:return h(g+"Milliseconds",3);default:return this.clone()}},v.endOf=function(e){return this.startOf(e,!1)},v.$set=function(e,t){var r,c=T.p(e),d="set"+(this.$u?"UTC":""),p=(r={},r[i]=d+"Date",r[f]=d+"Date",r[u]=d+"Month",r[l]=d+"FullYear",r[a]=d+"Hours",r[s]=d+"Minutes",r[o]=d+"Seconds",r[n]=d+"Milliseconds",r)[c],h=c===i?this.$D+(t-this.$W):t;if(c===u||c===l){var m=this.clone().set(f,1);m.$d[p](h),m.init(),this.$d=m.set(f,Math.min(this.$D,m.daysInMonth())).$d}else p&&this.$d[p](h);return this.init(),this},v.set=function(e,t){return this.clone().$set(e,t)},v.get=function(e){return this[T.p(e)]()},v.add=function(n,d){var f,p=this;n=Number(n);var h=T.p(d),m=function(e){var t=b(p);return T.w(t.date(t.date()+Math.round(e*n)),p)};if(h===u)return this.set(u,this.$M+n);if(h===l)return this.set(l,this.$y+n);if(h===i)return m(1);if(h===c)return m(7);var y=(f={},f[s]=t,f[a]=r,f[o]=e,f)[h]||1,v=this.$d.getTime()+n*y;return T.w(v,this)},v.subtract=function(e,t){return this.add(-1*e,t)},v.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||p;var n=e||"YYYY-MM-DDTHH:mm:ssZ",o=T.z(this),s=this.$H,a=this.$m,i=this.$M,c=r.weekdays,u=r.months,d=function(e,r,o,s){return e&&(e[r]||e(t,n))||o[r].substr(0,s)},l=function(e){return T.s(s%12||12,e,"0")},f=r.meridiem||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n},h={YY:String(this.$y).slice(-2),YYYY:this.$y,M:i+1,MM:T.s(i+1,2,"0"),MMM:d(r.monthsShort,i,u,3),MMMM:d(u,i),D:this.$D,DD:T.s(this.$D,2,"0"),d:String(this.$W),dd:d(r.weekdaysMin,this.$W,c,2),ddd:d(r.weekdaysShort,this.$W,c,3),dddd:c[this.$W],H:String(s),HH:T.s(s,2,"0"),h:l(1),hh:l(2),a:f(s,a,!0),A:f(s,a,!1),m:String(a),mm:T.s(a,2,"0"),s:String(this.$s),ss:T.s(this.$s,2,"0"),SSS:T.s(this.$ms,3,"0"),Z:o};return n.replace(m,(function(e,t){return t||h[e]||o.replace(":","")}))},v.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},v.diff=function(n,f,p){var h,m=T.p(f),y=b(n),v=(y.utcOffset()-this.utcOffset())*t,g=this-y,_=T.m(this,y);return _=(h={},h[l]=_/12,h[u]=_,h[d]=_/3,h[c]=(g-v)/6048e5,h[i]=(g-v)/864e5,h[a]=g/r,h[s]=g/t,h[o]=g/e,h)[m]||g,p?_:T.a(_)},v.daysInMonth=function(){return this.endOf(u).$D},v.$locale=function(){return E[this.$L]},v.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=$(e,t,!0);return n&&(r.$L=n),r},v.clone=function(){return T.w(this.$d,this)},v.toDate=function(){return new Date(this.valueOf())},v.toJSON=function(){return this.isValid()?this.toISOString():null},v.toISOString=function(){return this.$d.toISOString()},v.toString=function(){return this.$d.toUTCString()},y}(),S=O.prototype;return b.prototype=S,[["$ms",n],["$s",o],["$m",s],["$H",a],["$W",i],["$M",u],["$y",l],["$D",f]].forEach((function(e){S[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),b.extend=function(e,t){return e.$i||(e(t,O,b),e.$i=!0),b},b.locale=$,b.isDayjs=w,b.unix=function(e){return b(1e3*e)},b.en=E[_],b.Ls=E,b.p={},b}()},2699:e=>{"use strict";var t,r="object"==typeof Reflect?Reflect:null,n=r&&"function"==typeof r.apply?r.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};t=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var o=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}e.exports=s,e.exports.once=function(e,t){return new Promise((function(r,n){function o(r){e.removeListener(t,s),n(r)}function s(){"function"==typeof e.removeListener&&e.removeListener("error",o),r([].slice.call(arguments))}m(e,t,s,{once:!0}),"error"!==t&&function(e,t,r){"function"==typeof e.on&&m(e,"error",t,r)}(e,o,{once:!0})}))},s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var a=10;function i(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function u(e,t,r,n){var o,s,a,u;if(i(r),void 0===(s=e._events)?(s=e._events=Object.create(null),e._eventsCount=0):(void 0!==s.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),s=e._events),a=s[t]),void 0===a)a=s[t]=r,++e._eventsCount;else if("function"==typeof a?a=s[t]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),(o=c(e))>0&&a.length>o&&!a.warned){a.warned=!0;var d=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");d.name="MaxListenersExceededWarning",d.emitter=e,d.type=t,d.count=a.length,u=d,console&&console.warn&&console.warn(u)}return e}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},o=d.bind(n);return o.listener=r,n.wrapFn=o,o}function f(e,t,r){var n=e._events;if(void 0===n)return[];var o=n[t];return void 0===o?[]:"function"==typeof o?r?[o.listener||o]:[o]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(o):h(o,o.length)}function p(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function h(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function m(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function o(s){n.once&&e.removeEventListener(t,o),r(s)}))}}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return c(this)},s.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var o="error"===e,s=this._events;if(void 0!==s)o=o&&void 0===s.error;else if(!o)return!1;if(o){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var i=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw i.context=a,i}var c=s[e];if(void 0===c)return!1;if("function"==typeof c)n(c,this,t);else{var u=c.length,d=h(c,u);for(r=0;r<u;++r)n(d[r],this,t)}return!0},s.prototype.addListener=function(e,t){return u(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return u(this,e,t,!0)},s.prototype.once=function(e,t){return i(t),this.on(e,l(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){return i(t),this.prependListener(e,l(this,e,t)),this},s.prototype.removeListener=function(e,t){var r,n,o,s,a;if(i(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(o=-1,s=r.length-1;s>=0;s--)if(r[s]===t||r[s].listener===t){a=r[s].listener,o=s;break}if(o<0)return this;0===o?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,o),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,a||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var o,s=Object.keys(r);for(n=0;n<s.length;++n)"removeListener"!==(o=s[n])&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},s.prototype.listeners=function(e){return f(this,e,!0)},s.prototype.rawListeners=function(e){return f(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):p.call(e,t)},s.prototype.listenerCount=p,s.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},9378:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,o,s;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(!e(t[o],r[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(s=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,s[o]))return!1;for(o=n;0!=o--;){var a=s[o];if(!e(t[a],r[a]))return!1}return!0}return t!=t&&r!=r}},6150:e=>{"use strict";e.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},"string"!=typeof e)throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if("number"!=typeof t)throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if("string"!=typeof r.indent)throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(0===t)return e;const n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}},9522:e=>{"use strict";var t=e.exports=function(e,t,n){"function"==typeof t&&(n=t,t={}),r(t,"function"==typeof(n=t.cb||n)?n:n.pre||function(){},n.post||function(){},e,"",e)};function r(e,n,o,s,a,i,c,u,d,l){if(s&&"object"==typeof s&&!Array.isArray(s)){for(var f in n(s,a,i,c,u,d,l),s){var p=s[f];if(Array.isArray(p)){if(f in t.arrayKeywords)for(var h=0;h<p.length;h++)r(e,n,o,p[h],a+"/"+f+"/"+h,i,a,f,s,h)}else if(f in t.propsKeywords){if(p&&"object"==typeof p)for(var m in p)r(e,n,o,p[m],a+"/"+f+"/"+m.replace(/~/g,"~0").replace(/\//g,"~1"),i,a,f,s,m)}else(f in t.keywords||e.allKeys&&!(f in t.skipKeywords))&&r(e,n,o,p,a+"/"+f,i,a,f,s)}o(s,a,i,c,u,d,l)}}t.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},t.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},t.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},t.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},857:(e,t,r)=>{var n=r(7772).Symbol;e.exports=n},343:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},1207:e=>{e.exports=function(e,t,r,n){var o=-1,s=null==e?0:e.length;for(n&&s&&(r=e[++o]);++o<s;)r=t(r,e[o],o,e);return r}},217:e=>{e.exports=function(e){return e.split("")}},5981:e=>{var t=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;e.exports=function(e){return e.match(t)||[]}},3366:(e,t,r)=>{var n=r(857),o=r(2107),s=r(7157),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):s(e)}},6435:e=>{e.exports=function(e){return function(t){return null==e?void 0:e[t]}}},9872:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var s=Array(o);++n<o;)s[n]=e[n+t];return s}},1054:(e,t,r)=>{var n=r(857),o=r(343),s=r(6152),a=r(4795),i=n?n.prototype:void 0,c=i?i.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(s(t))return o(t,e)+"";if(a(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-Infinity?"-0":r}},3895:(e,t,r)=>{var n=r(9872);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},3126:(e,t,r)=>{var n=r(3895),o=r(3880),s=r(8435),a=r(6188);e.exports=function(e){return function(t){t=a(t);var r=o(t)?s(t):void 0,i=r?r[0]:t.charAt(0),c=r?n(r,1).join(""):t.slice(1);return i[e]()+c}}},4311:(e,t,r)=>{var n=r(1207),o=r(7329),s=r(1618),a=RegExp("['’]","g");e.exports=function(e){return function(t){return n(s(o(t).replace(a,"")),e,"")}}},1655:(e,t,r)=>{var n=r(6435)({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"});e.exports=n},1242:(e,t,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},2107:(e,t,r)=>{var n=r(857),o=Object.prototype,s=o.hasOwnProperty,a=o.toString,i=n?n.toStringTag:void 0;e.exports=function(e){var t=s.call(e,i),r=e[i];try{e[i]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[i]=r:delete e[i]),o}},3880:e=>{var t=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},3559:e=>{var t=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;e.exports=function(e){return t.test(e)}},7157:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},7772:(e,t,r)=>{var n=r(1242),o="object"==typeof self&&self&&self.Object===Object&&self,s=n||o||Function("return this")();e.exports=s},8435:(e,t,r)=>{var n=r(217),o=r(3880),s=r(3344);e.exports=function(e){return o(e)?s(e):n(e)}},3344:e=>{var t="[\\ud800-\\udfff]",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\\ud83c[\\udffb-\\udfff]",o="[^\\ud800-\\udfff]",s="(?:\\ud83c[\\udde6-\\uddff]){2}",a="[\\ud800-\\udbff][\\udc00-\\udfff]",i="(?:"+r+"|"+n+")"+"?",c="[\\ufe0e\\ufe0f]?",u=c+i+("(?:\\u200d(?:"+[o,s,a].join("|")+")"+c+i+")*"),d="(?:"+[o+r+"?",r,s,a,t].join("|")+")",l=RegExp(n+"(?="+n+")|"+d+u,"g");e.exports=function(e){return e.match(l)||[]}},5304:e=>{var t="\\u2700-\\u27bf",r="a-z\\xdf-\\xf6\\xf8-\\xff",n="A-Z\\xc0-\\xd6\\xd8-\\xde",o="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",s="["+o+"]",a="\\d+",i="[\\u2700-\\u27bf]",c="["+r+"]",u="[^\\ud800-\\udfff"+o+a+t+r+n+"]",d="(?:\\ud83c[\\udde6-\\uddff]){2}",l="[\\ud800-\\udbff][\\udc00-\\udfff]",f="["+n+"]",p="(?:"+c+"|"+u+")",h="(?:"+f+"|"+u+")",m="(?:['’](?:d|ll|m|re|s|t|ve))?",y="(?:['’](?:D|LL|M|RE|S|T|VE))?",v="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",g="[\\ufe0e\\ufe0f]?",_=g+v+("(?:\\u200d(?:"+["[^\\ud800-\\udfff]",d,l].join("|")+")"+g+v+")*"),E="(?:"+[i,d,l].join("|")+")"+_,w=RegExp([f+"?"+c+"+"+m+"(?="+[s,f,"$"].join("|")+")",h+"+"+y+"(?="+[s,f+p,"$"].join("|")+")",f+"?"+p+"+"+m,f+"+"+y,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",a,E].join("|"),"g");e.exports=function(e){return e.match(w)||[]}},6009:(e,t,r)=>{var n=r(2108),o=r(4311)((function(e,t,r){return t=t.toLowerCase(),e+(r?n(t):t)}));e.exports=o},2108:(e,t,r)=>{var n=r(6188),o=r(3779);e.exports=function(e){return o(n(e).toLowerCase())}},7329:(e,t,r)=>{var n=r(1655),o=r(6188),s=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,a=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");e.exports=function(e){return(e=o(e))&&e.replace(s,n).replace(a,"")}},6152:e=>{var t=Array.isArray;e.exports=t},5125:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},4795:(e,t,r)=>{var n=r(3366),o=r(5125);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},6188:(e,t,r)=>{var n=r(1054);e.exports=function(e){return null==e?"":n(e)}},3779:(e,t,r)=>{var n=r(3126)("toUpperCase");e.exports=n},1618:(e,t,r)=>{var n=r(5981),o=r(3559),s=r(6188),a=r(5304);e.exports=function(e,t,r){return e=s(e),void 0===(t=r?void 0:t)?o(e)?a(e):n(e):e.match(t)||[]}},2854:function(e,t){!function(e){"use strict";function t(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(t.length>1){t[0]=t[0].slice(0,-1);for(var n=t.length-1,o=1;o<n;++o)t[o]=t[o].slice(1,-1);return t[n]=t[n].slice(1),t.join("")}return t[0]}function r(e){return"(?:"+e+")"}function n(e){return void 0===e?"undefined":null===e?"null":Object.prototype.toString.call(e).split(" ").pop().split("]").shift().toLowerCase()}function o(e){return e.toUpperCase()}function s(e){return null!=e?e instanceof Array?e:"number"!=typeof e.length||e.split||e.setInterval||e.call?[e]:Array.prototype.slice.call(e):[]}function a(e,t){var r=e;if(t)for(var n in t)r[n]=t[n];return r}function i(e){var n="[A-Za-z]",o="[0-9]",s=t(o,"[A-Fa-f]"),a=r(r("%[EFef]"+s+"%"+s+s+"%"+s+s)+"|"+r("%[89A-Fa-f]"+s+"%"+s+s)+"|"+r("%"+s+s)),i="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",c=t("[\\:\\/\\?\\#\\[\\]\\@]",i),u=e?"[\\uE000-\\uF8FF]":"[]",d=t(n,o,"[\\-\\.\\_\\~]",e?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]"),l=r(n+t(n,o,"[\\+\\-\\.]")+"*"),f=r(r(a+"|"+t(d,i,"[\\:]"))+"*"),p=(r(r("25[0-5]")+"|"+r("2[0-4]"+o)+"|"+r("1"+o+o)+"|"+r("[1-9]"+o)+"|"+o),r(r("25[0-5]")+"|"+r("2[0-4]"+o)+"|"+r("1"+o+o)+"|"+r("0?[1-9]"+o)+"|0?0?"+o)),h=r(p+"\\."+p+"\\."+p+"\\."+p),m=r(s+"{1,4}"),y=r(r(m+"\\:"+m)+"|"+h),v=r(r(m+"\\:")+"{6}"+y),g=r("\\:\\:"+r(m+"\\:")+"{5}"+y),_=r(r(m)+"?\\:\\:"+r(m+"\\:")+"{4}"+y),E=r(r(r(m+"\\:")+"{0,1}"+m)+"?\\:\\:"+r(m+"\\:")+"{3}"+y),w=r(r(r(m+"\\:")+"{0,2}"+m)+"?\\:\\:"+r(m+"\\:")+"{2}"+y),$=r(r(r(m+"\\:")+"{0,3}"+m)+"?\\:\\:"+m+"\\:"+y),b=r(r(r(m+"\\:")+"{0,4}"+m)+"?\\:\\:"+y),T=r(r(r(m+"\\:")+"{0,5}"+m)+"?\\:\\:"+m),O=r(r(r(m+"\\:")+"{0,6}"+m)+"?\\:\\:"),S=r([v,g,_,E,w,$,b,T,O].join("|")),N=r(r(d+"|"+a)+"+"),P=(r(S+"\\%25"+N),r(S+r("\\%25|\\%(?!"+s+"{2})")+N)),A=r("[vV]"+s+"+\\."+t(d,i,"[\\:]")+"+"),I=r("\\["+r(P+"|"+S+"|"+A)+"\\]"),R=r(r(a+"|"+t(d,i))+"*"),C=r(I+"|"+h+"(?!"+R+")|"+R),j=r(o+"*"),D=r(r(f+"@")+"?"+C+r("\\:"+j)+"?"),x=r(a+"|"+t(d,i,"[\\:\\@]")),k=r(x+"*"),M=r(x+"+"),L=r(r(a+"|"+t(d,i,"[\\@]"))+"+"),U=r(r("\\/"+k)+"*"),F=r("\\/"+r(M+U)+"?"),V=r(L+U),q=r(M+U),z="(?!"+x+")",H=(r(U+"|"+F+"|"+V+"|"+q+"|"+z),r(r(x+"|"+t("[\\/\\?]",u))+"*")),K=r(r(x+"|[\\/\\?]")+"*"),G=r(r("\\/\\/"+D+U)+"|"+F+"|"+q+"|"+z),B=r(l+"\\:"+G+r("\\?"+H)+"?"+r("\\#"+K)+"?"),Y=r(r("\\/\\/"+D+U)+"|"+F+"|"+V+"|"+z),W=r(Y+r("\\?"+H)+"?"+r("\\#"+K)+"?");return r(B+"|"+W),r(l+"\\:"+G+r("\\?"+H)+"?"),r(r("\\/\\/("+r("("+f+")@")+"?("+C+")"+r("\\:("+j+")")+"?)")+"?("+U+"|"+F+"|"+q+"|"+z+")"),r("\\?("+H+")"),r("\\#("+K+")"),r(r("\\/\\/("+r("("+f+")@")+"?("+C+")"+r("\\:("+j+")")+"?)")+"?("+U+"|"+F+"|"+V+"|"+z+")"),r("\\?("+H+")"),r("\\#("+K+")"),r(r("\\/\\/("+r("("+f+")@")+"?("+C+")"+r("\\:("+j+")")+"?)")+"?("+U+"|"+F+"|"+q+"|"+z+")"),r("\\?("+H+")"),r("\\#("+K+")"),r("("+f+")@"),r("\\:("+j+")"),{NOT_SCHEME:new RegExp(t("[^]",n,o,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(t("[^\\%\\:]",d,i),"g"),NOT_HOST:new RegExp(t("[^\\%\\[\\]\\:]",d,i),"g"),NOT_PATH:new RegExp(t("[^\\%\\/\\:\\@]",d,i),"g"),NOT_PATH_NOSCHEME:new RegExp(t("[^\\%\\/\\@]",d,i),"g"),NOT_QUERY:new RegExp(t("[^\\%]",d,i,"[\\:\\@\\/\\?]",u),"g"),NOT_FRAGMENT:new RegExp(t("[^\\%]",d,i,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(t("[^]",d,i),"g"),UNRESERVED:new RegExp(d,"g"),OTHER_CHARS:new RegExp(t("[^\\%]",d,c),"g"),PCT_ENCODED:new RegExp(a,"g"),IPV4ADDRESS:new RegExp("^("+h+")$"),IPV6ADDRESS:new RegExp("^\\[?("+S+")"+r(r("\\%25|\\%(?!"+s+"{2})")+"("+N+")")+"?\\]?$")}}var c=i(!1),u=i(!0),d=function(){function e(e,t){var r=[],n=!0,o=!1,s=void 0;try{for(var a,i=e[Symbol.iterator]();!(n=(a=i.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,s=e}finally{try{!n&&i.return&&i.return()}finally{if(o)throw s}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),l=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},f=2147483647,p=36,h=1,m=26,y=38,v=700,g=72,_=128,E="-",w=/^xn--/,$=/[^\0-\x7E]/,b=/[\x2E\u3002\uFF0E\uFF61]/g,T={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},O=p-h,S=Math.floor,N=String.fromCharCode;function P(e){throw new RangeError(T[e])}function A(e,t){for(var r=[],n=e.length;n--;)r[n]=t(e[n]);return r}function I(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+A((e=e.replace(b,".")).split("."),t).join(".")}function R(e){for(var t=[],r=0,n=e.length;r<n;){var o=e.charCodeAt(r++);if(o>=55296&&o<=56319&&r<n){var s=e.charCodeAt(r++);56320==(64512&s)?t.push(((1023&o)<<10)+(1023&s)+65536):(t.push(o),r--)}else t.push(o)}return t}var C=function(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:p},j=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},D=function(e,t,r){var n=0;for(e=r?S(e/v):e>>1,e+=S(e/t);e>O*m>>1;n+=p)e=S(e/O);return S(n+(O+1)*e/(e+y))},x=function(e){var t=[],r=e.length,n=0,o=_,s=g,a=e.lastIndexOf(E);a<0&&(a=0);for(var i=0;i<a;++i)e.charCodeAt(i)>=128&&P("not-basic"),t.push(e.charCodeAt(i));for(var c=a>0?a+1:0;c<r;){for(var u=n,d=1,l=p;;l+=p){c>=r&&P("invalid-input");var y=C(e.charCodeAt(c++));(y>=p||y>S((f-n)/d))&&P("overflow"),n+=y*d;var v=l<=s?h:l>=s+m?m:l-s;if(y<v)break;var w=p-v;d>S(f/w)&&P("overflow"),d*=w}var $=t.length+1;s=D(n-u,$,0==u),S(n/$)>f-o&&P("overflow"),o+=S(n/$),n%=$,t.splice(n++,0,o)}return String.fromCodePoint.apply(String,t)},k=function(e){var t=[],r=(e=R(e)).length,n=_,o=0,s=g,a=!0,i=!1,c=void 0;try{for(var u,d=e[Symbol.iterator]();!(a=(u=d.next()).done);a=!0){var l=u.value;l<128&&t.push(N(l))}}catch(e){i=!0,c=e}finally{try{!a&&d.return&&d.return()}finally{if(i)throw c}}var y=t.length,v=y;for(y&&t.push(E);v<r;){var w=f,$=!0,b=!1,T=void 0;try{for(var O,A=e[Symbol.iterator]();!($=(O=A.next()).done);$=!0){var I=O.value;I>=n&&I<w&&(w=I)}}catch(e){b=!0,T=e}finally{try{!$&&A.return&&A.return()}finally{if(b)throw T}}var C=v+1;w-n>S((f-o)/C)&&P("overflow"),o+=(w-n)*C,n=w;var x=!0,k=!1,M=void 0;try{for(var L,U=e[Symbol.iterator]();!(x=(L=U.next()).done);x=!0){var F=L.value;if(F<n&&++o>f&&P("overflow"),F==n){for(var V=o,q=p;;q+=p){var z=q<=s?h:q>=s+m?m:q-s;if(V<z)break;var H=V-z,K=p-z;t.push(N(j(z+H%K,0))),V=S(H/K)}t.push(N(j(V,0))),s=D(o,C,v==y),o=0,++v}}}catch(e){k=!0,M=e}finally{try{!x&&U.return&&U.return()}finally{if(k)throw M}}++o,++n}return t.join("")},M=function(e){return I(e,(function(e){return w.test(e)?x(e.slice(4).toLowerCase()):e}))},L=function(e){return I(e,(function(e){return $.test(e)?"xn--"+k(e):e}))},U={version:"2.1.0",ucs2:{decode:R,encode:function(e){return String.fromCodePoint.apply(String,l(e))}},decode:x,encode:k,toASCII:L,toUnicode:M},F={};function V(e){var t=e.charCodeAt(0);return t<16?"%0"+t.toString(16).toUpperCase():t<128?"%"+t.toString(16).toUpperCase():t<2048?"%"+(t>>6|192).toString(16).toUpperCase()+"%"+(63&t|128).toString(16).toUpperCase():"%"+(t>>12|224).toString(16).toUpperCase()+"%"+(t>>6&63|128).toString(16).toUpperCase()+"%"+(63&t|128).toString(16).toUpperCase()}function q(e){for(var t="",r=0,n=e.length;r<n;){var o=parseInt(e.substr(r+1,2),16);if(o<128)t+=String.fromCharCode(o),r+=3;else if(o>=194&&o<224){if(n-r>=6){var s=parseInt(e.substr(r+4,2),16);t+=String.fromCharCode((31&o)<<6|63&s)}else t+=e.substr(r,6);r+=6}else if(o>=224){if(n-r>=9){var a=parseInt(e.substr(r+4,2),16),i=parseInt(e.substr(r+7,2),16);t+=String.fromCharCode((15&o)<<12|(63&a)<<6|63&i)}else t+=e.substr(r,9);r+=9}else t+=e.substr(r,3),r+=3}return t}function z(e,t){function r(e){var r=q(e);return r.match(t.UNRESERVED)?r:e}return e.scheme&&(e.scheme=String(e.scheme).replace(t.PCT_ENCODED,r).toLowerCase().replace(t.NOT_SCHEME,"")),void 0!==e.userinfo&&(e.userinfo=String(e.userinfo).replace(t.PCT_ENCODED,r).replace(t.NOT_USERINFO,V).replace(t.PCT_ENCODED,o)),void 0!==e.host&&(e.host=String(e.host).replace(t.PCT_ENCODED,r).toLowerCase().replace(t.NOT_HOST,V).replace(t.PCT_ENCODED,o)),void 0!==e.path&&(e.path=String(e.path).replace(t.PCT_ENCODED,r).replace(e.scheme?t.NOT_PATH:t.NOT_PATH_NOSCHEME,V).replace(t.PCT_ENCODED,o)),void 0!==e.query&&(e.query=String(e.query).replace(t.PCT_ENCODED,r).replace(t.NOT_QUERY,V).replace(t.PCT_ENCODED,o)),void 0!==e.fragment&&(e.fragment=String(e.fragment).replace(t.PCT_ENCODED,r).replace(t.NOT_FRAGMENT,V).replace(t.PCT_ENCODED,o)),e}function H(e){return e.replace(/^0*(.*)/,"$1")||"0"}function K(e,t){var r=e.match(t.IPV4ADDRESS)||[],n=d(r,2)[1];return n?n.split(".").map(H).join("."):e}function G(e,t){var r=e.match(t.IPV6ADDRESS)||[],n=d(r,3),o=n[1],s=n[2];if(o){for(var a=o.toLowerCase().split("::").reverse(),i=d(a,2),c=i[0],u=i[1],l=u?u.split(":").map(H):[],f=c.split(":").map(H),p=t.IPV4ADDRESS.test(f[f.length-1]),h=p?7:8,m=f.length-h,y=Array(h),v=0;v<h;++v)y[v]=l[v]||f[m+v]||"";p&&(y[h-1]=K(y[h-1],t));var g=y.reduce((function(e,t,r){if(!t||"0"===t){var n=e[e.length-1];n&&n.index+n.length===r?n.length++:e.push({index:r,length:1})}return e}),[]).sort((function(e,t){return t.length-e.length}))[0],_=void 0;if(g&&g.length>1){var E=y.slice(0,g.index),w=y.slice(g.index+g.length);_=E.join(":")+"::"+w.join(":")}else _=y.join(":");return s&&(_+="%"+s),_}return e}var B=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,Y=void 0==="".match(/(){0}/)[1];function W(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r={},n=!1!==t.iri?u:c;"suffix"===t.reference&&(e=(t.scheme?t.scheme+":":"")+"//"+e);var o=e.match(B);if(o){Y?(r.scheme=o[1],r.userinfo=o[3],r.host=o[4],r.port=parseInt(o[5],10),r.path=o[6]||"",r.query=o[7],r.fragment=o[8],isNaN(r.port)&&(r.port=o[5])):(r.scheme=o[1]||void 0,r.userinfo=-1!==e.indexOf("@")?o[3]:void 0,r.host=-1!==e.indexOf("//")?o[4]:void 0,r.port=parseInt(o[5],10),r.path=o[6]||"",r.query=-1!==e.indexOf("?")?o[7]:void 0,r.fragment=-1!==e.indexOf("#")?o[8]:void 0,isNaN(r.port)&&(r.port=e.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?o[4]:void 0)),r.host&&(r.host=G(K(r.host,n),n)),void 0!==r.scheme||void 0!==r.userinfo||void 0!==r.host||void 0!==r.port||r.path||void 0!==r.query?void 0===r.scheme?r.reference="relative":void 0===r.fragment?r.reference="absolute":r.reference="uri":r.reference="same-document",t.reference&&"suffix"!==t.reference&&t.reference!==r.reference&&(r.error=r.error||"URI is not a "+t.reference+" reference.");var s=F[(t.scheme||r.scheme||"").toLowerCase()];if(t.unicodeSupport||s&&s.unicodeSupport)z(r,n);else{if(r.host&&(t.domainHost||s&&s.domainHost))try{r.host=U.toASCII(r.host.replace(n.PCT_ENCODED,q).toLowerCase())}catch(e){r.error=r.error||"Host's domain name can not be converted to ASCII via punycode: "+e}z(r,c)}s&&s.parse&&s.parse(r,t)}else r.error=r.error||"URI can not be parsed.";return r}function J(e,t){var r=!1!==t.iri?u:c,n=[];return void 0!==e.userinfo&&(n.push(e.userinfo),n.push("@")),void 0!==e.host&&n.push(G(K(String(e.host),r),r).replace(r.IPV6ADDRESS,(function(e,t,r){return"["+t+(r?"%25"+r:"")+"]"}))),"number"!=typeof e.port&&"string"!=typeof e.port||(n.push(":"),n.push(String(e.port))),n.length?n.join(""):void 0}var Q=/^\.\.?\//,X=/^\/\.(\/|$)/,Z=/^\/\.\.(\/|$)/,ee=/^\/?(?:.|\n)*?(?=\/|$)/;function te(e){for(var t=[];e.length;)if(e.match(Q))e=e.replace(Q,"");else if(e.match(X))e=e.replace(X,"/");else if(e.match(Z))e=e.replace(Z,"/"),t.pop();else if("."===e||".."===e)e="";else{var r=e.match(ee);if(!r)throw new Error("Unexpected dot segment condition");var n=r[0];e=e.slice(n.length),t.push(n)}return t.join("")}function re(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.iri?u:c,n=[],o=F[(t.scheme||e.scheme||"").toLowerCase()];if(o&&o.serialize&&o.serialize(e,t),e.host)if(r.IPV6ADDRESS.test(e.host));else if(t.domainHost||o&&o.domainHost)try{e.host=t.iri?U.toUnicode(e.host):U.toASCII(e.host.replace(r.PCT_ENCODED,q).toLowerCase())}catch(r){e.error=e.error||"Host's domain name can not be converted to "+(t.iri?"Unicode":"ASCII")+" via punycode: "+r}z(e,r),"suffix"!==t.reference&&e.scheme&&(n.push(e.scheme),n.push(":"));var s=J(e,t);if(void 0!==s&&("suffix"!==t.reference&&n.push("//"),n.push(s),e.path&&"/"!==e.path.charAt(0)&&n.push("/")),void 0!==e.path){var a=e.path;t.absolutePath||o&&o.absolutePath||(a=te(a)),void 0===s&&(a=a.replace(/^\/\//,"/%2F")),n.push(a)}return void 0!==e.query&&(n.push("?"),n.push(e.query)),void 0!==e.fragment&&(n.push("#"),n.push(e.fragment)),n.join("")}function ne(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n={};return arguments[3]||(e=W(re(e,r),r),t=W(re(t,r),r)),!(r=r||{}).tolerant&&t.scheme?(n.scheme=t.scheme,n.userinfo=t.userinfo,n.host=t.host,n.port=t.port,n.path=te(t.path||""),n.query=t.query):(void 0!==t.userinfo||void 0!==t.host||void 0!==t.port?(n.userinfo=t.userinfo,n.host=t.host,n.port=t.port,n.path=te(t.path||""),n.query=t.query):(t.path?("/"===t.path.charAt(0)?n.path=te(t.path):(void 0===e.userinfo&&void 0===e.host&&void 0===e.port||e.path?e.path?n.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:n.path=t.path:n.path="/"+t.path,n.path=te(n.path)),n.query=t.query):(n.path=e.path,void 0!==t.query?n.query=t.query:n.query=e.query),n.userinfo=e.userinfo,n.host=e.host,n.port=e.port),n.scheme=e.scheme),n.fragment=t.fragment,n}function oe(e,t,r){var n=a({scheme:"null"},r);return re(ne(W(e,n),W(t,n),n,!0),n)}function se(e,t){return"string"==typeof e?e=re(W(e,t),t):"object"===n(e)&&(e=W(re(e,t),t)),e}function ae(e,t,r){return"string"==typeof e?e=re(W(e,r),r):"object"===n(e)&&(e=re(e,r)),"string"==typeof t?t=re(W(t,r),r):"object"===n(t)&&(t=re(t,r)),e===t}function ie(e,t){return e&&e.toString().replace(t&&t.iri?u.ESCAPE:c.ESCAPE,V)}function ce(e,t){return e&&e.toString().replace(t&&t.iri?u.PCT_ENCODED:c.PCT_ENCODED,q)}var ue={scheme:"http",domainHost:!0,parse:function(e,t){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e},serialize:function(e,t){var r="https"===String(e.scheme).toLowerCase();return e.port!==(r?443:80)&&""!==e.port||(e.port=void 0),e.path||(e.path="/"),e}},de={scheme:"https",domainHost:ue.domainHost,parse:ue.parse,serialize:ue.serialize};function le(e){return"boolean"==typeof e.secure?e.secure:"wss"===String(e.scheme).toLowerCase()}var fe={scheme:"ws",domainHost:!0,parse:function(e,t){var r=e;return r.secure=le(r),r.resourceName=(r.path||"/")+(r.query?"?"+r.query:""),r.path=void 0,r.query=void 0,r},serialize:function(e,t){if(e.port!==(le(e)?443:80)&&""!==e.port||(e.port=void 0),"boolean"==typeof e.secure&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){var r=e.resourceName.split("?"),n=d(r,2),o=n[0],s=n[1];e.path=o&&"/"!==o?o:void 0,e.query=s,e.resourceName=void 0}return e.fragment=void 0,e}},pe={scheme:"wss",domainHost:fe.domainHost,parse:fe.parse,serialize:fe.serialize},he={},me="[A-Za-z0-9\\-\\.\\_\\~\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]",ye="[0-9A-Fa-f]",ve=r(r("%[EFef]"+ye+"%"+ye+ye+"%"+ye+ye)+"|"+r("%[89A-Fa-f]"+ye+"%"+ye+ye)+"|"+r("%"+ye+ye)),ge="[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]",_e=t("[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",'[\\"\\\\]'),Ee="[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]",we=new RegExp(me,"g"),$e=new RegExp(ve,"g"),be=new RegExp(t("[^]",ge,"[\\.]",'[\\"]',_e),"g"),Te=new RegExp(t("[^]",me,Ee),"g"),Oe=Te;function Se(e){var t=q(e);return t.match(we)?t:e}var Ne={scheme:"mailto",parse:function(e,t){var r=e,n=r.to=r.path?r.path.split(","):[];if(r.path=void 0,r.query){for(var o=!1,s={},a=r.query.split("&"),i=0,c=a.length;i<c;++i){var u=a[i].split("=");switch(u[0]){case"to":for(var d=u[1].split(","),l=0,f=d.length;l<f;++l)n.push(d[l]);break;case"subject":r.subject=ce(u[1],t);break;case"body":r.body=ce(u[1],t);break;default:o=!0,s[ce(u[0],t)]=ce(u[1],t)}}o&&(r.headers=s)}r.query=void 0;for(var p=0,h=n.length;p<h;++p){var m=n[p].split("@");if(m[0]=ce(m[0]),t.unicodeSupport)m[1]=ce(m[1],t).toLowerCase();else try{m[1]=U.toASCII(ce(m[1],t).toLowerCase())}catch(e){r.error=r.error||"Email address's domain name can not be converted to ASCII via punycode: "+e}n[p]=m.join("@")}return r},serialize:function(e,t){var r=e,n=s(e.to);if(n){for(var a=0,i=n.length;a<i;++a){var c=String(n[a]),u=c.lastIndexOf("@"),d=c.slice(0,u).replace($e,Se).replace($e,o).replace(be,V),l=c.slice(u+1);try{l=t.iri?U.toUnicode(l):U.toASCII(ce(l,t).toLowerCase())}catch(e){r.error=r.error||"Email address's domain name can not be converted to "+(t.iri?"Unicode":"ASCII")+" via punycode: "+e}n[a]=d+"@"+l}r.path=n.join(",")}var f=e.headers=e.headers||{};e.subject&&(f.subject=e.subject),e.body&&(f.body=e.body);var p=[];for(var h in f)f[h]!==he[h]&&p.push(h.replace($e,Se).replace($e,o).replace(Te,V)+"="+f[h].replace($e,Se).replace($e,o).replace(Oe,V));return p.length&&(r.query=p.join("&")),r}},Pe=/^([^\:]+)\:(.*)/,Ae={scheme:"urn",parse:function(e,t){var r=e.path&&e.path.match(Pe),n=e;if(r){var o=t.scheme||n.scheme||"urn",s=r[1].toLowerCase(),a=r[2],i=o+":"+(t.nid||s),c=F[i];n.nid=s,n.nss=a,n.path=void 0,c&&(n=c.parse(n,t))}else n.error=n.error||"URN can not be parsed.";return n},serialize:function(e,t){var r=t.scheme||e.scheme||"urn",n=e.nid,o=r+":"+(t.nid||n),s=F[o];s&&(e=s.serialize(e,t));var a=e,i=e.nss;return a.path=(n||t.nid)+":"+i,a}},Ie=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,Re={scheme:"urn:uuid",parse:function(e,t){var r=e;return r.uuid=r.nss,r.nss=void 0,t.tolerant||r.uuid&&r.uuid.match(Ie)||(r.error=r.error||"UUID is not valid."),r},serialize:function(e,t){var r=e;return r.nss=(e.uuid||"").toLowerCase(),r}};F[ue.scheme]=ue,F[de.scheme]=de,F[fe.scheme]=fe,F[pe.scheme]=pe,F[Ne.scheme]=Ne,F[Ae.scheme]=Ae,F[Re.scheme]=Re,e.SCHEMES=F,e.pctEncChar=V,e.pctDecChars=q,e.parse=W,e.removeDotSegments=te,e.serialize=re,e.resolveComponents=ne,e.resolve=oe,e.normalize=se,e.equal=ae,e.escapeComponent=ie,e.unescapeComponent=ce,Object.defineProperty(e,"__esModule",{value:!0})}(t)},9768:e=>{e.exports={Liquid:class{constructor(e){this.args=e}parseAndRenderSync(e){return e}registerTag(e,t){}registerFilter(e,t){}}}},7128:(e,t,r)=>{"use strict";let n;r.d(t,{U:()=>o});try{n=new RegExp("\\[(?=\"|'|\\d)|\\.|(?<=\"|'|\\d)]+","g")}catch(e){n=/\[|\.|]+/g}function o(e,t){if(""===t||"."===t)return e;if(null===t||null==t)return;return(Array.isArray(t)?t:t.split(n).filter((e=>e)).map((e=>e.replace(/'|"/g,"")))).reduce(((e,t)=>e&&e[t]),e)}},3807:(e,t,r)=>{"use strict";r.d(t,{tY:()=>M,a8:()=>L,vs:()=>z});var n=r(7128),o=r(6933);function s(e){if(!(0,o.Kn)(e))return!1;const t=Object.keys(e);if(!t.some((e=>e.startsWith("@"))))return!1;return 0===t.filter((e=>!e.startsWith("@")&&"_metadata"!==e)).length}function a(e){if(Array.isArray(e))return e.map((e=>a(e)));if((0,o.Kn)(e)){const t=Object.assign({},e);return Object.keys(t).forEach((e=>{void 0===t[e]?delete t[e]:t[e]=a(t[e])})),t}return e}var i=r(1291),c=r.n(i),u=r(2501);class d extends u.CustomError{constructor(e,t=[]){super(`/${t.join("/")} ${e}.`)}}function l(e){const t=[];return e.forEach((e=>{e instanceof c()?t.push(...e):t.push(e)})),t}function f(e){const t=(0,o.X6)(e);return"object"===t&&Object.keys(e).some((e=>e.startsWith("@")))?"directive":t}const p={};function h(e,t=[]){if(!s(e)&&!(0,o.Kn)(e)){const r=(0,o.X6)(e);throw new d(`should be a directive object but it is ${N(r)} ${r}`,t)}const r=Object.keys(e),n=r.filter((e=>e.startsWith("@")));if(n.length>1)throw new d(`should only have one @-prefixed key but it has ${n.length} keys`,t);if(r.filter((e=>!e.startsWith("@")&&"_metadata"!==e)).length>0)throw new d(`should only have one @-prefixed key but it has ${r.length} keys`,t);const a=n[0],i=p[a];if("function"!=typeof i)throw new d(`has an invalid directive: ${a}`,t);i(e[a],t)}function m(e,t=[]){const r=f(e);switch(r){case"directive":return h(e,t);case"object":case"array":case"boolean":case"string":case"number":case"null":return;default:throw new d(`should be a mapping directive or a JSON value but it is ${N(r)} ${r}`,t)}}function y(e,t=[]){const r=f(e);switch(r){case"directive":return h(e,t);case"string":return;default:throw new d(`should be a string or a mapping directive but it is ${N(r)} ${r}`,t)}}function v(e,t=[]){const r=f(e);switch(r){case"directive":return h(e,t);case"object":return b(e,t);default:throw new d(`should be a object or a mapping directive but it is ${N(r)} ${r}`,t)}}function g(...e){return(t,r=[])=>{e.forEach((e=>{e(t,r)}))}}function _(e,t){return(r,n=[])=>{if("string"==typeof r&&r.length<e)throw new d(`should be a string of length ${e} or greater`,n);if("string"==typeof r&&r.length>t)throw new d(`should be a string of length ${t} or less`,n)}}function E(e,t=[]){const r=f(e);if("string"!==r)throw new d(`should be a string but it is ${N(r)} ${r}`,t)}function w(...e){return(t,r=[])=>{E(t,r);const n=t;if(!e.includes(n.toLowerCase()))throw new d(`should be one of ${e.join(", ")} but it is ${JSON.stringify(n)}`,r)}}function $(e,t=[]){const r=f(e);if("boolean"!==r)throw new d(`should be a boolean but it is ${N(r)} ${r}`,t)}function b(e,t=[]){const r=f(e);if("object"!==r)throw new d(`should be an object but it is ${N(r)} ${r}`,t);const n=e,o=Object.keys(n),s=o.find((e=>"@"===e.charAt(0)));if(s)throw new d(`shouldn't have directive (@-prefixed) keys but it has ${JSON.stringify(s)}`,t);const a=[];if(o.forEach((e=>{try{P(n[e],[...t,e])}catch(e){a.push(e)}})),a.length)throw new(c())(l(a))}function T(e,t,r=[]){b(e,r);const n=[],o=e;if(Object.entries(t).forEach((([e,{required:t,optional:s}])=>{try{if(t){if(void 0===o[e])throw new d(`should have field ${JSON.stringify(e)} but it doesn't`,r);t(o[e],[...r,e])}else s&&void 0!==o[e]&&s(o[e],[...r,e])}catch(e){n.push(e)}})),n.length)throw new(c())(l(n))}function O(e,t=[]){const r=(0,o.X6)(e);if("array"!==r)throw new d(`should be an array but it is ${N(r)} ${r}`,t)}function S(e,t){Array.isArray(e)||(e=[e]),e.forEach((e=>{p[e]=(r,n=[])=>{try{t(r,[...n,e])}catch(e){const t=e;if(e instanceof d||e instanceof c())throw e;throw new d(t.message,n)}}}))}function N(e){switch(e.charAt(0)){case"a":case"e":case"i":case"o":case"u":return"an";default:return"a"}}function P(e,t=[]){switch(f(e)){case"directive":return h(e,t);case"object":return b(e,t);case"array":return O(e,t);default:return null}}function A(e,t=!0){return null==e?t?[]:e:(0,o.kJ)(e)?e:[e]}S("@if",((e,t)=>{T(e,{exists:{optional:m},then:{optional:m},else:{optional:m}},t)})),S("@case",((e,t)=>{T(e,{operator:{optional:E},value:{optional:y}},t)})),S("@replace",((e,t)=>{T(e,{pattern:{required:g(E,_(1,M))},replacement:{optional:g(E,_(0,L))},value:{required:y},ignorecase:{optional:$},global:{optional:$}},t)})),S("@arrayPath",((e,t)=>{const r=e;O(r,t),y(r[0],t),P(r[1],t)})),S("@path",((e,t)=>{y(e,t)})),S("@json",((e,t)=>{T(e,{value:{required:m},mode:{required:w("encode","decode")}},t)})),S("@flatten",((e,t)=>{T(e,{separator:{optional:E},value:{required:m}},t)})),S("@merge",((e,t)=>{T(e,{direction:{optional:w("left","right")},objects:{required:O}},t);e.objects.forEach((e=>{v(e)}))})),S("@template",((e,t)=>{y(e,t)})),S("@literal",((e,t)=>{m(e,t)})),S("@transform",((e,t)=>{T(e,{apply:{required:v},mapping:{required:v}},t)})),S("@excludeWhenNull",((e,t)=>{m(e,t)})),S("@liquid",((e,t)=>{E(e,t)}));const I=(e,t="",r=".",n=!1)=>(0,o.Kn)(e)||!n&&(0,o.kJ)(e)?Object.entries(e).reduce(((e,[o,s])=>({...e,...I(s,t?`${t}${r}${o}`:o,r,n)})),{}):{[t]:e};var R=r(5145);const C="__segment_internal_directive",j={},D=/^@[a-z][a-zA-Z0-9]+$/;function x(e,t){if(!D.exec(e))throw new Error(`"${e}" is an invalid directive name`);j[e]=t}function k(e,t){x(e,((r,n)=>{const s=q(r,n);if("string"!=typeof s)throw new Error(`${e}: expected string, got ${(0,o.X6)(s)}`);return t(s,n)}))}x("@if",((e,t)=>{let r=!1;if(!(0,o.Kn)(e))throw new Error('@if requires an object with an "exists" key');if(!e.exists&&!e.blank)throw new Error('@if requires an "exists" key or a "blank" key');if(void 0!==e.exists){const n=q(e.exists,t);r=null!=n}else if(void 0!==e.blank){const n=q(e.blank,t);r=null!=n&&""!=n}return r&&void 0!==e.then?q(e.then,t):!r&&e.else?q(e.else,t):void 0})),x("@case",((e,t)=>{if(!(0,o.Kn)(e))throw new Error('@case requires an object with a "operator" key');if(!e.operator)throw new Error('@case requires a "operator" key');const r=e.operator;if(e.value){const n=q(e.value,t);if("string"==typeof n)switch(r){case"lower":return n.toLowerCase();case"upper":return n.toUpperCase();default:throw new Error('operator key should have a value of "lower" or "upper"')}return n}}));const M=10,L=10;function U(e,t,r,n){if(t.length>M)throw new Error(`@replace requires a "pattern" less than ${M} characters`);if(r.length>L)throw new Error(`@replace requires a "replacement" less than ${L} characters`);return t=t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),e.replace(new RegExp(t,n),r)}function F(e){if((0,o.Kn)(e)){const t=Object.assign({},e);for(const r of Object.keys(e)){const n=e[r];null===n?t[r]=void 0:(0,o.Kn)(n)&&(t[r]=F(n))}return t}return e}function V(e){let t={...e};if(Object.keys(e).includes(C)){const r={};for(const t in e)t!==C&&(r[t]=e[t]);if(t=e[C],Object.keys(t).length>1)throw new Error("The root mapping must only have a single directive object");const n=t[Object.keys(t)[0]];if(!n||"object"!=typeof n)throw new Error("The root directive must be an object");n.mapping=r}return t}function q(e,t){if(!(0,o.Kn)(e)&&!(0,o.kJ)(e))return e;if(s(e))return function(e,t){const r=Object.keys(e).find((e=>e.startsWith("@"))),n=j[r],s=e[r];if("function"!=typeof n)throw new Error(`${r} is not a valid directive, got ${(0,o.X6)(n)}`);return n(s,t)}(e,t);if(Array.isArray(e))return e.map((e=>q(e,t)));const r={};for(const n of Object.keys(e))r[n]=q(e[n],t);return r}function z(e,t={}){const r=(0,o.X6)(t);if("object"!==r)throw new Error(`data must be an object, got ${r}`);const n=V(e);P(n);return a(q(n,t))}x("@replace",((e,t)=>{if(!(0,o.Kn)(e))throw new Error('@replace requires an object with a "pattern" key');if(!e.pattern)throw new Error('@replace requires a "pattern" key');null==e.replacement&&(e.replacement=""),e.pattern2&&null==e.replacement2&&(e.replacement2=""),null==e.ignorecase&&(e.ignorecase=!1),null==e.global&&(e.global=!0);const r=e.pattern,n=e.replacement,s=e.ignorecase,a=e.global;if(e.value){let o=q(e.value,t),i="";if("boolean"!=typeof o&&"number"!=typeof o||(o=String(o)),"string"==typeof o&&"string"==typeof r&&"string"==typeof n&&"boolean"==typeof s&&"boolean"==typeof a){let t="";a&&(t+="g"),s&&(t+="i"),i=U(o,r,n,t),e.pattern2&&"string"==typeof e.pattern2&&"string"==typeof e.replacement2&&(i=U(i,e.pattern2,e.replacement2,t))}return i}})),x("@arrayPath",((e,t)=>{if(!Array.isArray(e))throw new Error(`@arrayPath expected array, got ${(0,o.X6)(e)}`);const[r,s]=e,a="string"==typeof r?(0,n.U)(t,r.replace("$.","")):q(r,t);return["object","array"].includes((0,o.X6)(a))&&"object"===(0,o.X6)(s)&&Object.keys(s).length>0?A(a).map((e=>q(s,e))):a})),k("@path",((e,t)=>(0,n.U)(t,e.replace("$.","")))),k("@template",((e,t)=>function(e,t={}){if("string"!=typeof e)throw new TypeError(`Invalid template! Template should be a "string" but ${(0,o.X6)(e)} was given.`);function r(e){return r=>(r=r.slice(e,-e).trim(),(0,n.U)(t,r)??"")}return e.replace(/\{\{\{([^}]+)\}\}\}/g,r(3)).replace(/\{\{([^}]+)\}\}/g,r(2))}(e,t))),x("@literal",((e,t)=>q(e,t))),x("@flatten",((e,t)=>{if(!(0,o.Kn)(e))throw new Error('@flatten requires an object with a "separator" key');if(!e.separator)throw new Error('@flatten requires a "separator" key');const r=q(e.separator,t);if("string"!=typeof r)throw new Error("@flatten requires a string separator");const n=q(e.value,t);return I(n,"",r,Boolean(e.omitArrays))})),x("@json",((e,t)=>{if(!(0,o.Kn)(e))throw new Error('@json requires an object with a "value" key');if(!e.mode)throw new Error('@json requires a "mode" key');if(!e.value)throw new Error('@json requires a "value" key');const r=q(e.value,t);return"encode"===e.mode?JSON.stringify(r):"decode"===e.mode?"string"==typeof r?JSON.parse(r):r:void 0})),x("@merge",((e,t)=>{if(!(0,o.Kn)(e))throw new Error('@merge requires an object with an "objects" key and a "direction" key');if(!e.direction)throw new Error('@merge requires a "direction" key');const r=q(e.direction,t);if(!e.objects)throw new Error('@merge requires a "objects" key');if(!Array.isArray(e.objects))throw new Error("@merge: expected opts.array, got "+typeof e.objects);const n=e.objects.map((e=>q(e,t)));return"left"===r&&n.reverse(),Object.assign({},...n)})),x("@transform",((e,t)=>{if(!(0,o.Kn)(e))throw new Error('@transform requires an object with an "apply" key and a "mapping" key');if(!e.mapping)throw new Error('@transform requires a "mapping" key');if(!e.apply)throw new Error('@transform requires a "apply" key');if(!(0,o.Kn)(e.apply))throw new Error('@transform "apply" key should be an object');const r=z(e.apply,t);return q(e.mapping,r)})),x("@excludeWhenNull",((e,t)=>{const r=q(e,t);if(null!==r)return F(r)})),x("@liquid",((e,t)=>(0,R.J)(e,t)))},5145:(e,t,r)=>{"use strict";r.d(t,{J:()=>s});var n=r(9768);const o=new n.Liquid({renderLimit:500,parseLimit:1e3,memoryLimit:1e8});function s(e,t){if("string"!=typeof e)throw new Error("liquid template value must be a string");if(0===e.length)return"";if(e.length>1e3)throw new Error("liquid template values are limited to 1000 characters");const r=o.parseAndRenderSync(e,t);return"string"!=typeof r?"error":r}["case","for","include","layout","render","tablerow"].forEach((e=>{const t={parse:function(){throw new Error(`tag "${e}" is disabled`)},render:function(){throw new Error(`tag "${e}" is disabled`)}};o.registerTag(e,t)})),["array_to_sentence_string","concat","find","find_exp","find_index","find_index_exp","group_by","group_by_exp","has","has_exp","map","newline_to_br","reject","reject_exp","reverse","sort","sort_natural","uniq","where_exp","type"].forEach((e=>{var t;o.registerFilter(e,(t=e,function(){throw new Error(`filter "${t}" is disabled`)}))}))},6933:(e,t,r)=>{"use strict";function n(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function o(e){return"object"===n(e)}function s(e){return Array.isArray(e)}r.d(t,{Kn:()=>o,X6:()=>n,kJ:()=>s})},1745:(e,t)=>{"use strict";function r(e,t){return{validate:e,compare:t}}Object.defineProperty(t,"__esModule",{value:!0}),t.formatNames=t.fastFormats=t.fullFormats=void 0,t.fullFormats={date:r(s,a),time:r(c,u),"date-time":r((function(e){const t=e.split(d);return 2===t.length&&s(t[0])&&c(t[1],!0)}),l),duration:/^P(?!$)((\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?|(\d+W)?)$/,uri:function(e){return f.test(e)&&p.test(e)},"uri-reference":/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,"uri-template":/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,url:/^(?:https?|ftp):\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)(?:\.(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i,regex:function(e){if(y.test(e))return!1;try{return new RegExp(e),!0}catch(e){return!1}},uuid:/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,"json-pointer":/^(?:\/(?:[^~/]|~0|~1)*)*$/,"json-pointer-uri-fragment":/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,"relative-json-pointer":/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,byte:function(e){return h.lastIndex=0,h.test(e)},int32:{type:"number",validate:function(e){return Number.isInteger(e)&&e<=2147483647&&e>=-2147483648}},int64:{type:"number",validate:function(e){return Number.isInteger(e)}},float:{type:"number",validate:m},double:{type:"number",validate:m},password:!0,binary:!0},t.fastFormats={...t.fullFormats,date:r(/^\d\d\d\d-[0-1]\d-[0-3]\d$/,a),time:r(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,u),"date-time":r(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,l),uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i},t.formatNames=Object.keys(t.fullFormats);const n=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,o=[0,31,28,31,30,31,30,31,31,30,31,30,31];function s(e){const t=n.exec(e);if(!t)return!1;const r=+t[1],s=+t[2],a=+t[3];return s>=1&&s<=12&&a>=1&&a<=(2===s&&function(e){return e%4==0&&(e%100!=0||e%400==0)}(r)?29:o[s])}function a(e,t){if(e&&t)return e>t?1:e<t?-1:0}const i=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function c(e,t){const r=i.exec(e);if(!r)return!1;const n=+r[1],o=+r[2],s=+r[3],a=r[5];return(n<=23&&o<=59&&s<=59||23===n&&59===o&&60===s)&&(!t||""!==a)}function u(e,t){if(!e||!t)return;const r=i.exec(e),n=i.exec(t);return r&&n?(e=r[1]+r[2]+r[3]+(r[4]||""))>(t=n[1]+n[2]+n[3]+(n[4]||""))?1:e<t?-1:0:void 0}const d=/t|\s/i;function l(e,t){if(!e||!t)return;const[r,n]=e.split(d),[o,s]=t.split(d),i=a(r,o);return void 0!==i?i||u(n,s):void 0}const f=/\/|:/,p=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;const h=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm;function m(){return!0}const y=/[^\\]\\Z/},3052:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1745),o=r(373),s=r(9143),a=new s.Name("fullFormats"),i=new s.Name("fastFormats"),c=(e,t={keywords:!0})=>{if(Array.isArray(t))return u(e,t,n.fullFormats,a),e;const[r,s]="fast"===t.mode?[n.fastFormats,i]:[n.fullFormats,a];return u(e,t.formats||n.formatNames,r,s),t.keywords&&o.default(e),e};function u(e,t,r,n){var o,a;null!==(o=(a=e.opts.code).formats)&&void 0!==o||(a.formats=s._`require("ajv-formats/dist/formats").${n}`);for(const n of t)e.addFormat(n,r[n])}c.get=(e,t="full")=>{const r=("fast"===t?n.fastFormats:n.fullFormats)[e];if(!r)throw new Error(`Unknown format "${e}"`);return r},e.exports=t=c,Object.defineProperty(t,"__esModule",{value:!0}),t.default=c},373:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatLimitDefinition=void 0;const n=r(3944),o=r(9143),s=o.operators,a={formatMaximum:{okStr:"<=",ok:s.LTE,fail:s.GT},formatMinimum:{okStr:">=",ok:s.GTE,fail:s.LT},formatExclusiveMaximum:{okStr:"<",ok:s.LT,fail:s.GTE},formatExclusiveMinimum:{okStr:">",ok:s.GT,fail:s.LTE}},i={message:({keyword:e,schemaCode:t})=>o.str`should be ${a[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>o._`{comparison: ${a[e].okStr}, limit: ${t}}`};t.formatLimitDefinition={keyword:Object.keys(a),type:"string",schemaType:"string",$data:!0,error:i,code(e){const{gen:t,data:r,schemaCode:s,keyword:i,it:c}=e,{opts:u,self:d}=c;if(!u.validateFormats)return;const l=new n.KeywordCxt(c,d.RULES.all.format.definition,"format");function f(e){return o._`${e}.compare(${r}, ${s}) ${a[i].fail} 0`}l.$data?function(){const r=t.scopeValue("formats",{ref:d.formats,code:u.code.formats}),n=t.const("fmt",o._`${r}[${l.schemaCode}]`);e.fail$data(o.or(o._`typeof ${n} != "object"`,o._`${n} instanceof RegExp`,o._`typeof ${n}.compare != "function"`,f(n)))}():function(){const r=l.schema,n=d.formats[r];if(!n||!0===n)return;if("object"!=typeof n||n instanceof RegExp||"function"!=typeof n.compare)throw new Error(`"${i}": format "${r}" does not define "compare" function`);const s=t.scopeValue("formats",{key:r,ref:n,code:u.code.formats?o._`${u.code.formats}${o.getProperty(r)}`:void 0});e.fail$data(f(s))}()},dependencies:["format"]};t.default=e=>(e.addKeyword(t.formatLimitDefinition),e)},3944:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;const n=r(3993),o=r(7578),s=r(242),a=r(2081),i=["/properties"],c="http://json-schema.org/draft-07/schema";class u extends n.default{_addVocabularies(){super._addVocabularies(),o.default.forEach((e=>this.addVocabulary(e))),this.opts.discriminator&&this.addKeyword(s.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(a,i):a;this.addMetaSchema(e,c,!1),this.refs["http://json-schema.org/schema"]=c}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(c)?c:void 0)}}e.exports=t=u,Object.defineProperty(t,"__esModule",{value:!0}),t.default=u;var d=r(5344);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return d.KeywordCxt}});var l=r(9143);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return l._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return l.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return l.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return l.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return l.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return l.CodeGen}})},9782:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class n extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=n;class o extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const e=this._items[0];return""===e||'""'===e}get str(){var e;return null!==(e=this._str)&&void 0!==e?e:this._str=this._items.reduce(((e,t)=>`${e}${t}`),"")}get names(){var e;return null!==(e=this._names)&&void 0!==e?e:this._names=this._items.reduce(((e,t)=>(t instanceof n&&(e[t.str]=(e[t.str]||0)+1),e)),{})}}function s(e,...t){const r=[e[0]];let n=0;for(;n<t.length;)c(r,t[n]),r.push(e[++n]);return new o(r)}t._Code=o,t.nil=new o(""),t._=s;const a=new o("+");function i(e,...t){const r=[d(e[0])];let n=0;for(;n<t.length;)r.push(a),c(r,t[n]),r.push(a,d(e[++n]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===a){const r=u(e[t-1],e[t+1]);if(void 0!==r){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}(r),new o(r)}function c(e,t){var r;t instanceof o?e.push(...t._items):t instanceof n?e.push(t):e.push("number"==typeof(r=t)||"boolean"==typeof r||null===r?r:d(Array.isArray(r)?r.join(","):r))}function u(e,t){if('""'===t)return e;if('""'===e)return t;if("string"==typeof e){if(t instanceof n||'"'!==e[e.length-1])return;return"string"!=typeof t?`${e.slice(0,-1)}${t}"`:'"'===t[0]?e.slice(0,-1)+t.slice(1):void 0}return"string"!=typeof t||'"'!==t[0]||e instanceof n?void 0:`"${e}${t.slice(1)}`}function d(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=i,t.addCodeArg=c,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:i`${e}${t}`},t.stringify=function(e){return new o(d(e))},t.safeStringify=d,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new o(`.${e}`):s`[${e}]`},t.regexpCode=function(e){return new o(e.toString())}},9143:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;const n=r(9782),o=r(8167);var s=r(9782);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return s._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return s.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return s.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return s.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return s.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return s.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return s.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return s.Name}});var a=r(8167);Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return a.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return a.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return a.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return a.varKinds}}),t.operators={GT:new n._Code(">"),GTE:new n._Code(">="),LT:new n._Code("<"),LTE:new n._Code("<="),EQ:new n._Code("==="),NEQ:new n._Code("!=="),NOT:new n._Code("!"),OR:new n._Code("||"),AND:new n._Code("&&"),ADD:new n._Code("+")};class i{optimizeNodes(){return this}optimizeNames(e,t){return this}}class c extends i{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){const r=e?o.varKinds.var:this.varKind,n=void 0===this.rhs?"":` = ${this.rhs}`;return`${r} ${this.name}${n};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=R(this.rhs,e,t)),this}get names(){return this.rhs instanceof n._CodeOrName?this.rhs.names:{}}}class u extends i{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof n.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=R(this.rhs,e,t),this}get names(){return I(this.lhs instanceof n.Name?{}:{...this.lhs.names},this.rhs)}}class d extends u{constructor(e,t,r,n){super(e,r,n),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class l extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class f extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`break${this.label?` ${this.label}`:""};`+e}}class p extends i{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class h extends i{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=R(this.code,e,t),this}get names(){return this.code instanceof n._CodeOrName?this.code.names:{}}}class m extends i{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce(((t,r)=>t+r.render(e)),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;for(;t--;){const r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){const{nodes:r}=this;let n=r.length;for(;n--;){const o=r[n];o.optimizeNames(e,t)||(C(e,o.names),r.splice(n,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce(((e,t)=>A(e,t.names)),{})}}class y extends m{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class v extends m{}class g extends y{}g.kind="else";class _ extends y{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new g(e):e}return t?!1===e?t instanceof _?t:t.nodes:this.nodes.length?this:new _(j(e),t instanceof _?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null===(r=this.else)||void 0===r?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=R(this.condition,e,t),this}get names(){const e=super.names;return I(e,this.condition),this.else&&A(e,this.else.names),e}}_.kind="if";class E extends y{}E.kind="for";class w extends E{constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=R(this.iteration,e,t),this}get names(){return A(super.names,this.iteration.names)}}class $ extends E{constructor(e,t,r,n){super(),this.varKind=e,this.name=t,this.from=r,this.to=n}render(e){const t=e.es5?o.varKinds.var:this.varKind,{name:r,from:n,to:s}=this;return`for(${t} ${r}=${n}; ${r}<${s}; ${r}++)`+super.render(e)}get names(){const e=I(super.names,this.from);return I(e,this.to)}}class b extends E{constructor(e,t,r,n){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=n}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=R(this.iterable,e,t),this}get names(){return A(super.names,this.iterable.names)}}class T extends y{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(e)}}T.kind="func";class O extends m{render(e){return"return "+super.render(e)}}O.kind="return";class S extends y{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null===(e=this.catch)||void 0===e||e.optimizeNodes(),null===(t=this.finally)||void 0===t||t.optimizeNodes(),this}optimizeNames(e,t){var r,n;return super.optimizeNames(e,t),null===(r=this.catch)||void 0===r||r.optimizeNames(e,t),null===(n=this.finally)||void 0===n||n.optimizeNames(e,t),this}get names(){const e=super.names;return this.catch&&A(e,this.catch.names),this.finally&&A(e,this.finally.names),e}}class N extends y{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}N.kind="catch";class P extends y{render(e){return"finally"+super.render(e)}}P.kind="finally";function A(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function I(e,t){return t instanceof n._CodeOrName?A(e,t.names):e}function R(e,t,r){return e instanceof n.Name?s(e):(o=e)instanceof n._Code&&o._items.some((e=>e instanceof n.Name&&1===t[e.str]&&void 0!==r[e.str]))?new n._Code(e._items.reduce(((e,t)=>(t instanceof n.Name&&(t=s(t)),t instanceof n._Code?e.push(...t._items):e.push(t),e)),[])):e;var o;function s(e){const n=r[e.str];return void 0===n||1!==t[e.str]?e:(delete t[e.str],n)}}function C(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function j(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:n._`!${M(e)}`}t.CodeGen=class{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new o.Scope({parent:e}),this._nodes=[new v]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,n){const o=this._scope.toName(t);return void 0!==r&&n&&(this._constants[o.str]=r),this._leafNode(new c(e,o,r)),o}const(e,t,r){return this._def(o.varKinds.const,e,t,r)}let(e,t,r){return this._def(o.varKinds.let,e,t,r)}var(e,t,r){return this._def(o.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new u(e,t,r))}add(e,r){return this._leafNode(new d(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==n.nil&&this._leafNode(new h(e)),this}object(...e){const t=["{"];for(const[r,o]of e)t.length>1&&t.push(","),t.push(r),(r!==o||this.opts.es5)&&(t.push(":"),(0,n.addCodeArg)(t,o));return t.push("}"),new n._Code(t)}if(e,t,r){if(this._blockNode(new _(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new _(e))}else(){return this._elseNode(new g)}endIf(){return this._endBlockNode(_,g)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new w(e),t)}forRange(e,t,r,n,s=(this.opts.es5?o.varKinds.var:o.varKinds.let)){const a=this._scope.toName(e);return this._for(new $(s,a,t,r),(()=>n(a)))}forOf(e,t,r,s=o.varKinds.const){const a=this._scope.toName(e);if(this.opts.es5){const e=t instanceof n.Name?t:this.var("_arr",t);return this.forRange("_i",0,n._`${e}.length`,(t=>{this.var(a,n._`${e}[${t}]`),r(a)}))}return this._for(new b("of",s,a,t),(()=>r(a)))}forIn(e,t,r,s=(this.opts.es5?o.varKinds.var:o.varKinds.const)){if(this.opts.ownProperties)return this.forOf(e,n._`Object.keys(${t})`,r);const a=this._scope.toName(e);return this._for(new b("in",s,a,t),(()=>r(a)))}endFor(){return this._endBlockNode(E)}label(e){return this._leafNode(new l(e))}break(e){return this._leafNode(new f(e))}return(e){const t=new O;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(O)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const n=new S;if(this._blockNode(n),this.code(e),t){const e=this.name("e");this._currNode=n.catch=new N(e),t(e)}return r&&(this._currNode=n.finally=new P,this.code(r)),this._endBlockNode(N,P)}throw(e){return this._leafNode(new p(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){const t=this._blockStarts.pop();if(void 0===t)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=n.nil,r,o){return this._blockNode(new T(e,t,r)),o&&this.code(o).endFunc(),this}endFunc(){return this._endBlockNode(T)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof _))throw new Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}},t.not=j;const D=k(t.operators.AND);t.and=function(...e){return e.reduce(D)};const x=k(t.operators.OR);function k(e){return(t,r)=>t===n.nil?r:r===n.nil?t:n._`${M(t)} ${e} ${M(r)}`}function M(e){return e instanceof n.Name?e:n._`(${e})`}t.or=function(...e){return e.reduce(x)}},8167:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;const n=r(9782);class o extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}var s;!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(s=t.UsedValueState||(t.UsedValueState={})),t.varKinds={const:new n.Name("const"),let:new n.Name("let"),var:new n.Name("var")};class a{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof n.Name?e:this.name(e)}name(e){return new n.Name(this._newName(e))}_newName(e){return`${e}${(this._names[e]||this._nameGroup(e)).index++}`}_nameGroup(e){var t,r;if((null===(r=null===(t=this._parent)||void 0===t?void 0:t._prefixes)||void 0===r?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=a;class i extends n.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=n._`.${new n.Name(t)}[${r}]`}}t.ValueScopeName=i;const c=n._`\n`;t.ValueScope=class extends a{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?c:n.nil}}get(){return this._scope}name(e){return new i(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw new Error("CodeGen: ref must be passed in value");const n=this.toName(e),{prefix:o}=n,s=null!==(r=t.key)&&void 0!==r?r:t.ref;let a=this._values[o];if(a){const e=a.get(s);if(e)return e}else a=this._values[o]=new Map;a.set(s,n);const i=this._scope[o]||(this._scope[o]=[]),c=i.length;return i[c]=t.ref,n.setValue(t,{property:o,itemIndex:c}),n}getValue(e,t){const r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,(t=>{if(void 0===t.scopePath)throw new Error(`CodeGen: name "${t}" has no value`);return n._`${e}${t.scopePath}`}))}scopeCode(e=this._values,t,r){return this._reduceValues(e,(e=>{if(void 0===e.value)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code}),t,r)}_reduceValues(e,r,a={},i){let c=n.nil;for(const u in e){const d=e[u];if(!d)continue;const l=a[u]=a[u]||new Map;d.forEach((e=>{if(l.has(e))return;l.set(e,s.Started);let a=r(e);if(a){const r=this.opts.es5?t.varKinds.var:t.varKinds.const;c=n._`${c}${r} ${e} = ${a};${this.opts._n}`}else{if(!(a=null==i?void 0:i(e)))throw new o(e);c=n._`${c}${a}${this.opts._n}`}l.set(e,s.Completed)}))}return c}}},3127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;const n=r(9143),o=r(2493),s=r(8460);function a(e,t){const r=e.const("err",t);e.if(n._`${s.default.vErrors} === null`,(()=>e.assign(s.default.vErrors,n._`[${r}]`)),n._`${s.default.vErrors}.push(${r})`),e.code(n._`${s.default.errors}++`)}function i(e,t){const{gen:r,validateName:o,schemaEnv:s}=e;s.$async?r.throw(n._`new ${e.ValidationError}(${t})`):(r.assign(n._`${o}.errors`,t),r.return(!1))}t.keywordError={message:({keyword:e})=>n.str`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?n.str`"${e}" keyword must be ${t} ($data)`:n.str`"${e}" keyword is invalid ($data)`},t.reportError=function(e,r=t.keywordError,o,s){const{it:c}=e,{gen:d,compositeRule:l,allErrors:f}=c,p=u(e,r,o);(null!=s?s:l||f)?a(d,p):i(c,n._`[${p}]`)},t.reportExtraError=function(e,r=t.keywordError,n){const{it:o}=e,{gen:c,compositeRule:d,allErrors:l}=o;a(c,u(e,r,n)),d||l||i(o,s.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(s.default.errors,t),e.if(n._`${s.default.vErrors} !== null`,(()=>e.if(t,(()=>e.assign(n._`${s.default.vErrors}.length`,t)),(()=>e.assign(s.default.vErrors,null)))))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:o,errsCount:a,it:i}){if(void 0===a)throw new Error("ajv implementation error");const c=e.name("err");e.forRange("i",a,s.default.errors,(a=>{e.const(c,n._`${s.default.vErrors}[${a}]`),e.if(n._`${c}.instancePath === undefined`,(()=>e.assign(n._`${c}.instancePath`,(0,n.strConcat)(s.default.instancePath,i.errorPath)))),e.assign(n._`${c}.schemaPath`,n.str`${i.errSchemaPath}/${t}`),i.opts.verbose&&(e.assign(n._`${c}.schema`,r),e.assign(n._`${c}.data`,o))}))};const c={keyword:new n.Name("keyword"),schemaPath:new n.Name("schemaPath"),params:new n.Name("params"),propertyName:new n.Name("propertyName"),message:new n.Name("message"),schema:new n.Name("schema"),parentSchema:new n.Name("parentSchema")};function u(e,t,r){const{createErrors:o}=e.it;return!1===o?n._`{}`:function(e,t,r={}){const{gen:o,it:a}=e,i=[d(a,r),l(e,r)];return function(e,{params:t,message:r},o){const{keyword:a,data:i,schemaValue:u,it:d}=e,{opts:l,propertyName:f,topSchemaRef:p,schemaPath:h}=d;o.push([c.keyword,a],[c.params,"function"==typeof t?t(e):t||n._`{}`]),l.messages&&o.push([c.message,"function"==typeof r?r(e):r]);l.verbose&&o.push([c.schema,u],[c.parentSchema,n._`${p}${h}`],[s.default.data,i]);f&&o.push([c.propertyName,f])}(e,t,i),o.object(...i)}(e,t,r)}function d({errorPath:e},{instancePath:t}){const r=t?n.str`${e}${(0,o.getErrorPath)(t,o.Type.Str)}`:e;return[s.default.instancePath,(0,n.strConcat)(s.default.instancePath,r)]}function l({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:s}){let a=s?t:n.str`${t}/${e}`;return r&&(a=n.str`${a}${(0,o.getErrorPath)(r,o.Type.Str)}`),[c.schemaPath,a]}},4335:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;const n=r(9143),o=r(2569),s=r(8460),a=r(21),i=r(2493),c=r(5344),u=r(2854);class d{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!==(t=e.baseId)&&void 0!==t?t:(0,a.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function l(e){const t=p.call(this,e);if(t)return t;const r=(0,a.getFullPath)(e.root.baseId),{es5:i,lines:u}=this.opts.code,{ownProperties:d}=this.opts,l=new n.CodeGen(this.scope,{es5:i,lines:u,ownProperties:d});let f;e.$async&&(f=l.scopeValue("Error",{ref:o.default,code:n._`require("ajv/dist/runtime/validation_error").default`}));const h=l.scopeName("validate");e.validateName=h;const m={gen:l,allErrors:this.opts.allErrors,data:s.default.data,parentData:s.default.parentData,parentDataProperty:s.default.parentDataProperty,dataNames:[s.default.data],dataPathArr:[n.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:l.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,n.stringify)(e.schema)}:{ref:e.schema}),validateName:h,ValidationError:f,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:n.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:n._`""`,opts:this.opts,self:this};let y;try{this._compilations.add(e),(0,c.validateFunctionCode)(m),l.optimize(this.opts.code.optimize);const t=l.toString();y=`${l.scopeRefs(s.default.scope)}return ${t}`,this.opts.code.process&&(y=this.opts.code.process(y,e));const r=new Function(`${s.default.self}`,`${s.default.scope}`,y)(this,this.scope.get());if(this.scope.value(h,{ref:r}),r.errors=null,r.schema=e.schema,r.schemaEnv=e,e.$async&&(r.$async=!0),!0===this.opts.code.source&&(r.source={validateName:h,validateCode:t,scopeValues:l._values}),this.opts.unevaluated){const{props:e,items:t}=m;r.evaluated={props:e instanceof n.Name?void 0:e,items:t instanceof n.Name?void 0:t,dynamicProps:e instanceof n.Name,dynamicItems:t instanceof n.Name},r.source&&(r.source.evaluated=(0,n.stringify)(r.evaluated))}return e.validate=r,e}catch(t){throw delete e.validate,delete e.validateName,y&&this.logger.error("Error compiling schema, function code:",y),t}finally{this._compilations.delete(e)}}function f(e){return(0,a.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:l.call(this,e)}function p(e){for(const n of this._compilations)if(r=e,(t=n).schema===r.schema&&t.root===r.root&&t.baseId===r.baseId)return n;var t,r}function h(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||m.call(this,e,t)}function m(e,t){const r=u.parse(t),n=(0,a._getFullPath)(r);let o=(0,a.getFullPath)(e.baseId);if(Object.keys(e.schema).length>0&&n===o)return v.call(this,r,e);const s=(0,a.normalizeId)(n),i=this.refs[s]||this.schemas[s];if("string"==typeof i){const t=m.call(this,e,i);if("object"!=typeof(null==t?void 0:t.schema))return;return v.call(this,r,t)}if("object"==typeof(null==i?void 0:i.schema)){if(i.validate||l.call(this,i),s===(0,a.normalizeId)(t)){const{schema:t}=i,{schemaId:r}=this.opts,n=t[r];return n&&(o=(0,a.resolveUrl)(o,n)),new d({schema:t,schemaId:r,root:e,baseId:o})}return v.call(this,r,i)}}t.SchemaEnv=d,t.compileSchema=l,t.resolveRef=function(e,t,r){var n;r=(0,a.resolveUrl)(t,r);const o=e.refs[r];if(o)return o;let s=h.call(this,e,r);if(void 0===s){const o=null===(n=e.localRefs)||void 0===n?void 0:n[r],{schemaId:a}=this.opts;o&&(s=new d({schema:o,schemaId:a,root:e,baseId:t}))}return void 0!==s?e.refs[r]=f.call(this,s):void 0},t.getCompilingSchema=p,t.resolveSchema=m;const y=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function v(e,{baseId:t,schema:r,root:n}){var o;if("/"!==(null===(o=e.fragment)||void 0===o?void 0:o[0]))return;for(const n of e.fragment.slice(1).split("/")){if("boolean"==typeof r)return;const e=r[(0,i.unescapeFragment)(n)];if(void 0===e)return;const o="object"==typeof(r=e)&&r[this.opts.schemaId];!y.has(n)&&o&&(t=(0,a.resolveUrl)(t,o))}let s;if("boolean"!=typeof r&&r.$ref&&!(0,i.schemaHasRulesButRef)(r,this.RULES)){const e=(0,a.resolveUrl)(t,r.$ref);s=m.call(this,n,e)}const{schemaId:c}=this.opts;return s=s||new d({schema:r,schemaId:c,root:n,baseId:t}),s.schema!==s.root.schema?s:void 0}},8460:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o={data:new n.Name("data"),valCxt:new n.Name("valCxt"),instancePath:new n.Name("instancePath"),parentData:new n.Name("parentData"),parentDataProperty:new n.Name("parentDataProperty"),rootData:new n.Name("rootData"),dynamicAnchors:new n.Name("dynamicAnchors"),vErrors:new n.Name("vErrors"),errors:new n.Name("errors"),this:new n.Name("this"),self:new n.Name("self"),scope:new n.Name("scope"),json:new n.Name("json"),jsonPos:new n.Name("jsonPos"),jsonLen:new n.Name("jsonLen"),jsonPart:new n.Name("jsonPart")};t.default=o},2589:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(21);class o extends Error{constructor(e,t,r){super(r||`can't resolve reference ${t} from id ${e}`),this.missingRef=(0,n.resolveUrl)(e,t),this.missingSchema=(0,n.normalizeId)((0,n.getFullPath)(this.missingRef))}}t.default=o},21:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const n=r(2493),o=r(9378),s=r(9522),a=r(2854),i=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!u(e):!!t&&d(e)<=t)};const c=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function u(e){for(const t in e){if(c.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(u))return!0;if("object"==typeof r&&u(r))return!0}return!1}function d(e){let t=0;for(const r in e){if("$ref"===r)return 1/0;if(t++,!i.has(r)&&("object"==typeof e[r]&&(0,n.eachItem)(e[r],(e=>t+=d(e))),t===1/0))return 1/0}return t}function l(e="",t){!1!==t&&(e=h(e));return f(a.parse(e))}function f(e){return a.serialize(e).split("#")[0]+"#"}t.getFullPath=l,t._getFullPath=f;const p=/#\/?$/;function h(e){return e?e.replace(p,""):""}t.normalizeId=h,t.resolveUrl=function(e,t){return t=h(t),a.resolve(e,t)};const m=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};const{schemaId:r}=this.opts,n=h(e[r]||t),i={"":n},c=l(n,!1),u={},d=new Set;return s(e,{allKeys:!0},((e,t,n,o)=>{if(void 0===o)return;const s=c+t;let l=i[o];function y(t){if(t=h(l?a.resolve(l,t):t),d.has(t))throw p(t);d.add(t);let r=this.refs[t];return"string"==typeof r&&(r=this.refs[r]),"object"==typeof r?f(e,r.schema,t):t!==h(s)&&("#"===t[0]?(f(e,u[t],t),u[t]=e):this.refs[t]=s),t}function v(e){if("string"==typeof e){if(!m.test(e))throw new Error(`invalid anchor "${e}"`);y.call(this,`#${e}`)}}"string"==typeof e[r]&&(l=y.call(this,e[r])),v.call(this,e.$anchor),v.call(this,e.$dynamicAnchor),i[t]=l})),u;function f(e,t,r){if(void 0!==t&&!o(e,t))throw p(r)}function p(e){return new Error(`reference "${e}" resolves to more than one schema`)}}},5766:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;const r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}},2493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;const n=r(9143),o=r(9782);function s(e,t=e.schema){const{opts:r,self:n}=e;if(!r.strictSchema)return;if("boolean"==typeof t)return;const o=n.RULES.keywords;for(const r in t)o[r]||h(e,`unknown keyword: "${r}"`)}function a(e,t){if("boolean"==typeof e)return!e;for(const r in e)if(t[r])return!0;return!1}function i(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function c(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function u({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:o}){return(s,a,i,c)=>{const u=void 0===i?a:i instanceof n.Name?(a instanceof n.Name?e(s,a,i):t(s,a,i),i):a instanceof n.Name?(t(s,i,a),a):r(a,i);return c!==n.Name||u instanceof n.Name?u:o(s,u)}}function d(e,t){if(!0===t)return e.var("props",!0);const r=e.var("props",n._`{}`);return void 0!==t&&l(e,r,t),r}function l(e,t,r){Object.keys(r).forEach((r=>e.assign(n._`${t}${(0,n.getProperty)(r)}`,!0)))}t.toHash=function(e){const t={};for(const r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(s(e,t),!a(t,e.self.RULES.all))},t.checkUnknownRules=s,t.schemaHasRules=a,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(const r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,o,s){if(!s){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return n._`${r}`}return n._`${e}${t}${(0,n.getProperty)(o)}`},t.unescapeFragment=function(e){return c(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(i(e))},t.escapeJsonPointer=i,t.unescapeJsonPointer=c,t.eachItem=function(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)},t.mergeEvaluated={props:u({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,(()=>{e.if(n._`${t} === true`,(()=>e.assign(r,!0)),(()=>e.assign(r,n._`${r} || {}`).code(n._`Object.assign(${r}, ${t})`)))})),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,(()=>{!0===t?e.assign(r,!0):(e.assign(r,n._`${r} || {}`),l(e,r,t))})),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:d}),items:u({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,(()=>e.assign(r,n._`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`))),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,(()=>e.assign(r,!0===t||n._`${r} > ${t} ? ${r} : ${t}`))),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=d,t.setEvaluated=l;const f={};var p;function h(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw new Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:f[t.code]||(f[t.code]=new o._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(p=t.Type||(t.Type={})),t.getErrorPath=function(e,t,r){if(e instanceof n.Name){const o=t===p.Num;return r?o?n._`"[" + ${e} + "]"`:n._`"['" + ${e} + "']"`:o?n._`"/" + ${e}`:n._`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,n.getProperty)(e).toString():"/"+i(e)},t.checkStrictMode=h},9032:(e,t)=>{"use strict";function r(e,t){return t.rules.some((t=>n(e,t)))}function n(e,t){var r;return void 0!==e[t.keyword]||(null===(r=t.definition.implements)||void 0===r?void 0:r.some((t=>void 0!==e[t])))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},n){const o=t.RULES.types[n];return o&&!0!==o&&r(e,o)},t.shouldUseGroup=r,t.shouldUseRule=n},4997:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const n=r(3127),o=r(9143),s=r(8460),a={message:"boolean schema is false"};function i(e,t){const{gen:r,data:o}=e,s={gen:r,keyword:"false schema",data:o,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,n.reportError)(s,a,void 0,t)}t.topBoolOrEmptySchema=function(e){const{gen:t,schema:r,validateName:n}=e;!1===r?i(e,!1):"object"==typeof r&&!0===r.$async?t.return(s.default.data):(t.assign(o._`${n}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){const{gen:r,schema:n}=e;!1===n?(r.var(t,!1),i(e)):r.var(t,!0)}},4327:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;const n=r(5766),o=r(9032),s=r(3127),a=r(9143),i=r(2493);var c;function u(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(n.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}!function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(c=t.DataType||(t.DataType={})),t.getSchemaTypes=function(e){const t=u(e.type);if(t.includes("null")){if(!1===e.nullable)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw new Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=u,t.coerceAndCheckDataType=function(e,t){const{gen:r,data:n,opts:s}=e,i=function(e,t){return t?e.filter((e=>d.has(e)||"array"===t&&"array"===e)):[]}(t,s.coerceTypes),u=t.length>0&&!(0===i.length&&1===t.length&&(0,o.schemaHasRulesForType)(e,t[0]));if(u){const o=f(t,n,s.strictNumbers,c.Wrong);r.if(o,(()=>{i.length?function(e,t,r){const{gen:n,data:o,opts:s}=e,i=n.let("dataType",a._`typeof ${o}`),c=n.let("coerced",a._`undefined`);"array"===s.coerceTypes&&n.if(a._`${i} == 'object' && Array.isArray(${o}) && ${o}.length == 1`,(()=>n.assign(o,a._`${o}[0]`).assign(i,a._`typeof ${o}`).if(f(t,o,s.strictNumbers),(()=>n.assign(c,o)))));n.if(a._`${c} !== undefined`);for(const e of r)(d.has(e)||"array"===e&&"array"===s.coerceTypes)&&u(e);function u(e){switch(e){case"string":return void n.elseIf(a._`${i} == "number" || ${i} == "boolean"`).assign(c,a._`"" + ${o}`).elseIf(a._`${o} === null`).assign(c,a._`""`);case"number":return void n.elseIf(a._`${i} == "boolean" || ${o} === null
              || (${i} == "string" && ${o} && ${o} == +${o})`).assign(c,a._`+${o}`);case"integer":return void n.elseIf(a._`${i} === "boolean" || ${o} === null
              || (${i} === "string" && ${o} && ${o} == +${o} && !(${o} % 1))`).assign(c,a._`+${o}`);case"boolean":return void n.elseIf(a._`${o} === "false" || ${o} === 0 || ${o} === null`).assign(c,!1).elseIf(a._`${o} === "true" || ${o} === 1`).assign(c,!0);case"null":return n.elseIf(a._`${o} === "" || ${o} === 0 || ${o} === false`),void n.assign(c,null);case"array":n.elseIf(a._`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${o} === null`).assign(c,a._`[${o}]`)}}n.else(),h(e),n.endIf(),n.if(a._`${c} !== undefined`,(()=>{n.assign(o,c),function({gen:e,parentData:t,parentDataProperty:r},n){e.if(a._`${t} !== undefined`,(()=>e.assign(a._`${t}[${r}]`,n)))}(e,c)}))}(e,t,i):h(e)}))}return u};const d=new Set(["string","number","integer","boolean","null"]);function l(e,t,r,n=c.Correct){const o=n===c.Correct?a.operators.EQ:a.operators.NEQ;let s;switch(e){case"null":return a._`${t} ${o} null`;case"array":s=a._`Array.isArray(${t})`;break;case"object":s=a._`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":s=i(a._`!(${t} % 1) && !isNaN(${t})`);break;case"number":s=i();break;default:return a._`typeof ${t} ${o} ${e}`}return n===c.Correct?s:(0,a.not)(s);function i(e=a.nil){return(0,a.and)(a._`typeof ${t} == "number"`,e,r?a._`isFinite(${t})`:a.nil)}}function f(e,t,r,n){if(1===e.length)return l(e[0],t,r,n);let o;const s=(0,i.toHash)(e);if(s.array&&s.object){const e=a._`typeof ${t} != "object"`;o=s.null?e:a._`!${t} || ${e}`,delete s.null,delete s.array,delete s.object}else o=a.nil;s.number&&delete s.integer;for(const e in s)o=(0,a.and)(o,l(e,t,r,n));return o}t.checkDataType=l,t.checkDataTypes=f;const p={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?a._`{type: ${e}}`:a._`{type: ${t}}`};function h(e){const t=function(e){const{gen:t,data:r,schema:n}=e,o=(0,i.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:o,schemaValue:o,parentSchema:n,params:{},it:e}}(e);(0,s.reportError)(t,p)}t.reportTypeError=h},3633:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0;const n=r(9143),o=r(2493);function s(e,t,r){const{gen:s,compositeRule:a,data:i,opts:c}=e;if(void 0===r)return;const u=n._`${i}${(0,n.getProperty)(t)}`;if(a)return void(0,o.checkStrictMode)(e,`default is ignored for: ${u}`);let d=n._`${u} === undefined`;"empty"===c.useDefaults&&(d=n._`${d} || ${u} === null || ${u} === ""`),s.if(d,n._`${u} = ${(0,n.stringify)(r)}`)}t.assignDefaults=function(e,t){const{properties:r,items:n}=e.schema;if("object"===t&&r)for(const t in r)s(e,t,r[t].default);else"array"===t&&Array.isArray(n)&&n.forEach(((t,r)=>s(e,r,t.default)))}},5344:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const n=r(4997),o=r(4327),s=r(9032),a=r(4327),i=r(3633),c=r(9264),u=r(692),d=r(9143),l=r(8460),f=r(21),p=r(2493),h=r(3127);function m({gen:e,validateName:t,schema:r,schemaEnv:n,opts:o},s){o.code.es5?e.func(t,d._`${l.default.data}, ${l.default.valCxt}`,n.$async,(()=>{e.code(d._`"use strict"; ${y(r,o)}`),function(e,t){e.if(l.default.valCxt,(()=>{e.var(l.default.instancePath,d._`${l.default.valCxt}.${l.default.instancePath}`),e.var(l.default.parentData,d._`${l.default.valCxt}.${l.default.parentData}`),e.var(l.default.parentDataProperty,d._`${l.default.valCxt}.${l.default.parentDataProperty}`),e.var(l.default.rootData,d._`${l.default.valCxt}.${l.default.rootData}`),t.dynamicRef&&e.var(l.default.dynamicAnchors,d._`${l.default.valCxt}.${l.default.dynamicAnchors}`)}),(()=>{e.var(l.default.instancePath,d._`""`),e.var(l.default.parentData,d._`undefined`),e.var(l.default.parentDataProperty,d._`undefined`),e.var(l.default.rootData,l.default.data),t.dynamicRef&&e.var(l.default.dynamicAnchors,d._`{}`)}))}(e,o),e.code(s)})):e.func(t,d._`${l.default.data}, ${function(e){return d._`{${l.default.instancePath}="", ${l.default.parentData}, ${l.default.parentDataProperty}, ${l.default.rootData}=${l.default.data}${e.dynamicRef?d._`, ${l.default.dynamicAnchors}={}`:d.nil}}={}`}(o)}`,n.$async,(()=>e.code(y(r,o)).code(s)))}function y(e,t){const r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?d._`/*# sourceURL=${r} */`:d.nil}function v(e,t){_(e)&&(E(e),g(e))?function(e,t){const{schema:r,gen:n,opts:o}=e;o.$comment&&r.$comment&&$(e);(function(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,f.resolveUrl)(e.baseId,t))})(e),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}(e);const s=n.const("_errs",l.default.errors);w(e,s),n.var(t,d._`${s} === ${l.default.errors}`)}(e,t):(0,n.boolOrEmptySchema)(e,t)}function g({schema:e,self:t}){if("boolean"==typeof e)return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function _(e){return"boolean"!=typeof e.schema}function E(e){(0,p.checkUnknownRules)(e),function(e){const{schema:t,errSchemaPath:r,opts:n,self:o}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,p.schemaHasRulesButRef)(t,o.RULES)&&o.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function w(e,t){if(e.opts.jtd)return b(e,[],!1,t);const r=(0,o.getSchemaTypes)(e.schema);b(e,r,!(0,o.coerceAndCheckDataType)(e,r),t)}function $({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:o}){const s=r.$comment;if(!0===o.$comment)e.code(d._`${l.default.self}.logger.log(${s})`);else if("function"==typeof o.$comment){const r=d.str`${n}/$comment`,o=e.scopeValue("root",{ref:t.root});e.code(d._`${l.default.self}.opts.$comment(${s}, ${r}, ${o}.schema)`)}}function b(e,t,r,n){const{gen:o,schema:i,data:c,allErrors:u,opts:f,self:h}=e,{RULES:m}=h;function y(p){(0,s.shouldUseGroup)(i,p)&&(p.type?(o.if((0,a.checkDataType)(p.type,c,f.strictNumbers)),T(e,p),1===t.length&&t[0]===p.type&&r&&(o.else(),(0,a.reportTypeError)(e)),o.endIf()):T(e,p),u||o.if(d._`${l.default.errors} === ${n||0}`))}!i.$ref||!f.ignoreKeywordsWithRef&&(0,p.schemaHasRulesButRef)(i,m)?(f.jtd||function(e,t){if(e.schemaEnv.meta||!e.opts.strictTypes)return;(function(e,t){if(!t.length)return;if(!e.dataTypes.length)return void(e.dataTypes=t);t.forEach((t=>{S(e.dataTypes,t)||N(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)})),e.dataTypes=e.dataTypes.filter((e=>S(t,e)))})(e,t),e.opts.allowUnionTypes||function(e,t){t.length>1&&(2!==t.length||!t.includes("null"))&&N(e,"use allowUnionTypes to allow union type keyword")}(e,t);!function(e,t){const r=e.self.RULES.all;for(const n in r){const o=r[n];if("object"==typeof o&&(0,s.shouldUseRule)(e.schema,o)){const{type:r}=o.definition;r.length&&!r.some((e=>O(t,e)))&&N(e,`missing type "${r.join(",")}" for keyword "${n}"`)}}}(e,e.dataTypes)}(e,t),o.block((()=>{for(const e of m.rules)y(e);y(m.post)}))):o.block((()=>A(e,"$ref",m.all.$ref.definition)))}function T(e,t){const{gen:r,schema:n,opts:{useDefaults:o}}=e;o&&(0,i.assignDefaults)(e,t.type),r.block((()=>{for(const r of t.rules)(0,s.shouldUseRule)(n,r)&&A(e,r.keyword,r.definition,t.type)}))}function O(e,t){return e.includes(t)||"number"===t&&e.includes("integer")}function S(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function N(e,t){t+=` at "${e.schemaEnv.baseId+e.errSchemaPath}" (strictTypes)`,(0,p.checkStrictMode)(e,t,e.opts.strictTypes)}t.validateFunctionCode=function(e){_(e)&&(E(e),g(e))?function(e){const{schema:t,opts:r,gen:n}=e;m(e,(()=>{r.$comment&&t.$comment&&$(e),function(e){const{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,p.checkStrictMode)(e,"default is ignored in the schema root")}(e),n.let(l.default.vErrors,null),n.let(l.default.errors,0),r.unevaluated&&function(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",d._`${r}.evaluated`),t.if(d._`${e.evaluated}.dynamicProps`,(()=>t.assign(d._`${e.evaluated}.props`,d._`undefined`))),t.if(d._`${e.evaluated}.dynamicItems`,(()=>t.assign(d._`${e.evaluated}.items`,d._`undefined`)))}(e),w(e),function(e){const{gen:t,schemaEnv:r,validateName:n,ValidationError:o,opts:s}=e;r.$async?t.if(d._`${l.default.errors} === 0`,(()=>t.return(l.default.data)),(()=>t.throw(d._`new ${o}(${l.default.vErrors})`))):(t.assign(d._`${n}.errors`,l.default.vErrors),s.unevaluated&&function({gen:e,evaluated:t,props:r,items:n}){r instanceof d.Name&&e.assign(d._`${t}.props`,r);n instanceof d.Name&&e.assign(d._`${t}.items`,n)}(e),t.return(d._`${l.default.errors} === 0`))}(e)}))}(e):m(e,(()=>(0,n.topBoolOrEmptySchema)(e)))};class P{constructor(e,t,r){if((0,c.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,p.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",C(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,c.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",l.default.errors))}result(e,t,r){this.failResult((0,d.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,d.not)(e),void 0,t)}fail(e){if(void 0===e)return this.error(),void(this.allErrors||this.gen.if(!1));this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail(d._`${t} !== undefined && (${(0,d.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t)return this.setParams(t),this._error(e,r),void this.setParams({});this._error(e,r)}_error(e,t){(e?h.reportExtraError:h.reportError)(this,this.def.error,t)}$dataError(){(0,h.reportError)(this,this.def.$dataError||h.keyword$DataError)}reset(){if(void 0===this.errsCount)throw new Error('add "trackErrors" to keyword definition');(0,h.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=d.nil){this.gen.block((()=>{this.check$data(e,r),t()}))}check$data(e=d.nil,t=d.nil){if(!this.$data)return;const{gen:r,schemaCode:n,schemaType:o,def:s}=this;r.if((0,d.or)(d._`${n} === undefined`,t)),e!==d.nil&&r.assign(e,!0),(o.length||s.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==d.nil&&r.assign(e,!1)),r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:r,def:n,it:o}=this;return(0,d.or)(function(){if(r.length){if(!(t instanceof d.Name))throw new Error("ajv implementation error");const e=Array.isArray(r)?r:[r];return d._`${(0,a.checkDataTypes)(e,t,o.opts.strictNumbers,a.DataType.Wrong)}`}return d.nil}(),function(){if(n.validateSchema){const r=e.scopeValue("validate$data",{ref:n.validateSchema});return d._`!${r}(${t})`}return d.nil}())}subschema(e,t){const r=(0,u.getSubschema)(this.it,e);(0,u.extendSubschemaData)(r,this.it,e),(0,u.extendSubschemaMode)(r,e);const n={...this.it,...r,items:void 0,props:void 0};return v(n,t),n}mergeEvaluated(e,t){const{it:r,gen:n}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=p.mergeEvaluated.props(n,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=p.mergeEvaluated.items(n,e.items,r.items,t)))}mergeValidEvaluated(e,t){const{it:r,gen:n}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return n.if(t,(()=>this.mergeEvaluated(e,d.Name))),!0}}function A(e,t,r,n){const o=new P(e,r,t);"code"in r?r.code(o,n):o.$data&&r.validate?(0,c.funcKeywordCode)(o,r):"macro"in r?(0,c.macroKeywordCode)(o,r):(r.compile||r.validate)&&(0,c.funcKeywordCode)(o,r)}t.KeywordCxt=P;const I=/^\/(?:[^~]|~0|~1)*$/,R=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function C(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let o,s;if(""===e)return l.default.rootData;if("/"===e[0]){if(!I.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);o=e,s=l.default.rootData}else{const a=R.exec(e);if(!a)throw new Error(`Invalid JSON-pointer: ${e}`);const i=+a[1];if(o=a[2],"#"===o){if(i>=t)throw new Error(c("property/index",i));return n[t-i]}if(i>t)throw new Error(c("data",i));if(s=r[t-i],!o)return s}let a=s;const i=o.split("/");for(const e of i)e&&(s=d._`${s}${(0,d.getProperty)((0,p.unescapeJsonPointer)(e))}`,a=d._`${a} && ${s}`);return a;function c(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=C},9264:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;const n=r(9143),o=r(8460),s=r(5255),a=r(3127);function i(e){const{gen:t,data:r,it:o}=e;t.if(o.parentData,(()=>t.assign(r,n._`${o.parentData}[${o.parentDataProperty}]`)))}function c(e,t,r){if(void 0===r)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,n.stringify)(r)})}t.macroKeywordCode=function(e,t){const{gen:r,keyword:o,schema:s,parentSchema:a,it:i}=e,u=t.macro.call(i.self,s,a,i),d=c(r,o,u);!1!==i.opts.validateSchema&&i.self.validateSchema(u,!0);const l=r.name("valid");e.subschema({schema:u,schemaPath:n.nil,errSchemaPath:`${i.errSchemaPath}/${o}`,topSchemaRef:d,compositeRule:!0},l),e.pass(l,(()=>e.error(!0)))},t.funcKeywordCode=function(e,t){var r;const{gen:u,keyword:d,schema:l,parentSchema:f,$data:p,it:h}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}(h,t);const m=!p&&t.compile?t.compile.call(h.self,l,f,h):t.validate,y=c(u,d,m),v=u.let("valid");function g(r=(t.async?n._`await `:n.nil)){const a=h.opts.passContext?o.default.this:o.default.self,i=!("compile"in t&&!p||!1===t.schema);u.assign(v,n._`${r}${(0,s.callValidateCode)(e,y,a,i)}`,t.modifying)}function _(e){var r;u.if((0,n.not)(null!==(r=t.valid)&&void 0!==r?r:v),e)}e.block$data(v,(function(){if(!1===t.errors)g(),t.modifying&&i(e),_((()=>e.error()));else{const r=t.async?function(){const e=u.let("ruleErrs",null);return u.try((()=>g(n._`await `)),(t=>u.assign(v,!1).if(n._`${t} instanceof ${h.ValidationError}`,(()=>u.assign(e,n._`${t}.errors`)),(()=>u.throw(t))))),e}():function(){const e=n._`${y}.errors`;return u.assign(e,null),g(n.nil),e}();t.modifying&&i(e),_((()=>function(e,t){const{gen:r}=e;r.if(n._`Array.isArray(${t})`,(()=>{r.assign(o.default.vErrors,n._`${o.default.vErrors} === null ? ${t} : ${o.default.vErrors}.concat(${t})`).assign(o.default.errors,n._`${o.default.vErrors}.length`),(0,a.extendErrors)(e)}),(()=>e.error()))}(e,r)))}})),e.ok(null!==(r=t.valid)&&void 0!==r?r:v)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some((t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e))},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:n},o,s){if(Array.isArray(o.keyword)?!o.keyword.includes(s):o.keyword!==s)throw new Error("ajv implementation error");const a=o.dependencies;if(null==a?void 0:a.some((t=>!Object.prototype.hasOwnProperty.call(e,t))))throw new Error(`parent schema must have dependencies of ${s}: ${a.join(",")}`);if(o.validateSchema){if(!o.validateSchema(e[s])){const e=`keyword "${s}" value is invalid at path "${n}": `+r.errorsText(o.validateSchema.errors);if("log"!==t.validateSchema)throw new Error(e);r.logger.error(e)}}}},692:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;const n=r(9143),o=r(2493);t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:s,schemaPath:a,errSchemaPath:i,topSchemaRef:c}){if(void 0!==t&&void 0!==s)throw new Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){const s=e.schema[t];return void 0===r?{schema:s,schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:s[r],schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}${(0,n.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,o.escapeFragment)(r)}`}}if(void 0!==s){if(void 0===a||void 0===i||void 0===c)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:s,schemaPath:a,topSchemaRef:c,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:s,data:a,dataTypes:i,propertyName:c}){if(void 0!==a&&void 0!==r)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:u}=t;if(void 0!==r){const{errorPath:a,dataPathArr:i,opts:c}=t;d(u.let("data",n._`${t.data}${(0,n.getProperty)(r)}`,!0)),e.errorPath=n.str`${a}${(0,o.getErrorPath)(r,s,c.jsPropertySyntax)}`,e.parentDataProperty=n._`${r}`,e.dataPathArr=[...i,e.parentDataProperty]}if(void 0!==a){d(a instanceof n.Name?a:u.let("data",a,!0)),void 0!==c&&(e.propertyName=c)}function d(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}i&&(e.dataTypes=i)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:o,allErrors:s}){void 0!==n&&(e.compositeRule=n),void 0!==o&&(e.createErrors=o),void 0!==s&&(e.allErrors=s),e.jtdDiscriminator=t,e.jtdMetadata=r}},3993:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var n=r(5344);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return n.KeywordCxt}});var o=r(9143);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return o._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return o.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return o.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return o.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return o.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return o.CodeGen}});const s=r(2569),a=r(2589),i=r(5766),c=r(4335),u=r(9143),d=r(21),l=r(4327),f=r(2493),p=r(7814),h=["removeAdditional","useDefaults","coerceTypes"],m=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),y={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},v={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};function g(e){var t,r,n,o,s,a,i,c,u,d,l,f,p,h,m,y,v,g,_,E,w,$;const b=e.strict,T=null===(t=e.code)||void 0===t?void 0:t.optimize,O=!0===T||void 0===T?1:T||0;return{strictSchema:null===(n=null!==(r=e.strictSchema)&&void 0!==r?r:b)||void 0===n||n,strictNumbers:null===(s=null!==(o=e.strictNumbers)&&void 0!==o?o:b)||void 0===s||s,strictTypes:null!==(i=null!==(a=e.strictTypes)&&void 0!==a?a:b)&&void 0!==i?i:"log",strictTuples:null!==(u=null!==(c=e.strictTuples)&&void 0!==c?c:b)&&void 0!==u?u:"log",strictRequired:null!==(l=null!==(d=e.strictRequired)&&void 0!==d?d:b)&&void 0!==l&&l,code:e.code?{...e.code,optimize:O}:{optimize:O},loopRequired:null!==(f=e.loopRequired)&&void 0!==f?f:200,loopEnum:null!==(p=e.loopEnum)&&void 0!==p?p:200,meta:null===(h=e.meta)||void 0===h||h,messages:null===(m=e.messages)||void 0===m||m,inlineRefs:null===(y=e.inlineRefs)||void 0===y||y,schemaId:null!==(v=e.schemaId)&&void 0!==v?v:"$id",addUsedSchema:null===(g=e.addUsedSchema)||void 0===g||g,validateSchema:null===(_=e.validateSchema)||void 0===_||_,validateFormats:null===(E=e.validateFormats)||void 0===E||E,unicodeRegExp:null===(w=e.unicodeRegExp)||void 0===w||w,int32range:null===($=e.int32range)||void 0===$||$}}class _{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,...g(e)};const{es5:t,lines:r}=this.opts.code;this.scope=new u.ValueScope({scope:{},prefixes:m,es5:t,lines:r}),this.logger=function(e){if(!1===e)return S;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}(e.logger);const n=e.validateFormats;e.validateFormats=!1,this.RULES=(0,i.getRules)(),E.call(this,y,e,"NOT SUPPORTED"),E.call(this,v,e,"DEPRECATED","warn"),this._metaOpts=O.call(this),e.formats&&b.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&T.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),$.call(this),e.validateFormats=n}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let n=p;"id"===r&&(n={...p},n.id=n.$id,delete n.$id),t&&e&&this.addMetaSchema(n,n[r],!1)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(r=this.getSchema(e),!r)throw new Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);const n=r(t);return"$async"in r||(this.errors=r.errors),n}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw new Error("options.loadSchema should be a function");const{loadSchema:r}=this.opts;return n.call(this,e,t);async function n(e,t){await o.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||s.call(this,r)}async function o(e){e&&!this.getSchema(e)&&await n.call(this,{$ref:e},!0)}async function s(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof a.default))throw t;return i.call(this,t),await c.call(this,t.missingSchema),s.call(this,e)}}function i({missingSchema:e,missingRef:t}){if(this.refs[e])throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function c(e){const r=await u.call(this,e);this.refs[e]||await o.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function u(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,n=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,void 0,r,n);return this}let o;if("object"==typeof e){const{schemaId:t}=this.opts;if(o=e[t],void 0!==o&&"string"!=typeof o)throw new Error(`schema ${t} must be string`)}return t=(0,d.normalizeId)(t||o),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,n,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){if("boolean"==typeof e)return!0;let r;if(r=e.$schema,void 0!==r&&"string"!=typeof r)throw new Error("$schema must be a string");if(r=r||this.opts.defaultMeta||this.defaultMeta(),!r)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const n=this.validate(r,e);if(!n&&t){const e="schema is invalid: "+this.errorsText();if("log"!==this.opts.validateSchema)throw new Error(e);this.logger.error(e)}return n}getSchema(e){let t;for(;"string"==typeof(t=w.call(this,e));)e=t;if(void 0===t){const{schemaId:r}=this.opts,n=new c.SchemaEnv({schema:{},schemaId:r});if(t=c.resolveSchema.call(this,n,e),!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const t=w.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{const t=e;this._cache.delete(t);let r=e[this.opts.schemaId];return r&&(r=(0,d.normalizeId)(r),delete this.schemas[r],delete this.refs[r]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else{if("object"!=typeof e||void 0!==t)throw new Error("invalid addKeywords parameters");if(r=(t=e).keyword,Array.isArray(r)&&!r.length)throw new Error("addKeywords: keyword must be string or non-empty array")}if(P.call(this,r,t),!t)return(0,f.eachItem)(r,(e=>A.call(this,e))),this;R.call(this,t);const n={...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)};return(0,f.eachItem)(r,0===n.type.length?e=>A.call(this,e,n):e=>n.type.forEach((t=>A.call(this,e,n,t)))),this}getKeyword(e){const t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e],delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex((t=>t.keyword===e));t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map((e=>`${r}${e.instancePath} ${e.message}`)).reduce(((e,r)=>e+t+r)):"No errors"}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const n of t){const t=n.split("/").slice(1);let o=e;for(const e of t)o=o[e];for(const e in r){const t=r[e];if("object"!=typeof t)continue;const{$data:n}=t.definition,s=o[e];n&&s&&(o[e]=j(s))}}return e}_removeAllSchemas(e,t){for(const r in e){const n=e[r];t&&!t.test(r)||("string"==typeof n?delete e[r]:n&&!n.meta&&(this._cache.delete(n.schema),delete e[r]))}}_addSchema(e,t,r,n=this.opts.validateSchema,o=this.opts.addUsedSchema){let s;const{schemaId:a}=this.opts;if("object"==typeof e)s=e[a];else{if(this.opts.jtd)throw new Error("schema must be object");if("boolean"!=typeof e)throw new Error("schema must be object or boolean")}let i=this._cache.get(e);if(void 0!==i)return i;r=(0,d.normalizeId)(s||r);const u=d.getSchemaRefs.call(this,e,r);return i=new c.SchemaEnv({schema:e,schemaId:a,meta:t,baseId:r,localRefs:u}),this._cache.set(i.schema,i),o&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=i),n&&this.validateSchema(e,!0),i}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw new Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):c.compileSchema.call(this,e),!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{c.compileSchema.call(this,e)}finally{this.opts=t}}}function E(e,t,r,n="error"){for(const o in e){const s=o;s in t&&this.logger[n](`${r}: option ${o}. ${e[s]}`)}}function w(e){return e=(0,d.normalizeId)(e),this.schemas[e]||this.refs[e]}function $(){const e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function b(){for(const e in this.opts.formats){const t=this.opts.formats[e];t&&this.addFormat(e,t)}}function T(e){if(Array.isArray(e))this.addVocabulary(e);else{this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}}function O(){const e={...this.opts};for(const t of h)delete e[t];return e}t.default=_,_.ValidationError=s.default,_.MissingRefError=a.default;const S={log(){},warn(){},error(){}};const N=/^[a-z_$][a-z0-9_$:-]*$/i;function P(e,t){const{RULES:r}=this;if((0,f.eachItem)(e,(e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!N.test(e))throw new Error(`Keyword ${e} has invalid name`)})),t&&t.$data&&!("code"in t)&&!("validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function A(e,t,r){var n;const o=null==t?void 0:t.post;if(r&&o)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:s}=this;let a=o?s.post:s.rules.find((({type:e})=>e===r));if(a||(a={type:r,rules:[]},s.rules.push(a)),s.keywords[e]=!0,!t)return;const i={keyword:e,definition:{...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)}};t.before?I.call(this,a,i,t.before):a.rules.push(i),s.all[e]=i,null===(n=t.implements)||void 0===n||n.forEach((e=>this.addKeyword(e)))}function I(e,t,r){const n=e.rules.findIndex((e=>e.keyword===r));n>=0?e.rules.splice(n,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function R(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=j(t)),e.validateSchema=this.compile(t,!0))}const C={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function j(e){return{anyOf:[e,C]}}},3717:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9378);n.code='require("ajv/dist/runtime/equal").default',t.default=n},158:(e,t)=>{"use strict";function r(e){const t=e.length;let r,n=0,o=0;for(;o<t;)n++,r=e.charCodeAt(o++),r>=55296&&r<=56319&&o<t&&(r=e.charCodeAt(o),56320==(64512&r)&&o++);return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,r.code='require("ajv/dist/runtime/ucs2length").default'},2569:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}t.default=r},5971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;const n=r(9143),o=r(2493),s={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:n}=t;Array.isArray(n)?a(e,n):(0,o.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas')}};function a(e,t){const{gen:r,schema:s,data:a,keyword:i,it:c}=e;c.items=!0;const u=r.const("len",n._`${a}.length`);if(!1===s)e.setParams({len:t.length}),e.pass(n._`${u} <= ${t.length}`);else if("object"==typeof s&&!(0,o.alwaysValidSchema)(c,s)){const s=r.var("valid",n._`${u} <= ${t.length}`);r.if((0,n.not)(s),(()=>function(s){r.forRange("i",t.length,u,(t=>{e.subschema({keyword:i,dataProp:t,dataPropType:o.Type.Num},s),c.allErrors||r.if((0,n.not)(s),(()=>r.break()))}))}(s))),e.ok(s)}}t.validateAdditionalItems=a,t.default=s},2012:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5255),o=r(9143),s=r(8460),a=r(2493),i={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>o._`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:i,data:c,errsCount:u,it:d}=e;if(!u)throw new Error("ajv implementation error");const{allErrors:l,opts:f}=d;if(d.props=!0,"all"!==f.removeAdditional&&(0,a.alwaysValidSchema)(d,r))return;const p=(0,n.allSchemaProperties)(i.properties),h=(0,n.allSchemaProperties)(i.patternProperties);function m(e){t.code(o._`delete ${c}[${e}]`)}function y(n){if("all"===f.removeAdditional||f.removeAdditional&&!1===r)m(n);else{if(!1===r)return e.setParams({additionalProperty:n}),e.error(),void(l||t.break());if("object"==typeof r&&!(0,a.alwaysValidSchema)(d,r)){const r=t.name("valid");"failing"===f.removeAdditional?(v(n,r,!1),t.if((0,o.not)(r),(()=>{e.reset(),m(n)}))):(v(n,r),l||t.if((0,o.not)(r),(()=>t.break())))}}}function v(t,r,n){const o={keyword:"additionalProperties",dataProp:t,dataPropType:a.Type.Str};!1===n&&Object.assign(o,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(o,r)}t.forIn("key",c,(r=>{p.length||h.length?t.if(function(r){let s;if(p.length>8){const e=(0,a.schemaRefOrVal)(d,i.properties,"properties");s=(0,n.isOwnProperty)(t,e,r)}else s=p.length?(0,o.or)(...p.map((e=>o._`${r} === ${e}`))):o.nil;return h.length&&(s=(0,o.or)(s,...h.map((t=>o._`${(0,n.usePattern)(e,t)}.test(${r})`)))),(0,o.not)(s)}(r),(()=>y(r))):y(r)})),e.ok(o._`${u} === ${s.default.errors}`)}};t.default=i},7206:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(2493),o={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:o}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const s=t.name("valid");r.forEach(((t,r)=>{if((0,n.alwaysValidSchema)(o,t))return;const a=e.subschema({keyword:"allOf",schemaProp:r},s);e.ok(s),e.mergeEvaluated(a)}))}};t.default=o},569:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:r(5255).validateUnion,error:{message:"must match a schema in anyOf"}};t.default=n},2063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?n.str`must contain at least ${e} valid item(s)`:n.str`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?n._`{minContains: ${e}}`:n._`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:s,data:a,it:i}=e;let c,u;const{minContains:d,maxContains:l}=s;i.opts.next?(c=void 0===d?1:d,u=l):c=1;const f=t.const("len",n._`${a}.length`);if(e.setParams({min:c,max:u}),void 0===u&&0===c)return void(0,o.checkStrictMode)(i,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==u&&c>u)return(0,o.checkStrictMode)(i,'"minContains" > "maxContains" is always invalid'),void e.fail();if((0,o.alwaysValidSchema)(i,r)){let t=n._`${f} >= ${c}`;return void 0!==u&&(t=n._`${t} && ${f} <= ${u}`),void e.pass(t)}i.items=!0;const p=t.name("valid");if(void 0===u&&1===c)h(p,(()=>t.if(p,(()=>t.break()))));else{t.let(p,!1);const e=t.name("_valid"),r=t.let("count",0);h(e,(()=>t.if(e,(()=>function(e){t.code(n._`${e}++`),void 0===u?t.if(n._`${e} >= ${c}`,(()=>t.assign(p,!0).break())):(t.if(n._`${e} > ${u}`,(()=>t.assign(p,!1).break())),1===c?t.assign(p,!0):t.if(n._`${e} >= ${c}`,(()=>t.assign(p,!0))))}(r)))))}function h(r,n){t.forRange("i",0,f,(t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:o.Type.Num,compositeRule:!0},r),n()}))}e.result(p,(()=>e.reset()))}};t.default=s},337:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;const n=r(9143),o=r(2493),s=r(5255);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{const o=1===t?"property":"properties";return n.str`must have ${o} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:o}})=>n._`{property: ${e},
    missingProperty: ${o},
    depsCount: ${t},
    deps: ${r}}`};const a={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=function({schema:e}){const t={},r={};for(const n in e){if("__proto__"===n)continue;(Array.isArray(e[n])?t:r)[n]=e[n]}return[t,r]}(e);i(e,t),c(e,r)}};function i(e,t=e.schema){const{gen:r,data:o,it:a}=e;if(0===Object.keys(t).length)return;const i=r.let("missing");for(const c in t){const u=t[c];if(0===u.length)continue;const d=(0,s.propertyInData)(r,o,c,a.opts.ownProperties);e.setParams({property:c,depsCount:u.length,deps:u.join(", ")}),a.allErrors?r.if(d,(()=>{for(const t of u)(0,s.checkReportMissingProp)(e,t)})):(r.if(n._`${d} && (${(0,s.checkMissingProp)(e,u,i)})`),(0,s.reportMissingProp)(e,i),r.else())}}function c(e,t=e.schema){const{gen:r,data:n,keyword:a,it:i}=e,c=r.name("valid");for(const u in t)(0,o.alwaysValidSchema)(i,t[u])||(r.if((0,s.propertyInData)(r,n,u,i.opts.ownProperties),(()=>{const t=e.subschema({keyword:a,schemaProp:u},c);e.mergeValidEvaluated(t,c)}),(()=>r.var(c,!0))),e.ok(c))}t.validatePropertyDeps=i,t.validateSchemaDeps=c,t.default=a},599:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>n.str`must match "${e.ifClause}" schema`,params:({params:e})=>n._`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:s}=e;void 0===r.then&&void 0===r.else&&(0,o.checkStrictMode)(s,'"if" without "then" and "else" is ignored');const i=a(s,"then"),c=a(s,"else");if(!i&&!c)return;const u=t.let("valid",!0),d=t.name("_valid");if(function(){const t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},d);e.mergeEvaluated(t)}(),e.reset(),i&&c){const r=t.let("ifClause");e.setParams({ifClause:r}),t.if(d,l("then",r),l("else",r))}else i?t.if(d,l("then")):t.if((0,n.not)(d),l("else"));function l(r,o){return()=>{const s=e.subschema({keyword:r},d);t.assign(u,d),e.mergeValidEvaluated(s,u),o?t.assign(o,n._`${r}`):e.setParams({ifClause:r})}}e.pass(u,(()=>e.error(!0)))}};function a(e,t){const r=e.schema[t];return void 0!==r&&!(0,o.alwaysValidSchema)(e,r)}t.default=s},5068:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5971),o=r(2340),s=r(8606),a=r(7287),i=r(2063),c=r(337),u=r(7884),d=r(2012),l=r(4280),f=r(5295),p=r(3015),h=r(569),m=r(2336),y=r(7206),v=r(599),g=r(1277);t.default=function(e=!1){const t=[p.default,h.default,m.default,y.default,v.default,g.default,u.default,d.default,c.default,l.default,f.default];return e?t.push(o.default,a.default):t.push(n.default,s.default),t.push(i.default),t}},8606:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;const n=r(9143),o=r(2493),s=r(5255),a={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return i(e,"additionalItems",t);r.items=!0,(0,o.alwaysValidSchema)(r,t)||e.ok((0,s.validateArray)(e))}};function i(e,t,r=e.schema){const{gen:s,parentSchema:a,data:i,keyword:c,it:u}=e;!function(e){const{opts:n,errSchemaPath:s}=u,a=r.length,i=a===e.minItems&&(a===e.maxItems||!1===e[t]);if(n.strictTuples&&!i){const e=`"${c}" is ${a}-tuple, but minItems or maxItems/${t} are not specified or different at path "${s}"`;(0,o.checkStrictMode)(u,e,n.strictTuples)}}(a),u.opts.unevaluated&&r.length&&!0!==u.items&&(u.items=o.mergeEvaluated.items(s,r.length,u.items));const d=s.name("valid"),l=s.const("len",n._`${i}.length`);r.forEach(((t,r)=>{(0,o.alwaysValidSchema)(u,t)||(s.if(n._`${l} > ${r}`,(()=>e.subschema({keyword:c,schemaProp:r,dataProp:r},d))),e.ok(d))}))}t.validateTuple=i,t.default=a},7287:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s=r(5255),a=r(5971),i={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:n}=e,{prefixItems:i}=r;n.items=!0,(0,o.alwaysValidSchema)(n,t)||(i?(0,a.validateAdditionalItems)(e,i):e.ok((0,s.validateArray)(e)))}};t.default=i},3015:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(2493),o={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:o}=e;if((0,n.alwaysValidSchema)(o,r))return void e.fail();const s=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},s),e.failResult(s,(()=>e.reset()),(()=>e.error()))},error:{message:"must NOT be valid"}};t.default=o},2336:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>n._`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:s,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(a.opts.discriminator&&s.discriminator)return;const i=r,c=t.let("valid",!1),u=t.let("passing",null),d=t.name("_valid");e.setParams({passing:u}),t.block((function(){i.forEach(((r,s)=>{let i;(0,o.alwaysValidSchema)(a,r)?t.var(d,!0):i=e.subschema({keyword:"oneOf",schemaProp:s,compositeRule:!0},d),s>0&&t.if(n._`${d} && ${c}`).assign(c,!1).assign(u,n._`[${u}, ${s}]`).else(),t.if(d,(()=>{t.assign(c,!0),t.assign(u,s),i&&e.mergeEvaluated(i,n.Name)}))}))})),e.result(c,(()=>e.reset()),(()=>e.error(!0)))}};t.default=s},5295:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5255),o=r(9143),s=r(2493),a=r(2493),i={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:i,parentSchema:c,it:u}=e,{opts:d}=u,l=(0,n.allSchemaProperties)(r),f=l.filter((e=>(0,s.alwaysValidSchema)(u,r[e])));if(0===l.length||f.length===l.length&&(!u.opts.unevaluated||!0===u.props))return;const p=d.strictSchema&&!d.allowMatchingProperties&&c.properties,h=t.name("valid");!0===u.props||u.props instanceof o.Name||(u.props=(0,a.evaluatedPropsToName)(t,u.props));const{props:m}=u;function y(e){for(const t in p)new RegExp(e).test(t)&&(0,s.checkStrictMode)(u,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}function v(r){t.forIn("key",i,(s=>{t.if(o._`${(0,n.usePattern)(e,r)}.test(${s})`,(()=>{const n=f.includes(r);n||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:s,dataPropType:a.Type.Str},h),u.opts.unevaluated&&!0!==m?t.assign(o._`${m}[${s}]`,!0):n||u.allErrors||t.if((0,o.not)(h),(()=>t.break()))}))}))}!function(){for(const e of l)p&&y(e),u.allErrors?v(e):(t.var(h,!0),v(e),t.if(h))}()}};t.default=i},2340:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(8606),o={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,n.validateTuple)(e,"items")};t.default=o},4280:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5344),o=r(5255),s=r(2493),a=r(2012),i={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:i,data:c,it:u}=e;"all"===u.opts.removeAdditional&&void 0===i.additionalProperties&&a.default.code(new n.KeywordCxt(u,a.default,"additionalProperties"));const d=(0,o.allSchemaProperties)(r);for(const e of d)u.definedProperties.add(e);u.opts.unevaluated&&d.length&&!0!==u.props&&(u.props=s.mergeEvaluated.props(t,(0,s.toHash)(d),u.props));const l=d.filter((e=>!(0,s.alwaysValidSchema)(u,r[e])));if(0===l.length)return;const f=t.name("valid");for(const r of l)p(r)?h(r):(t.if((0,o.propertyInData)(t,c,r,u.opts.ownProperties)),h(r),u.allErrors||t.else().var(f,!0),t.endIf()),e.it.definedProperties.add(r),e.ok(f);function p(e){return u.opts.useDefaults&&!u.compositeRule&&void 0!==r[e].default}function h(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},f)}}};t.default=i},7884:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>n._`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:s,it:a}=e;if((0,o.alwaysValidSchema)(a,r))return;const i=t.name("valid");t.forIn("key",s,(r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},i),t.if((0,n.not)(i),(()=>{e.error(!0),a.allErrors||t.break()}))})),e.ok(i)}};t.default=s},1277:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(2493),o={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,n.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};t.default=o},5255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const n=r(9143),o=r(2493),s=r(8460);function a(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:n._`Object.prototype.hasOwnProperty`})}function i(e,t,r){return n._`${a(e)}.call(${t}, ${r})`}function c(e,t,r,o){const s=n._`${t}${(0,n.getProperty)(r)} === undefined`;return o?(0,n.or)(s,(0,n.not)(i(e,t,r))):s}function u(e){return e?Object.keys(e).filter((e=>"__proto__"!==e)):[]}t.checkReportMissingProp=function(e,t){const{gen:r,data:o,it:s}=e;r.if(c(r,o,t,s.opts.ownProperties),(()=>{e.setParams({missingProperty:n._`${t}`},!0),e.error()}))},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},o,s){return(0,n.or)(...o.map((o=>(0,n.and)(c(e,t,o,r.ownProperties),n._`${s} = ${o}`))))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=a,t.isOwnProperty=i,t.propertyInData=function(e,t,r,o){const s=n._`${t}${(0,n.getProperty)(r)} !== undefined`;return o?n._`${s} && ${i(e,t,r)}`:s},t.noPropertyInData=c,t.allSchemaProperties=u,t.schemaProperties=function(e,t){return u(t).filter((r=>!(0,o.alwaysValidSchema)(e,t[r])))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:o,schemaPath:a,errorPath:i},it:c},u,d,l){const f=l?n._`${e}, ${t}, ${o}${a}`:t,p=[[s.default.instancePath,(0,n.strConcat)(s.default.instancePath,i)],[s.default.parentData,c.parentData],[s.default.parentDataProperty,c.parentDataProperty],[s.default.rootData,s.default.rootData]];c.opts.dynamicRef&&p.push([s.default.dynamicAnchors,s.default.dynamicAnchors]);const h=n._`${f}, ${r.object(...p)}`;return d!==n.nil?n._`${u}.call(${d}, ${h})`:n._`${u}(${h})`},t.usePattern=function({gen:e,it:{opts:t}},r){const o=t.unicodeRegExp?"u":"";return e.scopeValue("pattern",{key:r,ref:new RegExp(r,o),code:n._`new RegExp(${r}, ${o})`})},t.validateArray=function(e){const{gen:t,data:r,keyword:s,it:a}=e,i=t.name("valid");if(a.allErrors){const e=t.let("valid",!0);return c((()=>t.assign(e,!1))),e}return t.var(i,!0),c((()=>t.break())),i;function c(a){const c=t.const("len",n._`${r}.length`);t.forRange("i",0,c,(r=>{e.subschema({keyword:s,dataProp:r,dataPropType:o.Type.Num},i),t.if((0,n.not)(i),a)}))}},t.validateUnion=function(e){const{gen:t,schema:r,keyword:s,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some((e=>(0,o.alwaysValidSchema)(a,e)))&&!a.opts.unevaluated)return;const i=t.let("valid",!1),c=t.name("_valid");t.block((()=>r.forEach(((r,o)=>{const a=e.subschema({keyword:s,schemaProp:o,compositeRule:!0},c);t.assign(i,n._`${i} || ${c}`);e.mergeValidEvaluated(a,c)||t.if((0,n.not)(i))})))),e.result(i,(()=>e.reset()),(()=>e.error(!0)))}},4404:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};t.default=r},4031:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(4404),o=r(8126),s=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",n.default,o.default];t.default=s},8126:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;const n=r(2589),o=r(5255),s=r(9143),a=r(8460),i=r(4335),c=r(2493),u={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:o}=e,{baseId:a,schemaEnv:c,validateName:u,opts:f,self:p}=o,{root:h}=c;if(("#"===r||"#/"===r)&&a===h.baseId)return function(){if(c===h)return l(e,u,c,c.$async);const r=t.scopeValue("root",{ref:h});return l(e,s._`${r}.validate`,h,h.$async)}();const m=i.resolveRef.call(p,h,a,r);if(void 0===m)throw new n.default(a,r);return m instanceof i.SchemaEnv?function(t){const r=d(e,t);l(e,r,t,t.$async)}(m):function(n){const o=t.scopeValue("schema",!0===f.code.source?{ref:n,code:(0,s.stringify)(n)}:{ref:n}),a=t.name("valid"),i=e.subschema({schema:n,dataTypes:[],schemaPath:s.nil,topSchemaRef:o,errSchemaPath:r},a);e.mergeEvaluated(i),e.ok(a)}(m)}};function d(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):s._`${r.scopeValue("wrapper",{ref:t})}.validate`}function l(e,t,r,n){const{gen:i,it:u}=e,{allErrors:d,schemaEnv:l,opts:f}=u,p=f.passContext?a.default.this:s.nil;function h(e){const t=s._`${e}.errors`;i.assign(a.default.vErrors,s._`${a.default.vErrors} === null ? ${t} : ${a.default.vErrors}.concat(${t})`),i.assign(a.default.errors,s._`${a.default.vErrors}.length`)}function m(e){var t;if(!u.opts.unevaluated)return;const n=null===(t=null==r?void 0:r.validate)||void 0===t?void 0:t.evaluated;if(!0!==u.props)if(n&&!n.dynamicProps)void 0!==n.props&&(u.props=c.mergeEvaluated.props(i,n.props,u.props));else{const t=i.var("props",s._`${e}.evaluated.props`);u.props=c.mergeEvaluated.props(i,t,u.props,s.Name)}if(!0!==u.items)if(n&&!n.dynamicItems)void 0!==n.items&&(u.items=c.mergeEvaluated.items(i,n.items,u.items));else{const t=i.var("items",s._`${e}.evaluated.items`);u.items=c.mergeEvaluated.items(i,t,u.items,s.Name)}}n?function(){if(!l.$async)throw new Error("async schema referenced by sync schema");const r=i.let("valid");i.try((()=>{i.code(s._`await ${(0,o.callValidateCode)(e,t,p)}`),m(t),d||i.assign(r,!0)}),(e=>{i.if(s._`!(${e} instanceof ${u.ValidationError})`,(()=>i.throw(e))),h(e),d||i.assign(r,!1)})),e.ok(r)}():e.result((0,o.callValidateCode)(e,t,p),(()=>m(t)),(()=>h(t)))}t.getValidate=d,t.callRef=l,t.default=u},242:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(448),s={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===o.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>n._`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:s,parentSchema:a,it:i}=e,{oneOf:c}=a;if(!i.opts.discriminator)throw new Error("discriminator: requires discriminator option");const u=s.propertyName;if("string"!=typeof u)throw new Error("discriminator: requires propertyName");if(s.mapping)throw new Error("discriminator: mapping is not supported");if(!c)throw new Error("discriminator: requires oneOf keyword");const d=t.let("valid",!1),l=t.const("tag",n._`${r}${(0,n.getProperty)(u)}`);function f(r){const o=t.name("valid"),s=e.subschema({keyword:"oneOf",schemaProp:r},o);return e.mergeEvaluated(s,n.Name),o}t.if(n._`typeof ${l} == "string"`,(()=>function(){const r=function(){var e;const t={},r=o(a);let n=!0;for(let t=0;t<c.length;t++){const a=c[t],i=null===(e=a.properties)||void 0===e?void 0:e[u];if("object"!=typeof i)throw new Error(`discriminator: oneOf schemas must have "properties/${u}"`);n=n&&(r||o(a)),s(i,t)}if(!n)throw new Error(`discriminator: "${u}" must be required`);return t;function o({required:e}){return Array.isArray(e)&&e.includes(u)}function s(e,t){if(e.const)i(e.const,t);else{if(!e.enum)throw new Error(`discriminator: "properties/${u}" must have "const" or "enum"`);for(const r of e.enum)i(r,t)}}function i(e,r){if("string"!=typeof e||e in t)throw new Error(`discriminator: "${u}" values must be unique strings`);t[e]=r}}();t.if(!1);for(const e in r)t.elseIf(n._`${l} === ${e}`),t.assign(d,f(r[e]));t.else(),e.error(!1,{discrError:o.DiscrError.Mapping,tag:l,tagName:u}),t.endIf()}()),(()=>e.error(!1,{discrError:o.DiscrError.Tag,tag:l,tagName:u}))),e.ok(d)}};t.default=s},448:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,function(e){e.Tag="tag",e.Mapping="mapping"}(t.DiscrError||(t.DiscrError={}))},7578:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(4031),o=r(9013),s=r(5068),a=r(3610),i=r(508),c=[n.default,o.default,(0,s.default)(),a.default,i.metadataVocabulary,i.contentVocabulary];t.default=c},3095:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>n.str`must match format "${e}"`,params:({schemaCode:e})=>n._`{format: ${e}}`},code(e,t){const{gen:r,data:o,$data:s,schema:a,schemaCode:i,it:c}=e,{opts:u,errSchemaPath:d,schemaEnv:l,self:f}=c;u.validateFormats&&(s?function(){const s=r.scopeValue("formats",{ref:f.formats,code:u.code.formats}),a=r.const("fDef",n._`${s}[${i}]`),c=r.let("fType"),d=r.let("format");r.if(n._`typeof ${a} == "object" && !(${a} instanceof RegExp)`,(()=>r.assign(c,n._`${a}.type || "string"`).assign(d,n._`${a}.validate`)),(()=>r.assign(c,n._`"string"`).assign(d,a))),e.fail$data((0,n.or)(!1===u.strictSchema?n.nil:n._`${i} && !${d}`,function(){const e=l.$async?n._`(${a}.async ? await ${d}(${o}) : ${d}(${o}))`:n._`${d}(${o})`,r=n._`(typeof ${d} == "function" ? ${e} : ${d}.test(${o}))`;return n._`${d} && ${d} !== true && ${c} === ${t} && !${r}`}()))}():function(){const s=f.formats[a];if(!s)return void function(){if(!1===u.strictSchema)return void f.logger.warn(e());throw new Error(e());function e(){return`unknown format "${a}" ignored in schema at path "${d}"`}}();if(!0===s)return;const[i,c,p]=function(e){const t=e instanceof RegExp?(0,n.regexpCode)(e):u.code.formats?n._`${u.code.formats}${(0,n.getProperty)(a)}`:void 0,o=r.scopeValue("formats",{key:a,ref:e,code:t});if("object"==typeof e&&!(e instanceof RegExp))return[e.type||"string",e.validate,n._`${o}.validate`];return["string",e,o]}(s);i===t&&e.pass(function(){if("object"==typeof s&&!(s instanceof RegExp)&&s.async){if(!l.$async)throw new Error("async format in sync schema");return n._`await ${p}(${o})`}return"function"==typeof c?n._`${p}(${o})`:n._`${p}.test(${o})`}())}())}};t.default=o},3610:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=[r(3095).default];t.default=n},508:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},5684:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s=r(3717),a={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>n._`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:a,schemaCode:i,schema:c}=e;a||c&&"object"==typeof c?e.fail$data(n._`!${(0,o.useFunc)(t,s.default)}(${r}, ${i})`):e.fail(n._`${c} !== ${r}`)}};t.default=a},3216:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s=r(3717),a={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>n._`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:a,schema:i,schemaCode:c,it:u}=e;if(!a&&0===i.length)throw new Error("enum must have non-empty array");const d=i.length>=u.opts.loopEnum,l=(0,o.useFunc)(t,s.default);let f;if(d||a)f=t.let("valid"),e.block$data(f,(function(){t.assign(f,!1),t.forOf("v",c,(e=>t.if(n._`${l}(${r}, ${e})`,(()=>t.assign(f,!0).break()))))}));else{if(!Array.isArray(i))throw new Error("ajv implementation error");const e=t.const("vSchema",c);f=(0,n.or)(...i.map(((t,o)=>function(e,t){const o=i[t];return"object"==typeof o&&null!==o?n._`${l}(${r}, ${e}[${t}])`:n._`${r} === ${o}`}(e,o))))}e.pass(f)}};t.default=a},9013:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(167),o=r(7935),s=r(905),a=r(7103),i=r(2267),c=r(4022),u=r(2999),d=r(8906),l=r(5684),f=r(3216),p=[n.default,o.default,s.default,a.default,i.default,c.default,u.default,d.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},l.default,f.default];t.default=p},2999:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxItems"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:o}=e,s="maxItems"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`${r}.length ${s} ${o}`)}};t.default=o},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=r(2493),s=r(158),a={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxLength"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:a,it:i}=e,c="maxLength"===t?n.operators.GT:n.operators.LT,u=!1===i.opts.unicode?n._`${r}.length`:n._`${(0,o.useFunc)(e.gen,s.default)}(${r})`;e.fail$data(n._`${u} ${c} ${a}`)}};t.default=a},167:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o=n.operators,s={maximum:{okStr:"<=",ok:o.LTE,fail:o.GT},minimum:{okStr:">=",ok:o.GTE,fail:o.LT},exclusiveMaximum:{okStr:"<",ok:o.LT,fail:o.GTE},exclusiveMinimum:{okStr:">",ok:o.GT,fail:o.LTE}},a={message:({keyword:e,schemaCode:t})=>n.str`must be ${s[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>n._`{comparison: ${s[e].okStr}, limit: ${t}}`},i={keyword:Object.keys(s),type:"number",schemaType:"number",$data:!0,error:a,code(e){const{keyword:t,data:r,schemaCode:o}=e;e.fail$data(n._`${r} ${s[t].fail} ${o} || isNaN(${r})`)}};t.default=i},2267:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxProperties"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:o}=e,s="maxProperties"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`Object.keys(${r}).length ${s} ${o}`)}};t.default=o},7935:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9143),o={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>n.str`must be multiple of ${e}`,params:({schemaCode:e})=>n._`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:o,it:s}=e,a=s.opts.multipleOfPrecision,i=t.let("res"),c=a?n._`Math.abs(Math.round(${i}) - ${i}) > 1e-${a}`:n._`${i} !== parseInt(${i})`;e.fail$data(n._`(${o} === 0 || (${i} = ${r}/${o}, ${c}))`)}};t.default=o},7103:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5255),o=r(9143),s={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>o.str`must match pattern "${e}"`,params:({schemaCode:e})=>o._`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:s,schemaCode:a,it:i}=e,c=i.opts.unicodeRegExp?"u":"",u=r?o._`(new RegExp(${a}, ${c}))`:(0,n.usePattern)(e,s);e.fail$data(o._`!${u}.test(${t})`)}};t.default=s},4022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5255),o=r(9143),s=r(2493),a={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>o.str`must have required property '${e}'`,params:({params:{missingProperty:e}})=>o._`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:a,data:i,$data:c,it:u}=e,{opts:d}=u;if(!c&&0===r.length)return;const l=r.length>=d.loopRequired;if(u.allErrors?function(){if(l||c)e.block$data(o.nil,f);else for(const t of r)(0,n.checkReportMissingProp)(e,t)}():function(){const s=t.let("missing");if(l||c){const r=t.let("valid",!0);e.block$data(r,(()=>function(r,s){e.setParams({missingProperty:r}),t.forOf(r,a,(()=>{t.assign(s,(0,n.propertyInData)(t,i,r,d.ownProperties)),t.if((0,o.not)(s),(()=>{e.error(),t.break()}))}),o.nil)}(s,r))),e.ok(r)}else t.if((0,n.checkMissingProp)(e,r,s)),(0,n.reportMissingProp)(e,s),t.else()}(),d.strictRequired){const t=e.parentSchema.properties,{definedProperties:n}=e.it;for(const e of r)if(void 0===(null==t?void 0:t[e])&&!n.has(e)){const t=`required property "${e}" is not defined at "${u.schemaEnv.baseId+u.errSchemaPath}" (strictRequired)`;(0,s.checkStrictMode)(u,t,u.opts.strictRequired)}}function f(){t.forOf("prop",a,(r=>{e.setParams({missingProperty:r}),t.if((0,n.noPropertyInData)(t,i,r,d.ownProperties),(()=>e.error()))}))}}};t.default=a},8906:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(4327),o=r(9143),s=r(2493),a=r(3717),i={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>o.str`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>o._`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:i,schema:c,parentSchema:u,schemaCode:d,it:l}=e;if(!i&&!c)return;const f=t.let("valid"),p=u.items?(0,n.getSchemaTypes)(u.items):[];function h(s,a){const i=t.name("item"),c=(0,n.checkDataTypes)(p,i,l.opts.strictNumbers,n.DataType.Wrong),u=t.const("indices",o._`{}`);t.for(o._`;${s}--;`,(()=>{t.let(i,o._`${r}[${s}]`),t.if(c,o._`continue`),p.length>1&&t.if(o._`typeof ${i} == "string"`,o._`${i} += "_"`),t.if(o._`typeof ${u}[${i}] == "number"`,(()=>{t.assign(a,o._`${u}[${i}]`),e.error(),t.assign(f,!1).break()})).code(o._`${u}[${i}] = ${s}`)}))}function m(n,i){const c=(0,s.useFunc)(t,a.default),u=t.name("outer");t.label(u).for(o._`;${n}--;`,(()=>t.for(o._`${i} = ${n}; ${i}--;`,(()=>t.if(o._`${c}(${r}[${n}], ${r}[${i}])`,(()=>{e.error(),t.assign(f,!1).break(u)}))))))}e.block$data(f,(function(){const n=t.let("i",o._`${r}.length`),s=t.let("j");e.setParams({i:n,j:s}),t.assign(f,!0),t.if(o._`${n} > 1`,(()=>(p.length>0&&!p.some((e=>"object"===e||"array"===e))?h:m)(n,s)))}),o._`${d} === false`),e.ok(f)}};t.default=i},3033:function(e,t){var r="undefined"!=typeof self?self:this,n=function(){function e(){this.fetch=!1,this.DOMException=r.DOMException}return e.prototype=r,new e}();!function(e){!function(t){var r="URLSearchParams"in e,n="Symbol"in e&&"iterator"in Symbol,o="FileReader"in e&&"Blob"in e&&function(){try{return new Blob,!0}catch(e){return!1}}(),s="FormData"in e,a="ArrayBuffer"in e;if(a)var i=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],c=ArrayBuffer.isView||function(e){return e&&i.indexOf(Object.prototype.toString.call(e))>-1};function u(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(e))throw new TypeError("Invalid character in header field name");return e.toLowerCase()}function d(e){return"string"!=typeof e&&(e=String(e)),e}function l(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return n&&(t[Symbol.iterator]=function(){return t}),t}function f(e){this.map={},e instanceof f?e.forEach((function(e,t){this.append(t,e)}),this):Array.isArray(e)?e.forEach((function(e){this.append(e[0],e[1])}),this):e&&Object.getOwnPropertyNames(e).forEach((function(t){this.append(t,e[t])}),this)}function p(e){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}function h(e){return new Promise((function(t,r){e.onload=function(){t(e.result)},e.onerror=function(){r(e.error)}}))}function m(e){var t=new FileReader,r=h(t);return t.readAsArrayBuffer(e),r}function y(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function v(){return this.bodyUsed=!1,this._initBody=function(e){var t;this._bodyInit=e,e?"string"==typeof e?this._bodyText=e:o&&Blob.prototype.isPrototypeOf(e)?this._bodyBlob=e:s&&FormData.prototype.isPrototypeOf(e)?this._bodyFormData=e:r&&URLSearchParams.prototype.isPrototypeOf(e)?this._bodyText=e.toString():a&&o&&((t=e)&&DataView.prototype.isPrototypeOf(t))?(this._bodyArrayBuffer=y(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a&&(ArrayBuffer.prototype.isPrototypeOf(e)||c(e))?this._bodyArrayBuffer=y(e):this._bodyText=e=Object.prototype.toString.call(e):this._bodyText="",this.headers.get("content-type")||("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):r&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},o&&(this.blob=function(){var e=p(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?p(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(m)}),this.text=function(){var e,t,r,n=p(this);if(n)return n;if(this._bodyBlob)return e=this._bodyBlob,t=new FileReader,r=h(t),t.readAsText(e),r;if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),r=new Array(t.length),n=0;n<t.length;n++)r[n]=String.fromCharCode(t[n]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},s&&(this.formData=function(){return this.text().then(E)}),this.json=function(){return this.text().then(JSON.parse)},this}f.prototype.append=function(e,t){e=u(e),t=d(t);var r=this.map[e];this.map[e]=r?r+", "+t:t},f.prototype.delete=function(e){delete this.map[u(e)]},f.prototype.get=function(e){return e=u(e),this.has(e)?this.map[e]:null},f.prototype.has=function(e){return this.map.hasOwnProperty(u(e))},f.prototype.set=function(e,t){this.map[u(e)]=d(t)},f.prototype.forEach=function(e,t){for(var r in this.map)this.map.hasOwnProperty(r)&&e.call(t,this.map[r],r,this)},f.prototype.keys=function(){var e=[];return this.forEach((function(t,r){e.push(r)})),l(e)},f.prototype.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),l(e)},f.prototype.entries=function(){var e=[];return this.forEach((function(t,r){e.push([r,t])})),l(e)},n&&(f.prototype[Symbol.iterator]=f.prototype.entries);var g=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function _(e,t){var r,n,o=(t=t||{}).body;if(e instanceof _){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new f(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,o||null==e._bodyInit||(o=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",!t.headers&&this.headers||(this.headers=new f(t.headers)),this.method=(r=t.method||this.method||"GET",n=r.toUpperCase(),g.indexOf(n)>-1?n:r),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&o)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(o)}function E(e){var t=new FormData;return e.trim().split("&").forEach((function(e){if(e){var r=e.split("="),n=r.shift().replace(/\+/g," "),o=r.join("=").replace(/\+/g," ");t.append(decodeURIComponent(n),decodeURIComponent(o))}})),t}function w(e,t){t||(t={}),this.type="default",this.status=void 0===t.status?200:t.status,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in t?t.statusText:"OK",this.headers=new f(t.headers),this.url=t.url||"",this._initBody(e)}_.prototype.clone=function(){return new _(this,{body:this._bodyInit})},v.call(_.prototype),v.call(w.prototype),w.prototype.clone=function(){return new w(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new f(this.headers),url:this.url})},w.error=function(){var e=new w(null,{status:0,statusText:""});return e.type="error",e};var $=[301,302,303,307,308];w.redirect=function(e,t){if(-1===$.indexOf(t))throw new RangeError("Invalid status code");return new w(null,{status:t,headers:{location:e}})},t.DOMException=e.DOMException;try{new t.DOMException}catch(e){t.DOMException=function(e,t){this.message=e,this.name=t;var r=Error(e);this.stack=r.stack},t.DOMException.prototype=Object.create(Error.prototype),t.DOMException.prototype.constructor=t.DOMException}function b(e,r){return new Promise((function(n,s){var a=new _(e,r);if(a.signal&&a.signal.aborted)return s(new t.DOMException("Aborted","AbortError"));var i=new XMLHttpRequest;function c(){i.abort()}i.onload=function(){var e,t,r={status:i.status,statusText:i.statusText,headers:(e=i.getAllResponseHeaders()||"",t=new f,e.replace(/\r?\n[\t ]+/g," ").split(/\r?\n/).forEach((function(e){var r=e.split(":"),n=r.shift().trim();if(n){var o=r.join(":").trim();t.append(n,o)}})),t)};r.url="responseURL"in i?i.responseURL:r.headers.get("X-Request-URL");var o="response"in i?i.response:i.responseText;n(new w(o,r))},i.onerror=function(){s(new TypeError("Network request failed"))},i.ontimeout=function(){s(new TypeError("Network request failed"))},i.onabort=function(){s(new t.DOMException("Aborted","AbortError"))},i.open(a.method,a.url,!0),"include"===a.credentials?i.withCredentials=!0:"omit"===a.credentials&&(i.withCredentials=!1),"responseType"in i&&o&&(i.responseType="blob"),a.headers.forEach((function(e,t){i.setRequestHeader(t,e)})),a.signal&&(a.signal.addEventListener("abort",c),i.onreadystatechange=function(){4===i.readyState&&a.signal.removeEventListener("abort",c)}),i.send(void 0===a._bodyInit?null:a._bodyInit)}))}b.polyfill=!0,e.fetch||(e.fetch=b,e.Headers=f,e.Request=_,e.Response=w),t.Headers=f,t.Request=_,t.Response=w,t.fetch=b,Object.defineProperty(t,"__esModule",{value:!0})}({})}(n),n.fetch.ponyfill=!0,delete n.fetch.polyfill;var o=n;(t=o.fetch).default=o.fetch,t.fetch=o.fetch,t.Headers=o.Headers,t.Request=o.Request,t.Response=o.Response,e.exports=t},1209:()=>{},2501:(e,t,r)=>{"use strict";function n(e,t){void 0===t&&(t=e.constructor);var r=Error.captureStackTrace;r&&r(e,t)}r.d(t,{CustomError:()=>a});var o,s=(o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},o(e,t)},function(e,t){function r(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),a=function(e){function t(t){var r,o,s,a=this.constructor,i=e.call(this,t)||this;return Object.defineProperty(i,"name",{value:a.name,enumerable:!1,configurable:!0}),r=i,o=a.prototype,(s=Object.setPrototypeOf)?s(r,o):r.__proto__=o,n(i),i}return s(t,e),t}(Error)},7814:e=>{"use strict";e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},2081:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')}},n={};function o(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}};return r[e].call(s.exports,s,s.exports,o),s.exports}o.m=r,o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.f={},o.e=e=>Promise.all(Object.keys(o.f).reduce(((t,r)=>(o.f[r](e,t),t)),[])),o.u=e=>e+"/431110629a9fe8297174.js",o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="Destination:",o.l=(r,n,s,a)=>{if(e[r])e[r].push(n);else{var i,c;if(void 0!==s)for(var u=document.getElementsByTagName("script"),d=0;d<u.length;d++){var l=u[d];if(l.getAttribute("src")==r||l.getAttribute("data-webpack")==t+s){i=l;break}}i||(c=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,o.nc&&i.setAttribute("nonce",o.nc),i.setAttribute("data-webpack",t+s),i.src=r),e[r]=[n];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach((e=>e(n))),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),c&&document.head.appendChild(i)}},o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;o.g.importScripts&&(e=o.g.location+"");var t=o.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var n=r.length-1;n>-1&&!e;)e=r[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),o.p=e+"../"})(),(()=>{var e={114:0};o.f.j=(t,r)=>{var n=o.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var s=new Promise(((r,o)=>n=e[t]=[r,o]));r.push(n[2]=s);var a=o.p+o.u(t),i=new Error;o.l(a,(r=>{if(o.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var s=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+s+": "+a+")",i.name="ChunkLoadError",i.type=s,i.request=a,n[1](i)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,s,[a,i,c]=r,u=0;if(a.some((t=>0!==e[t]))){for(n in i)o.o(i,n)&&(o.m[n]=i[n]);if(c)c(o)}for(t&&t(r);u<a.length;u++)s=a[u],o.o(e,s)&&e[s]&&e[s][0](),e[s]=0},r=self.webpackChunkDestination=self.webpackChunkDestination||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var s={};(()=>{"use strict";o.d(s,{default:()=>oe,S:()=>ne});var e,t,r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.wnd,r=void 0===t?window:t;!function(e,t,r,n,o,s,a,i){var c,u,d=function(t){return!(t in e)||(e.console&&e.console.log&&e.console.log('FullStory namespace conflict. Please set window["_fs_namespace"].'),!1)}(r);d&&(a=e[r]=function(){var e=function(e,r,n,o){function s(s,a){t(e,r,n,s,a,o)}o=o||2;var a,i=/Async$/;return i.test(e)?(e=e.replace(i,""),"function"==typeof Promise?new Promise(s):l(s)):t(e,r,n,a,a,o)};function t(t,r,n,o,s,a){return e._api?e._api(t,r,n,o,s,a):(e.q&&e.q.push([t,r,n,o,s,a]),null)}return e.q=[],e}(),i=function(e){function t(t){"function"==typeof t[4]&&t[4](new Error(e))}var r=a.q;if(r){for(var n=0;n<r.length;n++)t(r[n]);r.length=0,r.push=t}},function(){(s=t.createElement(n)).async=!0,s.crossOrigin="anonymous",s.src="https://"+o,s.onerror=function(){i("Error loading "+o)};var e=t.getElementsByTagName(n)[0];e.parentNode.insertBefore(s,e)}(),function(){function e(){}function t(e,t,r){a(e,t,r,1)}function r(e,r,n){t("setProperties",{type:e,properties:r},n)}function n(e,t){r("user",e,t)}function o(e,t,r){n({uid:e},r),t&&n(t,r)}a.identify=o,a.setUserVars=n,a.identifyAccount=e,a.clearUserCookie=e,a.setVars=r,a.event=function(e,r,n){t("trackEvent",{name:e,properties:r},n)},a.anonymize=function(){o(!1)},a.shutdown=function(){t("shutdown")},a.restart=function(){t("restart")},a.log=function(e,r){t("log",{level:e,msg:r})},a.consent=function(e){t("setIdentity",{consent:!arguments.length||e})}}(),c="fetch",u="XMLHttpRequest",a._w={},a._w[u]=e[u],a._w[c]=e[c],e[c]&&(e[c]=function(){return a._w[c].apply(this,arguments)}),a._v="2.0.0");function l(e){var t,r=[];function n(){t&&(r.forEach((function(e){var r;try{r=e[t[0]]&&e[t[0]](t[1])}catch(t){return void(e[3]&&e[3](t))}r&&r.then?r.then(e[2],e[3]):e[2]&&e[2](r)})),r.length=0)}function o(e){return function(r){t||(t=[e,r],n())}}return e(o(0),o(1)),{then:function(e,t){return l((function(o,s){r.push([e,t,o,s]),n()}))}}}}(r,r.document,r._fs_namespace,"script",r._fs_script)},n=function(e){return"window"===e?"document":"".concat(e,".document")},a=(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.shouldInsertScript,r=void 0===t||t,o=e.globalVar,s=void 0===o?"window":o,a=e.apiVersion,i=void 0===a?"1.3.0":a;"(function(m,n,e,t,l,o,g,y){\n    if (e in m) {if(m.console && m.console.log) { m.console.log('FullStory namespace conflict. Please set window[\"_fs_namespace\"].');} return;}\n    g=m[e]=function(a,b,s){g.q?g.q.push([a,b,s]):g._api(a,b,s);};g.q=[];".concat(r?"\n    o=n.createElement(t);o.async=1;o.crossOrigin='anonymous';o.src='https://'+_fs_script;\n    y=n.getElementsByTagName(t)[0];y.parentNode.insertBefore(o,y);":"","\n    g.identify=function(i,v,s){g(l,{uid:i},s);if(v)g(l,v,s)};g.setUserVars=function(v,s){g(l,v,s)};g.event=function(i,v,s){g('event',{n:i,p:v},s)};\n    g.anonymize=function(){g.identify(!!0)};\n    g.shutdown=function(){g(\"rec\",!1)};g.restart=function(){g(\"rec\",!0)};\n    g.log = function(a,b){g(\"log\",[a,b])};\n    g.consent=function(a){g(\"consent\",!arguments.length||a)};\n    g.identifyAccount=function(i,v){o='account';v=v||{};v.acctId=i;g(o,v)};\n    g.clearUserCookie=function(){};\n    g.setVars=function(n, p){g('setVars',[n,p]);};\n    g._w={};y='XMLHttpRequest';g._w[y]=m[y];y='fetch';g._w[y]=m[y];\n    if(m[y])m[y]=function(){return g._w[y].apply(this,arguments)};\n    g._v=\"").concat(i,'";\n})(').concat(s,",").concat(n(s),",").concat(s,"['_fs_namespace'],'script','user');")}(),function(){return a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)}),i=function(){if(window._fs_namespace)return window[window._fs_namespace]},c=function(){var e=i();if(!e)throw Error("FullStory is not loaded, please ensure the init function is invoked before calling FullStory API functions");return e},u=function(e,t){var n=a({},e);if(i())console.warn("The FullStory snippet has already been defined elsewhere (likely in the <head> element)");else{n.recordCrossDomainIFrames&&(window._fs_run_in_iframe=!0),n.appHost&&(window._fs_app_host=n.appHost),n.assetMapId&&(window._fs_asset_map_id=n.assetMapId),n.startCaptureManually&&(window._fs_capture_on_startup=!1),n.recordOnlyThisIFrame&&(window._fs_is_outer_script=!0),n.cookieDomain&&(window._fs_cookie_domain=n.cookieDomain),!0===n.debug&&(n.script?console.warn("Ignoring `debug = true` because `script` is set"):n.script="edge.fullstory.com/s/fs-debug.js"),function(e){var t=e.orgId,n=e.namespace,o=void 0===n?"FS":n,s=e.host,a=void 0===s?"fullstory.com":s,i=e.script,c=void 0===i?"edge.fullstory.com/s/fs.js":i;if(!t)throw new Error("FullStory orgId is a required parameter");window._fs_host=a,window._fs_script=c,window._fs_org=t,window._fs_namespace=o,r()}(n);var o=i();if(o){if(t&&o("observe",{type:"start",callback:t}),!0===n.devMode){var s="FullStory was initialized in devMode and will stop recording";o("trackEvent",{name:"FullStory Dev Mode",properties:{message:s}}),o("shutdown"),window._fs_dev_mode=!0,console.warn(s)}}else console.warn("Failed to initialize FS snippet")}},d=(e="FullStory init has already been called once, additional invocations are ignored",function(t,r){window._fs_initialized?e&&console.warn(e):(u(t,r),window._fs_initialized=!0)}),l=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=c();return e.every((function(e){return r[e]}))},f=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(window._fs_dev_mode){var n="FullStory is in dev mode and is not recording: ".concat(e," method not executed");return console.warn(n),n}var o=i();return l(e)&&o?o[e].apply(o,t):(console.warn("FS.".concat(e," not ready")),null)}};(t=function(e,t,r){var n=c();if(!window._fs_dev_mode)return n(e,t,r);console.warn("FullStory is in dev mode and is not recording: method not executed")}).anonymize=f("anonymize"),t.consent=f("consent"),t.disableConsole=f("disableConsole"),t.enableConsole=f("enableConsole"),t.event=f("event"),t.getCurrentSessionURL=f("getCurrentSessionURL"),t.identify=f("identify"),t.log=f("log"),t.restart=f("restart"),t.setUserVars=f("setUserVars"),t.setVars=f("setVars"),t.shutdown=f("shutdown");const p={title:"Track Event",description:"Track events",platform:"web",defaultSubscription:'type = "track"',fields:{name:{description:"The name of the event.",label:"Name",required:!0,type:"string",default:{"@path":"$.event"}},properties:{description:"A JSON object containing additional information about the event that will be indexed by FullStory.",label:"Properties",required:!1,type:"object",default:{"@path":"$.properties"}}},perform:(e,t)=>{e.event(t.payload.name,t.payload.properties??{},ne)}},h=p,m={title:"Track Event V2",description:"Track events",platform:"web",defaultSubscription:'type = "track"',fields:{name:{description:"The name of the event.",label:"Name",required:!0,type:"string",default:{"@path":"$.event"}},properties:{description:"A JSON object containing additional information about the event that will be indexed by FullStory.",label:"Properties",required:!1,type:"object",default:{"@path":"$.properties"}}},perform:(e,t)=>{e("trackEvent",{name:t.payload.name,properties:t.payload.properties??{}},ne)}},y=m;var v=o(6009),g=o.n(v);const _={title:"Identify User",description:"Sets user identity variables",platform:"web",defaultSubscription:'type = "identify"',fields:{userId:{type:"string",required:!1,description:"The user's id",label:"User ID",default:{"@path":"$.userId"}},anonymousId:{type:"string",required:!1,description:"The user's anonymous id",label:"Anonymous ID",default:{"@path":"$.anonymousId"}},displayName:{type:"string",required:!1,description:"The user's display name",label:"Display Name",default:{"@path":"$.traits.name"}},email:{type:"string",required:!1,description:"The user's email",label:"Email",default:{"@path":"$.traits.email"}},traits:{type:"object",required:!1,description:"The Segment traits to be forwarded to FullStory",label:"Traits",default:{"@path":"$.traits"}}},perform:(e,t)=>{let r={};t.payload.traits&&(r=Object.entries(t.payload.traits).reduce(((e,[t,r])=>({...e,[E(t)]:r})),{})),t.payload.anonymousId&&(r.segmentAnonymousId_str=t.payload.anonymousId);const n={...r,...void 0!==t.payload.email&&{email:t.payload.email},...void 0!==t.payload.displayName&&{displayName:t.payload.displayName}};t.payload.userId?e.identify(t.payload.userId,n,ne):e.setUserVars(n,ne)}};function E(e){const t=e.split("_");if(t.length>1){const e=t.pop();switch(e){case"str":case"int":case"date":case"real":case"bool":case"strs":case"ints":case"dates":case"reals":case"bools":return g()(t.join("_"))+"_"+e}}return g()(e)}const w=_,$={title:"Identify User V2",description:"Sets user identity properties",platform:"web",defaultSubscription:'type = "identify"',fields:{userId:{type:"string",required:!1,description:"The user's id",label:"User ID",default:{"@path":"$.userId"}},anonymousId:{type:"string",required:!1,description:"The user's anonymous id",label:"Anonymous ID",default:{"@path":"$.anonymousId"}},displayName:{type:"string",required:!1,description:"The user's display name",label:"Display Name",default:{"@path":"$.traits.name"}},email:{type:"string",required:!1,description:"The user's email",label:"Email",default:{"@path":"$.traits.email"}},traits:{type:"object",required:!1,description:"The Segment traits to be forwarded to FullStory",label:"Traits",default:{"@path":"$.traits"}}},perform:(e,t)=>{const r=t.payload.traits||{};t.payload.anonymousId&&(r.segmentAnonymousId=t.payload.anonymousId);const n={...r,...void 0!==t.payload.email&&{email:t.payload.email},...void 0!==t.payload.displayName&&{displayName:t.payload.displayName}};t.payload.userId?e("setIdentity",{uid:t.payload.userId,properties:n},ne):e("setProperties",{type:"user",properties:n},ne)}},b=$,T={title:"Viewed Page",description:"Sets page properties events",defaultSubscription:'type = "page"',platform:"web",fields:{pageName:{type:"string",required:!1,description:"The name of the page that was viewed.",label:"Page Name",default:{"@if":{exists:{"@path":"$.category"},then:{"@path":"$.category"},else:{"@path":"$.name"}}}},properties:{type:"object",required:!1,description:"The properties of the page that was viewed.",label:"Properties",default:{"@path":"$.properties"}}},perform:(e,t)=>{t.payload.pageName?e.setVars("page",{pageName:t.payload.pageName,...t.payload.properties},ne):t.payload.properties&&e.setVars("page",t.payload.properties,ne)}},O=T,S={title:"Viewed Page V2",description:"Sets page properties",defaultSubscription:'type = "page"',platform:"web",fields:{pageName:{type:"string",required:!1,description:"The name of the page that was viewed.",label:"Page Name",default:{"@if":{exists:{"@path":"$.category"},then:{"@path":"$.category"},else:{"@path":"$.name"}}}},properties:{type:"object",required:!1,description:"The properties of the page that was viewed.",label:"Properties",default:{"@path":"$.properties"}}},perform:(e,t)=>{e("setProperties",{type:"page",properties:t.payload.pageName?{pageName:t.payload.pageName,...t.payload.properties}:{...t.payload.properties}},ne)}},N=S;o(2699);var P=o(3531),A=o.n(P);const I=e=>{if(e.username||e.password){const t=e.username||"",r=e.password||"";return{headers:{Authorization:`Basic ${A()(`${t}:${r}`)}`}}}},R=async(e,t,r)=>(Object.defineProperty(r.headers,"toJSON",{enumerable:!1,value:()=>(e=>{const t={};for(const[r,n]of e.entries())t[r]=n;return t})(r.headers)}),r),C=async(e,t,r)=>{const n=t.statsContext?.statsClient,o=r;let s;if(t.skipResponseCloning)s=await r.text();else{n?.incr("before-response-clone",1);const e=r.clone();s=await e.text(),n?.incr("after-response-clone",1)}const a=t.agent;let i;a&&a.destroy();try{i=o.headers.get("content-type")?.includes("application/json")?JSON.parse(s):s}catch(e){}return o.content=s,o.data=i,o};var j=o(2501),D=o(3033);const x=o.n(D)();var k=o(6933);if(null!=globalThis.process&&"object"==typeof globalThis.process.env&&globalThis.process.env.DEFAULT_REQUEST_TIMEOUT){const e=parseInt(globalThis.process.env.DEFAULT_REQUEST_TIMEOUT,10);Number.isNaN(e)}const M=e=>null!==e&&"object"==typeof e;function L(e,t){const r=new D.Headers(e||{});return new D.Headers(t||{}).forEach(((e,t)=>{"undefined"===e?r.delete(t):r.set(t,e)})),r}function U(...e){let t={},r={};for(const n of e){if(Array.isArray(n))Array.isArray(t)||(t=[]),t=[...t,...n];else if(M(n)){for(let[e,r]of Object.entries(n))M(r)&&e in t&&(r=U(t[e],r)),t[e]=r;M(n.headers)&&(r=L(r,n.headers))}t.headers=r}return t}function F(...e){for(const t of e)if(!(0,k.Kn)(t))throw new TypeError("The 'options' argument must be an object");return U({},...e)}class V extends j.CustomError{constructor(e,t,r){super(e.statusText??String(e.status??"Unknown response error")),this.response=e,this.request=t,this.options=r}}class q extends j.CustomError{constructor(e,t){super("Request timed out"),this.request=e,this.options=t,this.code="ETIMEDOUT"}}class z{constructor(e,t={}){this.setOptions(e,t)}setOptions(e,t){var r;if(this.options={...t,method:(r=t.method??"get",r.toUpperCase()),throwHttpErrors:!1!==t.throwHttpErrors,timeout:t.timeout??1e4},this.abortController=new AbortController,this.options.signal&&this.options.signal.addEventListener("abort",(()=>{this.abortController.abort()})),this.options.signal=this.abortController.signal,this.request=new D.Request(e,this.options),this.options.searchParams){const e=new URLSearchParams(this.options.searchParams),t=new URL(this.request.url);t.search=e.toString(),this.request=new D.Request(new D.Request(t.toString(),this.request),this.options)}void 0!==this.options.json&&(this.options.body=JSON.stringify(this.options.json),this.request.headers.set("content-type","application/json"),this.request=new D.Request(this.request,{body:this.options.body}))}async executeRequest(){let e=await this.fetch();for(const t of this.options.afterResponse??[]){const r=await t(this.request,this.options,e);r instanceof D.Response&&(e=r)}if(!e.ok&&this.options.throwHttpErrors)throw new V(e,this.request,this.options);return e}async fetch(){for(const e of this.options.beforeRequest??[]){const t=await e({...this.options});t&&(0,k.Kn)(t)&&this.setOptions(this.request.url,F(this.options,t))}return!1===this.options.timeout?x(this.request.clone()):(e=this.request.clone(),t=this.abortController,r=this.options,new Promise(((n,o)=>{const s=setTimeout((()=>{t&&t.abort(),o(new q(e,r))}),r.timeout);x(e).then(n).catch(o).then((()=>clearTimeout(s)))})));var e,t,r}}(function e(t={}){const r=(e,r={})=>new z(e,F(t,r)).executeRequest();return r.extend=r=>e(F(t,r)),r})({timeout:1e4,headers:{"user-agent":"Segment (Actions)"},beforeRequest:[I],afterResponse:[C,R]});o(3807);Error;Error,Symbol.iterator;var H=o(3944),K=o.n(H),G=o(3052),B=o.n(G),Y=o(8879),W=o.n(Y);const J=B()(new(K())({coerceTypes:"array",allErrors:!0,allowUnionTypes:!0,verbose:!0,removeAdditional:!0}));J.addFormat("text",!0),J.addFormat("date-like",(e=>{let t=W()(e);return String(Number(e))===e&&(13===e.length&&(t=W()(Number(e))),t=W().unix(Number(e))),t.isValid()}));var Q,X;!function(e){e.CONTINUE="CONTINUE",e.SWITCHING_PROTOCOLS="SWITCHING_PROTOCOLS",e.PROCESSING="PROCESSING",e.EARLY_HINTS="EARLY_HINTS",e.OK="OK",e.CREATED="CREATED",e.ACCEPTED="ACCEPTED",e.NON_AUTHORITATIVE_INFORMATION="NON_AUTHORITATIVE_INFORMATION",e.NO_CONTENT="NO_CONTENT",e.RESET_CONTENT="RESET_CONTENT",e.PARTIAL_CONTENT="PARTIAL_CONTENT",e.MULTI_STATUS="MULTI_STATUS",e.ALREADY_REPORTED="ALREADY_REPORTED",e.IM_USED="IM_USED",e.MULTIPLE_CHOICES="MULTIPLE_CHOICES",e.MOVED_PERMENANTLY="MOVED_PERMENANTLY",e.FOUND="FOUND",e.SEE_OTHER="SEE_OTHER",e.NOT_MODIFIED="NOT_MODIFIED",e.USE_PROXY="USE_PROXY",e.SWITCH_PROXY="SWITCH_PROXY",e.TEMPORARY_REDIRECT="TEMPORARY_REDIRECT",e.PERMANENT_REDIRECT="PERMANENT_REDIRECT",e.BAD_REQUEST="BAD_REQUEST",e.UNAUTHORIZED="UNAUTHORIZED",e.PAYMENT_REQUIRED="PAYMENT_REQUIRED",e.FORBIDDEN="FORBIDDEN",e.NOT_FOUND="NOT_FOUND",e.METHOD_NOT_ALLOWED="METHOD_NOT_ALLOWED",e.NOT_ACCEPTABLE="NOT_ACCEPTABLE",e.PROXY_AUTHENTICATION_REQUIRED="PROXY_AUTHENTICATION_REQUIRED",e.REQUEST_TIMEOUT="REQUEST_TIMEOUT",e.CONFLICT="CONFLICT",e.GONE="GONE",e.LENGTH_REQUIRED="LENGTH_REQUIRED",e.PRECONDITION_FAILED="PRECONDITION_FAILED",e.PAYLOAD_TOO_LARGE="PAYLOAD_TOO_LARGE",e.URI_TOO_LONG="URI_TOO_LONG",e.UNSUPPORTED_MEDIA_TYPE="UNSUPPORTED_MEDIA_TYPE",e.RANGE_NOT_SATISFIABLE="RANGE_NOT_SATISFIABLE",e.EXPECTATION_FAILED="EXPECTATION_FAILED",e.IM_A_TEAPOT="IM_A_TEAPOT",e.MISDIRECTED_REQUEST="MISDIRECTED_REQUEST",e.UNPROCESSABLE_ENTITY="UNPROCESSABLE_ENTITY",e.LOCKED="LOCKED",e.FAILED_DEPENDENCY="FAILED_DEPENDENCY",e.TOO_EARLY="TOO_EARLY",e.UPGRADE_REQUIRED="UPGRADE_REQUIRED",e.PRECONDITION_REQUIRED="PRECONDITION_REQUIRED",e.TOO_MANY_REQUESTS="TOO_MANY_REQUESTS",e.REQUEST_HEADER_FIELDS_TOO_LARGE="REQUEST_HEADER_FIELDS_TOO_LARGE",e.UNAVAILABLE_FOR_LEGAL_REASONS="UNAVAILABLE_FOR_LEGAL_REASONS",e.INTERNAL_SERVER_ERROR="INTERNAL_SERVER_ERROR",e.NOT_IMPLEMENTED="NOT_IMPLEMENTED",e.BAD_GATEWAY="BAD_GATEWAY",e.SERVICE_UNAVAILABLE="SERVICE_UNAVAILABLE",e.GATEWAY_TIMEOUT="GATEWAY_TIMEOUT",e.HTTP_VERSION_NOT_SUPPORTED="HTTP_VERSION_NOT_SUPPORTED",e.VARIANT_ALSO_NEGOTIATES="VARIANT_ALSO_NEGOTIATES",e.INSUFFICIENT_STORAGE="INSUFFICIENT_STORAGE",e.LOOP_DETECTED="LOOP_DETECTED",e.BANDWIDTH_LIMIT_EXCEEDED="BANDWIDTH_LIMIT_EXCEEDED",e.NOT_EXTENDED="NOT_EXTENDED",e.NETWORK_AUTHENTICATION_REQUIRED="NETWORK_AUTHENTICATION_REQUIRED",e.SITE_IS_OVERLOADED="SITE_IS_OVERLOADED",e.CANCELLED="CANCELLED"}(Q||(Q={})),function(e){e.INVALID_AUTHENTICATION="INVALID_AUTHENTICATION",e.PAYLOAD_VALIDATION_FAILED="PAYLOAD_VALIDATION_FAILED",e.INVALID_CURRENCY_CODE="INVALID_CURRENCY_CODE",e.RETRYABLE_ERROR="RETRYABLE_ERROR",e.REFRESH_TOKEN_EXPIRED="REFRESH_TOKEN_EXPIRED",e.OAUTH_REFRESH_FAILED="OAUTH_REFRESH_FAILED",e.SELF_TIMEOUT="SELF_TIMEOUT",e.UNKNOWN_ERROR="UNKNOWN_ERROR"}(X||(X={}));Q.CONTINUE,Q.SWITCHING_PROTOCOLS,Q.PROCESSING,Q.EARLY_HINTS,Q.OK,Q.CREATED,Q.ACCEPTED,Q.NON_AUTHORITATIVE_INFORMATION,Q.NO_CONTENT,Q.RESET_CONTENT,Q.PARTIAL_CONTENT,Q.MULTI_STATUS,Q.ALREADY_REPORTED,Q.IM_USED,Q.MULTIPLE_CHOICES,Q.MOVED_PERMENANTLY,Q.FOUND,Q.SEE_OTHER,Q.NOT_MODIFIED,Q.USE_PROXY,Q.SWITCH_PROXY,Q.TEMPORARY_REDIRECT,Q.PERMANENT_REDIRECT,Q.BAD_REQUEST,Q.UNAUTHORIZED,Q.PAYMENT_REQUIRED,Q.FORBIDDEN,Q.NOT_FOUND,Q.METHOD_NOT_ALLOWED,Q.NOT_ACCEPTABLE,Q.PROXY_AUTHENTICATION_REQUIRED,Q.REQUEST_TIMEOUT,Q.CONFLICT,Q.GONE,Q.LENGTH_REQUIRED,Q.PRECONDITION_FAILED,Q.PAYLOAD_TOO_LARGE,Q.URI_TOO_LONG,Q.UNSUPPORTED_MEDIA_TYPE,Q.RANGE_NOT_SATISFIABLE,Q.EXPECTATION_FAILED,Q.IM_A_TEAPOT,Q.MISDIRECTED_REQUEST,Q.UNPROCESSABLE_ENTITY,Q.LOCKED,Q.FAILED_DEPENDENCY,Q.TOO_EARLY,Q.UPGRADE_REQUIRED,Q.PRECONDITION_REQUIRED,Q.TOO_MANY_REQUESTS,Q.REQUEST_HEADER_FIELDS_TOO_LARGE,Q.UNAVAILABLE_FOR_LEGAL_REASONS,Q.CANCELLED,Q.INTERNAL_SERVER_ERROR,Q.NOT_IMPLEMENTED,Q.BAD_GATEWAY,Q.SERVICE_UNAVAILABLE,Q.GATEWAY_TIMEOUT,Q.HTTP_VERSION_NOT_SUPPORTED,Q.VARIANT_ALSO_NEGOTIATES,Q.INSUFFICIENT_STORAGE,Q.LOOP_DETECTED,Q.BANDWIDTH_LIMIT_EXCEEDED,Q.NOT_EXTENDED,Q.NETWORK_AUTHENTICATION_REQUIRED,Q.SITE_IS_OVERLOADED;var Z;!function(e){e.INTEGRATIONS="INTEGRATIONS",e.DESTINATION="DESTINATION"}(Z||(Z={}));o(7128);o(5145);for(var ee=256,te=[];ee--;)te[ee]=(ee+256).toString(16).substring(1);function re(e){const t={};for(const r of Object.keys(e)){const n=e[r].default;void 0!==n&&(t[r]=n)}return t}const ne="segment-browser-actions",oe=function(e){const t=async t=>(await o.e(845).then(o.bind(o,3962))).generatePlugins(e,t,t.subscriptions||[]);return t.pluginName=e.name,t}({name:"Fullstory (Actions)",slug:"actions-fullstory",mode:"device",presets:[{name:"Track Event",subscribe:'type = "track"',partnerAction:"trackEventV2",mapping:re(y.fields),type:"automatic"},{name:"Identify User",subscribe:'type = "identify"',partnerAction:"identifyUserV2",mapping:re(b.fields),type:"automatic"},{name:"Viewed Page",subscribe:'type = "page"',partnerAction:"viewedPageV2",mapping:re(N.fields),type:"automatic"}],settings:{orgId:{description:"The organization ID for FullStory.",label:"FS Org",type:"string",required:!0},debug:{description:"Enables FullStory debug mode.",label:"Debug mode",type:"boolean",required:!1,default:!1},recordOnlyThisIFrame:{description:"Enables FullStory inside an iframe.",label:"Capture only this iFrame",type:"boolean",required:!1,default:!1}},actions:{trackEvent:h,trackEventV2:y,identifyUser:w,identifyUserV2:b,viewedPage:O,viewedPageV2:N},initialize:async({settings:e},t)=>(d(e),await t.resolveWhen((()=>Object.prototype.hasOwnProperty.call(window,"FS")),100),window.FS)})})(),window.fullstoryDestination=s.default})();
//# sourceMappingURL=26abbdb928c4c2e81236.js.map